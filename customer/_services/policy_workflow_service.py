import uuid
import time
import logging
import json
import os
import re
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User

from customer.models import Customer, CustomerPlatformIdentity, CustomerPolicyWorkflowCache, PolicyWorkflowAuditLog
from .tpa_service import TPAApiService
from customer.exceptions import PolicyWorkflowError, CustomerDataError

logger = logging.getLogger(__name__)


class WorkflowConfigLoader:
    """Loads and manages workflow configurations from JSON files"""

    @staticmethod
    def load_workflow_config(workflow_type: str) -> Dict[str, Any]:
        """Load workflow configuration from JSON file"""
        config_files = {
            'POLICY_LIST': 'policy-list-workflow.json',
            'POLICY_DETAILS': 'policy-details-workflow.json'
        }

        if workflow_type not in config_files:
            raise PolicyWorkflowError(f"Unknown workflow type: {workflow_type}")

        config_file = config_files[workflow_type]
        config_path = os.path.join(os.path.dirname(__file__), config_file)

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            raise PolicyWorkflowError(f"Workflow configuration file not found: {config_file}")
        except json.JSONDecodeError as e:
            raise PolicyWorkflowError(f"Invalid JSON in workflow configuration: {str(e)}")


class WorkflowTemplateResolver:
    """Resolves template variables in workflow configurations"""

    @staticmethod
    def resolve_template_variables(template: str, context: Dict[str, Any]) -> str:
        """Resolve template variables like {{variable_name}} in strings"""
        if not isinstance(template, str):
            return template

        # Find all template variables in the format {{variable_name}}
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, template)

        resolved = template
        for match in matches:
            variable_name = match.strip()
            if variable_name in context:
                resolved = resolved.replace(f'{{{{{variable_name}}}}}', str(context[variable_name]))
            else:
                logger.warning(f"Template variable '{variable_name}' not found in context")

        return resolved

    @staticmethod
    def resolve_request_data(request_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve template variables in request data"""
        resolved = {}
        for key, value in request_data.items():
            if isinstance(value, str):
                resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
            else:
                resolved[key] = value
        return resolved

    @staticmethod
    def resolve_headers(headers: Dict[str, str], context: Dict[str, Any]) -> Dict[str, str]:
        """Resolve template variables in headers"""
        resolved = {}
        for key, value in headers.items():
            resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
        return resolved


class WorkflowDataExtractor:
    """Extracts data from API responses using JSONPath-like expressions"""

    @staticmethod
    def extract_data(response: Any, extract_config: Dict[str, str]) -> Dict[str, Any]:
        """Extract data from response using extraction configuration"""
        if not extract_config:
            return {}

        extracted = {}
        for key, path in extract_config.items():
            try:
                value = WorkflowDataExtractor._extract_by_path(response, path)
                extracted[key] = value
            except Exception as e:
                logger.warning(f"Failed to extract '{key}' using path '{path}': {str(e)}")
                extracted[key] = None

        return extracted

    @staticmethod
    def _extract_by_path(data: Any, path: str) -> Any:
        """Extract data using JSONPath-like syntax"""
        if path == '$':
            return data

        if not path.startswith('$.'):
            return None

        # Remove the '$.' prefix
        path_parts = path[2:].split('.')
        current = data

        for part in path_parts:
            if '[*]' in part:
                # Handle array extraction like 'ListOfPolicyListSocial[*].MemberCode'
                array_key = part.replace('[*]', '')
                if isinstance(current, dict) and array_key in current:
                    array_data = current[array_key]
                    if isinstance(array_data, list):
                        # Get the next part to extract from each array item
                        remaining_parts = path_parts[path_parts.index(part) + 1:]
                        if remaining_parts:
                            # Extract specific field from each array item
                            result = []
                            for item in array_data:
                                item_value = item
                                for remaining_part in remaining_parts:
                                    if isinstance(item_value, dict) and remaining_part in item_value:
                                        item_value = item_value[remaining_part]
                                    else:
                                        item_value = None
                                        break
                                if item_value is not None:
                                    result.append(item_value)
                            return result
                        else:
                            return array_data
                return None
            else:
                # Regular field access
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None

        return current


class WorkflowValidator:
    """Validates workflow step results"""

    @staticmethod
    def validate_response(response: Any, validation_rule: str) -> bool:
        """Validate response against validation rule"""
        if not validation_rule:
            return True

        try:
            # Handle citizen ID verification
            if "Status == '1'" in validation_rule:
                return WorkflowValidator._validate_citizen_id_status(response)

            # Handle registration verification
            elif "Status == 'YES'" in validation_rule:
                return WorkflowValidator._validate_registration_status(response)

            else:
                logger.warning(f"Unknown validation rule: {validation_rule}")
                return True

        except Exception as e:
            logger.error(f"Validation failed: {str(e)}")
            return False

    @staticmethod
    def _validate_citizen_id_status(response: Any) -> bool:
        """Validate citizen ID verification response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        search_results = response.get('ListOfSearchCitizenID', [])
        if not isinstance(search_results, list) or len(search_results) == 0:
            raise ValueError("No citizen ID search results found")

        first_result = search_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid search result format")

        status = first_result.get('Status')
        if status != '1':
            raise ValueError(f"Citizen ID verification failed: Status = {status}")

        return True

    @staticmethod
    def _validate_registration_status(response: Any) -> bool:
        """Validate registration check response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        check_results = response.get('ListOfCheckRegister', [])
        if not isinstance(check_results, list) or len(check_results) == 0:
            raise ValueError("No registration check results found")

        first_result = check_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid check result format")

        status = first_result.get('Status')
        if status != 'YES':
            raise ValueError(f"Registration verification failed: Status = {status}")

        return True


class GenericWorkflowExecutor:
    """Generic workflow execution engine that processes JSON-defined workflows"""

    def __init__(self, tpa_service: TPAApiService):
        self.tpa_service = tpa_service
        self.step_handlers = {
            'get_bearer_token': self._execute_get_bearer_token,
            'verify_citizen_id': self._execute_verify_citizen_id,
            'verify_registration': self._execute_verify_registration,
            'fetch_policy_list': self._execute_fetch_policy_list,
            'fetch_policy_details': self._execute_fetch_policy_details,
        }

    def execute_workflow(self, workflow_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a complete workflow based on JSON configuration"""
        config = WorkflowConfigLoader.load_workflow_config(workflow_type)

        # Initialize execution context
        execution_context = {
            'step_data': {},
            'step_results': {},
            'tpa_calls': 0,
            'tpa_time': 0,
            **context
        }

        # Resolve data source (database or fixed values)
        self._resolve_data_source(config, execution_context)

        # Execute each step in sequence
        steps = sorted(config['steps'], key=lambda x: x['id'])
        for step in steps:
            step_result = self._execute_step(step, execution_context)

            step_key = f"step_{step['id']}_{step['name']}"
            execution_context['step_results'][step_key] = step_result

            if not step_result['success']:
                raise PolicyWorkflowError(f"Step {step['id']} ({step['name']}) failed: {step_result.get('error', 'Unknown error')}")

            # Store extracted data for subsequent steps
            if 'extracted_data' in step_result:
                execution_context['step_data'].update(step_result['extracted_data'])

        return execution_context

    def _resolve_data_source(self, config: Dict[str, Any], context: Dict[str, Any]):
        """Resolve data source based on configuration mode"""
        config_mode = config.get('config', {}).get('mode', 'fixed')

        if config_mode == 'fixed':
            # Use fixed values from configuration
            fixed_values = config.get('config', {}).get('fixed_values', {})
            context['step_data'].update(fixed_values)
        elif config_mode == 'database':
            # Get values from database (would need customer context)
            # For now, we'll use the customer's platform identity
            customer = context.get('customer')
            if customer:
                platform_identity = PolicyWorkflowService._get_platform_identity(customer)
                context['step_data'].update({
                    'social_id': platform_identity.platform_user_id,
                    'channel_id': platform_identity.channel_id,
                    'citizen_id': customer.national_id,
                    'channel': platform_identity.platform
                })

        # Add member_code if provided in context
        if 'member_code' in context:
            context['step_data']['member_code'] = context['member_code']

    def _execute_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow step with retry logic"""
        step_name = step['name']
        max_retries = step.get('retry', 1)

        for attempt in range(max_retries):
            try:
                start_time = time.time()

                # Get step handler
                handler = self.step_handlers.get(step_name)
                if not handler:
                    raise PolicyWorkflowError(f"Unknown step handler: {step_name}")

                # Execute step
                response = handler(step, context)
                execution_time = (time.time() - start_time) * 1000

                # Extract data from response
                extracted_data = {}
                if 'extract' in step:
                    extracted_data = WorkflowDataExtractor.extract_data(response, step['extract'])

                # Validate response if validation rule exists
                if 'validate' in step:
                    WorkflowValidator.validate_response(response, step['validate'])

                # Update context counters
                context['tpa_calls'] += 1
                context['tpa_time'] += execution_time

                return {
                    'success': True,
                    'time_ms': execution_time,
                    'data': response,
                    'extracted_data': extracted_data,
                    'attempt': attempt + 1
                }

            except Exception as e:
                logger.warning(f"Step {step_name} attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    # Last attempt failed
                    return {
                        'success': False,
                        'error': str(e),
                        'attempt': attempt + 1
                    }
                else:
                    # Wait before retry
                    retry_delay = step.get('retry_delay_seconds', 3)
                    time.sleep(retry_delay * (2 ** attempt))  # Exponential backoff

        # This should never be reached, but just in case
        return {
            'success': False,
            'error': 'Maximum retries exceeded',
            'attempt': max_retries
        }

    def _execute_get_bearer_token(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute get bearer token step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step['request'], context['step_data'])
        headers = WorkflowTemplateResolver.resolve_headers(step.get('headers', {}), context['step_data'])

        return self.tpa_service.make_dynamic_request(
            step['endpoint'],
            step['method'],
            request_data,
            headers
        )

    def _execute_verify_citizen_id(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute verify citizen ID step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step['request'], context['step_data'])
        headers = WorkflowTemplateResolver.resolve_headers(step.get('headers', {}), context['step_data'])

        return self.tpa_service.make_dynamic_request(
            step['endpoint'],
            step['method'],
            request_data,
            headers
        )

    def _execute_verify_registration(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute verify registration step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step['request'], context['step_data'])
        headers = WorkflowTemplateResolver.resolve_headers(step.get('headers', {}), context['step_data'])

        return self.tpa_service.make_dynamic_request(
            step['endpoint'],
            step['method'],
            request_data,
            headers
        )

    def _execute_fetch_policy_list(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute fetch policy list step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step['request'], context['step_data'])
        headers = WorkflowTemplateResolver.resolve_headers(step.get('headers', {}), context['step_data'])

        return self.tpa_service.make_dynamic_request(
            step['endpoint'],
            step['method'],
            request_data,
            headers
        )

    def _execute_fetch_policy_details(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute fetch policy details step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step['request'], context['step_data'])
        headers = WorkflowTemplateResolver.resolve_headers(step.get('headers', {}), context['step_data'])

        return self.tpa_service.make_dynamic_request(
            step['endpoint'],
            step['method'],
            request_data,
            headers
        )



class PolicyWorkflowService:
    """Service for executing policy workflows with caching and audit logging"""
    
    CACHE_DURATION_MINUTES = 30
    
    @classmethod
    def execute_policy_list_workflow(cls, customer_id: int, user: User) -> Dict[str, Any]:
        """Execute complete policy list workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity(customer)
            
            # Check cache first
            cached_result = cls._get_cached_result(customer, 'POLICY_LIST')
            if cached_result:
                logger.info(f"Returning cached policy list for customer {customer_id}")
                return cached_result['processed_data']
            
            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                'customer': customer,
                'platform_identity': platform_identity,
                'execution_id': execution_id
            }

            execution_result = workflow_executor.execute_workflow('POLICY_LIST', context)

            # Extract workflow data
            policy_list_data = execution_result['step_data'].get('policies', [])
            raw_response = {'ListOfPolicyListSocial': policy_list_data}

            # Process and format data
            processed_data = cls._process_policy_list_data(raw_response)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            cls._cache_result(customer, 'POLICY_LIST', None, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, 'POLICY_LIST', execution_id, execution_result['step_results'],
                          total_time, True, None, execution_result['tpa_calls'], execution_result['tpa_time'])

            return processed_data
            
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            cls._log_audit(customer, user, 'POLICY_LIST', execution_id, step_results,
                          total_time, False, error_details, tpa_calls, tpa_time)

            logger.error(f"Policy list workflow failed for customer {customer_id}: {str(e)}")
            raise PolicyWorkflowError(f"Policy list workflow failed: {str(e)}")
    
    @classmethod
    def execute_policy_details_workflow(cls, customer_id: int, member_code: str, user: User) -> Dict[str, Any]:
        """Execute complete policy details workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()

        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity(customer)

            # Check cache first
            cached_result = cls._get_cached_result(customer, 'POLICY_DETAILS', member_code)
            if cached_result:
                logger.info(f"Returning cached policy details for customer {customer_id}, member {member_code}")
                return cached_result['processed_data']

            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                'customer': customer,
                'platform_identity': platform_identity,
                'execution_id': execution_id,
                'member_code': member_code
            }

            execution_result = workflow_executor.execute_workflow('POLICY_DETAILS', context)

            # Extract workflow data
            policy_details = execution_result['step_data'].get('policy_details', [])
            claims_data = execution_result['step_data'].get('claims_data', [])
            raw_response = {
                'ListOfPolDet': policy_details,
                'ListOfPolClaim': claims_data
            }

            # Process and format data
            processed_data = cls._process_policy_details_data(raw_response, member_code)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            cls._cache_result(customer, 'POLICY_DETAILS', member_code, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, 'POLICY_DETAILS', execution_id, execution_result['step_results'],
                          total_time, True, None, execution_result['tpa_calls'], execution_result['tpa_time'])

            return processed_data

        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            cls._log_audit(customer, user, 'POLICY_DETAILS', execution_id, step_results,
                          total_time, False, error_details, tpa_calls, tpa_time)

            logger.error(f"Policy details workflow failed for customer {customer_id}, member {member_code}: {str(e)}")
            raise PolicyWorkflowError(f"Policy details workflow failed: {str(e)}")
    
    @classmethod
    def _get_platform_identity(cls, customer: Customer) -> CustomerPlatformIdentity:
        """Get platform identity for customer"""
        platform_identity = customer.get_identity_for_platform('LINE')
        if not platform_identity:
            raise CustomerDataError(f"No LINE platform identity found for customer {customer.customer_id}")
        return platform_identity

    @classmethod
    def _get_cached_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str] = None) -> Optional[Dict]:
        """Get cached workflow result if available and not expired"""
        try:
            cache_entry = CustomerPolicyWorkflowCache.objects.get(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                expires_at__gt=timezone.now()
            )
            return {
                'processed_data': cache_entry.processed_data,
                'execution_id': cache_entry.execution_id
            }
        except CustomerPolicyWorkflowCache.DoesNotExist:
            return None

    @classmethod
    def _cache_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str],
                     platform_identity: CustomerPlatformIdentity, raw_data: Dict, processed_data: Dict,
                     execution_id: str, execution_time: float):
        """Cache workflow result"""
        expires_at = timezone.now() + timedelta(minutes=cls.CACHE_DURATION_MINUTES)

        with transaction.atomic():
            # Delete existing cache entry if exists
            CustomerPolicyWorkflowCache.objects.filter(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code
            ).delete()

            # Create new cache entry
            CustomerPolicyWorkflowCache.objects.create(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                citizen_id=customer.national_id,
                social_id=platform_identity.platform_user_id,
                channel_id=platform_identity.channel_id,
                raw_response_data=raw_data,
                processed_data=processed_data,
                execution_id=execution_id,
                execution_time_ms=int(execution_time),
                success=True,
                expires_at=expires_at
            )

    @classmethod
    def _log_audit(cls, customer: Customer, user: User, workflow_type: str, execution_id: str,
                  step_results: Dict, total_time: float, success: bool, error_details: Optional[Dict],
                  tpa_calls: int, tpa_time: float):
        """Log workflow execution audit"""
        PolicyWorkflowAuditLog.objects.create(
            customer=customer,
            requested_by=user,
            workflow_type=workflow_type,
            execution_id=execution_id,
            step_results=step_results,
            total_execution_time_ms=int(total_time),
            success=success,
            error_details=error_details,
            tpa_calls_made=tpa_calls,
            tpa_total_time_ms=int(tpa_time)
        )

    @classmethod
    def _process_policy_list_data(cls, raw_data: Dict) -> Dict[str, Any]:
        """Process and format policy list data for frontend"""
        policy_list = raw_data.get('ListOfPolicyListSocial', [])

        # Extract member codes
        member_codes = []
        for policy in policy_list:
            member_code = policy.get('MemberCode')
            if member_code and member_code not in member_codes:
                member_codes.append(member_code)

        return {
            'policy_list_data': raw_data,
            'member_codes': member_codes,
            'execution_metadata': {
                'total_policies': len(policy_list),
                'unique_member_codes': len(member_codes),
                'processed_at': timezone.now().isoformat()
            }
        }

    @classmethod
    def _process_policy_details_data(cls, raw_data: Dict, member_code: str) -> Dict[str, Any]:
        """Process and format policy details data for frontend"""
        policy_details = raw_data.get('ListOfPolDet', [])
        policy_claims = raw_data.get('ListOfPolClaim', [])

        return {
            'policy_details_data': raw_data,
            'member_code': member_code,
            'execution_metadata': {
                'total_policy_details': len(policy_details),
                'total_claims': len(policy_claims),
                'processed_at': timezone.now().isoformat()
            }
        }
