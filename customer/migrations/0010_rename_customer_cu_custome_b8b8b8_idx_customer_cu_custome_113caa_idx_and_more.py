# Generated by Django 5.2.6 on 2025-09-17 11:30

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0009_add_policy_workflow_models'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='customerpolicyworkflowcache',
            new_name='customer_cu_custome_113caa_idx',
            old_name='customer_cu_custome_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='customerpolicyworkflowcache',
            new_name='customer_cu_custome_92159f_idx',
            old_name='customer_cu_custome_4c4c4c_idx',
        ),
        migrations.RenameIndex(
            model_name='customerpolicyworkflowcache',
            new_name='customer_cu_executi_92305e_idx',
            old_name='customer_cu_executi_d8d8d8_idx',
        ),
        migrations.RenameIndex(
            model_name='customerpolicyworkflowcache',
            new_name='customer_cu_expires_ae2781_idx',
            old_name='customer_cu_expires_e8e8e8_idx',
        ),
        migrations.RenameIndex(
            model_name='policyworkflowauditlog',
            new_name='customer_po_custome_fb5b75_idx',
            old_name='customer_po_custome_a8a8a8_idx',
        ),
        migrations.RenameIndex(
            model_name='policyworkflowauditlog',
            new_name='customer_po_executi_98f608_idx',
            old_name='customer_po_executi_f8f8f8_idx',
        ),
        migrations.RenameIndex(
            model_name='policyworkflowauditlog',
            new_name='customer_po_workflo_048efe_idx',
            old_name='customer_po_workflo_c8c8c8_idx',
        ),
    ]
