Metadata-Version: 2.4
Name: azure-cli
Version: 2.77.0
Summary: Microsoft Azure Command-Line Tools
Home-page: https://github.com/Azure/azure-cli
Author: Microsoft Corporation
Author-email: <EMAIL>
License: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: License :: OSI Approved :: MIT License
Requires-Python: >=3.9.0
License-File: LICENSE.txt
Requires-Dist: antlr4-python3-runtime~=4.13.1
Requires-Dist: azure-appconfiguration~=1.7.1
Requires-Dist: azure-batch~=15.0.0b1
Requires-Dist: azure-cli-core==2.77.0
Requires-Dist: azure-cosmos>=3.0.2,~=3.0
Requires-Dist: azure-data-tables==12.4.0
Requires-Dist: azure-datalake-store~=1.0.1
Requires-Dist: azure-keyvault-administration==4.4.0
Requires-Dist: azure-keyvault-certificates==4.7.0
Requires-Dist: azure-keyvault-keys==4.11.0
Requires-Dist: azure-keyvault-secrets==4.7.0
Requires-Dist: azure-keyvault-securitydomain==1.0.0b1
Requires-Dist: azure-mgmt-advisor==9.0.0
Requires-Dist: azure-mgmt-apimanagement==4.0.0
Requires-Dist: azure-mgmt-appconfiguration==5.0.0
Requires-Dist: azure-mgmt-appcontainers==2.0.0
Requires-Dist: azure-mgmt-applicationinsights~=1.0.0
Requires-Dist: azure-mgmt-authorization==5.0.0b1
Requires-Dist: azure-mgmt-batchai==7.0.0b1
Requires-Dist: azure-mgmt-batch~=17.3.0
Requires-Dist: azure-mgmt-billing==6.0.0
Requires-Dist: azure-mgmt-botservice~=2.0.0b3
Requires-Dist: azure-mgmt-cdn==12.0.0
Requires-Dist: azure-mgmt-cognitiveservices~=13.5.0
Requires-Dist: azure-mgmt-compute~=34.1.0
Requires-Dist: azure-mgmt-containerinstance==10.2.0b1
Requires-Dist: azure-mgmt-containerregistry==14.1.0b1
Requires-Dist: azure-mgmt-containerservice~=39.1.0
Requires-Dist: azure-mgmt-cosmosdb==9.8.0
Requires-Dist: azure-mgmt-datalake-store~=1.1.0b1
Requires-Dist: azure-mgmt-datamigration~=10.0.0
Requires-Dist: azure-mgmt-eventgrid==10.2.0b2
Requires-Dist: azure-mgmt-eventhub~=12.0.0b1
Requires-Dist: azure-mgmt-extendedlocation==1.0.0b2
Requires-Dist: azure-mgmt-hdinsight==9.0.0b3
Requires-Dist: azure-mgmt-imagebuilder~=1.3.0
Requires-Dist: azure-mgmt-iotcentral~=10.0.0b1
Requires-Dist: azure-mgmt-iothub==3.0.0
Requires-Dist: azure-mgmt-iothubprovisioningservices==1.1.0
Requires-Dist: azure-mgmt-keyvault==12.0.0
Requires-Dist: azure-mgmt-loganalytics==13.0.0b4
Requires-Dist: azure-mgmt-managementgroups~=1.0.0
Requires-Dist: azure-mgmt-maps~=2.0.0
Requires-Dist: azure-mgmt-marketplaceordering==1.1.0
Requires-Dist: azure-mgmt-media~=9.0
Requires-Dist: azure-mgmt-monitor~=7.0.0b1
Requires-Dist: azure-mgmt-msi~=7.0.0
Requires-Dist: azure-mgmt-netapp~=10.1.0
Requires-Dist: azure-mgmt-policyinsights==1.1.0b4
Requires-Dist: azure-mgmt-postgresqlflexibleservers==1.1.0b2
Requires-Dist: azure-mgmt-privatedns~=1.0.0
Requires-Dist: azure-mgmt-rdbms==10.2.0b17
Requires-Dist: azure-mgmt-mysqlflexibleservers==1.0.0b3
Requires-Dist: azure-mgmt-recoveryservicesbackup~=9.2.0
Requires-Dist: azure-mgmt-recoveryservices~=3.1.0
Requires-Dist: azure-mgmt-redhatopenshift~=1.5.0
Requires-Dist: azure-mgmt-redis~=14.5.0
Requires-Dist: azure-mgmt-resource==23.3.0
Requires-Dist: azure-mgmt-resource-deployments==1.0.0b1
Requires-Dist: azure-mgmt-resource-deploymentscripts==1.0.0b1
Requires-Dist: azure-mgmt-resource-deploymentstacks==1.0.0b1
Requires-Dist: azure-mgmt-resource-templatespecs==1.0.0b1
Requires-Dist: azure-mgmt-search~=9.0
Requires-Dist: azure-mgmt-security==6.0.0
Requires-Dist: azure-mgmt-servicebus~=10.0.0b1
Requires-Dist: azure-mgmt-servicefabricmanagedclusters==2.1.0b1
Requires-Dist: azure-mgmt-servicelinker==1.2.0b3
Requires-Dist: azure-mgmt-servicefabric~=2.1.0
Requires-Dist: azure-mgmt-signalr==2.0.0b2
Requires-Dist: azure-mgmt-sqlvirtualmachine==1.0.0b5
Requires-Dist: azure-mgmt-sql==4.0.0b21
Requires-Dist: azure-mgmt-storage==23.0.0
Requires-Dist: azure-mgmt-synapse==2.1.0b5
Requires-Dist: azure-mgmt-trafficmanager~=1.0.0
Requires-Dist: azure-mgmt-web==9.0.0
Requires-Dist: azure-monitor-query==1.2.0
Requires-Dist: azure-multiapi-storage==1.5.0
Requires-Dist: azure-storage-common~=1.4
Requires-Dist: azure-synapse-accesscontrol~=0.5.0
Requires-Dist: azure-synapse-artifacts~=0.20.0
Requires-Dist: azure-synapse-managedprivateendpoints~=0.4.0
Requires-Dist: azure-synapse-spark~=0.7.0
Requires-Dist: chardet~=5.2.0
Requires-Dist: colorama~=0.4.4
Requires-Dist: distro; sys_platform == "linux"
Requires-Dist: fabric~=3.2.2
Requires-Dist: javaproperties~=0.5.1
Requires-Dist: jsondiff~=2.0.0
Requires-Dist: packaging>=20.9
Requires-Dist: paramiko<4.0.0,>=2.0.8
Requires-Dist: pycomposefile>=0.0.32
Requires-Dist: PyGithub~=1.38
Requires-Dist: PyNaCl~=1.5.0
Requires-Dist: scp~=0.13.2
Requires-Dist: semver~=3.0
Requires-Dist: setuptools
Requires-Dist: six>=1.10.0
Requires-Dist: sshtunnel~=0.1.4
Requires-Dist: tabulate
Requires-Dist: urllib3
Requires-Dist: websocket-client~=1.3.1
Requires-Dist: xmltodict~=0.12
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Microsoft Azure CLI
===================

A great cloud needs great tools; we're excited to introduce *Azure CLI*, our next generation multi-platform command line experience for Azure.

Usage
=====
.. code-block:: console

    $ az [ group ] [ subgroup ] [ command ] {parameters}


Getting Started
=====================

After installation, use the ``az configure`` command to help setup your environment.

.. code-block:: console

   $ az configure

For usage and help content, pass in the ``-h`` parameter, for example:

.. code-block:: console

   $ az storage -h
   $ az vm create -h

Highlights
===========

Here are a few features and concepts that can help you get the most out of the Azure CLI.

The following examples are showing using the ``--output table`` format, you can change your default using the ``$ az configure`` command.

Tab Completion
++++++++++++++

We support tab-completion for groups, commands, and some parameters

.. code-block:: console

   # looking up resource group and name
   $ az vm show -g [tab][tab]
   AccountingGroup   RGOne  WebPropertiesRG
   $ az vm show -g WebPropertiesRG -n [tab][tab]
   StoreVM  Bizlogic
   $ az vm show -g WebPropertiesRG -n Bizlogic

Querying
++++++++

You can use the ``--query`` parameter and the JMESPath query syntax to customize your output.

.. code-block:: console

   $ az vm list --query '[].{name:name,os:storageProfile.osDisk.osType}'
   Name                    Os
   ----------------------  -------
   storevm                 Linux
   bizlogic                Linux
   demo32111vm             Windows
   dcos-master-39DB807E-0  Linux

Creating a new Linux VM
+++++++++++++++++++++++
The following block creates a new resource group in the 'westus' region, then creates a new Ubuntu VM.  We automatically provide a series of smart defaults, such as setting up SSH with your  ``~/.ssh/id_rsa.pub`` key.  For more details, try ``az vm create -h``.

.. code-block:: console

   $ az group create -l westus -n MyGroup
   Name     Location
   -------  ----------
   MyGroup  westus

   $ az vm create -g MyGroup -n MyVM --image ubuntults
   MacAddress         ResourceGroup    PublicIpAddress    PrivateIpAddress
   -----------------  ---------------  -----------------  ------------------
   00-0D-3A-30-B2-D7  MyGroup          **************     ********

   $ ssh **************
   Welcome to Ubuntu 14.04.4 LTS (GNU/Linux 3.19.0-65-generic x86_64)

   System information as of Thu Sep 15 20:47:31 UTC 2016

   System load: 0.39              Memory usage: 2%   Processes:       80
   Usage of /:  39.6% of 1.94GB   Swap usage:   0%   Users logged in: 0

   jasonsha@MyVM:~$

More Samples and Snippets
+++++++++++++++++++++++++
For more usage examples, take a look at our `GitHub samples repo <http://github.com/Azure/azure-cli-samples>`__.

Reporting issues and feedback
=======================================

If you encounter any bugs with the tool please file an issue in the `Issues <https://github.com/Azure/azure-cli/issues>`__ section of our GitHub repo.

To provide feedback from the command line, try the ``az feedback`` command.

License
=======

`MIT <https://github.com/Azure/azure-cli/blob/master/LICENSE.txt>`__

Release History
===============

See `Azure CLI release notes <https://learn.microsoft.com/cli/azure/release-notes-azure-cli>`__.
