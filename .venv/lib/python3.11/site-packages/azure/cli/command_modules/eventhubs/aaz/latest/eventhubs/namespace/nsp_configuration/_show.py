# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "eventhubs namespace nsp-configuration show",
)
class Show(AAZCommand):
    """Get a NetworkSecurityPerimeterConfigurations resourceAssociationName
    """

    _aaz_info = {
        "version": "2025-05-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.eventhub/namespaces/{}/networksecurityperimeterconfigurations/{}", "2025-05-01-preview"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.namespace_name = AAZStrArg(
            options=["--namespace-name"],
            help="The Namespace name",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z][a-zA-Z0-9-]{6,50}[a-zA-Z0-9]$",
                max_length=50,
                min_length=6,
            ),
        )
        _args_schema.resource_association_name = AAZStrArg(
            options=["-n", "--name", "--resource-association-name"],
            help="The ResourceAssociation Name",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.NetworkSecurityPerimeterConfigurationsGetResourceAssociationName(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class NetworkSecurityPerimeterConfigurationsGetResourceAssociationName(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.EventHub/namespaces/{namespaceName}/networkSecurityPerimeterConfigurations/{resourceAssociationName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "namespaceName", self.ctx.args.namespace_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceAssociationName", self.ctx.args.resource_association_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-05-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType(
                flags={"read_only": True},
            )

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.location = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True, "read_only": True},
            )
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.applicable_features = AAZListType(
                serialized_name="applicableFeatures",
                flags={"read_only": True},
            )
            properties.is_backing_resource = AAZBoolType(
                serialized_name="isBackingResource",
                flags={"read_only": True},
            )
            properties.network_security_perimeter = AAZObjectType(
                serialized_name="networkSecurityPerimeter",
                flags={"read_only": True},
            )
            _ShowHelper._build_schema_network_security_perimeter_read(properties.network_security_perimeter)
            properties.parent_association_name = AAZStrType(
                serialized_name="parentAssociationName",
                flags={"read_only": True},
            )
            properties.profile = AAZObjectType(
                flags={"read_only": True},
            )
            properties.provisioning_issues = AAZListType(
                serialized_name="provisioningIssues",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
            )
            properties.resource_association = AAZObjectType(
                serialized_name="resourceAssociation",
                flags={"read_only": True},
            )
            properties.source_resource_id = AAZStrType(
                serialized_name="sourceResourceId",
                flags={"read_only": True},
            )

            applicable_features = cls._schema_on_200.properties.applicable_features
            applicable_features.Element = AAZStrType()

            profile = cls._schema_on_200.properties.profile
            profile.access_rules = AAZListType(
                serialized_name="accessRules",
            )
            profile.access_rules_version = AAZStrType(
                serialized_name="accessRulesVersion",
            )
            profile.name = AAZStrType()

            access_rules = cls._schema_on_200.properties.profile.access_rules
            access_rules.Element = AAZObjectType(
                flags={"read_only": True},
            )

            _element = cls._schema_on_200.properties.profile.access_rules.Element
            _element.id = AAZStrType()
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"read_only": True},
            )
            _element.type = AAZStrType()

            properties = cls._schema_on_200.properties.profile.access_rules.Element.properties
            properties.address_prefixes = AAZListType(
                serialized_name="addressPrefixes",
            )
            properties.direction = AAZStrType()
            properties.fully_qualified_domain_names = AAZListType(
                serialized_name="fullyQualifiedDomainNames",
                flags={"read_only": True},
            )
            properties.network_security_perimeters = AAZListType(
                serialized_name="networkSecurityPerimeters",
                flags={"read_only": True},
            )
            properties.subscriptions = AAZListType()

            address_prefixes = cls._schema_on_200.properties.profile.access_rules.Element.properties.address_prefixes
            address_prefixes.Element = AAZStrType()

            fully_qualified_domain_names = cls._schema_on_200.properties.profile.access_rules.Element.properties.fully_qualified_domain_names
            fully_qualified_domain_names.Element = AAZStrType()

            network_security_perimeters = cls._schema_on_200.properties.profile.access_rules.Element.properties.network_security_perimeters
            network_security_perimeters.Element = AAZObjectType(
                flags={"read_only": True},
            )
            _ShowHelper._build_schema_network_security_perimeter_read(network_security_perimeters.Element)

            subscriptions = cls._schema_on_200.properties.profile.access_rules.Element.properties.subscriptions
            subscriptions.Element = AAZObjectType(
                flags={"read_only": True},
            )

            _element = cls._schema_on_200.properties.profile.access_rules.Element.properties.subscriptions.Element
            _element.id = AAZStrType()

            provisioning_issues = cls._schema_on_200.properties.provisioning_issues
            provisioning_issues.Element = AAZObjectType(
                flags={"read_only": True},
            )

            _element = cls._schema_on_200.properties.provisioning_issues.Element
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties.provisioning_issues.Element.properties
            properties.description = AAZStrType()
            properties.issue_type = AAZStrType(
                serialized_name="issueType",
            )

            resource_association = cls._schema_on_200.properties.resource_association
            resource_association.access_mode = AAZStrType(
                serialized_name="accessMode",
            )
            resource_association.name = AAZStrType()

            return cls._schema_on_200


class _ShowHelper:
    """Helper class for Show"""

    _schema_network_security_perimeter_read = None

    @classmethod
    def _build_schema_network_security_perimeter_read(cls, _schema):
        if cls._schema_network_security_perimeter_read is not None:
            _schema.id = cls._schema_network_security_perimeter_read.id
            _schema.location = cls._schema_network_security_perimeter_read.location
            _schema.perimeter_guid = cls._schema_network_security_perimeter_read.perimeter_guid
            return

        cls._schema_network_security_perimeter_read = _schema_network_security_perimeter_read = AAZObjectType(
            flags={"read_only": True}
        )

        network_security_perimeter_read = _schema_network_security_perimeter_read
        network_security_perimeter_read.id = AAZStrType()
        network_security_perimeter_read.location = AAZStrType()
        network_security_perimeter_read.perimeter_guid = AAZStrType(
            serialized_name="perimeterGuid",
        )

        _schema.id = cls._schema_network_security_perimeter_read.id
        _schema.location = cls._schema_network_security_perimeter_read.location
        _schema.perimeter_guid = cls._schema_network_security_perimeter_read.perimeter_guid


__all__ = ["Show"]
