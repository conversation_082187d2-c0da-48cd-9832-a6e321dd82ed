# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "monitor action-group update",
)
class Update(AAZCommand):
    """Update an action group.

    :example: Update an action group
        az monitor action-group update --name MyActionGroup --resource-group MyResourceGroup --set retentionPolicy.days=365 --subscription MySubscription
    """

    _aaz_info = {
        "version": "2024-10-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.insights/actiongroups/{}", "2024-10-01-preview"],
        ]
    }

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.action_group_name = AAZStrArg(
            options=["-n", "--name", "--action-group-name"],
            help="The name of the action group.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.group_short_name = AAZStrArg(
            options=["--short-name", "--group-short-name"],
            help="The short name of the action group. This will be used in SMS messages.",
            fmt=AAZStrArgFormat(
                max_length=12,
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="Space-separated tags: key[=value] [key[=value] ...]. Use '' to clear existing tags.",
            nullable=True,
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg(
            nullable=True,
        )

        # define Arg Group "Identity"

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.arm_role_receivers = AAZListArg(
            options=["--arm-role-receivers"],
            arg_group="Properties",
            help="The list of ARM role receivers that are part of this action group. Roles are Azure RBAC roles and only built-in roles are supported.",
            nullable=True,
        )
        _args_schema.automation_runbook_receivers = AAZListArg(
            options=["--automation-runbook-receivers"],
            arg_group="Properties",
            help="The list of AutomationRunbook receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.azure_app_push_receivers = AAZListArg(
            options=["--azure-app-push-receivers"],
            arg_group="Properties",
            help="The list of AzureAppPush receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.azure_function_receivers = AAZListArg(
            options=["--azure-function-receivers"],
            arg_group="Properties",
            help="The list of azure function receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.email_receivers = AAZListArg(
            options=["--email-receivers"],
            arg_group="Properties",
            help="The list of email receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.enabled = AAZBoolArg(
            options=["--enabled"],
            arg_group="Properties",
            help="Indicates whether this action group is enabled. If an action group is not enabled, then none of its receivers will receive communications.",
        )
        _args_schema.event_hub_receivers = AAZListArg(
            options=["--event-hub-receivers"],
            arg_group="Properties",
            help="The list of event hub receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.incident_receivers = AAZListArg(
            options=["--incident-receivers"],
            arg_group="Properties",
            help="The list of incident receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.itsm_receivers = AAZListArg(
            options=["--itsm-receivers"],
            arg_group="Properties",
            help="The list of ITSM receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.logic_app_receivers = AAZListArg(
            options=["--logic-app-receivers"],
            arg_group="Properties",
            help="The list of logic app receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.sms_receivers = AAZListArg(
            options=["--sms-receivers"],
            arg_group="Properties",
            help="The list of SMS receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.voice_receivers = AAZListArg(
            options=["--voice-receivers"],
            arg_group="Properties",
            help="The list of voice receivers that are part of this action group.",
            nullable=True,
        )
        _args_schema.webhook_receivers = AAZListArg(
            options=["--webhook-receivers"],
            arg_group="Properties",
            help="The list of webhook receivers that are part of this action group.",
            nullable=True,
        )

        arm_role_receivers = cls._args_schema.arm_role_receivers
        arm_role_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.arm_role_receivers.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the arm role receiver. Names must be unique across all receivers within an action group.",
        )
        _element.role_id = AAZStrArg(
            options=["role-id"],
            help="The arm role id.",
        )
        _element.use_common_alert_schema = AAZBoolArg(
            options=["use-common-alert-schema"],
            help="Indicates whether to use common alert schema.",
            nullable=True,
        )

        automation_runbook_receivers = cls._args_schema.automation_runbook_receivers
        automation_runbook_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.automation_runbook_receivers.Element
        _element.automation_account_id = AAZStrArg(
            options=["automation-account-id"],
            help="The Azure automation account Id which holds this runbook and authenticate to Azure resource.",
        )
        _element.is_global_runbook = AAZBoolArg(
            options=["is-global-runbook"],
            help="Indicates whether this instance is global runbook.",
        )
        _element.managed_identity = AAZStrArg(
            options=["managed-identity"],
            help="The principal id of the managed identity. The value can be \"None\", \"SystemAssigned\" ",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="Indicates name of the webhook.",
            nullable=True,
        )
        _element.runbook_name = AAZStrArg(
            options=["runbook-name"],
            help="The name for this runbook.",
        )
        _element.service_uri = AAZStrArg(
            options=["service-uri"],
            help="The URI where webhooks should be sent.",
            nullable=True,
        )
        _element.use_common_alert_schema = AAZBoolArg(
            options=["use-common-alert-schema"],
            help="Indicates whether to use common alert schema.",
            nullable=True,
        )
        _element.webhook_resource_id = AAZStrArg(
            options=["webhook-resource-id"],
            help="The resource id for webhook linked to this runbook.",
        )

        azure_app_push_receivers = cls._args_schema.azure_app_push_receivers
        azure_app_push_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.azure_app_push_receivers.Element
        _element.email_address = AAZStrArg(
            options=["email-address"],
            help="The email address registered for the Azure mobile app.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the Azure mobile app push receiver. Names must be unique across all receivers within an action group.",
        )

        azure_function_receivers = cls._args_schema.azure_function_receivers
        azure_function_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.azure_function_receivers.Element
        _element.function_app_resource_id = AAZStrArg(
            options=["function-app-resource-id"],
            help="The azure resource id of the function app.",
        )
        _element.function_name = AAZStrArg(
            options=["function-name"],
            help="The function name in the function app.",
        )
        _element.http_trigger_url = AAZStrArg(
            options=["http-trigger-url"],
            help="The http trigger url where http request sent to.",
        )
        _element.managed_identity = AAZStrArg(
            options=["managed-identity"],
            help="The principal id of the managed identity. The value can be \"None\", \"SystemAssigned\" ",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the azure function receiver. Names must be unique across all receivers within an action group.",
        )
        _element.use_common_alert_schema = AAZBoolArg(
            options=["use-common-alert-schema"],
            help="Indicates whether to use common alert schema.",
            nullable=True,
        )

        email_receivers = cls._args_schema.email_receivers
        email_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.email_receivers.Element
        _element.email_address = AAZStrArg(
            options=["email-address"],
            help="The email address of this receiver.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the email receiver. Names must be unique across all receivers within an action group.",
        )
        _element.use_common_alert_schema = AAZBoolArg(
            options=["use-common-alert-schema"],
            help="Indicates whether to use common alert schema.",
            nullable=True,
        )

        event_hub_receivers = cls._args_schema.event_hub_receivers
        event_hub_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.event_hub_receivers.Element
        _element.event_hub_name = AAZStrArg(
            options=["event-hub-name"],
            help="The name of the specific Event Hub queue",
        )
        _element.event_hub_name_space = AAZStrArg(
            options=["event-hub-name-space"],
            help="The Event Hub namespace",
        )
        _element.managed_identity = AAZStrArg(
            options=["managed-identity"],
            help="The principal id of the managed identity. The value can be \"None\", \"SystemAssigned\" ",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the Event hub receiver. Names must be unique across all receivers within an action group.",
        )
        _element.subscription_id = AAZStrArg(
            options=["subscription-id"],
            help="The Id for the subscription containing this event hub",
        )
        _element.tenant_id = AAZStrArg(
            options=["tenant-id"],
            help="The tenant Id for the subscription containing this event hub",
            nullable=True,
        )
        _element.use_common_alert_schema = AAZBoolArg(
            options=["use-common-alert-schema"],
            help="Indicates whether to use common alert schema.",
            nullable=True,
        )

        incident_receivers = cls._args_schema.incident_receivers
        incident_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.incident_receivers.Element
        _element.connection = AAZObjectArg(
            options=["connection"],
            help="The incident service connection",
        )
        _element.incident_management_service = AAZStrArg(
            options=["incident-management-service"],
            help="The incident management service type",
            enum={"Icm": "Icm"},
        )
        _element.mappings = AAZDictArg(
            options=["mappings"],
            help="Field mappings for the incident service",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the Incident receiver. Names must be unique across all receivers within an action group.",
        )

        connection = cls._args_schema.incident_receivers.Element.connection
        connection.id = AAZStrArg(
            options=["id"],
            help="GUID value representing the connection ID for the incident management service.",
        )
        connection.name = AAZStrArg(
            options=["name"],
            help="The name of the connection.",
        )

        mappings = cls._args_schema.incident_receivers.Element.mappings
        mappings.Element = AAZStrArg(
            nullable=True,
        )

        itsm_receivers = cls._args_schema.itsm_receivers
        itsm_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.itsm_receivers.Element
        _element.connection_id = AAZStrArg(
            options=["connection-id"],
            help="Unique identification of ITSM connection among multiple defined in above workspace.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the Itsm receiver. Names must be unique across all receivers within an action group.",
        )
        _element.region = AAZStrArg(
            options=["region"],
            help="Region in which workspace resides. Supported values:'centralindia','japaneast','southeastasia','australiasoutheast','uksouth','westcentralus','canadacentral','eastus','westeurope'",
        )
        _element.ticket_configuration = AAZStrArg(
            options=["ticket-configuration"],
            help="JSON blob for the configurations of the ITSM action. CreateMultipleWorkItems option will be part of this blob as well.",
        )
        _element.workspace_id = AAZStrArg(
            options=["workspace-id"],
            help="OMS LA instance identifier.",
        )

        logic_app_receivers = cls._args_schema.logic_app_receivers
        logic_app_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.logic_app_receivers.Element
        _element.callback_url = AAZStrArg(
            options=["callback-url"],
            help="The callback url where http request sent to.",
        )
        _element.managed_identity = AAZStrArg(
            options=["managed-identity"],
            help="The principal id of the managed identity. The value can be \"None\", \"SystemAssigned\" ",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the logic app receiver. Names must be unique across all receivers within an action group.",
        )
        _element.resource_id = AAZStrArg(
            options=["resource-id"],
            help="The azure resource id of the logic app receiver.",
        )
        _element.use_common_alert_schema = AAZBoolArg(
            options=["use-common-alert-schema"],
            help="Indicates whether to use common alert schema.",
            nullable=True,
        )

        sms_receivers = cls._args_schema.sms_receivers
        sms_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.sms_receivers.Element
        _element.country_code = AAZStrArg(
            options=["country-code"],
            help="The country code of the SMS receiver.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the SMS receiver. Names must be unique across all receivers within an action group.",
        )
        _element.phone_number = AAZStrArg(
            options=["phone-number"],
            help="The phone number of the SMS receiver.",
        )

        voice_receivers = cls._args_schema.voice_receivers
        voice_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.voice_receivers.Element
        _element.country_code = AAZStrArg(
            options=["country-code"],
            help="The country code of the voice receiver.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the voice receiver. Names must be unique across all receivers within an action group.",
        )
        _element.phone_number = AAZStrArg(
            options=["phone-number"],
            help="The phone number of the voice receiver.",
        )

        webhook_receivers = cls._args_schema.webhook_receivers
        webhook_receivers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.webhook_receivers.Element
        _element.identifier_uri = AAZStrArg(
            options=["identifier-uri"],
            help="Indicates the identifier uri for aad auth.",
            nullable=True,
        )
        _element.managed_identity = AAZStrArg(
            options=["managed-identity"],
            help="The principal id of the managed identity. The value can be \"None\", \"SystemAssigned\" ",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the webhook receiver. Names must be unique across all receivers within an action group.",
        )
        _element.object_id = AAZStrArg(
            options=["object-id"],
            help="Indicates the webhook app object Id for aad auth.",
            nullable=True,
        )
        _element.service_uri = AAZStrArg(
            options=["service-uri"],
            help="The URI where webhooks should be sent.",
        )
        _element.tenant_id = AAZStrArg(
            options=["tenant-id"],
            help="Indicates the tenant id for aad auth.",
            nullable=True,
        )
        _element.use_aad_auth = AAZBoolArg(
            options=["use-aad-auth"],
            help="Indicates whether or not use AAD authentication.",
            nullable=True,
        )
        _element.use_common_alert_schema = AAZBoolArg(
            options=["use-common-alert-schema"],
            help="Indicates whether to use common alert schema.",
            nullable=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.ActionGroupsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        self.ActionGroupsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class ActionGroupsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Insights/actionGroups/{actionGroupName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "actionGroupName", self.ctx.args.action_group_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-10-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_action_group_resource_read(cls._schema_on_200)

            return cls._schema_on_200

    class ActionGroupsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200, 201]:
                return self.on_200_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Insights/actionGroups/{actionGroupName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "actionGroupName", self.ctx.args.action_group_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-10-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_action_group_resource_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("identity", AAZIdentityObjectType)
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("armRoleReceivers", AAZListType, ".arm_role_receivers")
                properties.set_prop("automationRunbookReceivers", AAZListType, ".automation_runbook_receivers")
                properties.set_prop("azureAppPushReceivers", AAZListType, ".azure_app_push_receivers")
                properties.set_prop("azureFunctionReceivers", AAZListType, ".azure_function_receivers")
                properties.set_prop("emailReceivers", AAZListType, ".email_receivers")
                properties.set_prop("enabled", AAZBoolType, ".enabled", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("eventHubReceivers", AAZListType, ".event_hub_receivers")
                properties.set_prop("groupShortName", AAZStrType, ".group_short_name", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("incidentReceivers", AAZListType, ".incident_receivers")
                properties.set_prop("itsmReceivers", AAZListType, ".itsm_receivers")
                properties.set_prop("logicAppReceivers", AAZListType, ".logic_app_receivers")
                properties.set_prop("smsReceivers", AAZListType, ".sms_receivers")
                properties.set_prop("voiceReceivers", AAZListType, ".voice_receivers")
                properties.set_prop("webhookReceivers", AAZListType, ".webhook_receivers")

            arm_role_receivers = _builder.get(".properties.armRoleReceivers")
            if arm_role_receivers is not None:
                arm_role_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.armRoleReceivers[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("roleId", AAZStrType, ".role_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("useCommonAlertSchema", AAZBoolType, ".use_common_alert_schema")

            automation_runbook_receivers = _builder.get(".properties.automationRunbookReceivers")
            if automation_runbook_receivers is not None:
                automation_runbook_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.automationRunbookReceivers[]")
            if _elements is not None:
                _elements.set_prop("automationAccountId", AAZStrType, ".automation_account_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("isGlobalRunbook", AAZBoolType, ".is_global_runbook", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("managedIdentity", AAZStrType, ".managed_identity")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("runbookName", AAZStrType, ".runbook_name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("serviceUri", AAZStrType, ".service_uri")
                _elements.set_prop("useCommonAlertSchema", AAZBoolType, ".use_common_alert_schema")
                _elements.set_prop("webhookResourceId", AAZStrType, ".webhook_resource_id", typ_kwargs={"flags": {"required": True}})

            azure_app_push_receivers = _builder.get(".properties.azureAppPushReceivers")
            if azure_app_push_receivers is not None:
                azure_app_push_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.azureAppPushReceivers[]")
            if _elements is not None:
                _elements.set_prop("emailAddress", AAZStrType, ".email_address", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})

            azure_function_receivers = _builder.get(".properties.azureFunctionReceivers")
            if azure_function_receivers is not None:
                azure_function_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.azureFunctionReceivers[]")
            if _elements is not None:
                _elements.set_prop("functionAppResourceId", AAZStrType, ".function_app_resource_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("functionName", AAZStrType, ".function_name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("httpTriggerUrl", AAZStrType, ".http_trigger_url", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("managedIdentity", AAZStrType, ".managed_identity")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("useCommonAlertSchema", AAZBoolType, ".use_common_alert_schema")

            email_receivers = _builder.get(".properties.emailReceivers")
            if email_receivers is not None:
                email_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.emailReceivers[]")
            if _elements is not None:
                _elements.set_prop("emailAddress", AAZStrType, ".email_address", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("useCommonAlertSchema", AAZBoolType, ".use_common_alert_schema")

            event_hub_receivers = _builder.get(".properties.eventHubReceivers")
            if event_hub_receivers is not None:
                event_hub_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.eventHubReceivers[]")
            if _elements is not None:
                _elements.set_prop("eventHubName", AAZStrType, ".event_hub_name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("eventHubNameSpace", AAZStrType, ".event_hub_name_space", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("managedIdentity", AAZStrType, ".managed_identity")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("subscriptionId", AAZStrType, ".subscription_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("tenantId", AAZStrType, ".tenant_id")
                _elements.set_prop("useCommonAlertSchema", AAZBoolType, ".use_common_alert_schema")

            incident_receivers = _builder.get(".properties.incidentReceivers")
            if incident_receivers is not None:
                incident_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.incidentReceivers[]")
            if _elements is not None:
                _elements.set_prop("connection", AAZObjectType, ".connection", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("incidentManagementService", AAZStrType, ".incident_management_service", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("mappings", AAZDictType, ".mappings", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})

            connection = _builder.get(".properties.incidentReceivers[].connection")
            if connection is not None:
                connection.set_prop("id", AAZStrType, ".id", typ_kwargs={"flags": {"required": True}})
                connection.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})

            mappings = _builder.get(".properties.incidentReceivers[].mappings")
            if mappings is not None:
                mappings.set_elements(AAZStrType, ".")

            itsm_receivers = _builder.get(".properties.itsmReceivers")
            if itsm_receivers is not None:
                itsm_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.itsmReceivers[]")
            if _elements is not None:
                _elements.set_prop("connectionId", AAZStrType, ".connection_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("region", AAZStrType, ".region", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("ticketConfiguration", AAZStrType, ".ticket_configuration", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("workspaceId", AAZStrType, ".workspace_id", typ_kwargs={"flags": {"required": True}})

            logic_app_receivers = _builder.get(".properties.logicAppReceivers")
            if logic_app_receivers is not None:
                logic_app_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.logicAppReceivers[]")
            if _elements is not None:
                _elements.set_prop("callbackUrl", AAZStrType, ".callback_url", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("managedIdentity", AAZStrType, ".managed_identity")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("resourceId", AAZStrType, ".resource_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("useCommonAlertSchema", AAZBoolType, ".use_common_alert_schema")

            sms_receivers = _builder.get(".properties.smsReceivers")
            if sms_receivers is not None:
                sms_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.smsReceivers[]")
            if _elements is not None:
                _elements.set_prop("countryCode", AAZStrType, ".country_code", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("phoneNumber", AAZStrType, ".phone_number", typ_kwargs={"flags": {"required": True}})

            voice_receivers = _builder.get(".properties.voiceReceivers")
            if voice_receivers is not None:
                voice_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.voiceReceivers[]")
            if _elements is not None:
                _elements.set_prop("countryCode", AAZStrType, ".country_code", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("phoneNumber", AAZStrType, ".phone_number", typ_kwargs={"flags": {"required": True}})

            webhook_receivers = _builder.get(".properties.webhookReceivers")
            if webhook_receivers is not None:
                webhook_receivers.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.webhookReceivers[]")
            if _elements is not None:
                _elements.set_prop("identifierUri", AAZStrType, ".identifier_uri")
                _elements.set_prop("managedIdentity", AAZStrType, ".managed_identity")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("objectId", AAZStrType, ".object_id")
                _elements.set_prop("serviceUri", AAZStrType, ".service_uri", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("tenantId", AAZStrType, ".tenant_id")
                _elements.set_prop("useAadAuth", AAZBoolType, ".use_aad_auth")
                _elements.set_prop("useCommonAlertSchema", AAZBoolType, ".use_common_alert_schema")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_action_group_resource_read = None

    @classmethod
    def _build_schema_action_group_resource_read(cls, _schema):
        if cls._schema_action_group_resource_read is not None:
            _schema.id = cls._schema_action_group_resource_read.id
            _schema.identity = cls._schema_action_group_resource_read.identity
            _schema.location = cls._schema_action_group_resource_read.location
            _schema.name = cls._schema_action_group_resource_read.name
            _schema.properties = cls._schema_action_group_resource_read.properties
            _schema.tags = cls._schema_action_group_resource_read.tags
            _schema.type = cls._schema_action_group_resource_read.type
            return

        cls._schema_action_group_resource_read = _schema_action_group_resource_read = AAZObjectType()

        action_group_resource_read = _schema_action_group_resource_read
        action_group_resource_read.id = AAZStrType(
            flags={"read_only": True},
        )
        action_group_resource_read.identity = AAZIdentityObjectType()
        action_group_resource_read.location = AAZStrType(
            flags={"required": True},
        )
        action_group_resource_read.name = AAZStrType(
            flags={"read_only": True},
        )
        action_group_resource_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        action_group_resource_read.tags = AAZDictType()
        action_group_resource_read.type = AAZStrType(
            flags={"read_only": True},
        )

        identity = _schema_action_group_resource_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType(
            flags={"required": True},
        )
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_action_group_resource_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType(
            nullable=True,
        )

        _element = _schema_action_group_resource_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_action_group_resource_read.properties
        properties.arm_role_receivers = AAZListType(
            serialized_name="armRoleReceivers",
        )
        properties.automation_runbook_receivers = AAZListType(
            serialized_name="automationRunbookReceivers",
        )
        properties.azure_app_push_receivers = AAZListType(
            serialized_name="azureAppPushReceivers",
        )
        properties.azure_function_receivers = AAZListType(
            serialized_name="azureFunctionReceivers",
        )
        properties.email_receivers = AAZListType(
            serialized_name="emailReceivers",
        )
        properties.enabled = AAZBoolType(
            flags={"required": True},
        )
        properties.event_hub_receivers = AAZListType(
            serialized_name="eventHubReceivers",
        )
        properties.group_short_name = AAZStrType(
            serialized_name="groupShortName",
            flags={"required": True},
        )
        properties.incident_receivers = AAZListType(
            serialized_name="incidentReceivers",
        )
        properties.itsm_receivers = AAZListType(
            serialized_name="itsmReceivers",
        )
        properties.logic_app_receivers = AAZListType(
            serialized_name="logicAppReceivers",
        )
        properties.sms_receivers = AAZListType(
            serialized_name="smsReceivers",
        )
        properties.voice_receivers = AAZListType(
            serialized_name="voiceReceivers",
        )
        properties.webhook_receivers = AAZListType(
            serialized_name="webhookReceivers",
        )

        arm_role_receivers = _schema_action_group_resource_read.properties.arm_role_receivers
        arm_role_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.arm_role_receivers.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.role_id = AAZStrType(
            serialized_name="roleId",
            flags={"required": True},
        )
        _element.use_common_alert_schema = AAZBoolType(
            serialized_name="useCommonAlertSchema",
        )

        automation_runbook_receivers = _schema_action_group_resource_read.properties.automation_runbook_receivers
        automation_runbook_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.automation_runbook_receivers.Element
        _element.automation_account_id = AAZStrType(
            serialized_name="automationAccountId",
            flags={"required": True},
        )
        _element.is_global_runbook = AAZBoolType(
            serialized_name="isGlobalRunbook",
            flags={"required": True},
        )
        _element.managed_identity = AAZStrType(
            serialized_name="managedIdentity",
        )
        _element.name = AAZStrType()
        _element.runbook_name = AAZStrType(
            serialized_name="runbookName",
            flags={"required": True},
        )
        _element.service_uri = AAZStrType(
            serialized_name="serviceUri",
        )
        _element.use_common_alert_schema = AAZBoolType(
            serialized_name="useCommonAlertSchema",
        )
        _element.webhook_resource_id = AAZStrType(
            serialized_name="webhookResourceId",
            flags={"required": True},
        )

        azure_app_push_receivers = _schema_action_group_resource_read.properties.azure_app_push_receivers
        azure_app_push_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.azure_app_push_receivers.Element
        _element.email_address = AAZStrType(
            serialized_name="emailAddress",
            flags={"required": True},
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )

        azure_function_receivers = _schema_action_group_resource_read.properties.azure_function_receivers
        azure_function_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.azure_function_receivers.Element
        _element.function_app_resource_id = AAZStrType(
            serialized_name="functionAppResourceId",
            flags={"required": True},
        )
        _element.function_name = AAZStrType(
            serialized_name="functionName",
            flags={"required": True},
        )
        _element.http_trigger_url = AAZStrType(
            serialized_name="httpTriggerUrl",
            flags={"required": True},
        )
        _element.managed_identity = AAZStrType(
            serialized_name="managedIdentity",
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.use_common_alert_schema = AAZBoolType(
            serialized_name="useCommonAlertSchema",
        )

        email_receivers = _schema_action_group_resource_read.properties.email_receivers
        email_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.email_receivers.Element
        _element.email_address = AAZStrType(
            serialized_name="emailAddress",
            flags={"required": True},
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.status = AAZStrType(
            flags={"read_only": True},
        )
        _element.use_common_alert_schema = AAZBoolType(
            serialized_name="useCommonAlertSchema",
        )

        event_hub_receivers = _schema_action_group_resource_read.properties.event_hub_receivers
        event_hub_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.event_hub_receivers.Element
        _element.event_hub_name = AAZStrType(
            serialized_name="eventHubName",
            flags={"required": True},
        )
        _element.event_hub_name_space = AAZStrType(
            serialized_name="eventHubNameSpace",
            flags={"required": True},
        )
        _element.managed_identity = AAZStrType(
            serialized_name="managedIdentity",
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.subscription_id = AAZStrType(
            serialized_name="subscriptionId",
            flags={"required": True},
        )
        _element.tenant_id = AAZStrType(
            serialized_name="tenantId",
        )
        _element.use_common_alert_schema = AAZBoolType(
            serialized_name="useCommonAlertSchema",
        )

        incident_receivers = _schema_action_group_resource_read.properties.incident_receivers
        incident_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.incident_receivers.Element
        _element.connection = AAZObjectType(
            flags={"required": True},
        )
        _element.incident_management_service = AAZStrType(
            serialized_name="incidentManagementService",
            flags={"required": True},
        )
        _element.mappings = AAZDictType(
            flags={"required": True},
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )

        connection = _schema_action_group_resource_read.properties.incident_receivers.Element.connection
        connection.id = AAZStrType(
            flags={"required": True},
        )
        connection.name = AAZStrType(
            flags={"required": True},
        )

        mappings = _schema_action_group_resource_read.properties.incident_receivers.Element.mappings
        mappings.Element = AAZStrType()

        itsm_receivers = _schema_action_group_resource_read.properties.itsm_receivers
        itsm_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.itsm_receivers.Element
        _element.connection_id = AAZStrType(
            serialized_name="connectionId",
            flags={"required": True},
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.region = AAZStrType(
            flags={"required": True},
        )
        _element.ticket_configuration = AAZStrType(
            serialized_name="ticketConfiguration",
            flags={"required": True},
        )
        _element.workspace_id = AAZStrType(
            serialized_name="workspaceId",
            flags={"required": True},
        )

        logic_app_receivers = _schema_action_group_resource_read.properties.logic_app_receivers
        logic_app_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.logic_app_receivers.Element
        _element.callback_url = AAZStrType(
            serialized_name="callbackUrl",
            flags={"required": True},
        )
        _element.managed_identity = AAZStrType(
            serialized_name="managedIdentity",
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.resource_id = AAZStrType(
            serialized_name="resourceId",
            flags={"required": True},
        )
        _element.use_common_alert_schema = AAZBoolType(
            serialized_name="useCommonAlertSchema",
        )

        sms_receivers = _schema_action_group_resource_read.properties.sms_receivers
        sms_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.sms_receivers.Element
        _element.country_code = AAZStrType(
            serialized_name="countryCode",
            flags={"required": True},
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.phone_number = AAZStrType(
            serialized_name="phoneNumber",
            flags={"required": True},
        )
        _element.status = AAZStrType(
            flags={"read_only": True},
        )

        voice_receivers = _schema_action_group_resource_read.properties.voice_receivers
        voice_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.voice_receivers.Element
        _element.country_code = AAZStrType(
            serialized_name="countryCode",
            flags={"required": True},
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.phone_number = AAZStrType(
            serialized_name="phoneNumber",
            flags={"required": True},
        )

        webhook_receivers = _schema_action_group_resource_read.properties.webhook_receivers
        webhook_receivers.Element = AAZObjectType()

        _element = _schema_action_group_resource_read.properties.webhook_receivers.Element
        _element.identifier_uri = AAZStrType(
            serialized_name="identifierUri",
        )
        _element.managed_identity = AAZStrType(
            serialized_name="managedIdentity",
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.object_id = AAZStrType(
            serialized_name="objectId",
        )
        _element.service_uri = AAZStrType(
            serialized_name="serviceUri",
            flags={"required": True},
        )
        _element.tenant_id = AAZStrType(
            serialized_name="tenantId",
        )
        _element.use_aad_auth = AAZBoolType(
            serialized_name="useAadAuth",
        )
        _element.use_common_alert_schema = AAZBoolType(
            serialized_name="useCommonAlertSchema",
        )

        tags = _schema_action_group_resource_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_action_group_resource_read.id
        _schema.identity = cls._schema_action_group_resource_read.identity
        _schema.location = cls._schema_action_group_resource_read.location
        _schema.name = cls._schema_action_group_resource_read.name
        _schema.properties = cls._schema_action_group_resource_read.properties
        _schema.tags = cls._schema_action_group_resource_read.tags
        _schema.type = cls._schema_action_group_resource_read.type


__all__ = ["Update"]
