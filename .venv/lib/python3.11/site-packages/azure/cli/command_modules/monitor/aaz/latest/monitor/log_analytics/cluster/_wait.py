# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "monitor log-analytics cluster wait",
)
class Wait(AAZWaitCommand):
    """Place the CLI in a waiting state until a condition is met.
    """

    _aaz_info = {
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.operationalinsights/clusters/{}", "2025-02-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.cluster_name = AAZStrArg(
            options=["-n", "--name", "--cluster-name"],
            help="Name of the Log Analytics Cluster.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.ClustersGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=False)
        return result

    class ClustersGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.OperationalInsights/clusters/{clusterName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "clusterName", self.ctx.args.cluster_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-02-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.identity = AAZIdentityObjectType()
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.sku = AAZObjectType()
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            identity = cls._schema_on_200.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType(
                flags={"required": True},
            )
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType(
                nullable=True,
            )

            _element = cls._schema_on_200.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.associated_workspaces = AAZListType(
                serialized_name="associatedWorkspaces",
                flags={"read_only": True},
            )
            properties.billing_type = AAZStrType(
                serialized_name="billingType",
            )
            properties.capacity_reservation_properties = AAZObjectType(
                serialized_name="capacityReservationProperties",
            )
            properties.cluster_id = AAZStrType(
                serialized_name="clusterId",
                flags={"read_only": True},
            )
            properties.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            properties.is_availability_zones_enabled = AAZBoolType(
                serialized_name="isAvailabilityZonesEnabled",
            )
            properties.is_double_encryption_enabled = AAZBoolType(
                serialized_name="isDoubleEncryptionEnabled",
            )
            properties.key_vault_properties = AAZObjectType(
                serialized_name="keyVaultProperties",
            )
            properties.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.replication = AAZObjectType()

            associated_workspaces = cls._schema_on_200.properties.associated_workspaces
            associated_workspaces.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.associated_workspaces.Element
            _element.associate_date = AAZStrType(
                serialized_name="associateDate",
                flags={"read_only": True},
            )
            _element.resource_id = AAZStrType(
                serialized_name="resourceId",
                flags={"read_only": True},
            )
            _element.workspace_id = AAZStrType(
                serialized_name="workspaceId",
                flags={"read_only": True},
            )
            _element.workspace_name = AAZStrType(
                serialized_name="workspaceName",
                flags={"read_only": True},
            )

            capacity_reservation_properties = cls._schema_on_200.properties.capacity_reservation_properties
            capacity_reservation_properties.last_sku_update = AAZStrType(
                serialized_name="lastSkuUpdate",
                flags={"read_only": True},
            )
            capacity_reservation_properties.min_capacity = AAZIntType(
                serialized_name="minCapacity",
                flags={"read_only": True},
            )

            key_vault_properties = cls._schema_on_200.properties.key_vault_properties
            key_vault_properties.key_name = AAZStrType(
                serialized_name="keyName",
            )
            key_vault_properties.key_rsa_size = AAZIntType(
                serialized_name="keyRsaSize",
            )
            key_vault_properties.key_vault_uri = AAZStrType(
                serialized_name="keyVaultUri",
            )
            key_vault_properties.key_version = AAZStrType(
                serialized_name="keyVersion",
            )

            replication = cls._schema_on_200.properties.replication
            replication.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            replication.enabled = AAZBoolType()
            replication.is_availability_zones_enabled = AAZBoolType(
                serialized_name="isAvailabilityZonesEnabled",
            )
            replication.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            replication.location = AAZStrType()
            replication.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )

            sku = cls._schema_on_200.sku
            sku.capacity = AAZIntType()
            sku.name = AAZStrType()

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _WaitHelper:
    """Helper class for Wait"""


__all__ = ["Wait"]
