# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "monitor log-analytics cluster identity remove",
)
class Remove(AAZCommand):
    """Remove the user or system managed identities.
    """

    _aaz_info = {
        "version": "2025-02-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.operationalinsights/clusters/{}", "2025-02-01", "identity"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        self.SubresourceSelector(ctx=self.ctx, name="subresource")
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.cluster_name = AAZStrArg(
            options=["-n", "--name", "--cluster-name"],
            help="Name of the Log Analytics Cluster.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "Parameters.identity"

        _args_schema = cls._args_schema
        _args_schema.mi_system_assigned = AAZStrArg(
            options=["--system-assigned", "--mi-system-assigned"],
            arg_group="Parameters.identity",
            help="Set the system managed identity.",
            blank="True",
        )
        _args_schema.mi_user_assigned = AAZListArg(
            options=["--user-assigned", "--mi-user-assigned"],
            arg_group="Parameters.identity",
            help="Set the user managed identities.",
            blank=[],
        )

        mi_user_assigned = cls._args_schema.mi_user_assigned
        mi_user_assigned.Element = AAZStrArg()
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.ClustersGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.selectors.subresource.get())
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.post_instance_update(self.ctx.selectors.subresource.get())
        yield self.ClustersCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.selectors.subresource.get(), client_flatten=True)
        return result

    class SubresourceSelector(AAZJsonSelector):

        def _get(self):
            result = self.ctx.vars.instance
            return result.identity

        def _set(self, value):
            result = self.ctx.vars.instance
            result.identity = value
            return

    class ClustersGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.OperationalInsights/clusters/{clusterName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "clusterName", self.ctx.args.cluster_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-02-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _RemoveHelper._build_schema_cluster_read(cls._schema_on_200)

            return cls._schema_on_200

    class ClustersCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.OperationalInsights/clusters/{clusterName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "clusterName", self.ctx.args.cluster_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-02-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _RemoveHelper._build_schema_cluster_read(cls._schema_on_200)

            return cls._schema_on_200

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.selectors.subresource.get())

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZIdentityObjectType
            )
            _builder.set_prop("userAssigned", AAZListType, ".mi_user_assigned", typ_kwargs={"flags": {"action": "remove"}})
            _builder.set_prop("systemAssigned", AAZStrType, ".mi_system_assigned", typ_kwargs={"flags": {"action": "remove"}})

            user_assigned = _builder.get(".userAssigned")
            if user_assigned is not None:
                user_assigned.set_elements(AAZStrType, ".")

            return _instance_value


class _RemoveHelper:
    """Helper class for Remove"""

    _schema_cluster_read = None

    @classmethod
    def _build_schema_cluster_read(cls, _schema):
        if cls._schema_cluster_read is not None:
            _schema.id = cls._schema_cluster_read.id
            _schema.identity = cls._schema_cluster_read.identity
            _schema.location = cls._schema_cluster_read.location
            _schema.name = cls._schema_cluster_read.name
            _schema.properties = cls._schema_cluster_read.properties
            _schema.sku = cls._schema_cluster_read.sku
            _schema.tags = cls._schema_cluster_read.tags
            _schema.type = cls._schema_cluster_read.type
            return

        cls._schema_cluster_read = _schema_cluster_read = AAZObjectType()

        cluster_read = _schema_cluster_read
        cluster_read.id = AAZStrType(
            flags={"read_only": True},
        )
        cluster_read.identity = AAZIdentityObjectType()
        cluster_read.location = AAZStrType(
            flags={"required": True},
        )
        cluster_read.name = AAZStrType(
            flags={"read_only": True},
        )
        cluster_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        cluster_read.sku = AAZObjectType()
        cluster_read.tags = AAZDictType()
        cluster_read.type = AAZStrType(
            flags={"read_only": True},
        )

        identity = _schema_cluster_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType(
            flags={"required": True},
        )
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_cluster_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType(
            nullable=True,
        )

        _element = _schema_cluster_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_cluster_read.properties
        properties.associated_workspaces = AAZListType(
            serialized_name="associatedWorkspaces",
            flags={"read_only": True},
        )
        properties.billing_type = AAZStrType(
            serialized_name="billingType",
        )
        properties.capacity_reservation_properties = AAZObjectType(
            serialized_name="capacityReservationProperties",
        )
        properties.cluster_id = AAZStrType(
            serialized_name="clusterId",
            flags={"read_only": True},
        )
        properties.created_date = AAZStrType(
            serialized_name="createdDate",
            flags={"read_only": True},
        )
        properties.is_availability_zones_enabled = AAZBoolType(
            serialized_name="isAvailabilityZonesEnabled",
        )
        properties.is_double_encryption_enabled = AAZBoolType(
            serialized_name="isDoubleEncryptionEnabled",
        )
        properties.key_vault_properties = AAZObjectType(
            serialized_name="keyVaultProperties",
        )
        properties.last_modified_date = AAZStrType(
            serialized_name="lastModifiedDate",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.replication = AAZObjectType()

        associated_workspaces = _schema_cluster_read.properties.associated_workspaces
        associated_workspaces.Element = AAZObjectType()

        _element = _schema_cluster_read.properties.associated_workspaces.Element
        _element.associate_date = AAZStrType(
            serialized_name="associateDate",
            flags={"read_only": True},
        )
        _element.resource_id = AAZStrType(
            serialized_name="resourceId",
            flags={"read_only": True},
        )
        _element.workspace_id = AAZStrType(
            serialized_name="workspaceId",
            flags={"read_only": True},
        )
        _element.workspace_name = AAZStrType(
            serialized_name="workspaceName",
            flags={"read_only": True},
        )

        capacity_reservation_properties = _schema_cluster_read.properties.capacity_reservation_properties
        capacity_reservation_properties.last_sku_update = AAZStrType(
            serialized_name="lastSkuUpdate",
            flags={"read_only": True},
        )
        capacity_reservation_properties.min_capacity = AAZIntType(
            serialized_name="minCapacity",
            flags={"read_only": True},
        )

        key_vault_properties = _schema_cluster_read.properties.key_vault_properties
        key_vault_properties.key_name = AAZStrType(
            serialized_name="keyName",
        )
        key_vault_properties.key_rsa_size = AAZIntType(
            serialized_name="keyRsaSize",
        )
        key_vault_properties.key_vault_uri = AAZStrType(
            serialized_name="keyVaultUri",
        )
        key_vault_properties.key_version = AAZStrType(
            serialized_name="keyVersion",
        )

        replication = _schema_cluster_read.properties.replication
        replication.created_date = AAZStrType(
            serialized_name="createdDate",
            flags={"read_only": True},
        )
        replication.enabled = AAZBoolType()
        replication.is_availability_zones_enabled = AAZBoolType(
            serialized_name="isAvailabilityZonesEnabled",
        )
        replication.last_modified_date = AAZStrType(
            serialized_name="lastModifiedDate",
            flags={"read_only": True},
        )
        replication.location = AAZStrType()
        replication.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        sku = _schema_cluster_read.sku
        sku.capacity = AAZIntType()
        sku.name = AAZStrType()

        tags = _schema_cluster_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_cluster_read.id
        _schema.identity = cls._schema_cluster_read.identity
        _schema.location = cls._schema_cluster_read.location
        _schema.name = cls._schema_cluster_read.name
        _schema.properties = cls._schema_cluster_read.properties
        _schema.sku = cls._schema_cluster_read.sku
        _schema.tags = cls._schema_cluster_read.tags
        _schema.type = cls._schema_cluster_read.type


__all__ = ["Remove"]
