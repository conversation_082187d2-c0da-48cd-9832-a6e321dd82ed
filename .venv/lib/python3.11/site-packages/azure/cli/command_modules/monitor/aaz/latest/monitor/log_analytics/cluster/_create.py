# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "monitor log-analytics cluster create",
)
class Create(AAZCommand):
    """Create a cluster instance.

    :example: Create a cluster instance.
        az monitor log-analytics cluster create -g MyResourceGroup -n MyCluster --sku-capacity 1000
    """

    _aaz_info = {
        "version": "2025-02-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.operationalinsights/clusters/{}", "2025-02-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.cluster_name = AAZStrArg(
            options=["-n", "--name", "--cluster-name"],
            help="The name of the Log Analytics cluster.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[A-Za-z0-9][A-Za-z0-9-]+[A-Za-z0-9]$",
                max_length=63,
                min_length=4,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "Identity"

        _args_schema = cls._args_schema
        _args_schema.identity_type = AAZStrArg(
            options=["--type", "--identity-type"],
            arg_group="Identity",
            help="Type of managed service identity.",
            default="SystemAssigned",
            enum={"None": "None", "SystemAssigned": "SystemAssigned", "SystemAssigned,UserAssigned": "SystemAssigned,UserAssigned", "UserAssigned": "UserAssigned"},
        )
        _args_schema.user_assigned = AAZDictArg(
            options=["--user-assigned"],
            arg_group="Identity",
            help="The list of user identities associated with the resource. The user identity dictionary key references will be ARM resource ids in the form: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.",
        )

        user_assigned = cls._args_schema.user_assigned
        user_assigned.Element = AAZObjectArg(
            nullable=True,
            blank={},
        )

        # define Arg Group "Key Properties"

        _args_schema = cls._args_schema
        _args_schema.key_name = AAZStrArg(
            options=["--key-name"],
            arg_group="Key Properties",
            help="The name of the key associated with the Log Analytics cluster.",
        )
        _args_schema.key_rsa_size = AAZIntArg(
            options=["--key-rsa-size"],
            arg_group="Key Properties",
            help="Selected key minimum required size.",
        )
        _args_schema.key_vault_uri = AAZStrArg(
            options=["--key-vault-uri"],
            arg_group="Key Properties",
            help="The Key Vault uri which holds they key associated with the Log Analytics cluster.",
        )
        _args_schema.key_version = AAZStrArg(
            options=["--key-version"],
            arg_group="Key Properties",
            help="The version of the key associated with the Log Analytics cluster.",
        )

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Parameters",
            help="The geo-location where the resource lives",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Parameters",
            help="Resource tags.",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.billing_type = AAZStrArg(
            options=["--billing-type"],
            arg_group="Properties",
            help="The cluster's billing type.",
            enum={"Cluster": "Cluster", "Workspaces": "Workspaces"},
        )

        # define Arg Group "Replication"

        _args_schema = cls._args_schema
        _args_schema.replication_enabled = AAZBoolArg(
            options=["--replication-enabled"],
            arg_group="Replication",
            help="Specifies whether the replication is enabled or not. When true the cluster is replicate to the specified location.",
        )
        _args_schema.replication_location = AAZStrArg(
            options=["--replication-location"],
            arg_group="Replication",
            help="The secondary location of the replication. If replication is being enabled, enabled must be provided.",
        )

        # define Arg Group "Sku"

        _args_schema = cls._args_schema
        _args_schema.sku_capacity = AAZIntArg(
            options=["--sku-capacity"],
            arg_group="Sku",
            help="The capacity of the SKU. It can be decreased only after 31 days.",
            enum={"100": 100, "1000": 1000, "10000": 10000, "200": 200, "2000": 2000, "25000": 25000, "300": 300, "400": 400, "500": 500, "5000": 5000, "50000": 50000},
        )
        _args_schema.sku_name = AAZStrArg(
            options=["--sku-name"],
            arg_group="Sku",
            help="The name of the SKU.",
            default="CapacityReservation",
            enum={"CapacityReservation": "CapacityReservation"},
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.ClustersCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class ClustersCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.OperationalInsights/clusters/{clusterName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "clusterName", self.ctx.args.cluster_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-02-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("identity", AAZIdentityObjectType)
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("sku", AAZObjectType)
            _builder.set_prop("tags", AAZDictType, ".tags")

            identity = _builder.get(".identity")
            if identity is not None:
                identity.set_prop("type", AAZStrType, ".identity_type", typ_kwargs={"flags": {"required": True}})
                identity.set_prop("userAssignedIdentities", AAZDictType, ".user_assigned")

            user_assigned_identities = _builder.get(".identity.userAssignedIdentities")
            if user_assigned_identities is not None:
                user_assigned_identities.set_elements(AAZObjectType, ".", typ_kwargs={"nullable": True})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("billingType", AAZStrType, ".billing_type")
                properties.set_prop("keyVaultProperties", AAZObjectType)
                properties.set_prop("replication", AAZObjectType)

            key_vault_properties = _builder.get(".properties.keyVaultProperties")
            if key_vault_properties is not None:
                key_vault_properties.set_prop("keyName", AAZStrType, ".key_name")
                key_vault_properties.set_prop("keyRsaSize", AAZIntType, ".key_rsa_size")
                key_vault_properties.set_prop("keyVaultUri", AAZStrType, ".key_vault_uri")
                key_vault_properties.set_prop("keyVersion", AAZStrType, ".key_version")

            replication = _builder.get(".properties.replication")
            if replication is not None:
                replication.set_prop("enabled", AAZBoolType, ".replication_enabled")
                replication.set_prop("location", AAZStrType, ".replication_location")

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("capacity", AAZIntType, ".sku_capacity")
                sku.set_prop("name", AAZStrType, ".sku_name")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.identity = AAZIdentityObjectType()
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.sku = AAZObjectType()
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            identity = cls._schema_on_200.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType(
                flags={"required": True},
            )
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType(
                nullable=True,
            )

            _element = cls._schema_on_200.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.associated_workspaces = AAZListType(
                serialized_name="associatedWorkspaces",
                flags={"read_only": True},
            )
            properties.billing_type = AAZStrType(
                serialized_name="billingType",
            )
            properties.capacity_reservation_properties = AAZObjectType(
                serialized_name="capacityReservationProperties",
            )
            properties.cluster_id = AAZStrType(
                serialized_name="clusterId",
                flags={"read_only": True},
            )
            properties.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            properties.is_availability_zones_enabled = AAZBoolType(
                serialized_name="isAvailabilityZonesEnabled",
            )
            properties.is_double_encryption_enabled = AAZBoolType(
                serialized_name="isDoubleEncryptionEnabled",
            )
            properties.key_vault_properties = AAZObjectType(
                serialized_name="keyVaultProperties",
            )
            properties.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.replication = AAZObjectType()

            associated_workspaces = cls._schema_on_200.properties.associated_workspaces
            associated_workspaces.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.associated_workspaces.Element
            _element.associate_date = AAZStrType(
                serialized_name="associateDate",
                flags={"read_only": True},
            )
            _element.resource_id = AAZStrType(
                serialized_name="resourceId",
                flags={"read_only": True},
            )
            _element.workspace_id = AAZStrType(
                serialized_name="workspaceId",
                flags={"read_only": True},
            )
            _element.workspace_name = AAZStrType(
                serialized_name="workspaceName",
                flags={"read_only": True},
            )

            capacity_reservation_properties = cls._schema_on_200.properties.capacity_reservation_properties
            capacity_reservation_properties.last_sku_update = AAZStrType(
                serialized_name="lastSkuUpdate",
                flags={"read_only": True},
            )
            capacity_reservation_properties.min_capacity = AAZIntType(
                serialized_name="minCapacity",
                flags={"read_only": True},
            )

            key_vault_properties = cls._schema_on_200.properties.key_vault_properties
            key_vault_properties.key_name = AAZStrType(
                serialized_name="keyName",
            )
            key_vault_properties.key_rsa_size = AAZIntType(
                serialized_name="keyRsaSize",
            )
            key_vault_properties.key_vault_uri = AAZStrType(
                serialized_name="keyVaultUri",
            )
            key_vault_properties.key_version = AAZStrType(
                serialized_name="keyVersion",
            )

            replication = cls._schema_on_200.properties.replication
            replication.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            replication.enabled = AAZBoolType()
            replication.is_availability_zones_enabled = AAZBoolType(
                serialized_name="isAvailabilityZonesEnabled",
            )
            replication.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            replication.location = AAZStrType()
            replication.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )

            sku = cls._schema_on_200.sku
            sku.capacity = AAZIntType()
            sku.name = AAZStrType()

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _CreateHelper:
    """Helper class for Create"""


__all__ = ["Create"]
