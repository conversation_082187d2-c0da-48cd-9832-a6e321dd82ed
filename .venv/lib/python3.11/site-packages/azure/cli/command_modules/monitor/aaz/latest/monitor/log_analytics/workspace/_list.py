# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "monitor log-analytics workspace list",
)
class List(AAZCommand):
    """Get a list of workspaces under a resource group or a subscription.
    """

    _aaz_info = {
        "version": "2025-02-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.operationalinsights/workspaces", "2025-02-01"],
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.operationalinsights/workspaces", "2025-02-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg()
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        condition_0 = has_value(self.ctx.subscription_id) and has_value(self.ctx.args.resource_group) is not True
        condition_1 = has_value(self.ctx.args.resource_group) and has_value(self.ctx.subscription_id)
        if condition_0:
            self.WorkspacesList(ctx=self.ctx)()
        if condition_1:
            self.WorkspacesListByResourceGroup(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        return result

    class WorkspacesList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.OperationalInsights/workspaces",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-02-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.value = AAZListType()

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType()
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.identity = AAZIdentityObjectType()
            _element.location = AAZStrType(
                flags={"required": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            identity = cls._schema_on_200.value.Element.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType(
                flags={"required": True},
            )
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.value.Element.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            properties.customer_id = AAZStrType(
                serialized_name="customerId",
                flags={"read_only": True},
            )
            properties.default_data_collection_rule_resource_id = AAZStrType(
                serialized_name="defaultDataCollectionRuleResourceId",
            )
            properties.failover = AAZObjectType()
            properties.features = AAZFreeFormDictType()
            properties.force_cmk_for_query = AAZBoolType(
                serialized_name="forceCmkForQuery",
            )
            properties.modified_date = AAZStrType(
                serialized_name="modifiedDate",
                flags={"read_only": True},
            )
            properties.private_link_scoped_resources = AAZListType(
                serialized_name="privateLinkScopedResources",
                flags={"read_only": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.public_network_access_for_ingestion = AAZStrType(
                serialized_name="publicNetworkAccessForIngestion",
            )
            properties.public_network_access_for_query = AAZStrType(
                serialized_name="publicNetworkAccessForQuery",
            )
            properties.replication = AAZObjectType()
            properties.retention_in_days = AAZIntType(
                serialized_name="retentionInDays",
                nullable=True,
            )
            properties.sku = AAZObjectType()
            properties.workspace_capping = AAZObjectType(
                serialized_name="workspaceCapping",
            )

            failover = cls._schema_on_200.value.Element.properties.failover
            failover.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            failover.state = AAZStrType(
                flags={"read_only": True},
            )

            private_link_scoped_resources = cls._schema_on_200.value.Element.properties.private_link_scoped_resources
            private_link_scoped_resources.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.private_link_scoped_resources.Element
            _element.resource_id = AAZStrType(
                serialized_name="resourceId",
            )
            _element.scope_id = AAZStrType(
                serialized_name="scopeId",
            )

            replication = cls._schema_on_200.value.Element.properties.replication
            replication.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            replication.enabled = AAZBoolType()
            replication.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            replication.location = AAZStrType()
            replication.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )

            sku = cls._schema_on_200.value.Element.properties.sku
            sku.capacity_reservation_level = AAZIntType(
                serialized_name="capacityReservationLevel",
            )
            sku.last_sku_update = AAZStrType(
                serialized_name="lastSkuUpdate",
                flags={"read_only": True},
            )
            sku.name = AAZStrType(
                flags={"required": True},
            )

            workspace_capping = cls._schema_on_200.value.Element.properties.workspace_capping
            workspace_capping.daily_quota_gb = AAZFloatType(
                serialized_name="dailyQuotaGb",
            )
            workspace_capping.data_ingestion_status = AAZStrType(
                serialized_name="dataIngestionStatus",
                flags={"read_only": True},
            )
            workspace_capping.quota_next_reset_time = AAZStrType(
                serialized_name="quotaNextResetTime",
                flags={"read_only": True},
            )

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            tags = cls._schema_on_200.value.Element.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200

    class WorkspacesListByResourceGroup(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.OperationalInsights/workspaces",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-02-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.value = AAZListType()

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType()
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.identity = AAZIdentityObjectType()
            _element.location = AAZStrType(
                flags={"required": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            identity = cls._schema_on_200.value.Element.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType(
                flags={"required": True},
            )
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.value.Element.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            properties.customer_id = AAZStrType(
                serialized_name="customerId",
                flags={"read_only": True},
            )
            properties.default_data_collection_rule_resource_id = AAZStrType(
                serialized_name="defaultDataCollectionRuleResourceId",
            )
            properties.failover = AAZObjectType()
            properties.features = AAZFreeFormDictType()
            properties.force_cmk_for_query = AAZBoolType(
                serialized_name="forceCmkForQuery",
            )
            properties.modified_date = AAZStrType(
                serialized_name="modifiedDate",
                flags={"read_only": True},
            )
            properties.private_link_scoped_resources = AAZListType(
                serialized_name="privateLinkScopedResources",
                flags={"read_only": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.public_network_access_for_ingestion = AAZStrType(
                serialized_name="publicNetworkAccessForIngestion",
            )
            properties.public_network_access_for_query = AAZStrType(
                serialized_name="publicNetworkAccessForQuery",
            )
            properties.replication = AAZObjectType()
            properties.retention_in_days = AAZIntType(
                serialized_name="retentionInDays",
                nullable=True,
            )
            properties.sku = AAZObjectType()
            properties.workspace_capping = AAZObjectType(
                serialized_name="workspaceCapping",
            )

            failover = cls._schema_on_200.value.Element.properties.failover
            failover.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            failover.state = AAZStrType(
                flags={"read_only": True},
            )

            private_link_scoped_resources = cls._schema_on_200.value.Element.properties.private_link_scoped_resources
            private_link_scoped_resources.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.private_link_scoped_resources.Element
            _element.resource_id = AAZStrType(
                serialized_name="resourceId",
            )
            _element.scope_id = AAZStrType(
                serialized_name="scopeId",
            )

            replication = cls._schema_on_200.value.Element.properties.replication
            replication.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            replication.enabled = AAZBoolType()
            replication.last_modified_date = AAZStrType(
                serialized_name="lastModifiedDate",
                flags={"read_only": True},
            )
            replication.location = AAZStrType()
            replication.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )

            sku = cls._schema_on_200.value.Element.properties.sku
            sku.capacity_reservation_level = AAZIntType(
                serialized_name="capacityReservationLevel",
            )
            sku.last_sku_update = AAZStrType(
                serialized_name="lastSkuUpdate",
                flags={"read_only": True},
            )
            sku.name = AAZStrType(
                flags={"required": True},
            )

            workspace_capping = cls._schema_on_200.value.Element.properties.workspace_capping
            workspace_capping.daily_quota_gb = AAZFloatType(
                serialized_name="dailyQuotaGb",
            )
            workspace_capping.data_ingestion_status = AAZStrType(
                serialized_name="dataIngestionStatus",
                flags={"read_only": True},
            )
            workspace_capping.quota_next_reset_time = AAZStrType(
                serialized_name="quotaNextResetTime",
                flags={"read_only": True},
            )

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            tags = cls._schema_on_200.value.Element.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""


__all__ = ["List"]
