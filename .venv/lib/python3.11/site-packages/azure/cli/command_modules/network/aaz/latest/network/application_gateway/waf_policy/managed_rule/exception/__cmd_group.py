# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "network application-gateway waf-policy managed-rule exception",
)
class __CMDGroup(AAZCommandGroup):
    """Manage exceptions to allow a request to skip the managed rules when the condition is satisfied.
    """
    pass


__all__ = ["__CMDGroup"]
