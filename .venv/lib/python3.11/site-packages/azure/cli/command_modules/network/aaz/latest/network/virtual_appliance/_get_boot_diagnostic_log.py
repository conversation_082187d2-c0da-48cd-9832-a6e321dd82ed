# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network virtual-appliance get-boot-diagnostic-log",
)
class GetBootDiagnosticLog(AAZCommand):
    """Retrieve the boot diagnostic logs for a VM instance belonging to the specified Network Virtual Appliance.

    :example: Get boot diagnostic logs
        az network virtual-appliance get-boot-diagnostic-log --resource-group rg --network-virtual-appliance-name name --scs-sas-url serialconsole-sas-url --css-sas-url consoleScreenshot-sas-url
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/networkvirtualappliances/{}/getbootdiagnosticlogs", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.network_virtual_appliance_name = AAZStrArg(
            options=["-n", "--name", "--network-virtual-appliance-name"],
            help="The name of Network Virtual Appliance.",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[A-Za-z0-9_]+",
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "Request"

        _args_schema = cls._args_schema
        _args_schema.console_screenshot_storage_sas_url = AAZStrArg(
            options=["--css-sas-url", "--console-screenshot-storage-sas-url"],
            arg_group="Request",
            help="Specify the sas-url to the storage blob into which console screen shot for the requested instance will be written",
        )
        _args_schema.instance_id = AAZIntArg(
            options=["--instance-id"],
            arg_group="Request",
            help="The network virtual appliance instance id for which boot diagnostic logs is being requested",
        )
        _args_schema.serial_console_storage_sas_url = AAZStrArg(
            options=["--scs-sas-url", "--serial-console-storage-sas-url"],
            arg_group="Request",
            help="Specify the sas-url to the storage blob into which serial console logs for the requested instance will be written",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.NetworkVirtualAppliancesGetBootDiagnosticLogs(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class NetworkVirtualAppliancesGetBootDiagnosticLogs(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/networkVirtualAppliances/{networkVirtualApplianceName}/getBootDiagnosticLogs",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "networkVirtualApplianceName", self.ctx.args.network_virtual_appliance_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("consoleScreenshotStorageSasUrl", AAZStrType, ".console_screenshot_storage_sas_url")
            _builder.set_prop("instanceId", AAZIntType, ".instance_id")
            _builder.set_prop("serialConsoleStorageSasUrl", AAZStrType, ".serial_console_storage_sas_url")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.instance_id = AAZIntType(
                serialized_name="instanceId",
            )

            return cls._schema_on_200


class _GetBootDiagnosticLogHelper:
    """Helper class for GetBootDiagnosticLog"""


__all__ = ["GetBootDiagnosticLog"]
