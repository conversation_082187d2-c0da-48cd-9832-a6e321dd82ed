# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network private-link-service create",
)
class Create(AAZCommand):
    """Create a private link service.

    :example: Create a private link service
        az network private-link-service create -g MyResourceGroup -n MyPLSName --vnet-name MyVnetName --subnet MySubnet --lb-name MyLBName --lb-frontend-ip-configs LoadBalancerFrontEnd -l centralus
    """

    _aaz_info = {
        "version": "2024-03-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/privatelinkservices/{}", "2024-03-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="Name of the private link service.",
            required=True,
        )
        _args_schema.edge_zone = AAZStrArg(
            options=["--edge-zone"],
            help="The name of edge zone.",
        )
        _args_schema.location = AAZResourceLocationArg(
            help="Location. Values from: `az account list-locations`. You can configure the default location using `az configure --defaults location=<location>`.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.auto_approval = AAZListArg(
            options=["--auto-approval"],
            help="Space-separated list of subscription IDs to auto-approve.",
        )
        _args_schema.destination_ip_address = AAZStrArg(
            options=["--destination-ip-address"],
            help="The destination IP address of the private link service.",
        )
        _args_schema.enable_proxy_protocol = AAZBoolArg(
            options=["--enable-proxy-protocol"],
            help="Enable proxy protocol for private link service.",
        )
        _args_schema.fqdns = AAZListArg(
            options=["--fqdns"],
            help="Space-separated list of FQDNs.",
        )
        _args_schema.visibility = AAZListArg(
            options=["--visibility"],
            help="Space-separated list of subscription IDs for which the private link service is visible.",
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="Resource tags.",
        )

        auto_approval = cls._args_schema.auto_approval
        auto_approval.Element = AAZStrArg()

        fqdns = cls._args_schema.fqdns
        fqdns.Element = AAZStrArg()

        visibility = cls._args_schema.visibility
        visibility.Element = AAZStrArg()

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "ExtendedLocation"

        _args_schema = cls._args_schema
        _args_schema.edge_zone_type = AAZStrArg(
            options=["--edge-zone-type"],
            arg_group="ExtendedLocation",
            help="The type of the extended location.",
            enum={"EdgeZone": "EdgeZone"},
        )

        # define Arg Group "IP Configuration"

        _args_schema = cls._args_schema
        _args_schema.ip_configurations = AAZListArg(
            options=["--ip-configurations"],
            arg_group="IP Configuration",
            help="An array of private link service IP configurations.",
        )

        ip_configurations = cls._args_schema.ip_configurations
        ip_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of private link service ip configuration.",
        )
        _element.primary = AAZBoolArg(
            options=["primary"],
            help="Whether the ip configuration is primary or not.",
        )
        _element.private_ip_address = AAZStrArg(
            options=["private-ip-address"],
            help="The private IP address of the IP configuration.",
        )
        _element.private_ip_address_version = AAZStrArg(
            options=["private-ip-address-version"],
            help="Whether the specific IP configuration is IPv4 or IPv6. Default is IPv4.",
            enum={"IPv4": "IPv4", "IPv6": "IPv6"},
        )
        _element.private_ip_allocation_method = AAZStrArg(
            options=["private-ip-allocation-method"],
            help="The private IP address allocation method.",
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )
        _element.subnet = AAZObjectArg(
            options=["subnet"],
            help="The reference to the subnet resource.",
        )

        subnet = cls._args_schema.ip_configurations.Element.subnet
        subnet.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/virtualNetworks/{}/subnets/{}",
            ),
        )
        subnet.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        subnet.address_prefix = AAZStrArg(
            options=["address-prefix"],
            help="The address prefix for the subnet.",
        )
        subnet.address_prefixes = AAZListArg(
            options=["address-prefixes"],
            help="List of address prefixes for the subnet.",
        )
        subnet.application_gateway_ip_configurations = AAZListArg(
            options=["application-gateway-ip-configurations"],
            help="Application gateway IP configurations of virtual network resource.",
        )
        subnet.default_outbound_access = AAZBoolArg(
            options=["default-outbound-access"],
            help="Set this property to false to disable default outbound connectivity for all VMs in the subnet. This property can only be set at the time of subnet creation and cannot be updated for an existing subnet.",
        )
        subnet.delegations = AAZListArg(
            options=["delegations"],
            help="An array of references to the delegations on the subnet.",
        )
        subnet.ip_allocations = AAZListArg(
            options=["ip-allocations"],
            help="Array of IpAllocation which reference this subnet.",
        )
        subnet.nat_gateway = AAZObjectArg(
            options=["nat-gateway"],
            help="Nat gateway associated with this subnet.",
        )
        cls._build_args_sub_resource_create(subnet.nat_gateway)
        subnet.network_security_group = AAZObjectArg(
            options=["network-security-group"],
            help="The reference to the NetworkSecurityGroup resource.",
        )
        subnet.private_endpoint_network_policies = AAZStrArg(
            options=["private-endpoint-network-policies"],
            help="Enable or Disable apply network policies on private end point in the subnet.",
            default="Disabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled", "NetworkSecurityGroupEnabled": "NetworkSecurityGroupEnabled", "RouteTableEnabled": "RouteTableEnabled"},
        )
        subnet.private_link_service_network_policies = AAZStrArg(
            options=["private-link-service-network-policies"],
            help="Enable or Disable apply network policies on private link service in the subnet.",
            default="Enabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        subnet.route_table = AAZObjectArg(
            options=["route-table"],
            help="The reference to the RouteTable resource.",
        )
        subnet.service_endpoint_policies = AAZListArg(
            options=["service-endpoint-policies"],
            help="An array of service endpoint policies.",
        )
        subnet.service_endpoints = AAZListArg(
            options=["service-endpoints"],
            help="An array of service endpoints.",
        )
        subnet.sharing_scope = AAZStrArg(
            options=["sharing-scope"],
            help="Set this property to Tenant to allow sharing subnet with other subscriptions in your AAD tenant. This property can only be set if defaultOutboundAccess is set to false, both properties can only be set if subnet is empty.",
            enum={"DelegatedServices": "DelegatedServices", "Tenant": "Tenant"},
        )
        subnet.type = AAZStrArg(
            options=["type"],
            help="Resource type.",
        )

        address_prefixes = cls._args_schema.ip_configurations.Element.subnet.address_prefixes
        address_prefixes.Element = AAZStrArg()

        application_gateway_ip_configurations = cls._args_schema.ip_configurations.Element.subnet.application_gateway_ip_configurations
        application_gateway_ip_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element.subnet.application_gateway_ip_configurations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="Name of the IP configuration that is unique within an Application Gateway.",
        )
        _element.subnet = AAZObjectArg(
            options=["subnet"],
            help="Reference to the subnet resource. A subnet from where application gateway gets its private address.",
        )
        cls._build_args_sub_resource_create(_element.subnet)

        delegations = cls._args_schema.ip_configurations.Element.subnet.delegations
        delegations.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element.subnet.delegations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a subnet. This name can be used to access the resource.",
        )
        _element.service_name = AAZStrArg(
            options=["service-name"],
            help="The name of the service to whom the subnet should be delegated (e.g. Microsoft.Sql/servers).",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="Resource type.",
        )

        ip_allocations = cls._args_schema.ip_configurations.Element.subnet.ip_allocations
        ip_allocations.Element = AAZObjectArg()
        cls._build_args_sub_resource_create(ip_allocations.Element)

        network_security_group = cls._args_schema.ip_configurations.Element.subnet.network_security_group
        network_security_group.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/networkSecurityGroups/{}",
            ),
        )
        network_security_group.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        network_security_group.flush_connection = AAZBoolArg(
            options=["flush-connection"],
            help="When enabled, flows created from Network Security Group connections will be re-evaluated when rules are updates. Initial enablement will trigger re-evaluation.",
        )
        network_security_group.security_rules = AAZListArg(
            options=["security-rules"],
            help="A collection of security rules of the network security group.",
        )
        network_security_group.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        security_rules = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules
        security_rules.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.access = AAZStrArg(
            options=["access"],
            help="The network traffic is allowed or denied.",
            enum={"Allow": "Allow", "Deny": "Deny"},
        )
        _element.description = AAZStrArg(
            options=["description"],
            help="A description for this rule. Restricted to 140 chars.",
        )
        _element.destination_address_prefix = AAZStrArg(
            options=["destination-address-prefix"],
            help="The destination address prefix. CIDR or destination IP range. Asterisk '*' can also be used to match all source IPs. Default tags such as 'VirtualNetwork', 'AzureLoadBalancer' and 'Internet' can also be used.",
        )
        _element.destination_address_prefixes = AAZListArg(
            options=["destination-address-prefixes"],
            help="The destination address prefixes. CIDR or destination IP ranges.",
        )
        _element.destination_application_security_groups = AAZListArg(
            options=["destination-application-security-groups"],
            help="The application security group specified as destination.",
        )
        _element.destination_port_range = AAZStrArg(
            options=["destination-port-range"],
            help="The destination port or range. Integer or range between 0 and 65535. Asterisk '*' can also be used to match all ports.",
        )
        _element.destination_port_ranges = AAZListArg(
            options=["destination-port-ranges"],
            help="The destination port ranges.",
        )
        _element.direction = AAZStrArg(
            options=["direction"],
            help="The direction of the rule. The direction specifies if rule will be evaluated on incoming or outgoing traffic.",
            enum={"Inbound": "Inbound", "Outbound": "Outbound"},
        )
        _element.priority = AAZIntArg(
            options=["priority"],
            help="The priority of the rule. The value can be between 100 and 4096. The priority number must be unique for each rule in the collection. The lower the priority number, the higher the priority of the rule.",
        )
        _element.protocol = AAZStrArg(
            options=["protocol"],
            help="Network protocol this rule applies to.",
            enum={"*": "*", "Ah": "Ah", "Esp": "Esp", "Icmp": "Icmp", "Tcp": "Tcp", "Udp": "Udp"},
        )
        _element.source_address_prefix = AAZStrArg(
            options=["source-address-prefix"],
            help="The CIDR or source IP range. Asterisk '*' can also be used to match all source IPs. Default tags such as 'VirtualNetwork', 'AzureLoadBalancer' and 'Internet' can also be used. If this is an ingress rule, specifies where network traffic originates from.",
        )
        _element.source_address_prefixes = AAZListArg(
            options=["source-address-prefixes"],
            help="The CIDR or source IP ranges.",
        )
        _element.source_application_security_groups = AAZListArg(
            options=["source-application-security-groups"],
            help="The application security group specified as source.",
        )
        _element.source_port_range = AAZStrArg(
            options=["source-port-range"],
            help="The source port or range. Integer or range between 0 and 65535. Asterisk '*' can also be used to match all ports.",
        )
        _element.source_port_ranges = AAZListArg(
            options=["source-port-ranges"],
            help="The source port ranges.",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of the resource.",
        )

        destination_address_prefixes = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules.Element.destination_address_prefixes
        destination_address_prefixes.Element = AAZStrArg()

        destination_application_security_groups = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules.Element.destination_application_security_groups
        destination_application_security_groups.Element = AAZObjectArg()
        cls._build_args_application_security_group_create(destination_application_security_groups.Element)

        destination_port_ranges = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules.Element.destination_port_ranges
        destination_port_ranges.Element = AAZStrArg()

        source_address_prefixes = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules.Element.source_address_prefixes
        source_address_prefixes.Element = AAZStrArg()

        source_application_security_groups = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules.Element.source_application_security_groups
        source_application_security_groups.Element = AAZObjectArg()
        cls._build_args_application_security_group_create(source_application_security_groups.Element)

        source_port_ranges = cls._args_schema.ip_configurations.Element.subnet.network_security_group.security_rules.Element.source_port_ranges
        source_port_ranges.Element = AAZStrArg()

        tags = cls._args_schema.ip_configurations.Element.subnet.network_security_group.tags
        tags.Element = AAZStrArg()

        route_table = cls._args_schema.ip_configurations.Element.subnet.route_table
        route_table.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/routeTables/{}",
            ),
        )
        route_table.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        route_table.disable_bgp_route_propagation = AAZBoolArg(
            options=["disable-bgp-route-propagation"],
            help="Whether to disable the routes learned by BGP on that route table. True means disable.",
        )
        route_table.routes = AAZListArg(
            options=["routes"],
            help="Collection of routes contained within a route table.",
        )
        route_table.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        routes = cls._args_schema.ip_configurations.Element.subnet.route_table.routes
        routes.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element.subnet.route_table.routes.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/routeTables/{}/routes/{}",
            ),
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.address_prefix = AAZStrArg(
            options=["address-prefix"],
            help="The destination CIDR to which the route applies.",
        )
        _element.next_hop_ip_address = AAZStrArg(
            options=["next-hop-ip-address"],
            help="The IP address packets should be forwarded to. Next hop values are only allowed in routes where the next hop type is VirtualAppliance.",
        )
        _element.next_hop_type = AAZStrArg(
            options=["next-hop-type"],
            help="The type of Azure hop the packet should be sent to.",
            enum={"Internet": "Internet", "None": "None", "VirtualAppliance": "VirtualAppliance", "VirtualNetworkGateway": "VirtualNetworkGateway", "VnetLocal": "VnetLocal"},
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of the resource.",
        )

        tags = cls._args_schema.ip_configurations.Element.subnet.route_table.tags
        tags.Element = AAZStrArg()

        service_endpoint_policies = cls._args_schema.ip_configurations.Element.subnet.service_endpoint_policies
        service_endpoint_policies.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element.subnet.service_endpoint_policies.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/serviceEndpointPolicies/{}",
            ),
        )
        _element.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _element.contextual_service_endpoint_policies = AAZListArg(
            options=["contextual-service-endpoint-policies"],
            help="A collection of contextual service endpoint policy.",
        )
        _element.service_alias = AAZStrArg(
            options=["service-alias"],
            help="The alias indicating if the policy belongs to a service",
        )
        _element.service_endpoint_policy_definitions = AAZListArg(
            options=["service-endpoint-policy-definitions"],
            help="A collection of service endpoint policy definitions of the service endpoint policy.",
        )
        _element.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        contextual_service_endpoint_policies = cls._args_schema.ip_configurations.Element.subnet.service_endpoint_policies.Element.contextual_service_endpoint_policies
        contextual_service_endpoint_policies.Element = AAZStrArg()

        service_endpoint_policy_definitions = cls._args_schema.ip_configurations.Element.subnet.service_endpoint_policies.Element.service_endpoint_policy_definitions
        service_endpoint_policy_definitions.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element.subnet.service_endpoint_policies.Element.service_endpoint_policy_definitions.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/serviceEndpointPolicies/{}/serviceEndpointPolicyDefinitions/{}",
            ),
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.description = AAZStrArg(
            options=["description"],
            help="A description for this rule. Restricted to 140 chars.",
        )
        _element.service = AAZStrArg(
            options=["service"],
            help="Service endpoint name.",
        )
        _element.service_resources = AAZListArg(
            options=["service-resources"],
            help="A list of service resources.",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of the resource.",
        )

        service_resources = cls._args_schema.ip_configurations.Element.subnet.service_endpoint_policies.Element.service_endpoint_policy_definitions.Element.service_resources
        service_resources.Element = AAZStrArg()

        tags = cls._args_schema.ip_configurations.Element.subnet.service_endpoint_policies.Element.tags
        tags.Element = AAZStrArg()

        service_endpoints = cls._args_schema.ip_configurations.Element.subnet.service_endpoints
        service_endpoints.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element.subnet.service_endpoints.Element
        _element.locations = AAZListArg(
            options=["locations"],
            help="A list of locations.",
        )
        _element.network_identifier = AAZObjectArg(
            options=["network-identifier"],
            help="SubResource as network identifier.",
        )
        cls._build_args_sub_resource_create(_element.network_identifier)
        _element.service = AAZStrArg(
            options=["service"],
            help="The type of the endpoint service.",
        )

        locations = cls._args_schema.ip_configurations.Element.subnet.service_endpoints.Element.locations
        locations.Element = AAZStrArg()

        # define Arg Group "Parameters"

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.load_balancer_frontend_ip_configurations = AAZListArg(
            options=["--load-balancer-frontend-ip-configurations"],
            arg_group="Properties",
            help="An array of references to the load balancer IP configurations.",
        )

        load_balancer_frontend_ip_configurations = cls._args_schema.load_balancer_frontend_ip_configurations
        load_balancer_frontend_ip_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/loadBalancers/{}/frontendIPConfigurations/{}",
            ),
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within the set of frontend IP configurations used by the load balancer. This name can be used to access the resource.",
        )
        _element.gateway_load_balancer = AAZObjectArg(
            options=["gateway-load-balancer"],
            help="The reference to gateway load balancer frontend IP.",
        )
        _element.private_ip_address = AAZStrArg(
            options=["private-ip-address"],
            help="The private IP address of the IP configuration.",
        )
        _element.private_ip_address_version = AAZStrArg(
            options=["private-ip-address-version"],
            help="Whether the specific ipconfiguration is IPv4 or IPv6. Default is taken as IPv4.",
            enum={"IPv4": "IPv4", "IPv6": "IPv6"},
        )
        _element.private_ip_allocation_method = AAZStrArg(
            options=["private-ip-allocation-method"],
            help="The Private IP allocation method.",
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )
        _element.public_ip_prefix = AAZObjectArg(
            options=["public-ip-prefix"],
            help="The reference to the Public IP Prefix resource.",
        )
        _element.subnet = AAZObjectArg(
            options=["subnet"],
            help="The reference to the subnet resource.",
        )
        _element.zones = AAZListArg(
            options=["zones"],
            help="A list of availability zones denoting the IP allocated for the resource needs to come from.",
        )

        gateway_load_balancer = cls._args_schema.load_balancer_frontend_ip_configurations.Element.gateway_load_balancer
        gateway_load_balancer.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        public_ip_prefix = cls._args_schema.load_balancer_frontend_ip_configurations.Element.public_ip_prefix
        public_ip_prefix.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        subnet = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet
        subnet.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/virtualNetworks/{}/subnets/{}",
            ),
        )
        subnet.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        subnet.address_prefix = AAZStrArg(
            options=["address-prefix"],
            help="The address prefix for the subnet.",
        )
        subnet.address_prefixes = AAZListArg(
            options=["address-prefixes"],
            help="List of address prefixes for the subnet.",
        )
        subnet.application_gateway_ip_configurations = AAZListArg(
            options=["application-gateway-ip-configurations"],
            help="Application gateway IP configurations of virtual network resource.",
        )
        subnet.default_outbound_access = AAZBoolArg(
            options=["default-outbound-access"],
            help="Set this property to false to disable default outbound connectivity for all VMs in the subnet. This property can only be set at the time of subnet creation and cannot be updated for an existing subnet.",
        )
        subnet.delegations = AAZListArg(
            options=["delegations"],
            help="An array of references to the delegations on the subnet.",
        )
        subnet.ip_allocations = AAZListArg(
            options=["ip-allocations"],
            help="Array of IpAllocation which reference this subnet.",
        )
        subnet.nat_gateway = AAZObjectArg(
            options=["nat-gateway"],
            help="Nat gateway associated with this subnet.",
        )
        cls._build_args_sub_resource_create(subnet.nat_gateway)
        subnet.network_security_group = AAZObjectArg(
            options=["network-security-group"],
            help="The reference to the NetworkSecurityGroup resource.",
        )
        subnet.private_endpoint_network_policies = AAZStrArg(
            options=["private-endpoint-network-policies"],
            help="Enable or Disable apply network policies on private end point in the subnet.",
            default="Disabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled", "NetworkSecurityGroupEnabled": "NetworkSecurityGroupEnabled", "RouteTableEnabled": "RouteTableEnabled"},
        )
        subnet.private_link_service_network_policies = AAZStrArg(
            options=["private-link-service-network-policies"],
            help="Enable or Disable apply network policies on private link service in the subnet.",
            default="Enabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        subnet.route_table = AAZObjectArg(
            options=["route-table"],
            help="The reference to the RouteTable resource.",
        )
        subnet.service_endpoint_policies = AAZListArg(
            options=["service-endpoint-policies"],
            help="An array of service endpoint policies.",
        )
        subnet.service_endpoints = AAZListArg(
            options=["service-endpoints"],
            help="An array of service endpoints.",
        )
        subnet.sharing_scope = AAZStrArg(
            options=["sharing-scope"],
            help="Set this property to Tenant to allow sharing subnet with other subscriptions in your AAD tenant. This property can only be set if defaultOutboundAccess is set to false, both properties can only be set if subnet is empty.",
            enum={"DelegatedServices": "DelegatedServices", "Tenant": "Tenant"},
        )
        subnet.type = AAZStrArg(
            options=["type"],
            help="Resource type.",
        )

        address_prefixes = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.address_prefixes
        address_prefixes.Element = AAZStrArg()

        application_gateway_ip_configurations = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.application_gateway_ip_configurations
        application_gateway_ip_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.application_gateway_ip_configurations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="Name of the IP configuration that is unique within an Application Gateway.",
        )
        _element.subnet = AAZObjectArg(
            options=["subnet"],
            help="Reference to the subnet resource. A subnet from where application gateway gets its private address.",
        )
        cls._build_args_sub_resource_create(_element.subnet)

        delegations = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.delegations
        delegations.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.delegations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a subnet. This name can be used to access the resource.",
        )
        _element.service_name = AAZStrArg(
            options=["service-name"],
            help="The name of the service to whom the subnet should be delegated (e.g. Microsoft.Sql/servers).",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="Resource type.",
        )

        ip_allocations = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.ip_allocations
        ip_allocations.Element = AAZObjectArg()
        cls._build_args_sub_resource_create(ip_allocations.Element)

        network_security_group = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group
        network_security_group.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/networkSecurityGroups/{}",
            ),
        )
        network_security_group.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        network_security_group.flush_connection = AAZBoolArg(
            options=["flush-connection"],
            help="When enabled, flows created from Network Security Group connections will be re-evaluated when rules are updates. Initial enablement will trigger re-evaluation.",
        )
        network_security_group.security_rules = AAZListArg(
            options=["security-rules"],
            help="A collection of security rules of the network security group.",
        )
        network_security_group.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        security_rules = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules
        security_rules.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.access = AAZStrArg(
            options=["access"],
            help="The network traffic is allowed or denied.",
            enum={"Allow": "Allow", "Deny": "Deny"},
        )
        _element.description = AAZStrArg(
            options=["description"],
            help="A description for this rule. Restricted to 140 chars.",
        )
        _element.destination_address_prefix = AAZStrArg(
            options=["destination-address-prefix"],
            help="The destination address prefix. CIDR or destination IP range. Asterisk '*' can also be used to match all source IPs. Default tags such as 'VirtualNetwork', 'AzureLoadBalancer' and 'Internet' can also be used.",
        )
        _element.destination_address_prefixes = AAZListArg(
            options=["destination-address-prefixes"],
            help="The destination address prefixes. CIDR or destination IP ranges.",
        )
        _element.destination_application_security_groups = AAZListArg(
            options=["destination-application-security-groups"],
            help="The application security group specified as destination.",
        )
        _element.destination_port_range = AAZStrArg(
            options=["destination-port-range"],
            help="The destination port or range. Integer or range between 0 and 65535. Asterisk '*' can also be used to match all ports.",
        )
        _element.destination_port_ranges = AAZListArg(
            options=["destination-port-ranges"],
            help="The destination port ranges.",
        )
        _element.direction = AAZStrArg(
            options=["direction"],
            help="The direction of the rule. The direction specifies if rule will be evaluated on incoming or outgoing traffic.",
            enum={"Inbound": "Inbound", "Outbound": "Outbound"},
        )
        _element.priority = AAZIntArg(
            options=["priority"],
            help="The priority of the rule. The value can be between 100 and 4096. The priority number must be unique for each rule in the collection. The lower the priority number, the higher the priority of the rule.",
        )
        _element.protocol = AAZStrArg(
            options=["protocol"],
            help="Network protocol this rule applies to.",
            enum={"*": "*", "Ah": "Ah", "Esp": "Esp", "Icmp": "Icmp", "Tcp": "Tcp", "Udp": "Udp"},
        )
        _element.source_address_prefix = AAZStrArg(
            options=["source-address-prefix"],
            help="The CIDR or source IP range. Asterisk '*' can also be used to match all source IPs. Default tags such as 'VirtualNetwork', 'AzureLoadBalancer' and 'Internet' can also be used. If this is an ingress rule, specifies where network traffic originates from.",
        )
        _element.source_address_prefixes = AAZListArg(
            options=["source-address-prefixes"],
            help="The CIDR or source IP ranges.",
        )
        _element.source_application_security_groups = AAZListArg(
            options=["source-application-security-groups"],
            help="The application security group specified as source.",
        )
        _element.source_port_range = AAZStrArg(
            options=["source-port-range"],
            help="The source port or range. Integer or range between 0 and 65535. Asterisk '*' can also be used to match all ports.",
        )
        _element.source_port_ranges = AAZListArg(
            options=["source-port-ranges"],
            help="The source port ranges.",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of the resource.",
        )

        destination_address_prefixes = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules.Element.destination_address_prefixes
        destination_address_prefixes.Element = AAZStrArg()

        destination_application_security_groups = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules.Element.destination_application_security_groups
        destination_application_security_groups.Element = AAZObjectArg()
        cls._build_args_application_security_group_create(destination_application_security_groups.Element)

        destination_port_ranges = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules.Element.destination_port_ranges
        destination_port_ranges.Element = AAZStrArg()

        source_address_prefixes = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules.Element.source_address_prefixes
        source_address_prefixes.Element = AAZStrArg()

        source_application_security_groups = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules.Element.source_application_security_groups
        source_application_security_groups.Element = AAZObjectArg()
        cls._build_args_application_security_group_create(source_application_security_groups.Element)

        source_port_ranges = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.security_rules.Element.source_port_ranges
        source_port_ranges.Element = AAZStrArg()

        tags = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.network_security_group.tags
        tags.Element = AAZStrArg()

        route_table = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.route_table
        route_table.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/routeTables/{}",
            ),
        )
        route_table.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        route_table.disable_bgp_route_propagation = AAZBoolArg(
            options=["disable-bgp-route-propagation"],
            help="Whether to disable the routes learned by BGP on that route table. True means disable.",
        )
        route_table.routes = AAZListArg(
            options=["routes"],
            help="Collection of routes contained within a route table.",
        )
        route_table.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        routes = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.route_table.routes
        routes.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.route_table.routes.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/routeTables/{}/routes/{}",
            ),
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.address_prefix = AAZStrArg(
            options=["address-prefix"],
            help="The destination CIDR to which the route applies.",
        )
        _element.next_hop_ip_address = AAZStrArg(
            options=["next-hop-ip-address"],
            help="The IP address packets should be forwarded to. Next hop values are only allowed in routes where the next hop type is VirtualAppliance.",
        )
        _element.next_hop_type = AAZStrArg(
            options=["next-hop-type"],
            help="The type of Azure hop the packet should be sent to.",
            enum={"Internet": "Internet", "None": "None", "VirtualAppliance": "VirtualAppliance", "VirtualNetworkGateway": "VirtualNetworkGateway", "VnetLocal": "VnetLocal"},
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of the resource.",
        )

        tags = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.route_table.tags
        tags.Element = AAZStrArg()

        service_endpoint_policies = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoint_policies
        service_endpoint_policies.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoint_policies.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/serviceEndpointPolicies/{}",
            ),
        )
        _element.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _element.contextual_service_endpoint_policies = AAZListArg(
            options=["contextual-service-endpoint-policies"],
            help="A collection of contextual service endpoint policy.",
        )
        _element.service_alias = AAZStrArg(
            options=["service-alias"],
            help="The alias indicating if the policy belongs to a service",
        )
        _element.service_endpoint_policy_definitions = AAZListArg(
            options=["service-endpoint-policy-definitions"],
            help="A collection of service endpoint policy definitions of the service endpoint policy.",
        )
        _element.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        contextual_service_endpoint_policies = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoint_policies.Element.contextual_service_endpoint_policies
        contextual_service_endpoint_policies.Element = AAZStrArg()

        service_endpoint_policy_definitions = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoint_policies.Element.service_endpoint_policy_definitions
        service_endpoint_policy_definitions.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoint_policies.Element.service_endpoint_policy_definitions.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/serviceEndpointPolicies/{}/serviceEndpointPolicyDefinitions/{}",
            ),
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.description = AAZStrArg(
            options=["description"],
            help="A description for this rule. Restricted to 140 chars.",
        )
        _element.service = AAZStrArg(
            options=["service"],
            help="Service endpoint name.",
        )
        _element.service_resources = AAZListArg(
            options=["service-resources"],
            help="A list of service resources.",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of the resource.",
        )

        service_resources = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoint_policies.Element.service_endpoint_policy_definitions.Element.service_resources
        service_resources.Element = AAZStrArg()

        tags = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoint_policies.Element.tags
        tags.Element = AAZStrArg()

        service_endpoints = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoints
        service_endpoints.Element = AAZObjectArg()

        _element = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoints.Element
        _element.locations = AAZListArg(
            options=["locations"],
            help="A list of locations.",
        )
        _element.network_identifier = AAZObjectArg(
            options=["network-identifier"],
            help="SubResource as network identifier.",
        )
        cls._build_args_sub_resource_create(_element.network_identifier)
        _element.service = AAZStrArg(
            options=["service"],
            help="The type of the endpoint service.",
        )

        locations = cls._args_schema.load_balancer_frontend_ip_configurations.Element.subnet.service_endpoints.Element.locations
        locations.Element = AAZStrArg()

        zones = cls._args_schema.load_balancer_frontend_ip_configurations.Element.zones
        zones.Element = AAZStrArg()
        return cls._args_schema

    _args_application_security_group_create = None

    @classmethod
    def _build_args_application_security_group_create(cls, _schema):
        if cls._args_application_security_group_create is not None:
            _schema.id = cls._args_application_security_group_create.id
            _schema.location = cls._args_application_security_group_create.location
            _schema.tags = cls._args_application_security_group_create.tags
            return

        cls._args_application_security_group_create = AAZObjectArg()

        application_security_group_create = cls._args_application_security_group_create
        application_security_group_create.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/applicationSecurityGroups/{}",
            ),
        )
        application_security_group_create.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        application_security_group_create.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        tags = cls._args_application_security_group_create.tags
        tags.Element = AAZStrArg()

        _schema.id = cls._args_application_security_group_create.id
        _schema.location = cls._args_application_security_group_create.location
        _schema.tags = cls._args_application_security_group_create.tags

    _args_extended_location_create = None

    @classmethod
    def _build_args_extended_location_create(cls, _schema):
        if cls._args_extended_location_create is not None:
            _schema.name = cls._args_extended_location_create.name
            _schema.type = cls._args_extended_location_create.type
            return

        cls._args_extended_location_create = AAZObjectArg()

        extended_location_create = cls._args_extended_location_create
        extended_location_create.name = AAZStrArg(
            options=["name"],
            help="The name of the extended location.",
        )
        extended_location_create.type = AAZStrArg(
            options=["type"],
            help="The type of the extended location.",
            enum={"EdgeZone": "EdgeZone"},
        )

        _schema.name = cls._args_extended_location_create.name
        _schema.type = cls._args_extended_location_create.type

    _args_public_ip_address_create = None

    @classmethod
    def _build_args_public_ip_address_create(cls, _schema):
        if cls._args_public_ip_address_create is not None:
            _schema.ddos_settings = cls._args_public_ip_address_create.ddos_settings
            _schema.delete_option = cls._args_public_ip_address_create.delete_option
            _schema.dns_settings = cls._args_public_ip_address_create.dns_settings
            _schema.extended_location = cls._args_public_ip_address_create.extended_location
            _schema.id = cls._args_public_ip_address_create.id
            _schema.idle_timeout_in_minutes = cls._args_public_ip_address_create.idle_timeout_in_minutes
            _schema.ip_address = cls._args_public_ip_address_create.ip_address
            _schema.ip_tags = cls._args_public_ip_address_create.ip_tags
            _schema.linked_public_ip_address = cls._args_public_ip_address_create.linked_public_ip_address
            _schema.location = cls._args_public_ip_address_create.location
            _schema.migration_phase = cls._args_public_ip_address_create.migration_phase
            _schema.nat_gateway = cls._args_public_ip_address_create.nat_gateway
            _schema.public_ip_address_version = cls._args_public_ip_address_create.public_ip_address_version
            _schema.public_ip_allocation_method = cls._args_public_ip_address_create.public_ip_allocation_method
            _schema.public_ip_prefix = cls._args_public_ip_address_create.public_ip_prefix
            _schema.service_public_ip_address = cls._args_public_ip_address_create.service_public_ip_address
            _schema.sku = cls._args_public_ip_address_create.sku
            _schema.tags = cls._args_public_ip_address_create.tags
            _schema.zones = cls._args_public_ip_address_create.zones
            return

        cls._args_public_ip_address_create = AAZObjectArg()

        public_ip_address_create = cls._args_public_ip_address_create
        public_ip_address_create.extended_location = AAZObjectArg(
            options=["extended-location"],
            help="The extended location of the public ip address.",
        )
        cls._build_args_extended_location_create(public_ip_address_create.extended_location)
        public_ip_address_create.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/publicIPAddresses/{}",
            ),
        )
        public_ip_address_create.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        public_ip_address_create.ddos_settings = AAZObjectArg(
            options=["ddos-settings"],
            help="The DDoS protection custom policy associated with the public IP address.",
        )
        public_ip_address_create.delete_option = AAZStrArg(
            options=["delete-option"],
            help="Specify what happens to the public IP address when the VM using it is deleted",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        public_ip_address_create.dns_settings = AAZObjectArg(
            options=["dns-settings"],
            help="The FQDN of the DNS record associated with the public IP address.",
        )
        public_ip_address_create.idle_timeout_in_minutes = AAZIntArg(
            options=["idle-timeout-in-minutes"],
            help="The idle timeout of the public IP address.",
        )
        public_ip_address_create.ip_address = AAZStrArg(
            options=["ip-address"],
            help="The IP address associated with the public IP address resource.",
        )
        public_ip_address_create.ip_tags = AAZListArg(
            options=["ip-tags"],
            help="The list of tags associated with the public IP address.",
        )
        public_ip_address_create.linked_public_ip_address = AAZObjectArg(
            options=["linked-public-ip-address"],
            help="The linked public IP address of the public IP address resource.",
        )
        cls._build_args_public_ip_address_create(public_ip_address_create.linked_public_ip_address)
        public_ip_address_create.migration_phase = AAZStrArg(
            options=["migration-phase"],
            help="Migration phase of Public IP Address.",
            enum={"Abort": "Abort", "Commit": "Commit", "Committed": "Committed", "None": "None", "Prepare": "Prepare"},
        )
        public_ip_address_create.nat_gateway = AAZObjectArg(
            options=["nat-gateway"],
            help="The NatGateway for the Public IP address.",
        )
        public_ip_address_create.public_ip_address_version = AAZStrArg(
            options=["public-ip-address-version"],
            help="The public IP address version.",
            enum={"IPv4": "IPv4", "IPv6": "IPv6"},
        )
        public_ip_address_create.public_ip_allocation_method = AAZStrArg(
            options=["public-ip-allocation-method"],
            help="The public IP address allocation method.",
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )
        public_ip_address_create.public_ip_prefix = AAZObjectArg(
            options=["public-ip-prefix"],
            help="The Public IP Prefix this Public IP Address should be allocated from.",
        )
        cls._build_args_sub_resource_create(public_ip_address_create.public_ip_prefix)
        public_ip_address_create.service_public_ip_address = AAZObjectArg(
            options=["service-public-ip-address"],
            help="The service public IP address of the public IP address resource.",
        )
        cls._build_args_public_ip_address_create(public_ip_address_create.service_public_ip_address)
        public_ip_address_create.sku = AAZObjectArg(
            options=["sku"],
            help="The public IP address SKU.",
        )
        public_ip_address_create.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )
        public_ip_address_create.zones = AAZListArg(
            options=["zones"],
            help="A list of availability zones denoting the IP allocated for the resource needs to come from.",
        )

        ddos_settings = cls._args_public_ip_address_create.ddos_settings
        ddos_settings.ddos_protection_plan = AAZObjectArg(
            options=["ddos-protection-plan"],
            help="The DDoS protection plan associated with the public IP. Can only be set if ProtectionMode is Enabled",
        )
        cls._build_args_sub_resource_create(ddos_settings.ddos_protection_plan)
        ddos_settings.protection_mode = AAZStrArg(
            options=["protection-mode"],
            help="The DDoS protection mode of the public IP",
            enum={"Disabled": "Disabled", "Enabled": "Enabled", "VirtualNetworkInherited": "VirtualNetworkInherited"},
        )

        dns_settings = cls._args_public_ip_address_create.dns_settings
        dns_settings.domain_name_label = AAZStrArg(
            options=["domain-name-label"],
            help="The domain name label. The concatenation of the domain name label and the regionalized DNS zone make up the fully qualified domain name associated with the public IP address. If a domain name label is specified, an A DNS record is created for the public IP in the Microsoft Azure DNS system.",
        )
        dns_settings.domain_name_label_scope = AAZStrArg(
            options=["domain-name-label-scope"],
            help="The domain name label scope. If a domain name label and a domain name label scope are specified, an A DNS record is created for the public IP in the Microsoft Azure DNS system with a hashed value includes in FQDN.",
            enum={"NoReuse": "NoReuse", "ResourceGroupReuse": "ResourceGroupReuse", "SubscriptionReuse": "SubscriptionReuse", "TenantReuse": "TenantReuse"},
        )
        dns_settings.fqdn = AAZStrArg(
            options=["fqdn"],
            help="The Fully Qualified Domain Name of the A DNS record associated with the public IP. This is the concatenation of the domainNameLabel and the regionalized DNS zone.",
        )
        dns_settings.reverse_fqdn = AAZStrArg(
            options=["reverse-fqdn"],
            help="The reverse FQDN. A user-visible, fully qualified domain name that resolves to this public IP address. If the reverseFqdn is specified, then a PTR DNS record is created pointing from the IP address in the in-addr.arpa domain to the reverse FQDN.",
        )

        ip_tags = cls._args_public_ip_address_create.ip_tags
        ip_tags.Element = AAZObjectArg()

        _element = cls._args_public_ip_address_create.ip_tags.Element
        _element.ip_tag_type = AAZStrArg(
            options=["ip-tag-type"],
            help="The IP tag type. Example: FirstPartyUsage.",
        )
        _element.tag = AAZStrArg(
            options=["tag"],
            help="The value of the IP tag associated with the public IP. Example: SQL.",
        )

        nat_gateway = cls._args_public_ip_address_create.nat_gateway
        nat_gateway.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/natGateways/{}",
            ),
        )
        nat_gateway.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        nat_gateway.idle_timeout_in_minutes = AAZIntArg(
            options=["idle-timeout-in-minutes"],
            help="The idle timeout of the nat gateway.",
        )
        nat_gateway.public_ip_addresses = AAZListArg(
            options=["public-ip-addresses"],
            help="An array of public ip addresses associated with the nat gateway resource.",
        )
        nat_gateway.public_ip_prefixes = AAZListArg(
            options=["public-ip-prefixes"],
            help="An array of public ip prefixes associated with the nat gateway resource.",
        )
        nat_gateway.sku = AAZObjectArg(
            options=["sku"],
            help="The nat gateway SKU.",
        )
        nat_gateway.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )
        nat_gateway.zones = AAZListArg(
            options=["zones"],
            help="A list of availability zones denoting the zone in which Nat Gateway should be deployed.",
        )

        public_ip_addresses = cls._args_public_ip_address_create.nat_gateway.public_ip_addresses
        public_ip_addresses.Element = AAZObjectArg()
        cls._build_args_sub_resource_create(public_ip_addresses.Element)

        public_ip_prefixes = cls._args_public_ip_address_create.nat_gateway.public_ip_prefixes
        public_ip_prefixes.Element = AAZObjectArg()
        cls._build_args_sub_resource_create(public_ip_prefixes.Element)

        sku = cls._args_public_ip_address_create.nat_gateway.sku
        sku.name = AAZStrArg(
            options=["name"],
            help="Name of Nat Gateway SKU.",
            enum={"Standard": "Standard"},
        )

        tags = cls._args_public_ip_address_create.nat_gateway.tags
        tags.Element = AAZStrArg()

        zones = cls._args_public_ip_address_create.nat_gateway.zones
        zones.Element = AAZStrArg()

        sku = cls._args_public_ip_address_create.sku
        sku.name = AAZStrArg(
            options=["name"],
            help="Name of a public IP address SKU.",
            enum={"Basic": "Basic", "Standard": "Standard"},
        )
        sku.tier = AAZStrArg(
            options=["tier"],
            help="Tier of a public IP address SKU.",
            enum={"Global": "Global", "Regional": "Regional"},
        )

        tags = cls._args_public_ip_address_create.tags
        tags.Element = AAZStrArg()

        zones = cls._args_public_ip_address_create.zones
        zones.Element = AAZStrArg()

        _schema.ddos_settings = cls._args_public_ip_address_create.ddos_settings
        _schema.delete_option = cls._args_public_ip_address_create.delete_option
        _schema.dns_settings = cls._args_public_ip_address_create.dns_settings
        _schema.extended_location = cls._args_public_ip_address_create.extended_location
        _schema.id = cls._args_public_ip_address_create.id
        _schema.idle_timeout_in_minutes = cls._args_public_ip_address_create.idle_timeout_in_minutes
        _schema.ip_address = cls._args_public_ip_address_create.ip_address
        _schema.ip_tags = cls._args_public_ip_address_create.ip_tags
        _schema.linked_public_ip_address = cls._args_public_ip_address_create.linked_public_ip_address
        _schema.location = cls._args_public_ip_address_create.location
        _schema.migration_phase = cls._args_public_ip_address_create.migration_phase
        _schema.nat_gateway = cls._args_public_ip_address_create.nat_gateway
        _schema.public_ip_address_version = cls._args_public_ip_address_create.public_ip_address_version
        _schema.public_ip_allocation_method = cls._args_public_ip_address_create.public_ip_allocation_method
        _schema.public_ip_prefix = cls._args_public_ip_address_create.public_ip_prefix
        _schema.service_public_ip_address = cls._args_public_ip_address_create.service_public_ip_address
        _schema.sku = cls._args_public_ip_address_create.sku
        _schema.tags = cls._args_public_ip_address_create.tags
        _schema.zones = cls._args_public_ip_address_create.zones

    _args_sub_resource_create = None

    @classmethod
    def _build_args_sub_resource_create(cls, _schema):
        if cls._args_sub_resource_create is not None:
            _schema.id = cls._args_sub_resource_create.id
            return

        cls._args_sub_resource_create = AAZObjectArg()

        sub_resource_create = cls._args_sub_resource_create
        sub_resource_create.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        _schema.id = cls._args_sub_resource_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.PrivateLinkServicesCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class PrivateLinkServicesCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/privateLinkServices/{serviceName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "serviceName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-03-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("extendedLocation", AAZObjectType)
            _builder.set_prop("location", AAZStrType, ".location")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            extended_location = _builder.get(".extendedLocation")
            if extended_location is not None:
                extended_location.set_prop("name", AAZStrType, ".edge_zone")
                extended_location.set_prop("type", AAZStrType, ".edge_zone_type")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("autoApproval", AAZObjectType)
                properties.set_prop("destinationIPAddress", AAZStrType, ".destination_ip_address")
                properties.set_prop("enableProxyProtocol", AAZBoolType, ".enable_proxy_protocol")
                properties.set_prop("fqdns", AAZListType, ".fqdns")
                properties.set_prop("ipConfigurations", AAZListType, ".ip_configurations")
                properties.set_prop("loadBalancerFrontendIpConfigurations", AAZListType, ".load_balancer_frontend_ip_configurations")
                properties.set_prop("visibility", AAZObjectType)

            auto_approval = _builder.get(".properties.autoApproval")
            if auto_approval is not None:
                auto_approval.set_prop("subscriptions", AAZListType, ".auto_approval")

            subscriptions = _builder.get(".properties.autoApproval.subscriptions")
            if subscriptions is not None:
                subscriptions.set_elements(AAZStrType, ".")

            fqdns = _builder.get(".properties.fqdns")
            if fqdns is not None:
                fqdns.set_elements(AAZStrType, ".")

            ip_configurations = _builder.get(".properties.ipConfigurations")
            if ip_configurations is not None:
                ip_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.ipConfigurations[].properties")
            if properties is not None:
                properties.set_prop("primary", AAZBoolType, ".primary")
                properties.set_prop("privateIPAddress", AAZStrType, ".private_ip_address")
                properties.set_prop("privateIPAddressVersion", AAZStrType, ".private_ip_address_version")
                properties.set_prop("privateIPAllocationMethod", AAZStrType, ".private_ip_allocation_method")
                properties.set_prop("subnet", AAZObjectType, ".subnet")

            subnet = _builder.get(".properties.ipConfigurations[].properties.subnet")
            if subnet is not None:
                subnet.set_prop("id", AAZStrType, ".id")
                subnet.set_prop("name", AAZStrType, ".name")
                subnet.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                subnet.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties")
            if properties is not None:
                properties.set_prop("addressPrefix", AAZStrType, ".address_prefix")
                properties.set_prop("addressPrefixes", AAZListType, ".address_prefixes")
                properties.set_prop("applicationGatewayIPConfigurations", AAZListType, ".application_gateway_ip_configurations")
                properties.set_prop("defaultOutboundAccess", AAZBoolType, ".default_outbound_access")
                properties.set_prop("delegations", AAZListType, ".delegations")
                properties.set_prop("ipAllocations", AAZListType, ".ip_allocations")
                _CreateHelper._build_schema_sub_resource_create(properties.set_prop("natGateway", AAZObjectType, ".nat_gateway"))
                properties.set_prop("networkSecurityGroup", AAZObjectType, ".network_security_group")
                properties.set_prop("privateEndpointNetworkPolicies", AAZStrType, ".private_endpoint_network_policies")
                properties.set_prop("privateLinkServiceNetworkPolicies", AAZStrType, ".private_link_service_network_policies")
                properties.set_prop("routeTable", AAZObjectType, ".route_table")
                properties.set_prop("serviceEndpointPolicies", AAZListType, ".service_endpoint_policies")
                properties.set_prop("serviceEndpoints", AAZListType, ".service_endpoints")
                properties.set_prop("sharingScope", AAZStrType, ".sharing_scope")

            address_prefixes = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.addressPrefixes")
            if address_prefixes is not None:
                address_prefixes.set_elements(AAZStrType, ".")

            application_gateway_ip_configurations = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.applicationGatewayIPConfigurations")
            if application_gateway_ip_configurations is not None:
                application_gateway_ip_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.applicationGatewayIPConfigurations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.applicationGatewayIPConfigurations[].properties")
            if properties is not None:
                _CreateHelper._build_schema_sub_resource_create(properties.set_prop("subnet", AAZObjectType, ".subnet"))

            delegations = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.delegations")
            if delegations is not None:
                delegations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.delegations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.delegations[].properties")
            if properties is not None:
                properties.set_prop("serviceName", AAZStrType, ".service_name")

            ip_allocations = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.ipAllocations")
            if ip_allocations is not None:
                _CreateHelper._build_schema_sub_resource_create(ip_allocations.set_elements(AAZObjectType, "."))

            network_security_group = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup")
            if network_security_group is not None:
                network_security_group.set_prop("id", AAZStrType, ".id")
                network_security_group.set_prop("location", AAZStrType, ".location")
                network_security_group.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                network_security_group.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties")
            if properties is not None:
                properties.set_prop("flushConnection", AAZBoolType, ".flush_connection")
                properties.set_prop("securityRules", AAZListType, ".security_rules")

            security_rules = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules")
            if security_rules is not None:
                security_rules.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties")
            if properties is not None:
                properties.set_prop("access", AAZStrType, ".access", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("destinationAddressPrefix", AAZStrType, ".destination_address_prefix")
                properties.set_prop("destinationAddressPrefixes", AAZListType, ".destination_address_prefixes")
                properties.set_prop("destinationApplicationSecurityGroups", AAZListType, ".destination_application_security_groups")
                properties.set_prop("destinationPortRange", AAZStrType, ".destination_port_range")
                properties.set_prop("destinationPortRanges", AAZListType, ".destination_port_ranges")
                properties.set_prop("direction", AAZStrType, ".direction", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("priority", AAZIntType, ".priority", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("protocol", AAZStrType, ".protocol", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("sourceAddressPrefix", AAZStrType, ".source_address_prefix")
                properties.set_prop("sourceAddressPrefixes", AAZListType, ".source_address_prefixes")
                properties.set_prop("sourceApplicationSecurityGroups", AAZListType, ".source_application_security_groups")
                properties.set_prop("sourcePortRange", AAZStrType, ".source_port_range")
                properties.set_prop("sourcePortRanges", AAZListType, ".source_port_ranges")

            destination_address_prefixes = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.destinationAddressPrefixes")
            if destination_address_prefixes is not None:
                destination_address_prefixes.set_elements(AAZStrType, ".")

            destination_application_security_groups = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.destinationApplicationSecurityGroups")
            if destination_application_security_groups is not None:
                _CreateHelper._build_schema_application_security_group_create(destination_application_security_groups.set_elements(AAZObjectType, "."))

            destination_port_ranges = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.destinationPortRanges")
            if destination_port_ranges is not None:
                destination_port_ranges.set_elements(AAZStrType, ".")

            source_address_prefixes = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.sourceAddressPrefixes")
            if source_address_prefixes is not None:
                source_address_prefixes.set_elements(AAZStrType, ".")

            source_application_security_groups = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.sourceApplicationSecurityGroups")
            if source_application_security_groups is not None:
                _CreateHelper._build_schema_application_security_group_create(source_application_security_groups.set_elements(AAZObjectType, "."))

            source_port_ranges = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.sourcePortRanges")
            if source_port_ranges is not None:
                source_port_ranges.set_elements(AAZStrType, ".")

            tags = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.networkSecurityGroup.tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            route_table = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.routeTable")
            if route_table is not None:
                route_table.set_prop("id", AAZStrType, ".id")
                route_table.set_prop("location", AAZStrType, ".location")
                route_table.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                route_table.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.routeTable.properties")
            if properties is not None:
                properties.set_prop("disableBgpRoutePropagation", AAZBoolType, ".disable_bgp_route_propagation")
                properties.set_prop("routes", AAZListType, ".routes")

            routes = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.routeTable.properties.routes")
            if routes is not None:
                routes.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.routeTable.properties.routes[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.routeTable.properties.routes[].properties")
            if properties is not None:
                properties.set_prop("addressPrefix", AAZStrType, ".address_prefix")
                properties.set_prop("nextHopIpAddress", AAZStrType, ".next_hop_ip_address")
                properties.set_prop("nextHopType", AAZStrType, ".next_hop_type", typ_kwargs={"flags": {"required": True}})

            tags = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.routeTable.tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            service_endpoint_policies = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies")
            if service_endpoint_policies is not None:
                service_endpoint_policies.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("location", AAZStrType, ".location")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties")
            if properties is not None:
                properties.set_prop("contextualServiceEndpointPolicies", AAZListType, ".contextual_service_endpoint_policies")
                properties.set_prop("serviceAlias", AAZStrType, ".service_alias")
                properties.set_prop("serviceEndpointPolicyDefinitions", AAZListType, ".service_endpoint_policy_definitions")

            contextual_service_endpoint_policies = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.contextualServiceEndpointPolicies")
            if contextual_service_endpoint_policies is not None:
                contextual_service_endpoint_policies.set_elements(AAZStrType, ".")

            service_endpoint_policy_definitions = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions")
            if service_endpoint_policy_definitions is not None:
                service_endpoint_policy_definitions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[].properties")
            if properties is not None:
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("service", AAZStrType, ".service")
                properties.set_prop("serviceResources", AAZListType, ".service_resources")

            service_resources = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[].properties.serviceResources")
            if service_resources is not None:
                service_resources.set_elements(AAZStrType, ".")

            tags = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            service_endpoints = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpoints")
            if service_endpoints is not None:
                service_endpoints.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpoints[]")
            if _elements is not None:
                _elements.set_prop("locations", AAZListType, ".locations")
                _CreateHelper._build_schema_sub_resource_create(_elements.set_prop("networkIdentifier", AAZObjectType, ".network_identifier"))
                _elements.set_prop("service", AAZStrType, ".service")

            locations = _builder.get(".properties.ipConfigurations[].properties.subnet.properties.serviceEndpoints[].locations")
            if locations is not None:
                locations.set_elements(AAZStrType, ".")

            load_balancer_frontend_ip_configurations = _builder.get(".properties.loadBalancerFrontendIpConfigurations")
            if load_balancer_frontend_ip_configurations is not None:
                load_balancer_frontend_ip_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("zones", AAZListType, ".zones")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties")
            if properties is not None:
                properties.set_prop("gatewayLoadBalancer", AAZObjectType, ".gateway_load_balancer")
                properties.set_prop("privateIPAddress", AAZStrType, ".private_ip_address")
                properties.set_prop("privateIPAddressVersion", AAZStrType, ".private_ip_address_version")
                properties.set_prop("privateIPAllocationMethod", AAZStrType, ".private_ip_allocation_method")
                properties.set_prop("publicIPPrefix", AAZObjectType, ".public_ip_prefix")
                properties.set_prop("subnet", AAZObjectType, ".subnet")

            gateway_load_balancer = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.gatewayLoadBalancer")
            if gateway_load_balancer is not None:
                gateway_load_balancer.set_prop("id", AAZStrType, ".id")

            public_ip_prefix = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.publicIPPrefix")
            if public_ip_prefix is not None:
                public_ip_prefix.set_prop("id", AAZStrType, ".id")

            subnet = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet")
            if subnet is not None:
                subnet.set_prop("id", AAZStrType, ".id")
                subnet.set_prop("name", AAZStrType, ".name")
                subnet.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                subnet.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties")
            if properties is not None:
                properties.set_prop("addressPrefix", AAZStrType, ".address_prefix")
                properties.set_prop("addressPrefixes", AAZListType, ".address_prefixes")
                properties.set_prop("applicationGatewayIPConfigurations", AAZListType, ".application_gateway_ip_configurations")
                properties.set_prop("defaultOutboundAccess", AAZBoolType, ".default_outbound_access")
                properties.set_prop("delegations", AAZListType, ".delegations")
                properties.set_prop("ipAllocations", AAZListType, ".ip_allocations")
                _CreateHelper._build_schema_sub_resource_create(properties.set_prop("natGateway", AAZObjectType, ".nat_gateway"))
                properties.set_prop("networkSecurityGroup", AAZObjectType, ".network_security_group")
                properties.set_prop("privateEndpointNetworkPolicies", AAZStrType, ".private_endpoint_network_policies")
                properties.set_prop("privateLinkServiceNetworkPolicies", AAZStrType, ".private_link_service_network_policies")
                properties.set_prop("routeTable", AAZObjectType, ".route_table")
                properties.set_prop("serviceEndpointPolicies", AAZListType, ".service_endpoint_policies")
                properties.set_prop("serviceEndpoints", AAZListType, ".service_endpoints")
                properties.set_prop("sharingScope", AAZStrType, ".sharing_scope")

            address_prefixes = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.addressPrefixes")
            if address_prefixes is not None:
                address_prefixes.set_elements(AAZStrType, ".")

            application_gateway_ip_configurations = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.applicationGatewayIPConfigurations")
            if application_gateway_ip_configurations is not None:
                application_gateway_ip_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.applicationGatewayIPConfigurations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.applicationGatewayIPConfigurations[].properties")
            if properties is not None:
                _CreateHelper._build_schema_sub_resource_create(properties.set_prop("subnet", AAZObjectType, ".subnet"))

            delegations = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.delegations")
            if delegations is not None:
                delegations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.delegations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.delegations[].properties")
            if properties is not None:
                properties.set_prop("serviceName", AAZStrType, ".service_name")

            ip_allocations = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.ipAllocations")
            if ip_allocations is not None:
                _CreateHelper._build_schema_sub_resource_create(ip_allocations.set_elements(AAZObjectType, "."))

            network_security_group = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup")
            if network_security_group is not None:
                network_security_group.set_prop("id", AAZStrType, ".id")
                network_security_group.set_prop("location", AAZStrType, ".location")
                network_security_group.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                network_security_group.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties")
            if properties is not None:
                properties.set_prop("flushConnection", AAZBoolType, ".flush_connection")
                properties.set_prop("securityRules", AAZListType, ".security_rules")

            security_rules = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules")
            if security_rules is not None:
                security_rules.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties")
            if properties is not None:
                properties.set_prop("access", AAZStrType, ".access", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("destinationAddressPrefix", AAZStrType, ".destination_address_prefix")
                properties.set_prop("destinationAddressPrefixes", AAZListType, ".destination_address_prefixes")
                properties.set_prop("destinationApplicationSecurityGroups", AAZListType, ".destination_application_security_groups")
                properties.set_prop("destinationPortRange", AAZStrType, ".destination_port_range")
                properties.set_prop("destinationPortRanges", AAZListType, ".destination_port_ranges")
                properties.set_prop("direction", AAZStrType, ".direction", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("priority", AAZIntType, ".priority", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("protocol", AAZStrType, ".protocol", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("sourceAddressPrefix", AAZStrType, ".source_address_prefix")
                properties.set_prop("sourceAddressPrefixes", AAZListType, ".source_address_prefixes")
                properties.set_prop("sourceApplicationSecurityGroups", AAZListType, ".source_application_security_groups")
                properties.set_prop("sourcePortRange", AAZStrType, ".source_port_range")
                properties.set_prop("sourcePortRanges", AAZListType, ".source_port_ranges")

            destination_address_prefixes = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.destinationAddressPrefixes")
            if destination_address_prefixes is not None:
                destination_address_prefixes.set_elements(AAZStrType, ".")

            destination_application_security_groups = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.destinationApplicationSecurityGroups")
            if destination_application_security_groups is not None:
                _CreateHelper._build_schema_application_security_group_create(destination_application_security_groups.set_elements(AAZObjectType, "."))

            destination_port_ranges = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.destinationPortRanges")
            if destination_port_ranges is not None:
                destination_port_ranges.set_elements(AAZStrType, ".")

            source_address_prefixes = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.sourceAddressPrefixes")
            if source_address_prefixes is not None:
                source_address_prefixes.set_elements(AAZStrType, ".")

            source_application_security_groups = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.sourceApplicationSecurityGroups")
            if source_application_security_groups is not None:
                _CreateHelper._build_schema_application_security_group_create(source_application_security_groups.set_elements(AAZObjectType, "."))

            source_port_ranges = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.properties.securityRules[].properties.sourcePortRanges")
            if source_port_ranges is not None:
                source_port_ranges.set_elements(AAZStrType, ".")

            tags = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.networkSecurityGroup.tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            route_table = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.routeTable")
            if route_table is not None:
                route_table.set_prop("id", AAZStrType, ".id")
                route_table.set_prop("location", AAZStrType, ".location")
                route_table.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                route_table.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.routeTable.properties")
            if properties is not None:
                properties.set_prop("disableBgpRoutePropagation", AAZBoolType, ".disable_bgp_route_propagation")
                properties.set_prop("routes", AAZListType, ".routes")

            routes = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.routeTable.properties.routes")
            if routes is not None:
                routes.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.routeTable.properties.routes[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.routeTable.properties.routes[].properties")
            if properties is not None:
                properties.set_prop("addressPrefix", AAZStrType, ".address_prefix")
                properties.set_prop("nextHopIpAddress", AAZStrType, ".next_hop_ip_address")
                properties.set_prop("nextHopType", AAZStrType, ".next_hop_type", typ_kwargs={"flags": {"required": True}})

            tags = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.routeTable.tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            service_endpoint_policies = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies")
            if service_endpoint_policies is not None:
                service_endpoint_policies.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("location", AAZStrType, ".location")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties")
            if properties is not None:
                properties.set_prop("contextualServiceEndpointPolicies", AAZListType, ".contextual_service_endpoint_policies")
                properties.set_prop("serviceAlias", AAZStrType, ".service_alias")
                properties.set_prop("serviceEndpointPolicyDefinitions", AAZListType, ".service_endpoint_policy_definitions")

            contextual_service_endpoint_policies = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.contextualServiceEndpointPolicies")
            if contextual_service_endpoint_policies is not None:
                contextual_service_endpoint_policies.set_elements(AAZStrType, ".")

            service_endpoint_policy_definitions = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions")
            if service_endpoint_policy_definitions is not None:
                service_endpoint_policy_definitions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[].properties")
            if properties is not None:
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("service", AAZStrType, ".service")
                properties.set_prop("serviceResources", AAZListType, ".service_resources")

            service_resources = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[].properties.serviceResources")
            if service_resources is not None:
                service_resources.set_elements(AAZStrType, ".")

            tags = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpointPolicies[].tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            service_endpoints = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpoints")
            if service_endpoints is not None:
                service_endpoints.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpoints[]")
            if _elements is not None:
                _elements.set_prop("locations", AAZListType, ".locations")
                _CreateHelper._build_schema_sub_resource_create(_elements.set_prop("networkIdentifier", AAZObjectType, ".network_identifier"))
                _elements.set_prop("service", AAZStrType, ".service")

            locations = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].properties.subnet.properties.serviceEndpoints[].locations")
            if locations is not None:
                locations.set_elements(AAZStrType, ".")

            zones = _builder.get(".properties.loadBalancerFrontendIpConfigurations[].zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            visibility = _builder.get(".properties.visibility")
            if visibility is not None:
                visibility.set_prop("subscriptions", AAZListType, ".visibility")

            subscriptions = _builder.get(".properties.visibility.subscriptions")
            if subscriptions is not None:
                subscriptions.set_elements(AAZStrType, ".")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_private_link_service_read(cls._schema_on_200_201)

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_application_security_group_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")
        _builder.set_prop("location", AAZStrType, ".location")
        _builder.set_prop("tags", AAZDictType, ".tags")

        tags = _builder.get(".tags")
        if tags is not None:
            tags.set_elements(AAZStrType, ".")

    @classmethod
    def _build_schema_extended_location_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("name", AAZStrType, ".name")
        _builder.set_prop("type", AAZStrType, ".type")

    @classmethod
    def _build_schema_public_ip_address_create(cls, _builder):
        if _builder is None:
            return
        cls._build_schema_extended_location_create(_builder.set_prop("extendedLocation", AAZObjectType, ".extended_location"))
        _builder.set_prop("id", AAZStrType, ".id")
        _builder.set_prop("location", AAZStrType, ".location")
        _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
        _builder.set_prop("sku", AAZObjectType, ".sku")
        _builder.set_prop("tags", AAZDictType, ".tags")
        _builder.set_prop("zones", AAZListType, ".zones")

        properties = _builder.get(".properties")
        if properties is not None:
            properties.set_prop("ddosSettings", AAZObjectType, ".ddos_settings")
            properties.set_prop("deleteOption", AAZStrType, ".delete_option")
            properties.set_prop("dnsSettings", AAZObjectType, ".dns_settings")
            properties.set_prop("idleTimeoutInMinutes", AAZIntType, ".idle_timeout_in_minutes")
            properties.set_prop("ipAddress", AAZStrType, ".ip_address")
            properties.set_prop("ipTags", AAZListType, ".ip_tags")
            cls._build_schema_public_ip_address_create(properties.set_prop("linkedPublicIPAddress", AAZObjectType, ".linked_public_ip_address"))
            properties.set_prop("migrationPhase", AAZStrType, ".migration_phase")
            properties.set_prop("natGateway", AAZObjectType, ".nat_gateway")
            properties.set_prop("publicIPAddressVersion", AAZStrType, ".public_ip_address_version")
            properties.set_prop("publicIPAllocationMethod", AAZStrType, ".public_ip_allocation_method")
            cls._build_schema_sub_resource_create(properties.set_prop("publicIPPrefix", AAZObjectType, ".public_ip_prefix"))
            cls._build_schema_public_ip_address_create(properties.set_prop("servicePublicIPAddress", AAZObjectType, ".service_public_ip_address"))

        ddos_settings = _builder.get(".properties.ddosSettings")
        if ddos_settings is not None:
            cls._build_schema_sub_resource_create(ddos_settings.set_prop("ddosProtectionPlan", AAZObjectType, ".ddos_protection_plan"))
            ddos_settings.set_prop("protectionMode", AAZStrType, ".protection_mode")

        dns_settings = _builder.get(".properties.dnsSettings")
        if dns_settings is not None:
            dns_settings.set_prop("domainNameLabel", AAZStrType, ".domain_name_label")
            dns_settings.set_prop("domainNameLabelScope", AAZStrType, ".domain_name_label_scope")
            dns_settings.set_prop("fqdn", AAZStrType, ".fqdn")
            dns_settings.set_prop("reverseFqdn", AAZStrType, ".reverse_fqdn")

        ip_tags = _builder.get(".properties.ipTags")
        if ip_tags is not None:
            ip_tags.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.ipTags[]")
        if _elements is not None:
            _elements.set_prop("ipTagType", AAZStrType, ".ip_tag_type")
            _elements.set_prop("tag", AAZStrType, ".tag")

        nat_gateway = _builder.get(".properties.natGateway")
        if nat_gateway is not None:
            nat_gateway.set_prop("id", AAZStrType, ".id")
            nat_gateway.set_prop("location", AAZStrType, ".location")
            nat_gateway.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            nat_gateway.set_prop("sku", AAZObjectType, ".sku")
            nat_gateway.set_prop("tags", AAZDictType, ".tags")
            nat_gateway.set_prop("zones", AAZListType, ".zones")

        properties = _builder.get(".properties.natGateway.properties")
        if properties is not None:
            properties.set_prop("idleTimeoutInMinutes", AAZIntType, ".idle_timeout_in_minutes")
            properties.set_prop("publicIpAddresses", AAZListType, ".public_ip_addresses")
            properties.set_prop("publicIpPrefixes", AAZListType, ".public_ip_prefixes")

        public_ip_addresses = _builder.get(".properties.natGateway.properties.publicIpAddresses")
        if public_ip_addresses is not None:
            cls._build_schema_sub_resource_create(public_ip_addresses.set_elements(AAZObjectType, "."))

        public_ip_prefixes = _builder.get(".properties.natGateway.properties.publicIpPrefixes")
        if public_ip_prefixes is not None:
            cls._build_schema_sub_resource_create(public_ip_prefixes.set_elements(AAZObjectType, "."))

        sku = _builder.get(".properties.natGateway.sku")
        if sku is not None:
            sku.set_prop("name", AAZStrType, ".name")

        tags = _builder.get(".properties.natGateway.tags")
        if tags is not None:
            tags.set_elements(AAZStrType, ".")

        zones = _builder.get(".properties.natGateway.zones")
        if zones is not None:
            zones.set_elements(AAZStrType, ".")

        sku = _builder.get(".sku")
        if sku is not None:
            sku.set_prop("name", AAZStrType, ".name")
            sku.set_prop("tier", AAZStrType, ".tier")

        tags = _builder.get(".tags")
        if tags is not None:
            tags.set_elements(AAZStrType, ".")

        zones = _builder.get(".zones")
        if zones is not None:
            zones.set_elements(AAZStrType, ".")

    @classmethod
    def _build_schema_sub_resource_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_application_security_group_read = None

    @classmethod
    def _build_schema_application_security_group_read(cls, _schema):
        if cls._schema_application_security_group_read is not None:
            _schema.etag = cls._schema_application_security_group_read.etag
            _schema.id = cls._schema_application_security_group_read.id
            _schema.location = cls._schema_application_security_group_read.location
            _schema.name = cls._schema_application_security_group_read.name
            _schema.properties = cls._schema_application_security_group_read.properties
            _schema.tags = cls._schema_application_security_group_read.tags
            _schema.type = cls._schema_application_security_group_read.type
            return

        cls._schema_application_security_group_read = _schema_application_security_group_read = AAZObjectType()

        application_security_group_read = _schema_application_security_group_read
        application_security_group_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        application_security_group_read.id = AAZStrType()
        application_security_group_read.location = AAZStrType()
        application_security_group_read.name = AAZStrType(
            flags={"read_only": True},
        )
        application_security_group_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        application_security_group_read.tags = AAZDictType()
        application_security_group_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_application_security_group_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )

        tags = _schema_application_security_group_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_application_security_group_read.etag
        _schema.id = cls._schema_application_security_group_read.id
        _schema.location = cls._schema_application_security_group_read.location
        _schema.name = cls._schema_application_security_group_read.name
        _schema.properties = cls._schema_application_security_group_read.properties
        _schema.tags = cls._schema_application_security_group_read.tags
        _schema.type = cls._schema_application_security_group_read.type

    _schema_extended_location_read = None

    @classmethod
    def _build_schema_extended_location_read(cls, _schema):
        if cls._schema_extended_location_read is not None:
            _schema.name = cls._schema_extended_location_read.name
            _schema.type = cls._schema_extended_location_read.type
            return

        cls._schema_extended_location_read = _schema_extended_location_read = AAZObjectType()

        extended_location_read = _schema_extended_location_read
        extended_location_read.name = AAZStrType()
        extended_location_read.type = AAZStrType()

        _schema.name = cls._schema_extended_location_read.name
        _schema.type = cls._schema_extended_location_read.type

    _schema_frontend_ip_configuration_read = None

    @classmethod
    def _build_schema_frontend_ip_configuration_read(cls, _schema):
        if cls._schema_frontend_ip_configuration_read is not None:
            _schema.etag = cls._schema_frontend_ip_configuration_read.etag
            _schema.id = cls._schema_frontend_ip_configuration_read.id
            _schema.name = cls._schema_frontend_ip_configuration_read.name
            _schema.properties = cls._schema_frontend_ip_configuration_read.properties
            _schema.type = cls._schema_frontend_ip_configuration_read.type
            _schema.zones = cls._schema_frontend_ip_configuration_read.zones
            return

        cls._schema_frontend_ip_configuration_read = _schema_frontend_ip_configuration_read = AAZObjectType()

        frontend_ip_configuration_read = _schema_frontend_ip_configuration_read
        frontend_ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        frontend_ip_configuration_read.id = AAZStrType()
        frontend_ip_configuration_read.name = AAZStrType()
        frontend_ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        frontend_ip_configuration_read.type = AAZStrType(
            flags={"read_only": True},
        )
        frontend_ip_configuration_read.zones = AAZListType()

        properties = _schema_frontend_ip_configuration_read.properties
        properties.gateway_load_balancer = AAZObjectType(
            serialized_name="gatewayLoadBalancer",
        )
        cls._build_schema_sub_resource_read(properties.gateway_load_balancer)
        properties.inbound_nat_pools = AAZListType(
            serialized_name="inboundNatPools",
            flags={"read_only": True},
        )
        properties.inbound_nat_rules = AAZListType(
            serialized_name="inboundNatRules",
            flags={"read_only": True},
        )
        properties.load_balancing_rules = AAZListType(
            serialized_name="loadBalancingRules",
            flags={"read_only": True},
        )
        properties.outbound_rules = AAZListType(
            serialized_name="outboundRules",
            flags={"read_only": True},
        )
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.public_ip_prefix = AAZObjectType(
            serialized_name="publicIPPrefix",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_prefix)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        inbound_nat_pools = _schema_frontend_ip_configuration_read.properties.inbound_nat_pools
        inbound_nat_pools.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_pools.Element)

        inbound_nat_rules = _schema_frontend_ip_configuration_read.properties.inbound_nat_rules
        inbound_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_rules.Element)

        load_balancing_rules = _schema_frontend_ip_configuration_read.properties.load_balancing_rules
        load_balancing_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancing_rules.Element)

        outbound_rules = _schema_frontend_ip_configuration_read.properties.outbound_rules
        outbound_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(outbound_rules.Element)

        zones = _schema_frontend_ip_configuration_read.zones
        zones.Element = AAZStrType()

        _schema.etag = cls._schema_frontend_ip_configuration_read.etag
        _schema.id = cls._schema_frontend_ip_configuration_read.id
        _schema.name = cls._schema_frontend_ip_configuration_read.name
        _schema.properties = cls._schema_frontend_ip_configuration_read.properties
        _schema.type = cls._schema_frontend_ip_configuration_read.type
        _schema.zones = cls._schema_frontend_ip_configuration_read.zones

    _schema_ip_configuration_read = None

    @classmethod
    def _build_schema_ip_configuration_read(cls, _schema):
        if cls._schema_ip_configuration_read is not None:
            _schema.etag = cls._schema_ip_configuration_read.etag
            _schema.id = cls._schema_ip_configuration_read.id
            _schema.name = cls._schema_ip_configuration_read.name
            _schema.properties = cls._schema_ip_configuration_read.properties
            return

        cls._schema_ip_configuration_read = _schema_ip_configuration_read = AAZObjectType(
            flags={"read_only": True}
        )

        ip_configuration_read = _schema_ip_configuration_read
        ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        ip_configuration_read.id = AAZStrType()
        ip_configuration_read.name = AAZStrType()
        ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_ip_configuration_read.properties
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        _schema.etag = cls._schema_ip_configuration_read.etag
        _schema.id = cls._schema_ip_configuration_read.id
        _schema.name = cls._schema_ip_configuration_read.name
        _schema.properties = cls._schema_ip_configuration_read.properties

    _schema_network_interface_ip_configuration_read = None

    @classmethod
    def _build_schema_network_interface_ip_configuration_read(cls, _schema):
        if cls._schema_network_interface_ip_configuration_read is not None:
            _schema.etag = cls._schema_network_interface_ip_configuration_read.etag
            _schema.id = cls._schema_network_interface_ip_configuration_read.id
            _schema.name = cls._schema_network_interface_ip_configuration_read.name
            _schema.properties = cls._schema_network_interface_ip_configuration_read.properties
            _schema.type = cls._schema_network_interface_ip_configuration_read.type
            return

        cls._schema_network_interface_ip_configuration_read = _schema_network_interface_ip_configuration_read = AAZObjectType()

        network_interface_ip_configuration_read = _schema_network_interface_ip_configuration_read
        network_interface_ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_ip_configuration_read.id = AAZStrType()
        network_interface_ip_configuration_read.name = AAZStrType()
        network_interface_ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_ip_configuration_read.type = AAZStrType()

        properties = _schema_network_interface_ip_configuration_read.properties
        properties.application_gateway_backend_address_pools = AAZListType(
            serialized_name="applicationGatewayBackendAddressPools",
        )
        properties.application_security_groups = AAZListType(
            serialized_name="applicationSecurityGroups",
        )
        properties.gateway_load_balancer = AAZObjectType(
            serialized_name="gatewayLoadBalancer",
        )
        cls._build_schema_sub_resource_read(properties.gateway_load_balancer)
        properties.load_balancer_backend_address_pools = AAZListType(
            serialized_name="loadBalancerBackendAddressPools",
        )
        properties.load_balancer_inbound_nat_rules = AAZListType(
            serialized_name="loadBalancerInboundNatRules",
        )
        properties.primary = AAZBoolType()
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_prefix_length = AAZIntType(
            serialized_name="privateIPAddressPrefixLength",
            nullable=True,
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.private_link_connection_properties = AAZObjectType(
            serialized_name="privateLinkConnectionProperties",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)
        properties.virtual_network_taps = AAZListType(
            serialized_name="virtualNetworkTaps",
        )

        application_gateway_backend_address_pools = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools
        application_gateway_backend_address_pools.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties
        properties.backend_addresses = AAZListType(
            serialized_name="backendAddresses",
        )
        properties.backend_ip_configurations = AAZListType(
            serialized_name="backendIPConfigurations",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        backend_addresses = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties.backend_addresses
        backend_addresses.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties.backend_addresses.Element
        _element.fqdn = AAZStrType()
        _element.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )

        backend_ip_configurations = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties.backend_ip_configurations
        backend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(backend_ip_configurations.Element)

        application_security_groups = _schema_network_interface_ip_configuration_read.properties.application_security_groups
        application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(application_security_groups.Element)

        load_balancer_backend_address_pools = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools
        load_balancer_backend_address_pools.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties
        properties.backend_ip_configurations = AAZListType(
            serialized_name="backendIPConfigurations",
            flags={"read_only": True},
        )
        properties.drain_period_in_seconds = AAZIntType(
            serialized_name="drainPeriodInSeconds",
        )
        properties.inbound_nat_rules = AAZListType(
            serialized_name="inboundNatRules",
            flags={"read_only": True},
        )
        properties.load_balancer_backend_addresses = AAZListType(
            serialized_name="loadBalancerBackendAddresses",
        )
        properties.load_balancing_rules = AAZListType(
            serialized_name="loadBalancingRules",
            flags={"read_only": True},
        )
        properties.location = AAZStrType()
        properties.outbound_rule = AAZObjectType(
            serialized_name="outboundRule",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.outbound_rule)
        properties.outbound_rules = AAZListType(
            serialized_name="outboundRules",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.sync_mode = AAZStrType(
            serialized_name="syncMode",
        )
        properties.tunnel_interfaces = AAZListType(
            serialized_name="tunnelInterfaces",
        )
        properties.virtual_network = AAZObjectType(
            serialized_name="virtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.virtual_network)

        backend_ip_configurations = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.backend_ip_configurations
        backend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(backend_ip_configurations.Element)

        inbound_nat_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.inbound_nat_rules
        inbound_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_rules.Element)

        load_balancer_backend_addresses = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses
        load_balancer_backend_addresses.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties
        properties.admin_state = AAZStrType(
            serialized_name="adminState",
        )
        properties.inbound_nat_rules_port_mapping = AAZListType(
            serialized_name="inboundNatRulesPortMapping",
            flags={"read_only": True},
        )
        properties.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )
        properties.load_balancer_frontend_ip_configuration = AAZObjectType(
            serialized_name="loadBalancerFrontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.load_balancer_frontend_ip_configuration)
        properties.network_interface_ip_configuration = AAZObjectType(
            serialized_name="networkInterfaceIPConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.network_interface_ip_configuration)
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)
        properties.virtual_network = AAZObjectType(
            serialized_name="virtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.virtual_network)

        inbound_nat_rules_port_mapping = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties.inbound_nat_rules_port_mapping
        inbound_nat_rules_port_mapping.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties.inbound_nat_rules_port_mapping.Element
        _element.backend_port = AAZIntType(
            serialized_name="backendPort",
        )
        _element.frontend_port = AAZIntType(
            serialized_name="frontendPort",
        )
        _element.inbound_nat_rule_name = AAZStrType(
            serialized_name="inboundNatRuleName",
        )

        load_balancing_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancing_rules
        load_balancing_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancing_rules.Element)

        outbound_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.outbound_rules
        outbound_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(outbound_rules.Element)

        tunnel_interfaces = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.tunnel_interfaces
        tunnel_interfaces.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.tunnel_interfaces.Element
        _element.identifier = AAZIntType()
        _element.port = AAZIntType()
        _element.protocol = AAZStrType()
        _element.type = AAZStrType()

        load_balancer_inbound_nat_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules
        load_balancer_inbound_nat_rules.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules.Element.properties
        properties.backend_address_pool = AAZObjectType(
            serialized_name="backendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.backend_address_pool)
        properties.backend_ip_configuration = AAZObjectType(
            serialized_name="backendIPConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_network_interface_ip_configuration_read(properties.backend_ip_configuration)
        properties.backend_port = AAZIntType(
            serialized_name="backendPort",
        )
        properties.enable_floating_ip = AAZBoolType(
            serialized_name="enableFloatingIP",
        )
        properties.enable_tcp_reset = AAZBoolType(
            serialized_name="enableTcpReset",
        )
        properties.frontend_ip_configuration = AAZObjectType(
            serialized_name="frontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.frontend_ip_configuration)
        properties.frontend_port = AAZIntType(
            serialized_name="frontendPort",
        )
        properties.frontend_port_range_end = AAZIntType(
            serialized_name="frontendPortRangeEnd",
        )
        properties.frontend_port_range_start = AAZIntType(
            serialized_name="frontendPortRangeStart",
        )
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        private_link_connection_properties = _schema_network_interface_ip_configuration_read.properties.private_link_connection_properties
        private_link_connection_properties.fqdns = AAZListType(
            flags={"read_only": True},
        )
        private_link_connection_properties.group_id = AAZStrType(
            serialized_name="groupId",
            flags={"read_only": True},
        )
        private_link_connection_properties.required_member_name = AAZStrType(
            serialized_name="requiredMemberName",
            flags={"read_only": True},
        )

        fqdns = _schema_network_interface_ip_configuration_read.properties.private_link_connection_properties.fqdns
        fqdns.Element = AAZStrType()

        virtual_network_taps = _schema_network_interface_ip_configuration_read.properties.virtual_network_taps
        virtual_network_taps.Element = AAZObjectType()
        cls._build_schema_virtual_network_tap_read(virtual_network_taps.Element)

        _schema.etag = cls._schema_network_interface_ip_configuration_read.etag
        _schema.id = cls._schema_network_interface_ip_configuration_read.id
        _schema.name = cls._schema_network_interface_ip_configuration_read.name
        _schema.properties = cls._schema_network_interface_ip_configuration_read.properties
        _schema.type = cls._schema_network_interface_ip_configuration_read.type

    _schema_network_interface_tap_configuration_read = None

    @classmethod
    def _build_schema_network_interface_tap_configuration_read(cls, _schema):
        if cls._schema_network_interface_tap_configuration_read is not None:
            _schema.etag = cls._schema_network_interface_tap_configuration_read.etag
            _schema.id = cls._schema_network_interface_tap_configuration_read.id
            _schema.name = cls._schema_network_interface_tap_configuration_read.name
            _schema.properties = cls._schema_network_interface_tap_configuration_read.properties
            _schema.type = cls._schema_network_interface_tap_configuration_read.type
            return

        cls._schema_network_interface_tap_configuration_read = _schema_network_interface_tap_configuration_read = AAZObjectType()

        network_interface_tap_configuration_read = _schema_network_interface_tap_configuration_read
        network_interface_tap_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_tap_configuration_read.id = AAZStrType()
        network_interface_tap_configuration_read.name = AAZStrType()
        network_interface_tap_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_tap_configuration_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_tap_configuration_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.virtual_network_tap = AAZObjectType(
            serialized_name="virtualNetworkTap",
        )
        cls._build_schema_virtual_network_tap_read(properties.virtual_network_tap)

        _schema.etag = cls._schema_network_interface_tap_configuration_read.etag
        _schema.id = cls._schema_network_interface_tap_configuration_read.id
        _schema.name = cls._schema_network_interface_tap_configuration_read.name
        _schema.properties = cls._schema_network_interface_tap_configuration_read.properties
        _schema.type = cls._schema_network_interface_tap_configuration_read.type

    _schema_network_interface_read = None

    @classmethod
    def _build_schema_network_interface_read(cls, _schema):
        if cls._schema_network_interface_read is not None:
            _schema.etag = cls._schema_network_interface_read.etag
            _schema.extended_location = cls._schema_network_interface_read.extended_location
            _schema.id = cls._schema_network_interface_read.id
            _schema.location = cls._schema_network_interface_read.location
            _schema.name = cls._schema_network_interface_read.name
            _schema.properties = cls._schema_network_interface_read.properties
            _schema.tags = cls._schema_network_interface_read.tags
            _schema.type = cls._schema_network_interface_read.type
            return

        cls._schema_network_interface_read = _schema_network_interface_read = AAZObjectType()

        network_interface_read = _schema_network_interface_read
        network_interface_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(network_interface_read.extended_location)
        network_interface_read.id = AAZStrType()
        network_interface_read.location = AAZStrType()
        network_interface_read.name = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_read.tags = AAZDictType()
        network_interface_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties
        properties.auxiliary_mode = AAZStrType(
            serialized_name="auxiliaryMode",
        )
        properties.auxiliary_sku = AAZStrType(
            serialized_name="auxiliarySku",
        )
        properties.disable_tcp_state_tracking = AAZBoolType(
            serialized_name="disableTcpStateTracking",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.dscp_configuration = AAZObjectType(
            serialized_name="dscpConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.dscp_configuration)
        properties.enable_accelerated_networking = AAZBoolType(
            serialized_name="enableAcceleratedNetworking",
        )
        properties.enable_ip_forwarding = AAZBoolType(
            serialized_name="enableIPForwarding",
        )
        properties.hosted_workloads = AAZListType(
            serialized_name="hostedWorkloads",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.mac_address = AAZStrType(
            serialized_name="macAddress",
            flags={"read_only": True},
        )
        properties.migration_phase = AAZStrType(
            serialized_name="migrationPhase",
        )
        properties.network_security_group = AAZObjectType(
            serialized_name="networkSecurityGroup",
        )
        cls._build_schema_network_security_group_read(properties.network_security_group)
        properties.nic_type = AAZStrType(
            serialized_name="nicType",
        )
        properties.primary = AAZBoolType(
            flags={"read_only": True},
        )
        properties.private_endpoint = AAZObjectType(
            serialized_name="privateEndpoint",
            flags={"read_only": True},
        )
        cls._build_schema_private_endpoint_read(properties.private_endpoint)
        properties.private_link_service = AAZObjectType(
            serialized_name="privateLinkService",
        )
        cls._build_schema_private_link_service_read(properties.private_link_service)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.tap_configurations = AAZListType(
            serialized_name="tapConfigurations",
            flags={"read_only": True},
        )
        properties.virtual_machine = AAZObjectType(
            serialized_name="virtualMachine",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.virtual_machine)
        properties.vnet_encryption_supported = AAZBoolType(
            serialized_name="vnetEncryptionSupported",
            flags={"read_only": True},
        )
        properties.workload_type = AAZStrType(
            serialized_name="workloadType",
        )

        dns_settings = _schema_network_interface_read.properties.dns_settings
        dns_settings.applied_dns_servers = AAZListType(
            serialized_name="appliedDnsServers",
            flags={"read_only": True},
        )
        dns_settings.dns_servers = AAZListType(
            serialized_name="dnsServers",
        )
        dns_settings.internal_dns_name_label = AAZStrType(
            serialized_name="internalDnsNameLabel",
        )
        dns_settings.internal_domain_name_suffix = AAZStrType(
            serialized_name="internalDomainNameSuffix",
            flags={"read_only": True},
        )
        dns_settings.internal_fqdn = AAZStrType(
            serialized_name="internalFqdn",
            flags={"read_only": True},
        )

        applied_dns_servers = _schema_network_interface_read.properties.dns_settings.applied_dns_servers
        applied_dns_servers.Element = AAZStrType()

        dns_servers = _schema_network_interface_read.properties.dns_settings.dns_servers
        dns_servers.Element = AAZStrType()

        hosted_workloads = _schema_network_interface_read.properties.hosted_workloads
        hosted_workloads.Element = AAZStrType()

        ip_configurations = _schema_network_interface_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(ip_configurations.Element)

        tap_configurations = _schema_network_interface_read.properties.tap_configurations
        tap_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_tap_configuration_read(tap_configurations.Element)

        tags = _schema_network_interface_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_network_interface_read.etag
        _schema.extended_location = cls._schema_network_interface_read.extended_location
        _schema.id = cls._schema_network_interface_read.id
        _schema.location = cls._schema_network_interface_read.location
        _schema.name = cls._schema_network_interface_read.name
        _schema.properties = cls._schema_network_interface_read.properties
        _schema.tags = cls._schema_network_interface_read.tags
        _schema.type = cls._schema_network_interface_read.type

    _schema_network_security_group_read = None

    @classmethod
    def _build_schema_network_security_group_read(cls, _schema):
        if cls._schema_network_security_group_read is not None:
            _schema.etag = cls._schema_network_security_group_read.etag
            _schema.id = cls._schema_network_security_group_read.id
            _schema.location = cls._schema_network_security_group_read.location
            _schema.name = cls._schema_network_security_group_read.name
            _schema.properties = cls._schema_network_security_group_read.properties
            _schema.tags = cls._schema_network_security_group_read.tags
            _schema.type = cls._schema_network_security_group_read.type
            return

        cls._schema_network_security_group_read = _schema_network_security_group_read = AAZObjectType()

        network_security_group_read = _schema_network_security_group_read
        network_security_group_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_security_group_read.id = AAZStrType()
        network_security_group_read.location = AAZStrType()
        network_security_group_read.name = AAZStrType(
            flags={"read_only": True},
        )
        network_security_group_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_security_group_read.tags = AAZDictType()
        network_security_group_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_security_group_read.properties
        properties.default_security_rules = AAZListType(
            serialized_name="defaultSecurityRules",
            flags={"read_only": True},
        )
        properties.flow_logs = AAZListType(
            serialized_name="flowLogs",
            flags={"read_only": True},
        )
        properties.flush_connection = AAZBoolType(
            serialized_name="flushConnection",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.security_rules = AAZListType(
            serialized_name="securityRules",
        )
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        default_security_rules = _schema_network_security_group_read.properties.default_security_rules
        default_security_rules.Element = AAZObjectType()
        cls._build_schema_security_rule_read(default_security_rules.Element)

        flow_logs = _schema_network_security_group_read.properties.flow_logs
        flow_logs.Element = AAZObjectType()

        _element = _schema_network_security_group_read.properties.flow_logs.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.identity = AAZIdentityObjectType()
        _element.location = AAZStrType()
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        identity = _schema_network_security_group_read.properties.flow_logs.Element.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType()
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_network_security_group_read.properties.flow_logs.Element.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_network_security_group_read.properties.flow_logs.Element.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_network_security_group_read.properties.flow_logs.Element.properties
        properties.enabled = AAZBoolType()
        properties.enabled_filtering_criteria = AAZStrType(
            serialized_name="enabledFilteringCriteria",
        )
        properties.flow_analytics_configuration = AAZObjectType(
            serialized_name="flowAnalyticsConfiguration",
        )
        properties.format = AAZObjectType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.retention_policy = AAZObjectType(
            serialized_name="retentionPolicy",
        )
        properties.storage_id = AAZStrType(
            serialized_name="storageId",
            flags={"required": True},
        )
        properties.target_resource_guid = AAZStrType(
            serialized_name="targetResourceGuid",
            flags={"read_only": True},
        )
        properties.target_resource_id = AAZStrType(
            serialized_name="targetResourceId",
            flags={"required": True},
        )

        flow_analytics_configuration = _schema_network_security_group_read.properties.flow_logs.Element.properties.flow_analytics_configuration
        flow_analytics_configuration.network_watcher_flow_analytics_configuration = AAZObjectType(
            serialized_name="networkWatcherFlowAnalyticsConfiguration",
        )

        network_watcher_flow_analytics_configuration = _schema_network_security_group_read.properties.flow_logs.Element.properties.flow_analytics_configuration.network_watcher_flow_analytics_configuration
        network_watcher_flow_analytics_configuration.enabled = AAZBoolType()
        network_watcher_flow_analytics_configuration.traffic_analytics_interval = AAZIntType(
            serialized_name="trafficAnalyticsInterval",
        )
        network_watcher_flow_analytics_configuration.workspace_id = AAZStrType(
            serialized_name="workspaceId",
        )
        network_watcher_flow_analytics_configuration.workspace_region = AAZStrType(
            serialized_name="workspaceRegion",
        )
        network_watcher_flow_analytics_configuration.workspace_resource_id = AAZStrType(
            serialized_name="workspaceResourceId",
        )

        format = _schema_network_security_group_read.properties.flow_logs.Element.properties.format
        format.type = AAZStrType()
        format.version = AAZIntType()

        retention_policy = _schema_network_security_group_read.properties.flow_logs.Element.properties.retention_policy
        retention_policy.days = AAZIntType()
        retention_policy.enabled = AAZBoolType()

        tags = _schema_network_security_group_read.properties.flow_logs.Element.tags
        tags.Element = AAZStrType()

        network_interfaces = _schema_network_security_group_read.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        security_rules = _schema_network_security_group_read.properties.security_rules
        security_rules.Element = AAZObjectType()
        cls._build_schema_security_rule_read(security_rules.Element)

        subnets = _schema_network_security_group_read.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_network_security_group_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_network_security_group_read.etag
        _schema.id = cls._schema_network_security_group_read.id
        _schema.location = cls._schema_network_security_group_read.location
        _schema.name = cls._schema_network_security_group_read.name
        _schema.properties = cls._schema_network_security_group_read.properties
        _schema.tags = cls._schema_network_security_group_read.tags
        _schema.type = cls._schema_network_security_group_read.type

    _schema_private_endpoint_read = None

    @classmethod
    def _build_schema_private_endpoint_read(cls, _schema):
        if cls._schema_private_endpoint_read is not None:
            _schema.etag = cls._schema_private_endpoint_read.etag
            _schema.extended_location = cls._schema_private_endpoint_read.extended_location
            _schema.id = cls._schema_private_endpoint_read.id
            _schema.location = cls._schema_private_endpoint_read.location
            _schema.name = cls._schema_private_endpoint_read.name
            _schema.properties = cls._schema_private_endpoint_read.properties
            _schema.tags = cls._schema_private_endpoint_read.tags
            _schema.type = cls._schema_private_endpoint_read.type
            return

        cls._schema_private_endpoint_read = _schema_private_endpoint_read = AAZObjectType(
            flags={"read_only": True}
        )

        private_endpoint_read = _schema_private_endpoint_read
        private_endpoint_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_endpoint_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(private_endpoint_read.extended_location)
        private_endpoint_read.id = AAZStrType()
        private_endpoint_read.location = AAZStrType()
        private_endpoint_read.name = AAZStrType(
            flags={"read_only": True},
        )
        private_endpoint_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_endpoint_read.tags = AAZDictType()
        private_endpoint_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_endpoint_read.properties
        properties.application_security_groups = AAZListType(
            serialized_name="applicationSecurityGroups",
        )
        properties.custom_dns_configs = AAZListType(
            serialized_name="customDnsConfigs",
        )
        properties.custom_network_interface_name = AAZStrType(
            serialized_name="customNetworkInterfaceName",
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.manual_private_link_service_connections = AAZListType(
            serialized_name="manualPrivateLinkServiceConnections",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.private_link_service_connections = AAZListType(
            serialized_name="privateLinkServiceConnections",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        application_security_groups = _schema_private_endpoint_read.properties.application_security_groups
        application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(application_security_groups.Element)

        custom_dns_configs = _schema_private_endpoint_read.properties.custom_dns_configs
        custom_dns_configs.Element = AAZObjectType()

        _element = _schema_private_endpoint_read.properties.custom_dns_configs.Element
        _element.fqdn = AAZStrType()
        _element.ip_addresses = AAZListType(
            serialized_name="ipAddresses",
        )

        ip_addresses = _schema_private_endpoint_read.properties.custom_dns_configs.Element.ip_addresses
        ip_addresses.Element = AAZStrType()

        ip_configurations = _schema_private_endpoint_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_private_endpoint_read.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_endpoint_read.properties.ip_configurations.Element.properties
        properties.group_id = AAZStrType(
            serialized_name="groupId",
        )
        properties.member_name = AAZStrType(
            serialized_name="memberName",
        )
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )

        manual_private_link_service_connections = _schema_private_endpoint_read.properties.manual_private_link_service_connections
        manual_private_link_service_connections.Element = AAZObjectType()
        cls._build_schema_private_link_service_connection_read(manual_private_link_service_connections.Element)

        network_interfaces = _schema_private_endpoint_read.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        private_link_service_connections = _schema_private_endpoint_read.properties.private_link_service_connections
        private_link_service_connections.Element = AAZObjectType()
        cls._build_schema_private_link_service_connection_read(private_link_service_connections.Element)

        tags = _schema_private_endpoint_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_private_endpoint_read.etag
        _schema.extended_location = cls._schema_private_endpoint_read.extended_location
        _schema.id = cls._schema_private_endpoint_read.id
        _schema.location = cls._schema_private_endpoint_read.location
        _schema.name = cls._schema_private_endpoint_read.name
        _schema.properties = cls._schema_private_endpoint_read.properties
        _schema.tags = cls._schema_private_endpoint_read.tags
        _schema.type = cls._schema_private_endpoint_read.type

    _schema_private_link_service_connection_state_read = None

    @classmethod
    def _build_schema_private_link_service_connection_state_read(cls, _schema):
        if cls._schema_private_link_service_connection_state_read is not None:
            _schema.actions_required = cls._schema_private_link_service_connection_state_read.actions_required
            _schema.description = cls._schema_private_link_service_connection_state_read.description
            _schema.status = cls._schema_private_link_service_connection_state_read.status
            return

        cls._schema_private_link_service_connection_state_read = _schema_private_link_service_connection_state_read = AAZObjectType()

        private_link_service_connection_state_read = _schema_private_link_service_connection_state_read
        private_link_service_connection_state_read.actions_required = AAZStrType(
            serialized_name="actionsRequired",
        )
        private_link_service_connection_state_read.description = AAZStrType()
        private_link_service_connection_state_read.status = AAZStrType()

        _schema.actions_required = cls._schema_private_link_service_connection_state_read.actions_required
        _schema.description = cls._schema_private_link_service_connection_state_read.description
        _schema.status = cls._schema_private_link_service_connection_state_read.status

    _schema_private_link_service_connection_read = None

    @classmethod
    def _build_schema_private_link_service_connection_read(cls, _schema):
        if cls._schema_private_link_service_connection_read is not None:
            _schema.etag = cls._schema_private_link_service_connection_read.etag
            _schema.id = cls._schema_private_link_service_connection_read.id
            _schema.name = cls._schema_private_link_service_connection_read.name
            _schema.properties = cls._schema_private_link_service_connection_read.properties
            _schema.type = cls._schema_private_link_service_connection_read.type
            return

        cls._schema_private_link_service_connection_read = _schema_private_link_service_connection_read = AAZObjectType()

        private_link_service_connection_read = _schema_private_link_service_connection_read
        private_link_service_connection_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service_connection_read.id = AAZStrType()
        private_link_service_connection_read.name = AAZStrType()
        private_link_service_connection_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_link_service_connection_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_link_service_connection_read.properties
        properties.group_ids = AAZListType(
            serialized_name="groupIds",
        )
        properties.private_link_service_connection_state = AAZObjectType(
            serialized_name="privateLinkServiceConnectionState",
        )
        cls._build_schema_private_link_service_connection_state_read(properties.private_link_service_connection_state)
        properties.private_link_service_id = AAZStrType(
            serialized_name="privateLinkServiceId",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.request_message = AAZStrType(
            serialized_name="requestMessage",
        )

        group_ids = _schema_private_link_service_connection_read.properties.group_ids
        group_ids.Element = AAZStrType()

        _schema.etag = cls._schema_private_link_service_connection_read.etag
        _schema.id = cls._schema_private_link_service_connection_read.id
        _schema.name = cls._schema_private_link_service_connection_read.name
        _schema.properties = cls._schema_private_link_service_connection_read.properties
        _schema.type = cls._schema_private_link_service_connection_read.type

    _schema_private_link_service_read = None

    @classmethod
    def _build_schema_private_link_service_read(cls, _schema):
        if cls._schema_private_link_service_read is not None:
            _schema.etag = cls._schema_private_link_service_read.etag
            _schema.extended_location = cls._schema_private_link_service_read.extended_location
            _schema.id = cls._schema_private_link_service_read.id
            _schema.location = cls._schema_private_link_service_read.location
            _schema.name = cls._schema_private_link_service_read.name
            _schema.properties = cls._schema_private_link_service_read.properties
            _schema.tags = cls._schema_private_link_service_read.tags
            _schema.type = cls._schema_private_link_service_read.type
            return

        cls._schema_private_link_service_read = _schema_private_link_service_read = AAZObjectType()

        private_link_service_read = _schema_private_link_service_read
        private_link_service_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(private_link_service_read.extended_location)
        private_link_service_read.id = AAZStrType()
        private_link_service_read.location = AAZStrType()
        private_link_service_read.name = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_link_service_read.tags = AAZDictType()
        private_link_service_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_link_service_read.properties
        properties.alias = AAZStrType(
            flags={"read_only": True},
        )
        properties.auto_approval = AAZObjectType(
            serialized_name="autoApproval",
        )
        properties.destination_ip_address = AAZStrType(
            serialized_name="destinationIPAddress",
        )
        properties.enable_proxy_protocol = AAZBoolType(
            serialized_name="enableProxyProtocol",
        )
        properties.fqdns = AAZListType()
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.load_balancer_frontend_ip_configurations = AAZListType(
            serialized_name="loadBalancerFrontendIpConfigurations",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.private_endpoint_connections = AAZListType(
            serialized_name="privateEndpointConnections",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.visibility = AAZObjectType()

        auto_approval = _schema_private_link_service_read.properties.auto_approval
        auto_approval.subscriptions = AAZListType()

        subscriptions = _schema_private_link_service_read.properties.auto_approval.subscriptions
        subscriptions.Element = AAZStrType()

        fqdns = _schema_private_link_service_read.properties.fqdns
        fqdns.Element = AAZStrType()

        ip_configurations = _schema_private_link_service_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_private_link_service_read.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_link_service_read.properties.ip_configurations.Element.properties
        properties.primary = AAZBoolType()
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        load_balancer_frontend_ip_configurations = _schema_private_link_service_read.properties.load_balancer_frontend_ip_configurations
        load_balancer_frontend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_frontend_ip_configuration_read(load_balancer_frontend_ip_configurations.Element)

        network_interfaces = _schema_private_link_service_read.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        private_endpoint_connections = _schema_private_link_service_read.properties.private_endpoint_connections
        private_endpoint_connections.Element = AAZObjectType()

        _element = _schema_private_link_service_read.properties.private_endpoint_connections.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_link_service_read.properties.private_endpoint_connections.Element.properties
        properties.link_identifier = AAZStrType(
            serialized_name="linkIdentifier",
            flags={"read_only": True},
        )
        properties.private_endpoint = AAZObjectType(
            serialized_name="privateEndpoint",
            flags={"read_only": True},
        )
        cls._build_schema_private_endpoint_read(properties.private_endpoint)
        properties.private_endpoint_location = AAZStrType(
            serialized_name="privateEndpointLocation",
            flags={"read_only": True},
        )
        properties.private_link_service_connection_state = AAZObjectType(
            serialized_name="privateLinkServiceConnectionState",
        )
        cls._build_schema_private_link_service_connection_state_read(properties.private_link_service_connection_state)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        visibility = _schema_private_link_service_read.properties.visibility
        visibility.subscriptions = AAZListType()

        subscriptions = _schema_private_link_service_read.properties.visibility.subscriptions
        subscriptions.Element = AAZStrType()

        tags = _schema_private_link_service_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_private_link_service_read.etag
        _schema.extended_location = cls._schema_private_link_service_read.extended_location
        _schema.id = cls._schema_private_link_service_read.id
        _schema.location = cls._schema_private_link_service_read.location
        _schema.name = cls._schema_private_link_service_read.name
        _schema.properties = cls._schema_private_link_service_read.properties
        _schema.tags = cls._schema_private_link_service_read.tags
        _schema.type = cls._schema_private_link_service_read.type

    _schema_public_ip_address_read = None

    @classmethod
    def _build_schema_public_ip_address_read(cls, _schema):
        if cls._schema_public_ip_address_read is not None:
            _schema.etag = cls._schema_public_ip_address_read.etag
            _schema.extended_location = cls._schema_public_ip_address_read.extended_location
            _schema.id = cls._schema_public_ip_address_read.id
            _schema.location = cls._schema_public_ip_address_read.location
            _schema.name = cls._schema_public_ip_address_read.name
            _schema.properties = cls._schema_public_ip_address_read.properties
            _schema.sku = cls._schema_public_ip_address_read.sku
            _schema.tags = cls._schema_public_ip_address_read.tags
            _schema.type = cls._schema_public_ip_address_read.type
            _schema.zones = cls._schema_public_ip_address_read.zones
            return

        cls._schema_public_ip_address_read = _schema_public_ip_address_read = AAZObjectType()

        public_ip_address_read = _schema_public_ip_address_read
        public_ip_address_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(public_ip_address_read.extended_location)
        public_ip_address_read.id = AAZStrType()
        public_ip_address_read.location = AAZStrType()
        public_ip_address_read.name = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        public_ip_address_read.sku = AAZObjectType()
        public_ip_address_read.tags = AAZDictType()
        public_ip_address_read.type = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.zones = AAZListType()

        properties = _schema_public_ip_address_read.properties
        properties.ddos_settings = AAZObjectType(
            serialized_name="ddosSettings",
        )
        properties.delete_option = AAZStrType(
            serialized_name="deleteOption",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )
        properties.ip_configuration = AAZObjectType(
            serialized_name="ipConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_ip_configuration_read(properties.ip_configuration)
        properties.ip_tags = AAZListType(
            serialized_name="ipTags",
        )
        properties.linked_public_ip_address = AAZObjectType(
            serialized_name="linkedPublicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.linked_public_ip_address)
        properties.migration_phase = AAZStrType(
            serialized_name="migrationPhase",
        )
        properties.nat_gateway = AAZObjectType(
            serialized_name="natGateway",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address_version = AAZStrType(
            serialized_name="publicIPAddressVersion",
        )
        properties.public_ip_allocation_method = AAZStrType(
            serialized_name="publicIPAllocationMethod",
        )
        properties.public_ip_prefix = AAZObjectType(
            serialized_name="publicIPPrefix",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_prefix)
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.service_public_ip_address = AAZObjectType(
            serialized_name="servicePublicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.service_public_ip_address)

        ddos_settings = _schema_public_ip_address_read.properties.ddos_settings
        ddos_settings.ddos_protection_plan = AAZObjectType(
            serialized_name="ddosProtectionPlan",
        )
        cls._build_schema_sub_resource_read(ddos_settings.ddos_protection_plan)
        ddos_settings.protection_mode = AAZStrType(
            serialized_name="protectionMode",
        )

        dns_settings = _schema_public_ip_address_read.properties.dns_settings
        dns_settings.domain_name_label = AAZStrType(
            serialized_name="domainNameLabel",
        )
        dns_settings.domain_name_label_scope = AAZStrType(
            serialized_name="domainNameLabelScope",
        )
        dns_settings.fqdn = AAZStrType()
        dns_settings.reverse_fqdn = AAZStrType(
            serialized_name="reverseFqdn",
        )

        ip_tags = _schema_public_ip_address_read.properties.ip_tags
        ip_tags.Element = AAZObjectType()

        _element = _schema_public_ip_address_read.properties.ip_tags.Element
        _element.ip_tag_type = AAZStrType(
            serialized_name="ipTagType",
        )
        _element.tag = AAZStrType()

        nat_gateway = _schema_public_ip_address_read.properties.nat_gateway
        nat_gateway.etag = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.id = AAZStrType()
        nat_gateway.location = AAZStrType()
        nat_gateway.name = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        nat_gateway.sku = AAZObjectType()
        nat_gateway.tags = AAZDictType()
        nat_gateway.type = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.zones = AAZListType()

        properties = _schema_public_ip_address_read.properties.nat_gateway.properties
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_addresses = AAZListType(
            serialized_name="publicIpAddresses",
        )
        properties.public_ip_prefixes = AAZListType(
            serialized_name="publicIpPrefixes",
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        public_ip_addresses = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_addresses
        public_ip_addresses.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_addresses.Element)

        public_ip_prefixes = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_prefixes
        public_ip_prefixes.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_prefixes.Element)

        subnets = _schema_public_ip_address_read.properties.nat_gateway.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(subnets.Element)

        sku = _schema_public_ip_address_read.properties.nat_gateway.sku
        sku.name = AAZStrType()

        tags = _schema_public_ip_address_read.properties.nat_gateway.tags
        tags.Element = AAZStrType()

        zones = _schema_public_ip_address_read.properties.nat_gateway.zones
        zones.Element = AAZStrType()

        sku = _schema_public_ip_address_read.sku
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        tags = _schema_public_ip_address_read.tags
        tags.Element = AAZStrType()

        zones = _schema_public_ip_address_read.zones
        zones.Element = AAZStrType()

        _schema.etag = cls._schema_public_ip_address_read.etag
        _schema.extended_location = cls._schema_public_ip_address_read.extended_location
        _schema.id = cls._schema_public_ip_address_read.id
        _schema.location = cls._schema_public_ip_address_read.location
        _schema.name = cls._schema_public_ip_address_read.name
        _schema.properties = cls._schema_public_ip_address_read.properties
        _schema.sku = cls._schema_public_ip_address_read.sku
        _schema.tags = cls._schema_public_ip_address_read.tags
        _schema.type = cls._schema_public_ip_address_read.type
        _schema.zones = cls._schema_public_ip_address_read.zones

    _schema_security_rule_read = None

    @classmethod
    def _build_schema_security_rule_read(cls, _schema):
        if cls._schema_security_rule_read is not None:
            _schema.etag = cls._schema_security_rule_read.etag
            _schema.id = cls._schema_security_rule_read.id
            _schema.name = cls._schema_security_rule_read.name
            _schema.properties = cls._schema_security_rule_read.properties
            _schema.type = cls._schema_security_rule_read.type
            return

        cls._schema_security_rule_read = _schema_security_rule_read = AAZObjectType()

        security_rule_read = _schema_security_rule_read
        security_rule_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        security_rule_read.id = AAZStrType()
        security_rule_read.name = AAZStrType()
        security_rule_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        security_rule_read.type = AAZStrType()

        properties = _schema_security_rule_read.properties
        properties.access = AAZStrType(
            flags={"required": True},
        )
        properties.description = AAZStrType()
        properties.destination_address_prefix = AAZStrType(
            serialized_name="destinationAddressPrefix",
        )
        properties.destination_address_prefixes = AAZListType(
            serialized_name="destinationAddressPrefixes",
        )
        properties.destination_application_security_groups = AAZListType(
            serialized_name="destinationApplicationSecurityGroups",
        )
        properties.destination_port_range = AAZStrType(
            serialized_name="destinationPortRange",
        )
        properties.destination_port_ranges = AAZListType(
            serialized_name="destinationPortRanges",
        )
        properties.direction = AAZStrType(
            flags={"required": True},
        )
        properties.priority = AAZIntType(
            flags={"required": True},
        )
        properties.protocol = AAZStrType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.source_address_prefix = AAZStrType(
            serialized_name="sourceAddressPrefix",
        )
        properties.source_address_prefixes = AAZListType(
            serialized_name="sourceAddressPrefixes",
        )
        properties.source_application_security_groups = AAZListType(
            serialized_name="sourceApplicationSecurityGroups",
        )
        properties.source_port_range = AAZStrType(
            serialized_name="sourcePortRange",
        )
        properties.source_port_ranges = AAZListType(
            serialized_name="sourcePortRanges",
        )

        destination_address_prefixes = _schema_security_rule_read.properties.destination_address_prefixes
        destination_address_prefixes.Element = AAZStrType()

        destination_application_security_groups = _schema_security_rule_read.properties.destination_application_security_groups
        destination_application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(destination_application_security_groups.Element)

        destination_port_ranges = _schema_security_rule_read.properties.destination_port_ranges
        destination_port_ranges.Element = AAZStrType()

        source_address_prefixes = _schema_security_rule_read.properties.source_address_prefixes
        source_address_prefixes.Element = AAZStrType()

        source_application_security_groups = _schema_security_rule_read.properties.source_application_security_groups
        source_application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(source_application_security_groups.Element)

        source_port_ranges = _schema_security_rule_read.properties.source_port_ranges
        source_port_ranges.Element = AAZStrType()

        _schema.etag = cls._schema_security_rule_read.etag
        _schema.id = cls._schema_security_rule_read.id
        _schema.name = cls._schema_security_rule_read.name
        _schema.properties = cls._schema_security_rule_read.properties
        _schema.type = cls._schema_security_rule_read.type

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_subnet_read = None

    @classmethod
    def _build_schema_subnet_read(cls, _schema):
        if cls._schema_subnet_read is not None:
            _schema.etag = cls._schema_subnet_read.etag
            _schema.id = cls._schema_subnet_read.id
            _schema.name = cls._schema_subnet_read.name
            _schema.properties = cls._schema_subnet_read.properties
            _schema.type = cls._schema_subnet_read.type
            return

        cls._schema_subnet_read = _schema_subnet_read = AAZObjectType()

        subnet_read = _schema_subnet_read
        subnet_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        subnet_read.id = AAZStrType()
        subnet_read.name = AAZStrType()
        subnet_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        subnet_read.type = AAZStrType()

        properties = _schema_subnet_read.properties
        properties.address_prefix = AAZStrType(
            serialized_name="addressPrefix",
        )
        properties.address_prefixes = AAZListType(
            serialized_name="addressPrefixes",
        )
        properties.application_gateway_ip_configurations = AAZListType(
            serialized_name="applicationGatewayIPConfigurations",
        )
        properties.default_outbound_access = AAZBoolType(
            serialized_name="defaultOutboundAccess",
        )
        properties.delegations = AAZListType()
        properties.ip_allocations = AAZListType(
            serialized_name="ipAllocations",
        )
        properties.ip_configuration_profiles = AAZListType(
            serialized_name="ipConfigurationProfiles",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
            flags={"read_only": True},
        )
        properties.nat_gateway = AAZObjectType(
            serialized_name="natGateway",
        )
        cls._build_schema_sub_resource_read(properties.nat_gateway)
        properties.network_security_group = AAZObjectType(
            serialized_name="networkSecurityGroup",
        )
        cls._build_schema_network_security_group_read(properties.network_security_group)
        properties.private_endpoint_network_policies = AAZStrType(
            serialized_name="privateEndpointNetworkPolicies",
        )
        properties.private_endpoints = AAZListType(
            serialized_name="privateEndpoints",
            flags={"read_only": True},
        )
        properties.private_link_service_network_policies = AAZStrType(
            serialized_name="privateLinkServiceNetworkPolicies",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.purpose = AAZStrType(
            flags={"read_only": True},
        )
        properties.resource_navigation_links = AAZListType(
            serialized_name="resourceNavigationLinks",
            flags={"read_only": True},
        )
        properties.route_table = AAZObjectType(
            serialized_name="routeTable",
        )
        properties.service_association_links = AAZListType(
            serialized_name="serviceAssociationLinks",
            flags={"read_only": True},
        )
        properties.service_endpoint_policies = AAZListType(
            serialized_name="serviceEndpointPolicies",
        )
        properties.service_endpoints = AAZListType(
            serialized_name="serviceEndpoints",
        )
        properties.sharing_scope = AAZStrType(
            serialized_name="sharingScope",
        )

        address_prefixes = _schema_subnet_read.properties.address_prefixes
        address_prefixes.Element = AAZStrType()

        application_gateway_ip_configurations = _schema_subnet_read.properties.application_gateway_ip_configurations
        application_gateway_ip_configurations.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.application_gateway_ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.application_gateway_ip_configurations.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)

        delegations = _schema_subnet_read.properties.delegations
        delegations.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.delegations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.delegations.Element.properties
        properties.actions = AAZListType(
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.service_name = AAZStrType(
            serialized_name="serviceName",
        )

        actions = _schema_subnet_read.properties.delegations.Element.properties.actions
        actions.Element = AAZStrType()

        ip_allocations = _schema_subnet_read.properties.ip_allocations
        ip_allocations.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(ip_allocations.Element)

        ip_configuration_profiles = _schema_subnet_read.properties.ip_configuration_profiles
        ip_configuration_profiles.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.ip_configuration_profiles.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.ip_configuration_profiles.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        ip_configurations = _schema_subnet_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()
        cls._build_schema_ip_configuration_read(ip_configurations.Element)

        private_endpoints = _schema_subnet_read.properties.private_endpoints
        private_endpoints.Element = AAZObjectType()
        cls._build_schema_private_endpoint_read(private_endpoints.Element)

        resource_navigation_links = _schema_subnet_read.properties.resource_navigation_links
        resource_navigation_links.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.resource_navigation_links.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.resource_navigation_links.Element.properties
        properties.link = AAZStrType()
        properties.linked_resource_type = AAZStrType(
            serialized_name="linkedResourceType",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        route_table = _schema_subnet_read.properties.route_table
        route_table.etag = AAZStrType(
            flags={"read_only": True},
        )
        route_table.id = AAZStrType()
        route_table.location = AAZStrType()
        route_table.name = AAZStrType(
            flags={"read_only": True},
        )
        route_table.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        route_table.tags = AAZDictType()
        route_table.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.route_table.properties
        properties.disable_bgp_route_propagation = AAZBoolType(
            serialized_name="disableBgpRoutePropagation",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.routes = AAZListType()
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        routes = _schema_subnet_read.properties.route_table.properties.routes
        routes.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.route_table.properties.routes.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.route_table.properties.routes.Element.properties
        properties.address_prefix = AAZStrType(
            serialized_name="addressPrefix",
        )
        properties.has_bgp_override = AAZBoolType(
            serialized_name="hasBgpOverride",
            flags={"read_only": True},
        )
        properties.next_hop_ip_address = AAZStrType(
            serialized_name="nextHopIpAddress",
        )
        properties.next_hop_type = AAZStrType(
            serialized_name="nextHopType",
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        subnets = _schema_subnet_read.properties.route_table.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_subnet_read.properties.route_table.tags
        tags.Element = AAZStrType()

        service_association_links = _schema_subnet_read.properties.service_association_links
        service_association_links.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_association_links.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.service_association_links.Element.properties
        properties.allow_delete = AAZBoolType(
            serialized_name="allowDelete",
        )
        properties.link = AAZStrType()
        properties.linked_resource_type = AAZStrType(
            serialized_name="linkedResourceType",
        )
        properties.locations = AAZListType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        locations = _schema_subnet_read.properties.service_association_links.Element.properties.locations
        locations.Element = AAZStrType()

        service_endpoint_policies = _schema_subnet_read.properties.service_endpoint_policies
        service_endpoint_policies.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoint_policies.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.kind = AAZStrType(
            flags={"read_only": True},
        )
        _element.location = AAZStrType()
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.service_endpoint_policies.Element.properties
        properties.contextual_service_endpoint_policies = AAZListType(
            serialized_name="contextualServiceEndpointPolicies",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.service_alias = AAZStrType(
            serialized_name="serviceAlias",
        )
        properties.service_endpoint_policy_definitions = AAZListType(
            serialized_name="serviceEndpointPolicyDefinitions",
        )
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        contextual_service_endpoint_policies = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.contextual_service_endpoint_policies
        contextual_service_endpoint_policies.Element = AAZStrType()

        service_endpoint_policy_definitions = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions
        service_endpoint_policy_definitions.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element.properties
        properties.description = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.service = AAZStrType()
        properties.service_resources = AAZListType(
            serialized_name="serviceResources",
        )

        service_resources = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element.properties.service_resources
        service_resources.Element = AAZStrType()

        subnets = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_subnet_read.properties.service_endpoint_policies.Element.tags
        tags.Element = AAZStrType()

        service_endpoints = _schema_subnet_read.properties.service_endpoints
        service_endpoints.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoints.Element
        _element.locations = AAZListType()
        _element.network_identifier = AAZObjectType(
            serialized_name="networkIdentifier",
        )
        cls._build_schema_sub_resource_read(_element.network_identifier)
        _element.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        _element.service = AAZStrType()

        locations = _schema_subnet_read.properties.service_endpoints.Element.locations
        locations.Element = AAZStrType()

        _schema.etag = cls._schema_subnet_read.etag
        _schema.id = cls._schema_subnet_read.id
        _schema.name = cls._schema_subnet_read.name
        _schema.properties = cls._schema_subnet_read.properties
        _schema.type = cls._schema_subnet_read.type

    _schema_virtual_network_tap_read = None

    @classmethod
    def _build_schema_virtual_network_tap_read(cls, _schema):
        if cls._schema_virtual_network_tap_read is not None:
            _schema.etag = cls._schema_virtual_network_tap_read.etag
            _schema.id = cls._schema_virtual_network_tap_read.id
            _schema.location = cls._schema_virtual_network_tap_read.location
            _schema.name = cls._schema_virtual_network_tap_read.name
            _schema.properties = cls._schema_virtual_network_tap_read.properties
            _schema.tags = cls._schema_virtual_network_tap_read.tags
            _schema.type = cls._schema_virtual_network_tap_read.type
            return

        cls._schema_virtual_network_tap_read = _schema_virtual_network_tap_read = AAZObjectType()

        virtual_network_tap_read = _schema_virtual_network_tap_read
        virtual_network_tap_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_tap_read.id = AAZStrType()
        virtual_network_tap_read.location = AAZStrType()
        virtual_network_tap_read.name = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_tap_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        virtual_network_tap_read.tags = AAZDictType()
        virtual_network_tap_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_tap_read.properties
        properties.destination_load_balancer_front_end_ip_configuration = AAZObjectType(
            serialized_name="destinationLoadBalancerFrontEndIPConfiguration",
        )
        cls._build_schema_frontend_ip_configuration_read(properties.destination_load_balancer_front_end_ip_configuration)
        properties.destination_network_interface_ip_configuration = AAZObjectType(
            serialized_name="destinationNetworkInterfaceIPConfiguration",
        )
        cls._build_schema_network_interface_ip_configuration_read(properties.destination_network_interface_ip_configuration)
        properties.destination_port = AAZIntType(
            serialized_name="destinationPort",
        )
        properties.network_interface_tap_configurations = AAZListType(
            serialized_name="networkInterfaceTapConfigurations",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )

        network_interface_tap_configurations = _schema_virtual_network_tap_read.properties.network_interface_tap_configurations
        network_interface_tap_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_tap_configuration_read(network_interface_tap_configurations.Element)

        tags = _schema_virtual_network_tap_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_virtual_network_tap_read.etag
        _schema.id = cls._schema_virtual_network_tap_read.id
        _schema.location = cls._schema_virtual_network_tap_read.location
        _schema.name = cls._schema_virtual_network_tap_read.name
        _schema.properties = cls._schema_virtual_network_tap_read.properties
        _schema.tags = cls._schema_virtual_network_tap_read.tags
        _schema.type = cls._schema_virtual_network_tap_read.type


__all__ = ["Create"]
