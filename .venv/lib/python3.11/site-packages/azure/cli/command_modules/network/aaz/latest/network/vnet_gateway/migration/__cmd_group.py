# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "network vnet-gateway migration",
)
class __CMDGroup(AAZCommandGroup):
    """Migrate vpn gateway from basic IP CSES to Standard IP based VMSS deployment.
    """
    pass


__all__ = ["__CMDGroup"]
