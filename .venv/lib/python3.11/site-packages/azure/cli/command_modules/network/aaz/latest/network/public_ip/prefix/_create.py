# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network public-ip prefix create",
)
class Create(AAZCommand):
    """Create a public IP prefix resource.

    :example: Create a public IP prefix resource. (autogenerated)
        az network public-ip prefix create --length 28 --location westus2 --name MyPublicIPPrefix --resource-group MyResourceGroup
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/publicipprefixes/{}", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The name of the public IP prefix.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.edge_zone = AAZStrArg(
            options=["--edge-zone"],
            help="The name of edge zone.",
        )
        _args_schema.location = AAZResourceLocationArg(
            help="Location. Values from: `az account list-locations`. You can configure the default location using `az configure --defaults location=<location>`.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.custom_ip_prefix_name = AAZStrArg(
            options=["--custom-ip-prefix-name"],
            help="A custom prefix from which the public prefix derived. If you'd like to cross subscription, please use Resource ID instead.",
        )
        _args_schema.length = AAZIntArg(
            options=["--length"],
            help="Length of the prefix (i.e. `XX.XX.XX.XX/<Length>`).",
        )
        _args_schema.version = AAZStrArg(
            options=["--version"],
            help="IP address type.  Allowed values: IPv4, IPv6.",
            default="IPv4",
            enum={"IPv4": "IPv4", "IPv6": "IPv6"},
        )
        _args_schema.tier = AAZStrArg(
            options=["--tier"],
            help="Tier of a public IP prefix SKU.",
            enum={"Global": "Global", "Regional": "Regional"},
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="Space-separated tags: key[=value] [key[=value] ...]. Use \"\" to clear existing tags.",
        )
        _args_schema.zone = AAZListArg(
            options=["-z", "--zone"],
            help="Space-separated list of availability zones into which to provision the resource.  Allowed values: 1, 2, 3.",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        zone = cls._args_schema.zone
        zone.Element = AAZStrArg()

        # define Arg Group "ExtendedLocation"

        _args_schema = cls._args_schema
        _args_schema.type = AAZStrArg(
            options=["--type"],
            arg_group="ExtendedLocation",
            help="The type of the extended location.",
            enum={"EdgeZone": "EdgeZone"},
        )

        # define Arg Group "Parameters"

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.ip_tags_list = AAZListArg(
            options=["--ip-tags-list"],
            arg_group="Properties",
            help="The list of tags associated with the public IP prefix.",
        )

        ip_tags_list = cls._args_schema.ip_tags_list
        ip_tags_list.Element = AAZObjectArg()

        _element = cls._args_schema.ip_tags_list.Element
        _element.ip_tag_type = AAZStrArg(
            options=["ip-tag-type"],
            help="The IP tag type. Example: FirstPartyUsage.",
        )
        _element.tag = AAZStrArg(
            options=["tag"],
            help="The value of the IP tag associated with the public IP. Example: SQL.",
        )

        # define Arg Group "Sku"

        _args_schema = cls._args_schema
        _args_schema.sku = AAZStrArg(
            options=["--sku"],
            arg_group="Sku",
            help="Name of a public IP prefix SKU.",
            default="Standard",
            enum={"Standard": "Standard", "StandardV2": "StandardV2"},
        )
        return cls._args_schema

    _args_sub_resource_create = None

    @classmethod
    def _build_args_sub_resource_create(cls, _schema):
        if cls._args_sub_resource_create is not None:
            _schema.id = cls._args_sub_resource_create.id
            return

        cls._args_sub_resource_create = AAZObjectArg()

        sub_resource_create = cls._args_sub_resource_create
        sub_resource_create.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        _schema.id = cls._args_sub_resource_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.PublicIPPrefixesCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class PublicIPPrefixesCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/publicIPPrefixes/{publicIpPrefixName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "publicIpPrefixName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("extendedLocation", AAZObjectType)
            _builder.set_prop("location", AAZStrType, ".location")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("sku", AAZObjectType)
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zone")

            extended_location = _builder.get(".extendedLocation")
            if extended_location is not None:
                extended_location.set_prop("name", AAZStrType, ".edge_zone")
                extended_location.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("customIPPrefix", AAZObjectType)
                properties.set_prop("ipTags", AAZListType, ".ip_tags_list")
                properties.set_prop("prefixLength", AAZIntType, ".length")
                properties.set_prop("publicIPAddressVersion", AAZStrType, ".version")

            custom_ip_prefix = _builder.get(".properties.customIPPrefix")
            if custom_ip_prefix is not None:
                custom_ip_prefix.set_prop("id", AAZStrType, ".custom_ip_prefix_name")

            ip_tags = _builder.get(".properties.ipTags")
            if ip_tags is not None:
                ip_tags.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipTags[]")
            if _elements is not None:
                _elements.set_prop("ipTagType", AAZStrType, ".ip_tag_type")
                _elements.set_prop("tag", AAZStrType, ".tag")

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("name", AAZStrType, ".sku")
                sku.set_prop("tier", AAZStrType, ".tier")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.extended_location = AAZObjectType(
                serialized_name="extendedLocation",
            )
            _schema_on_200_201.id = AAZStrType()
            _schema_on_200_201.location = AAZStrType()
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200_201.sku = AAZObjectType()
            _schema_on_200_201.tags = AAZDictType()
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.zones = AAZListType()

            extended_location = cls._schema_on_200_201.extended_location
            extended_location.name = AAZStrType()
            extended_location.type = AAZStrType()

            properties = cls._schema_on_200_201.properties
            properties.custom_ip_prefix = AAZObjectType(
                serialized_name="customIPPrefix",
            )
            _CreateHelper._build_schema_sub_resource_read(properties.custom_ip_prefix)
            properties.ip_prefix = AAZStrType(
                serialized_name="ipPrefix",
                flags={"read_only": True},
            )
            properties.ip_tags = AAZListType(
                serialized_name="ipTags",
            )
            properties.load_balancer_frontend_ip_configuration = AAZObjectType(
                serialized_name="loadBalancerFrontendIpConfiguration",
                flags={"read_only": True},
            )
            _CreateHelper._build_schema_sub_resource_read(properties.load_balancer_frontend_ip_configuration)
            properties.nat_gateway = AAZObjectType(
                serialized_name="natGateway",
            )
            properties.prefix_length = AAZIntType(
                serialized_name="prefixLength",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.public_ip_address_version = AAZStrType(
                serialized_name="publicIPAddressVersion",
            )
            properties.public_ip_addresses = AAZListType(
                serialized_name="publicIPAddresses",
                flags={"read_only": True},
            )
            properties.resource_guid = AAZStrType(
                serialized_name="resourceGuid",
                flags={"read_only": True},
            )

            ip_tags = cls._schema_on_200_201.properties.ip_tags
            ip_tags.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.ip_tags.Element
            _element.ip_tag_type = AAZStrType(
                serialized_name="ipTagType",
            )
            _element.tag = AAZStrType()

            nat_gateway = cls._schema_on_200_201.properties.nat_gateway
            nat_gateway.etag = AAZStrType(
                flags={"read_only": True},
            )
            nat_gateway.id = AAZStrType()
            nat_gateway.location = AAZStrType()
            nat_gateway.name = AAZStrType(
                flags={"read_only": True},
            )
            nat_gateway.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            nat_gateway.sku = AAZObjectType()
            nat_gateway.tags = AAZDictType()
            nat_gateway.type = AAZStrType(
                flags={"read_only": True},
            )
            nat_gateway.zones = AAZListType()

            properties = cls._schema_on_200_201.properties.nat_gateway.properties
            properties.idle_timeout_in_minutes = AAZIntType(
                serialized_name="idleTimeoutInMinutes",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.public_ip_addresses = AAZListType(
                serialized_name="publicIpAddresses",
            )
            properties.public_ip_addresses_v6 = AAZListType(
                serialized_name="publicIpAddressesV6",
            )
            properties.public_ip_prefixes = AAZListType(
                serialized_name="publicIpPrefixes",
            )
            properties.public_ip_prefixes_v6 = AAZListType(
                serialized_name="publicIpPrefixesV6",
            )
            properties.resource_guid = AAZStrType(
                serialized_name="resourceGuid",
                flags={"read_only": True},
            )
            properties.source_virtual_network = AAZObjectType(
                serialized_name="sourceVirtualNetwork",
            )
            _CreateHelper._build_schema_sub_resource_read(properties.source_virtual_network)
            properties.subnets = AAZListType(
                flags={"read_only": True},
            )

            public_ip_addresses = cls._schema_on_200_201.properties.nat_gateway.properties.public_ip_addresses
            public_ip_addresses.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_addresses.Element)

            public_ip_addresses_v6 = cls._schema_on_200_201.properties.nat_gateway.properties.public_ip_addresses_v6
            public_ip_addresses_v6.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_addresses_v6.Element)

            public_ip_prefixes = cls._schema_on_200_201.properties.nat_gateway.properties.public_ip_prefixes
            public_ip_prefixes.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_prefixes.Element)

            public_ip_prefixes_v6 = cls._schema_on_200_201.properties.nat_gateway.properties.public_ip_prefixes_v6
            public_ip_prefixes_v6.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_prefixes_v6.Element)

            subnets = cls._schema_on_200_201.properties.nat_gateway.properties.subnets
            subnets.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(subnets.Element)

            sku = cls._schema_on_200_201.properties.nat_gateway.sku
            sku.name = AAZStrType()

            tags = cls._schema_on_200_201.properties.nat_gateway.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200_201.properties.nat_gateway.zones
            zones.Element = AAZStrType()

            public_ip_addresses = cls._schema_on_200_201.properties.public_ip_addresses
            public_ip_addresses.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.public_ip_addresses.Element
            _element.id = AAZStrType()

            sku = cls._schema_on_200_201.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            tags = cls._schema_on_200_201.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200_201.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_sub_resource_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType(
            flags={"read_only": True}
        )

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id


__all__ = ["Create"]
