# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vnet-gateway get-failover-single-test-detail",
)
class GetFailoverSingleTestDetail(AAZCommand):
    """This operation retrieves the details of a particular failover test performed on the gateway based on the test Guid

    :example: VirtualNetworkGatewayGetFailoverSingleTestDetails
        az network vnet-gateway get-failover-single-test-detail --resource-group rg1 --virtual-network-gateway-name ergw --peering-location Vancouver --failover-test-id fe458ae8-d2ae-4520-a104-44bc233bde7e
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/virtualnetworkgateways/{}/getfailoversingletestdetails", "2024-05-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.virtual_network_gateway_name = AAZStrArg(
            options=["--virtual-network-gateway-name", "--name"],
            help="The name of the virtual network gateway.",
            required=True,
            id_part="name",
        )
        _args_schema.failover_test_id = AAZStrArg(
            options=["--failover-test-id"],
            help="The unique Guid value which identifies the test",
            required=True,
        )
        _args_schema.peering_location = AAZStrArg(
            options=["--peering-location"],
            help="Peering location of the test",
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualNetworkGatewaysGetFailoverSingleTestDetails(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualNetworkGatewaysGetFailoverSingleTestDetails(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworkGateways/{virtualNetworkGatewayName}/getFailoverSingleTestDetails",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayName", self.ctx.args.virtual_network_gateway_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "failoverTestId", self.ctx.args.failover_test_id,
                    required=True,
                ),
                **self.serialize_query_param(
                    "peeringLocation", self.ctx.args.peering_location,
                    required=True,
                ),
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZListType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.Element = AAZObjectType()

            _element = cls._schema_on_200.Element
            _element.end_time_utc = AAZStrType(
                serialized_name="endTimeUtc",
            )
            _element.failover_connection_details = AAZListType(
                serialized_name="failoverConnectionDetails",
            )
            _element.non_redundant_routes = AAZListType(
                serialized_name="nonRedundantRoutes",
            )
            _element.peering_location = AAZStrType(
                serialized_name="peeringLocation",
            )
            _element.redundant_routes = AAZListType(
                serialized_name="redundantRoutes",
            )
            _element.start_time_utc = AAZStrType(
                serialized_name="startTimeUtc",
            )
            _element.status = AAZStrType()
            _element.was_simulation_successful = AAZBoolType(
                serialized_name="wasSimulationSuccessful",
            )

            failover_connection_details = cls._schema_on_200.Element.failover_connection_details
            failover_connection_details.Element = AAZObjectType()

            _element = cls._schema_on_200.Element.failover_connection_details.Element
            _element.failover_connection_name = AAZStrType(
                serialized_name="failoverConnectionName",
            )
            _element.failover_location = AAZStrType(
                serialized_name="failoverLocation",
            )
            _element.is_verified = AAZBoolType(
                serialized_name="isVerified",
            )

            non_redundant_routes = cls._schema_on_200.Element.non_redundant_routes
            non_redundant_routes.Element = AAZStrType()

            redundant_routes = cls._schema_on_200.Element.redundant_routes
            redundant_routes.Element = AAZObjectType()

            _element = cls._schema_on_200.Element.redundant_routes.Element
            _element.peering_locations = AAZListType(
                serialized_name="peeringLocations",
            )
            _element.routes = AAZListType()

            peering_locations = cls._schema_on_200.Element.redundant_routes.Element.peering_locations
            peering_locations.Element = AAZStrType()

            routes = cls._schema_on_200.Element.redundant_routes.Element.routes
            routes.Element = AAZStrType()

            return cls._schema_on_200


class _GetFailoverSingleTestDetailHelper:
    """Helper class for GetFailoverSingleTestDetail"""


__all__ = ["GetFailoverSingleTestDetail"]
