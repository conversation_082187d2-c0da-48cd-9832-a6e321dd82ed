# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vnet-gateway get-failover-all-tests-detail",
)
class GetFailoverAllTestsDetail(AAZCommand):
    """This operation retrieves the details of all the failover tests performed on the gateway for different peering locations

    :example: VirtualNetworkGatewayGetFailoverAllTestsDetails
        az network vnet-gateway get-failover-all-tests-detail --resource-group rg1 --virtual-network-gateway-name ergw --type SingleSiteFailover --fetch-latest true
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/virtualnetworkgateways/{}/getfailoveralltestsdetails", "2024-05-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.virtual_network_gateway_name = AAZStrArg(
            options=["--virtual-network-gateway-name", "--name"],
            help="The name of the virtual network gateway.",
            required=True,
            id_part="name",
        )
        _args_schema.fetch_latest = AAZBoolArg(
            options=["--fetch-latest"],
            help="Fetch only the latest tests for each peering location",
            required=True,
        )
        _args_schema.type = AAZStrArg(
            options=["--type"],
            help="The type of failover test",
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualNetworkGatewaysGetFailoverAllTestDetails(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualNetworkGatewaysGetFailoverAllTestDetails(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworkGateways/{virtualNetworkGatewayName}/getFailoverAllTestsDetails",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayName", self.ctx.args.virtual_network_gateway_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "fetchLatest", self.ctx.args.fetch_latest,
                    required=True,
                ),
                **self.serialize_query_param(
                    "type", self.ctx.args.type,
                    required=True,
                ),
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZListType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.Element = AAZObjectType()

            _element = cls._schema_on_200.Element
            _element.circuits = AAZListType()
            _element.connections = AAZListType()
            _element.end_time = AAZStrType(
                serialized_name="endTime",
            )
            _element.issues = AAZListType()
            _element.peering_location = AAZStrType(
                serialized_name="peeringLocation",
            )
            _element.start_time = AAZStrType(
                serialized_name="startTime",
            )
            _element.status = AAZStrType()
            _element.test_guid = AAZStrType(
                serialized_name="testGuid",
            )
            _element.test_type = AAZStrType(
                serialized_name="testType",
            )

            circuits = cls._schema_on_200.Element.circuits
            circuits.Element = AAZObjectType()

            _element = cls._schema_on_200.Element.circuits.Element
            _element.connection_name = AAZStrType(
                serialized_name="connectionName",
            )
            _element.name = AAZStrType()
            _element.nrp_resource_uri = AAZStrType(
                serialized_name="nrpResourceUri",
            )

            connections = cls._schema_on_200.Element.connections
            connections.Element = AAZObjectType()

            _element = cls._schema_on_200.Element.connections.Element
            _element.last_updated_time = AAZStrType(
                serialized_name="lastUpdatedTime",
            )
            _element.name = AAZStrType()
            _element.nrp_resource_uri = AAZStrType(
                serialized_name="nrpResourceUri",
            )
            _element.status = AAZStrType()

            issues = cls._schema_on_200.Element.issues
            issues.Element = AAZStrType()

            return cls._schema_on_200


class _GetFailoverAllTestsDetailHelper:
    """Helper class for GetFailoverAllTestsDetail"""


__all__ = ["GetFailoverAllTestsDetail"]
