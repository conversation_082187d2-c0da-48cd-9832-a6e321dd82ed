# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vnet-gateway stop-site-failover-test",
)
class StopSiteFailoverTest(AAZCommand):
    """This operation stops failover simulation on the gateway for the specified peering location

    :example: VirtualNetworkGatewayStopSiteFailoverSimulation
        az network vnet-gateway stop-site-failover-test --resource-group rg1 --virtual-network-gateway-name ergw --peering-location Vancouver --was-simulation-successful True --details "[{failover-connection-name:conn1,failover-location:Denver,is-verified:False},{failover-connection-name:conn2,failover-location:Amsterdam,is-verified:True}]"
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/virtualnetworkgateways/{}/stopsitefailovertest", "2024-05-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.virtual_network_gateway_name = AAZStrArg(
            options=["--virtual-network-gateway-name", "--name"],
            help="The name of the virtual network gateway.",
            required=True,
            id_part="name",
        )

        # define Arg Group "StopParameters"

        _args_schema = cls._args_schema
        _args_schema.details = AAZListArg(
            options=["--details"],
            arg_group="StopParameters",
            help="List of all the failover connections for this peering location",
        )
        _args_schema.peering_location = AAZStrArg(
            options=["--peering-location"],
            arg_group="StopParameters",
            help="Peering location of the test",
        )
        _args_schema.was_simulation_successful = AAZBoolArg(
            options=["--was-simulation-successful", "--simulation-success"],
            arg_group="StopParameters",
            help="Whether the failover simulation was successful or not",
        )

        details = cls._args_schema.details
        details.Element = AAZObjectArg()

        _element = cls._args_schema.details.Element
        _element.failover_connection_name = AAZStrArg(
            options=["failover-connection-name"],
            help="Name of the failover connection",
        )
        _element.failover_location = AAZStrArg(
            options=["failover-location"],
            help="Location of the failover connection",
        )
        _element.is_verified = AAZBoolArg(
            options=["is-verified"],
            help="Whether the customer was able to establish connectivity through this failover connection or not",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualNetworkGatewaysStopExpressRouteSiteFailoverSimulation(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=False)
        return result

    class VirtualNetworkGatewaysStopExpressRouteSiteFailoverSimulation(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworkGateways/{virtualNetworkGatewayName}/stopSiteFailoverTest",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayName", self.ctx.args.virtual_network_gateway_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("details", AAZListType, ".details")
            _builder.set_prop("peeringLocation", AAZStrType, ".peering_location")
            _builder.set_prop("wasSimulationSuccessful", AAZBoolType, ".was_simulation_successful")

            details = _builder.get(".details")
            if details is not None:
                details.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".details[]")
            if _elements is not None:
                _elements.set_prop("failoverConnectionName", AAZStrType, ".failover_connection_name")
                _elements.set_prop("failoverLocation", AAZStrType, ".failover_location")
                _elements.set_prop("isVerified", AAZBoolType, ".is_verified")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZStrType()

            return cls._schema_on_200


class _StopSiteFailoverTestHelper:
    """Helper class for StopSiteFailoverTest"""


__all__ = ["StopSiteFailoverTest"]
