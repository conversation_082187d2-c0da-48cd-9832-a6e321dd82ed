# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network application-gateway waf-policy custom-rule create",
)
class Create(AAZCommand):
    """Create an application gateway WAF policy custom rule.

    :example: Create an application gateway WAF policy custom rule.
        az network application-gateway waf-policy custom-rule create --action Allow --name MyWafPolicyRule --policy-name MyPolicy --priority 500 --resource-group MyResourceGroup --rule-type MatchRule

    :example: Create an application gateway WAF policy custom rule with user session identifier.
        az network application-gateway waf-policy custom-rule create -g MyResourceGroup --policy-name MyPolicy -n MyRule --priority 3 --action Block --rule-type RateLimitRule --rate-limit-duration FiveMins --rate-limit-threshold 15 --group-by-user-session "[{group-by-variables:[{variable-name:GeoLocation}]}]"
    """

    _aaz_info = {
        "version": "2024-10-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/applicationgatewaywebapplicationfirewallpolicies/{}", "2024-10-01", "properties.customRules[]"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self.SubresourceSelector(ctx=self.ctx, name="subresource")
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.policy_name = AAZStrArg(
            options=["--policy-name"],
            help="Name of the application gateway WAF policy.",
            required=True,
            fmt=AAZStrArgFormat(
                max_length=128,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.action = AAZStrArg(
            options=["--action"],
            help="Action to take.",
            required=True,
            enum={"Allow": "Allow", "Block": "Block", "JSChallenge": "JSChallenge", "Log": "Log"},
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="Name of the WAF policy rule.",
            required=True,
            fmt=AAZStrArgFormat(
                max_length=128,
            ),
        )
        _args_schema.priority = AAZIntArg(
            options=["--priority"],
            help="Rule priority. Lower values are evaluated prior to higher values.",
            required=True,
        )
        _args_schema.rule_type = AAZStrArg(
            options=["--rule-type"],
            help="Type of rule.",
            required=True,
            enum={"Invalid": "Invalid", "MatchRule": "MatchRule", "RateLimitRule": "RateLimitRule"},
        )
        _args_schema.state = AAZStrArg(
            options=["--state"],
            help="Describe if the custom rule is in enabled or disabled state.",
            default="Enabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.group_by_user_session = AAZListArg(
            options=["--group-by-user-session"],
            arg_group="Properties",
            help="List of user session identifier group by clauses.",
        )
        _args_schema.match_conditions = AAZListArg(
            options=["--match-conditions"],
            arg_group="Properties",
            help="List of match conditions.",
            required=True,
            default=[],
        )
        _args_schema.rate_limit_duration = AAZStrArg(
            options=["--rate-limit-duration"],
            arg_group="Properties",
            help="Duration over which Rate Limit policy will be applied. Applies only when ruleType is RateLimitRule.",
            enum={"FiveMins": "FiveMins", "OneMin": "OneMin"},
        )
        _args_schema.rate_limit_threshold = AAZIntArg(
            options=["--rate-limit-threshold"],
            arg_group="Properties",
            help="Rate Limit threshold to apply in case ruleType is RateLimitRule. Must be greater than or equal to 1",
        )

        group_by_user_session = cls._args_schema.group_by_user_session
        group_by_user_session.Element = AAZObjectArg()

        _element = cls._args_schema.group_by_user_session.Element
        _element.group_by_variables = AAZListArg(
            options=["group-by-variables"],
            help="List of group by clause variables.",
            required=True,
        )

        group_by_variables = cls._args_schema.group_by_user_session.Element.group_by_variables
        group_by_variables.Element = AAZObjectArg()

        _element = cls._args_schema.group_by_user_session.Element.group_by_variables.Element
        _element.variable_name = AAZStrArg(
            options=["variable-name"],
            help="User Session clause variable.",
            required=True,
            enum={"ClientAddr": "ClientAddr", "ClientAddrXFFHeader": "ClientAddrXFFHeader", "GeoLocation": "GeoLocation", "GeoLocationXFFHeader": "GeoLocationXFFHeader", "None": "None"},
        )

        match_conditions = cls._args_schema.match_conditions
        match_conditions.Element = AAZObjectArg()

        _element = cls._args_schema.match_conditions.Element
        _element.values = AAZListArg(
            options=["values"],
            help="Space-separated list of values to match.",
            required=True,
        )
        _element.variables = AAZListArg(
            options=["variables"],
            help="Space-separated list of variables to use when matching. Variable values: RemoteAddr, RequestMethod, QueryString, PostArgs, RequestUri, RequestHeaders, RequestBody, RequestCookies.",
            required=True,
        )
        _element.negate = AAZBoolArg(
            options=["negate"],
            help="Match the negative of the condition.",
        )
        _element.operator = AAZStrArg(
            options=["operator"],
            help="Operator for matching.",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GeoMatch": "GeoMatch", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "IPMatch": "IPMatch", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "Regex": "Regex"},
        )
        _element.transforms = AAZListArg(
            options=["transforms"],
            help="Space-separated list of transforms to apply when matching. Allowed values: HtmlEntityDecode, Uppercase, Lowercase, RemoveNulls, Trim, UrlDecode, UrlEncode.",
        )

        values = cls._args_schema.match_conditions.Element.values
        values.Element = AAZStrArg()

        variables = cls._args_schema.match_conditions.Element.variables
        variables.Element = AAZObjectArg()

        _element = cls._args_schema.match_conditions.Element.variables.Element
        _element.selector = AAZStrArg(
            options=["selector"],
            help="The selector of match variable.",
        )
        _element.variable_name = AAZStrArg(
            options=["variable-name"],
            help="Match Variable.",
            required=True,
            enum={"PostArgs": "PostArgs", "QueryString": "QueryString", "RemoteAddr": "RemoteAddr", "RequestBody": "RequestBody", "RequestCookies": "RequestCookies", "RequestHeaders": "RequestHeaders", "RequestMethod": "RequestMethod", "RequestUri": "RequestUri"},
        )

        transforms = cls._args_schema.match_conditions.Element.transforms
        transforms.Element = AAZStrArg(
            enum={"HtmlEntityDecode": "HtmlEntityDecode", "Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.WebApplicationFirewallPoliciesGet(ctx=self.ctx)()
        self.pre_instance_create()
        self.InstanceCreateByJson(ctx=self.ctx)()
        self.post_instance_create(self.ctx.selectors.subresource.get())
        self.WebApplicationFirewallPoliciesCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_create(self):
        pass

    @register_callback
    def post_instance_create(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.selectors.subresource.get(), client_flatten=True)
        return result

    class SubresourceSelector(AAZJsonSelector):

        def _get(self):
            result = self.ctx.vars.instance
            result = result.properties.customRules
            filters = enumerate(result)
            filters = filter(
                lambda e: e[1].name == self.ctx.args.name,
                filters
            )
            idx = next(filters)[0]
            return result[idx]

        def _set(self, value):
            result = self.ctx.vars.instance
            result = result.properties.customRules
            filters = enumerate(result)
            filters = filter(
                lambda e: e[1].name == self.ctx.args.name,
                filters
            )
            idx = next(filters, [len(result)])[0]
            result[idx] = value
            return

    class WebApplicationFirewallPoliciesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/ApplicationGatewayWebApplicationFirewallPolicies/{policyName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "policyName", self.ctx.args.policy_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-10-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _CreateHelper._build_schema_web_application_firewall_policy_read(cls._schema_on_200)

            return cls._schema_on_200

    class WebApplicationFirewallPoliciesCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200, 201]:
                return self.on_200_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/ApplicationGatewayWebApplicationFirewallPolicies/{policyName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "policyName", self.ctx.args.policy_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-10-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_web_application_firewall_policy_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceCreateByJson(AAZJsonInstanceCreateOperation):

        def __call__(self, *args, **kwargs):
            self.ctx.selectors.subresource.set(self._create_instance())

        def _create_instance(self):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType
            )
            _builder.set_prop("action", AAZStrType, ".action", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("groupByUserSession", AAZListType, ".group_by_user_session")
            _builder.set_prop("matchConditions", AAZListType, ".match_conditions", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("name", AAZStrType, ".name")
            _builder.set_prop("priority", AAZIntType, ".priority", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("rateLimitDuration", AAZStrType, ".rate_limit_duration")
            _builder.set_prop("rateLimitThreshold", AAZIntType, ".rate_limit_threshold")
            _builder.set_prop("ruleType", AAZStrType, ".rule_type", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("state", AAZStrType, ".state")

            group_by_user_session = _builder.get(".groupByUserSession")
            if group_by_user_session is not None:
                group_by_user_session.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".groupByUserSession[]")
            if _elements is not None:
                _elements.set_prop("groupByVariables", AAZListType, ".group_by_variables", typ_kwargs={"flags": {"required": True}})

            group_by_variables = _builder.get(".groupByUserSession[].groupByVariables")
            if group_by_variables is not None:
                group_by_variables.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".groupByUserSession[].groupByVariables[]")
            if _elements is not None:
                _elements.set_prop("variableName", AAZStrType, ".variable_name", typ_kwargs={"flags": {"required": True}})

            match_conditions = _builder.get(".matchConditions")
            if match_conditions is not None:
                match_conditions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".matchConditions[]")
            if _elements is not None:
                _elements.set_prop("matchValues", AAZListType, ".values", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("matchVariables", AAZListType, ".variables", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("negationConditon", AAZBoolType, ".negate")
                _elements.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("transforms", AAZListType, ".transforms")

            match_values = _builder.get(".matchConditions[].matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            match_variables = _builder.get(".matchConditions[].matchVariables")
            if match_variables is not None:
                match_variables.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".matchConditions[].matchVariables[]")
            if _elements is not None:
                _elements.set_prop("selector", AAZStrType, ".selector")
                _elements.set_prop("variableName", AAZStrType, ".variable_name", typ_kwargs={"flags": {"required": True}})

            transforms = _builder.get(".matchConditions[].transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            return _instance_value


class _CreateHelper:
    """Helper class for Create"""

    _schema_application_gateway_backend_address_pool_read = None

    @classmethod
    def _build_schema_application_gateway_backend_address_pool_read(cls, _schema):
        if cls._schema_application_gateway_backend_address_pool_read is not None:
            _schema.etag = cls._schema_application_gateway_backend_address_pool_read.etag
            _schema.id = cls._schema_application_gateway_backend_address_pool_read.id
            _schema.name = cls._schema_application_gateway_backend_address_pool_read.name
            _schema.properties = cls._schema_application_gateway_backend_address_pool_read.properties
            _schema.type = cls._schema_application_gateway_backend_address_pool_read.type
            return

        cls._schema_application_gateway_backend_address_pool_read = _schema_application_gateway_backend_address_pool_read = AAZObjectType()

        application_gateway_backend_address_pool_read = _schema_application_gateway_backend_address_pool_read
        application_gateway_backend_address_pool_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        application_gateway_backend_address_pool_read.id = AAZStrType()
        application_gateway_backend_address_pool_read.name = AAZStrType()
        application_gateway_backend_address_pool_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        application_gateway_backend_address_pool_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_application_gateway_backend_address_pool_read.properties
        properties.backend_addresses = AAZListType(
            serialized_name="backendAddresses",
        )
        properties.backend_ip_configurations = AAZListType(
            serialized_name="backendIPConfigurations",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        backend_addresses = _schema_application_gateway_backend_address_pool_read.properties.backend_addresses
        backend_addresses.Element = AAZObjectType()

        _element = _schema_application_gateway_backend_address_pool_read.properties.backend_addresses.Element
        _element.fqdn = AAZStrType()
        _element.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )

        backend_ip_configurations = _schema_application_gateway_backend_address_pool_read.properties.backend_ip_configurations
        backend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(backend_ip_configurations.Element)

        _schema.etag = cls._schema_application_gateway_backend_address_pool_read.etag
        _schema.id = cls._schema_application_gateway_backend_address_pool_read.id
        _schema.name = cls._schema_application_gateway_backend_address_pool_read.name
        _schema.properties = cls._schema_application_gateway_backend_address_pool_read.properties
        _schema.type = cls._schema_application_gateway_backend_address_pool_read.type

    _schema_application_gateway_custom_error_read = None

    @classmethod
    def _build_schema_application_gateway_custom_error_read(cls, _schema):
        if cls._schema_application_gateway_custom_error_read is not None:
            _schema.custom_error_page_url = cls._schema_application_gateway_custom_error_read.custom_error_page_url
            _schema.status_code = cls._schema_application_gateway_custom_error_read.status_code
            return

        cls._schema_application_gateway_custom_error_read = _schema_application_gateway_custom_error_read = AAZObjectType()

        application_gateway_custom_error_read = _schema_application_gateway_custom_error_read
        application_gateway_custom_error_read.custom_error_page_url = AAZStrType(
            serialized_name="customErrorPageUrl",
        )
        application_gateway_custom_error_read.status_code = AAZStrType(
            serialized_name="statusCode",
        )

        _schema.custom_error_page_url = cls._schema_application_gateway_custom_error_read.custom_error_page_url
        _schema.status_code = cls._schema_application_gateway_custom_error_read.status_code

    _schema_application_gateway_header_configuration_read = None

    @classmethod
    def _build_schema_application_gateway_header_configuration_read(cls, _schema):
        if cls._schema_application_gateway_header_configuration_read is not None:
            _schema.header_name = cls._schema_application_gateway_header_configuration_read.header_name
            _schema.header_value = cls._schema_application_gateway_header_configuration_read.header_value
            _schema.header_value_matcher = cls._schema_application_gateway_header_configuration_read.header_value_matcher
            return

        cls._schema_application_gateway_header_configuration_read = _schema_application_gateway_header_configuration_read = AAZObjectType()

        application_gateway_header_configuration_read = _schema_application_gateway_header_configuration_read
        application_gateway_header_configuration_read.header_name = AAZStrType(
            serialized_name="headerName",
        )
        application_gateway_header_configuration_read.header_value = AAZStrType(
            serialized_name="headerValue",
        )
        application_gateway_header_configuration_read.header_value_matcher = AAZObjectType(
            serialized_name="headerValueMatcher",
        )

        header_value_matcher = _schema_application_gateway_header_configuration_read.header_value_matcher
        header_value_matcher.ignore_case = AAZBoolType(
            serialized_name="ignoreCase",
        )
        header_value_matcher.negate = AAZBoolType()
        header_value_matcher.pattern = AAZStrType()

        _schema.header_name = cls._schema_application_gateway_header_configuration_read.header_name
        _schema.header_value = cls._schema_application_gateway_header_configuration_read.header_value
        _schema.header_value_matcher = cls._schema_application_gateway_header_configuration_read.header_value_matcher

    _schema_application_gateway_ip_configuration_read = None

    @classmethod
    def _build_schema_application_gateway_ip_configuration_read(cls, _schema):
        if cls._schema_application_gateway_ip_configuration_read is not None:
            _schema.etag = cls._schema_application_gateway_ip_configuration_read.etag
            _schema.id = cls._schema_application_gateway_ip_configuration_read.id
            _schema.name = cls._schema_application_gateway_ip_configuration_read.name
            _schema.properties = cls._schema_application_gateway_ip_configuration_read.properties
            _schema.type = cls._schema_application_gateway_ip_configuration_read.type
            return

        cls._schema_application_gateway_ip_configuration_read = _schema_application_gateway_ip_configuration_read = AAZObjectType()

        application_gateway_ip_configuration_read = _schema_application_gateway_ip_configuration_read
        application_gateway_ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        application_gateway_ip_configuration_read.id = AAZStrType()
        application_gateway_ip_configuration_read.name = AAZStrType()
        application_gateway_ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        application_gateway_ip_configuration_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_application_gateway_ip_configuration_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)

        _schema.etag = cls._schema_application_gateway_ip_configuration_read.etag
        _schema.id = cls._schema_application_gateway_ip_configuration_read.id
        _schema.name = cls._schema_application_gateway_ip_configuration_read.name
        _schema.properties = cls._schema_application_gateway_ip_configuration_read.properties
        _schema.type = cls._schema_application_gateway_ip_configuration_read.type

    _schema_application_gateway_ssl_policy_read = None

    @classmethod
    def _build_schema_application_gateway_ssl_policy_read(cls, _schema):
        if cls._schema_application_gateway_ssl_policy_read is not None:
            _schema.cipher_suites = cls._schema_application_gateway_ssl_policy_read.cipher_suites
            _schema.disabled_ssl_protocols = cls._schema_application_gateway_ssl_policy_read.disabled_ssl_protocols
            _schema.min_protocol_version = cls._schema_application_gateway_ssl_policy_read.min_protocol_version
            _schema.policy_name = cls._schema_application_gateway_ssl_policy_read.policy_name
            _schema.policy_type = cls._schema_application_gateway_ssl_policy_read.policy_type
            return

        cls._schema_application_gateway_ssl_policy_read = _schema_application_gateway_ssl_policy_read = AAZObjectType()

        application_gateway_ssl_policy_read = _schema_application_gateway_ssl_policy_read
        application_gateway_ssl_policy_read.cipher_suites = AAZListType(
            serialized_name="cipherSuites",
        )
        application_gateway_ssl_policy_read.disabled_ssl_protocols = AAZListType(
            serialized_name="disabledSslProtocols",
        )
        application_gateway_ssl_policy_read.min_protocol_version = AAZStrType(
            serialized_name="minProtocolVersion",
        )
        application_gateway_ssl_policy_read.policy_name = AAZStrType(
            serialized_name="policyName",
        )
        application_gateway_ssl_policy_read.policy_type = AAZStrType(
            serialized_name="policyType",
        )

        cipher_suites = _schema_application_gateway_ssl_policy_read.cipher_suites
        cipher_suites.Element = AAZStrType()

        disabled_ssl_protocols = _schema_application_gateway_ssl_policy_read.disabled_ssl_protocols
        disabled_ssl_protocols.Element = AAZStrType()

        _schema.cipher_suites = cls._schema_application_gateway_ssl_policy_read.cipher_suites
        _schema.disabled_ssl_protocols = cls._schema_application_gateway_ssl_policy_read.disabled_ssl_protocols
        _schema.min_protocol_version = cls._schema_application_gateway_ssl_policy_read.min_protocol_version
        _schema.policy_name = cls._schema_application_gateway_ssl_policy_read.policy_name
        _schema.policy_type = cls._schema_application_gateway_ssl_policy_read.policy_type

    _schema_application_security_group_read = None

    @classmethod
    def _build_schema_application_security_group_read(cls, _schema):
        if cls._schema_application_security_group_read is not None:
            _schema.etag = cls._schema_application_security_group_read.etag
            _schema.id = cls._schema_application_security_group_read.id
            _schema.location = cls._schema_application_security_group_read.location
            _schema.name = cls._schema_application_security_group_read.name
            _schema.properties = cls._schema_application_security_group_read.properties
            _schema.tags = cls._schema_application_security_group_read.tags
            _schema.type = cls._schema_application_security_group_read.type
            return

        cls._schema_application_security_group_read = _schema_application_security_group_read = AAZObjectType()

        application_security_group_read = _schema_application_security_group_read
        application_security_group_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        application_security_group_read.id = AAZStrType()
        application_security_group_read.location = AAZStrType()
        application_security_group_read.name = AAZStrType(
            flags={"read_only": True},
        )
        application_security_group_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        application_security_group_read.tags = AAZDictType()
        application_security_group_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_application_security_group_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )

        tags = _schema_application_security_group_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_application_security_group_read.etag
        _schema.id = cls._schema_application_security_group_read.id
        _schema.location = cls._schema_application_security_group_read.location
        _schema.name = cls._schema_application_security_group_read.name
        _schema.properties = cls._schema_application_security_group_read.properties
        _schema.tags = cls._schema_application_security_group_read.tags
        _schema.type = cls._schema_application_security_group_read.type

    _schema_exclusion_managed_rule_set_read = None

    @classmethod
    def _build_schema_exclusion_managed_rule_set_read(cls, _schema):
        if cls._schema_exclusion_managed_rule_set_read is not None:
            _schema.rule_groups = cls._schema_exclusion_managed_rule_set_read.rule_groups
            _schema.rule_set_type = cls._schema_exclusion_managed_rule_set_read.rule_set_type
            _schema.rule_set_version = cls._schema_exclusion_managed_rule_set_read.rule_set_version
            return

        cls._schema_exclusion_managed_rule_set_read = _schema_exclusion_managed_rule_set_read = AAZObjectType()

        exclusion_managed_rule_set_read = _schema_exclusion_managed_rule_set_read
        exclusion_managed_rule_set_read.rule_groups = AAZListType(
            serialized_name="ruleGroups",
        )
        exclusion_managed_rule_set_read.rule_set_type = AAZStrType(
            serialized_name="ruleSetType",
            flags={"required": True},
        )
        exclusion_managed_rule_set_read.rule_set_version = AAZStrType(
            serialized_name="ruleSetVersion",
            flags={"required": True},
        )

        rule_groups = _schema_exclusion_managed_rule_set_read.rule_groups
        rule_groups.Element = AAZObjectType()

        _element = _schema_exclusion_managed_rule_set_read.rule_groups.Element
        _element.rule_group_name = AAZStrType(
            serialized_name="ruleGroupName",
            flags={"required": True},
        )
        _element.rules = AAZListType()

        rules = _schema_exclusion_managed_rule_set_read.rule_groups.Element.rules
        rules.Element = AAZObjectType()

        _element = _schema_exclusion_managed_rule_set_read.rule_groups.Element.rules.Element
        _element.rule_id = AAZStrType(
            serialized_name="ruleId",
            flags={"required": True},
        )

        _schema.rule_groups = cls._schema_exclusion_managed_rule_set_read.rule_groups
        _schema.rule_set_type = cls._schema_exclusion_managed_rule_set_read.rule_set_type
        _schema.rule_set_version = cls._schema_exclusion_managed_rule_set_read.rule_set_version

    _schema_extended_location_read = None

    @classmethod
    def _build_schema_extended_location_read(cls, _schema):
        if cls._schema_extended_location_read is not None:
            _schema.name = cls._schema_extended_location_read.name
            _schema.type = cls._schema_extended_location_read.type
            return

        cls._schema_extended_location_read = _schema_extended_location_read = AAZObjectType()

        extended_location_read = _schema_extended_location_read
        extended_location_read.name = AAZStrType()
        extended_location_read.type = AAZStrType()

        _schema.name = cls._schema_extended_location_read.name
        _schema.type = cls._schema_extended_location_read.type

    _schema_frontend_ip_configuration_read = None

    @classmethod
    def _build_schema_frontend_ip_configuration_read(cls, _schema):
        if cls._schema_frontend_ip_configuration_read is not None:
            _schema.etag = cls._schema_frontend_ip_configuration_read.etag
            _schema.id = cls._schema_frontend_ip_configuration_read.id
            _schema.name = cls._schema_frontend_ip_configuration_read.name
            _schema.properties = cls._schema_frontend_ip_configuration_read.properties
            _schema.type = cls._schema_frontend_ip_configuration_read.type
            _schema.zones = cls._schema_frontend_ip_configuration_read.zones
            return

        cls._schema_frontend_ip_configuration_read = _schema_frontend_ip_configuration_read = AAZObjectType()

        frontend_ip_configuration_read = _schema_frontend_ip_configuration_read
        frontend_ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        frontend_ip_configuration_read.id = AAZStrType()
        frontend_ip_configuration_read.name = AAZStrType()
        frontend_ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        frontend_ip_configuration_read.type = AAZStrType(
            flags={"read_only": True},
        )
        frontend_ip_configuration_read.zones = AAZListType()

        properties = _schema_frontend_ip_configuration_read.properties
        properties.gateway_load_balancer = AAZObjectType(
            serialized_name="gatewayLoadBalancer",
        )
        cls._build_schema_sub_resource_read(properties.gateway_load_balancer)
        properties.inbound_nat_pools = AAZListType(
            serialized_name="inboundNatPools",
            flags={"read_only": True},
        )
        properties.inbound_nat_rules = AAZListType(
            serialized_name="inboundNatRules",
            flags={"read_only": True},
        )
        properties.load_balancing_rules = AAZListType(
            serialized_name="loadBalancingRules",
            flags={"read_only": True},
        )
        properties.outbound_rules = AAZListType(
            serialized_name="outboundRules",
            flags={"read_only": True},
        )
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.public_ip_prefix = AAZObjectType(
            serialized_name="publicIPPrefix",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_prefix)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        inbound_nat_pools = _schema_frontend_ip_configuration_read.properties.inbound_nat_pools
        inbound_nat_pools.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_pools.Element)

        inbound_nat_rules = _schema_frontend_ip_configuration_read.properties.inbound_nat_rules
        inbound_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_rules.Element)

        load_balancing_rules = _schema_frontend_ip_configuration_read.properties.load_balancing_rules
        load_balancing_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancing_rules.Element)

        outbound_rules = _schema_frontend_ip_configuration_read.properties.outbound_rules
        outbound_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(outbound_rules.Element)

        zones = _schema_frontend_ip_configuration_read.zones
        zones.Element = AAZStrType()

        _schema.etag = cls._schema_frontend_ip_configuration_read.etag
        _schema.id = cls._schema_frontend_ip_configuration_read.id
        _schema.name = cls._schema_frontend_ip_configuration_read.name
        _schema.properties = cls._schema_frontend_ip_configuration_read.properties
        _schema.type = cls._schema_frontend_ip_configuration_read.type
        _schema.zones = cls._schema_frontend_ip_configuration_read.zones

    _schema_ip_configuration_read = None

    @classmethod
    def _build_schema_ip_configuration_read(cls, _schema):
        if cls._schema_ip_configuration_read is not None:
            _schema.etag = cls._schema_ip_configuration_read.etag
            _schema.id = cls._schema_ip_configuration_read.id
            _schema.name = cls._schema_ip_configuration_read.name
            _schema.properties = cls._schema_ip_configuration_read.properties
            return

        cls._schema_ip_configuration_read = _schema_ip_configuration_read = AAZObjectType()

        ip_configuration_read = _schema_ip_configuration_read
        ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        ip_configuration_read.id = AAZStrType()
        ip_configuration_read.name = AAZStrType()
        ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_ip_configuration_read.properties
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        _schema.etag = cls._schema_ip_configuration_read.etag
        _schema.id = cls._schema_ip_configuration_read.id
        _schema.name = cls._schema_ip_configuration_read.name
        _schema.properties = cls._schema_ip_configuration_read.properties

    _schema_managed_service_identity_read = None

    @classmethod
    def _build_schema_managed_service_identity_read(cls, _schema):
        if cls._schema_managed_service_identity_read is not None:
            _schema.principal_id = cls._schema_managed_service_identity_read.principal_id
            _schema.tenant_id = cls._schema_managed_service_identity_read.tenant_id
            _schema.type = cls._schema_managed_service_identity_read.type
            _schema.user_assigned_identities = cls._schema_managed_service_identity_read.user_assigned_identities
            return

        cls._schema_managed_service_identity_read = _schema_managed_service_identity_read = AAZIdentityObjectType()

        managed_service_identity_read = _schema_managed_service_identity_read
        managed_service_identity_read.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        managed_service_identity_read.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        managed_service_identity_read.type = AAZStrType()
        managed_service_identity_read.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_managed_service_identity_read.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_managed_service_identity_read.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        _schema.principal_id = cls._schema_managed_service_identity_read.principal_id
        _schema.tenant_id = cls._schema_managed_service_identity_read.tenant_id
        _schema.type = cls._schema_managed_service_identity_read.type
        _schema.user_assigned_identities = cls._schema_managed_service_identity_read.user_assigned_identities

    _schema_network_interface_ip_configuration_read = None

    @classmethod
    def _build_schema_network_interface_ip_configuration_read(cls, _schema):
        if cls._schema_network_interface_ip_configuration_read is not None:
            _schema.etag = cls._schema_network_interface_ip_configuration_read.etag
            _schema.id = cls._schema_network_interface_ip_configuration_read.id
            _schema.name = cls._schema_network_interface_ip_configuration_read.name
            _schema.properties = cls._schema_network_interface_ip_configuration_read.properties
            _schema.type = cls._schema_network_interface_ip_configuration_read.type
            return

        cls._schema_network_interface_ip_configuration_read = _schema_network_interface_ip_configuration_read = AAZObjectType()

        network_interface_ip_configuration_read = _schema_network_interface_ip_configuration_read
        network_interface_ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_ip_configuration_read.id = AAZStrType()
        network_interface_ip_configuration_read.name = AAZStrType()
        network_interface_ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_ip_configuration_read.type = AAZStrType()

        properties = _schema_network_interface_ip_configuration_read.properties
        properties.application_gateway_backend_address_pools = AAZListType(
            serialized_name="applicationGatewayBackendAddressPools",
        )
        properties.application_security_groups = AAZListType(
            serialized_name="applicationSecurityGroups",
        )
        properties.gateway_load_balancer = AAZObjectType(
            serialized_name="gatewayLoadBalancer",
        )
        cls._build_schema_sub_resource_read(properties.gateway_load_balancer)
        properties.load_balancer_backend_address_pools = AAZListType(
            serialized_name="loadBalancerBackendAddressPools",
        )
        properties.load_balancer_inbound_nat_rules = AAZListType(
            serialized_name="loadBalancerInboundNatRules",
        )
        properties.primary = AAZBoolType()
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_prefix_length = AAZIntType(
            serialized_name="privateIPAddressPrefixLength",
            nullable=True,
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.private_link_connection_properties = AAZObjectType(
            serialized_name="privateLinkConnectionProperties",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)
        properties.virtual_network_taps = AAZListType(
            serialized_name="virtualNetworkTaps",
        )

        application_gateway_backend_address_pools = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools
        application_gateway_backend_address_pools.Element = AAZObjectType()
        cls._build_schema_application_gateway_backend_address_pool_read(application_gateway_backend_address_pools.Element)

        application_security_groups = _schema_network_interface_ip_configuration_read.properties.application_security_groups
        application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(application_security_groups.Element)

        load_balancer_backend_address_pools = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools
        load_balancer_backend_address_pools.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties
        properties.backend_ip_configurations = AAZListType(
            serialized_name="backendIPConfigurations",
            flags={"read_only": True},
        )
        properties.drain_period_in_seconds = AAZIntType(
            serialized_name="drainPeriodInSeconds",
        )
        properties.inbound_nat_rules = AAZListType(
            serialized_name="inboundNatRules",
            flags={"read_only": True},
        )
        properties.load_balancer_backend_addresses = AAZListType(
            serialized_name="loadBalancerBackendAddresses",
        )
        properties.load_balancing_rules = AAZListType(
            serialized_name="loadBalancingRules",
            flags={"read_only": True},
        )
        properties.location = AAZStrType()
        properties.outbound_rule = AAZObjectType(
            serialized_name="outboundRule",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.outbound_rule)
        properties.outbound_rules = AAZListType(
            serialized_name="outboundRules",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.sync_mode = AAZStrType(
            serialized_name="syncMode",
        )
        properties.tunnel_interfaces = AAZListType(
            serialized_name="tunnelInterfaces",
        )
        properties.virtual_network = AAZObjectType(
            serialized_name="virtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.virtual_network)

        backend_ip_configurations = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.backend_ip_configurations
        backend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(backend_ip_configurations.Element)

        inbound_nat_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.inbound_nat_rules
        inbound_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_rules.Element)

        load_balancer_backend_addresses = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses
        load_balancer_backend_addresses.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties
        properties.admin_state = AAZStrType(
            serialized_name="adminState",
        )
        properties.inbound_nat_rules_port_mapping = AAZListType(
            serialized_name="inboundNatRulesPortMapping",
            flags={"read_only": True},
        )
        properties.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )
        properties.load_balancer_frontend_ip_configuration = AAZObjectType(
            serialized_name="loadBalancerFrontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.load_balancer_frontend_ip_configuration)
        properties.network_interface_ip_configuration = AAZObjectType(
            serialized_name="networkInterfaceIPConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.network_interface_ip_configuration)
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)
        properties.virtual_network = AAZObjectType(
            serialized_name="virtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.virtual_network)

        inbound_nat_rules_port_mapping = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties.inbound_nat_rules_port_mapping
        inbound_nat_rules_port_mapping.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties.inbound_nat_rules_port_mapping.Element
        _element.backend_port = AAZIntType(
            serialized_name="backendPort",
        )
        _element.frontend_port = AAZIntType(
            serialized_name="frontendPort",
        )
        _element.inbound_nat_rule_name = AAZStrType(
            serialized_name="inboundNatRuleName",
        )

        load_balancing_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancing_rules
        load_balancing_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancing_rules.Element)

        outbound_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.outbound_rules
        outbound_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(outbound_rules.Element)

        tunnel_interfaces = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.tunnel_interfaces
        tunnel_interfaces.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.tunnel_interfaces.Element
        _element.identifier = AAZIntType()
        _element.port = AAZIntType()
        _element.protocol = AAZStrType()
        _element.type = AAZStrType()

        load_balancer_inbound_nat_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules
        load_balancer_inbound_nat_rules.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules.Element.properties
        properties.backend_address_pool = AAZObjectType(
            serialized_name="backendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.backend_address_pool)
        properties.backend_ip_configuration = AAZObjectType(
            serialized_name="backendIPConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_network_interface_ip_configuration_read(properties.backend_ip_configuration)
        properties.backend_port = AAZIntType(
            serialized_name="backendPort",
        )
        properties.enable_floating_ip = AAZBoolType(
            serialized_name="enableFloatingIP",
        )
        properties.enable_tcp_reset = AAZBoolType(
            serialized_name="enableTcpReset",
        )
        properties.frontend_ip_configuration = AAZObjectType(
            serialized_name="frontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.frontend_ip_configuration)
        properties.frontend_port = AAZIntType(
            serialized_name="frontendPort",
        )
        properties.frontend_port_range_end = AAZIntType(
            serialized_name="frontendPortRangeEnd",
        )
        properties.frontend_port_range_start = AAZIntType(
            serialized_name="frontendPortRangeStart",
        )
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        private_link_connection_properties = _schema_network_interface_ip_configuration_read.properties.private_link_connection_properties
        private_link_connection_properties.fqdns = AAZListType(
            flags={"read_only": True},
        )
        private_link_connection_properties.group_id = AAZStrType(
            serialized_name="groupId",
            flags={"read_only": True},
        )
        private_link_connection_properties.required_member_name = AAZStrType(
            serialized_name="requiredMemberName",
            flags={"read_only": True},
        )

        fqdns = _schema_network_interface_ip_configuration_read.properties.private_link_connection_properties.fqdns
        fqdns.Element = AAZStrType()

        virtual_network_taps = _schema_network_interface_ip_configuration_read.properties.virtual_network_taps
        virtual_network_taps.Element = AAZObjectType()
        cls._build_schema_virtual_network_tap_read(virtual_network_taps.Element)

        _schema.etag = cls._schema_network_interface_ip_configuration_read.etag
        _schema.id = cls._schema_network_interface_ip_configuration_read.id
        _schema.name = cls._schema_network_interface_ip_configuration_read.name
        _schema.properties = cls._schema_network_interface_ip_configuration_read.properties
        _schema.type = cls._schema_network_interface_ip_configuration_read.type

    _schema_network_interface_tap_configuration_read = None

    @classmethod
    def _build_schema_network_interface_tap_configuration_read(cls, _schema):
        if cls._schema_network_interface_tap_configuration_read is not None:
            _schema.etag = cls._schema_network_interface_tap_configuration_read.etag
            _schema.id = cls._schema_network_interface_tap_configuration_read.id
            _schema.name = cls._schema_network_interface_tap_configuration_read.name
            _schema.properties = cls._schema_network_interface_tap_configuration_read.properties
            _schema.type = cls._schema_network_interface_tap_configuration_read.type
            return

        cls._schema_network_interface_tap_configuration_read = _schema_network_interface_tap_configuration_read = AAZObjectType()

        network_interface_tap_configuration_read = _schema_network_interface_tap_configuration_read
        network_interface_tap_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_tap_configuration_read.id = AAZStrType()
        network_interface_tap_configuration_read.name = AAZStrType()
        network_interface_tap_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_tap_configuration_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_tap_configuration_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.virtual_network_tap = AAZObjectType(
            serialized_name="virtualNetworkTap",
        )
        cls._build_schema_virtual_network_tap_read(properties.virtual_network_tap)

        _schema.etag = cls._schema_network_interface_tap_configuration_read.etag
        _schema.id = cls._schema_network_interface_tap_configuration_read.id
        _schema.name = cls._schema_network_interface_tap_configuration_read.name
        _schema.properties = cls._schema_network_interface_tap_configuration_read.properties
        _schema.type = cls._schema_network_interface_tap_configuration_read.type

    _schema_network_interface_read = None

    @classmethod
    def _build_schema_network_interface_read(cls, _schema):
        if cls._schema_network_interface_read is not None:
            _schema.etag = cls._schema_network_interface_read.etag
            _schema.extended_location = cls._schema_network_interface_read.extended_location
            _schema.id = cls._schema_network_interface_read.id
            _schema.location = cls._schema_network_interface_read.location
            _schema.name = cls._schema_network_interface_read.name
            _schema.properties = cls._schema_network_interface_read.properties
            _schema.tags = cls._schema_network_interface_read.tags
            _schema.type = cls._schema_network_interface_read.type
            return

        cls._schema_network_interface_read = _schema_network_interface_read = AAZObjectType()

        network_interface_read = _schema_network_interface_read
        network_interface_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(network_interface_read.extended_location)
        network_interface_read.id = AAZStrType()
        network_interface_read.location = AAZStrType()
        network_interface_read.name = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_read.tags = AAZDictType()
        network_interface_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties
        properties.auxiliary_mode = AAZStrType(
            serialized_name="auxiliaryMode",
        )
        properties.auxiliary_sku = AAZStrType(
            serialized_name="auxiliarySku",
        )
        properties.default_outbound_connectivity_enabled = AAZBoolType(
            serialized_name="defaultOutboundConnectivityEnabled",
            flags={"read_only": True},
        )
        properties.disable_tcp_state_tracking = AAZBoolType(
            serialized_name="disableTcpStateTracking",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.dscp_configuration = AAZObjectType(
            serialized_name="dscpConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.dscp_configuration)
        properties.enable_accelerated_networking = AAZBoolType(
            serialized_name="enableAcceleratedNetworking",
        )
        properties.enable_ip_forwarding = AAZBoolType(
            serialized_name="enableIPForwarding",
        )
        properties.hosted_workloads = AAZListType(
            serialized_name="hostedWorkloads",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.mac_address = AAZStrType(
            serialized_name="macAddress",
            flags={"read_only": True},
        )
        properties.migration_phase = AAZStrType(
            serialized_name="migrationPhase",
        )
        properties.network_security_group = AAZObjectType(
            serialized_name="networkSecurityGroup",
        )
        cls._build_schema_network_security_group_read(properties.network_security_group)
        properties.nic_type = AAZStrType(
            serialized_name="nicType",
        )
        properties.primary = AAZBoolType(
            flags={"read_only": True},
        )
        properties.private_endpoint = AAZObjectType(
            serialized_name="privateEndpoint",
            flags={"read_only": True},
        )
        cls._build_schema_private_endpoint_read(properties.private_endpoint)
        properties.private_link_service = AAZObjectType(
            serialized_name="privateLinkService",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.tap_configurations = AAZListType(
            serialized_name="tapConfigurations",
            flags={"read_only": True},
        )
        properties.virtual_machine = AAZObjectType(
            serialized_name="virtualMachine",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.virtual_machine)
        properties.vnet_encryption_supported = AAZBoolType(
            serialized_name="vnetEncryptionSupported",
            flags={"read_only": True},
        )
        properties.workload_type = AAZStrType(
            serialized_name="workloadType",
        )

        dns_settings = _schema_network_interface_read.properties.dns_settings
        dns_settings.applied_dns_servers = AAZListType(
            serialized_name="appliedDnsServers",
            flags={"read_only": True},
        )
        dns_settings.dns_servers = AAZListType(
            serialized_name="dnsServers",
        )
        dns_settings.internal_dns_name_label = AAZStrType(
            serialized_name="internalDnsNameLabel",
        )
        dns_settings.internal_domain_name_suffix = AAZStrType(
            serialized_name="internalDomainNameSuffix",
            flags={"read_only": True},
        )
        dns_settings.internal_fqdn = AAZStrType(
            serialized_name="internalFqdn",
            flags={"read_only": True},
        )

        applied_dns_servers = _schema_network_interface_read.properties.dns_settings.applied_dns_servers
        applied_dns_servers.Element = AAZStrType()

        dns_servers = _schema_network_interface_read.properties.dns_settings.dns_servers
        dns_servers.Element = AAZStrType()

        hosted_workloads = _schema_network_interface_read.properties.hosted_workloads
        hosted_workloads.Element = AAZStrType()

        ip_configurations = _schema_network_interface_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(ip_configurations.Element)

        private_link_service = _schema_network_interface_read.properties.private_link_service
        private_link_service.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(private_link_service.extended_location)
        private_link_service.id = AAZStrType()
        private_link_service.location = AAZStrType()
        private_link_service.name = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_link_service.tags = AAZDictType()
        private_link_service.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties.private_link_service.properties
        properties.alias = AAZStrType(
            flags={"read_only": True},
        )
        properties.auto_approval = AAZObjectType(
            serialized_name="autoApproval",
        )
        properties.destination_ip_address = AAZStrType(
            serialized_name="destinationIPAddress",
        )
        properties.enable_proxy_protocol = AAZBoolType(
            serialized_name="enableProxyProtocol",
        )
        properties.fqdns = AAZListType()
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.load_balancer_frontend_ip_configurations = AAZListType(
            serialized_name="loadBalancerFrontendIpConfigurations",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.private_endpoint_connections = AAZListType(
            serialized_name="privateEndpointConnections",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.visibility = AAZObjectType()

        auto_approval = _schema_network_interface_read.properties.private_link_service.properties.auto_approval
        auto_approval.subscriptions = AAZListType()

        subscriptions = _schema_network_interface_read.properties.private_link_service.properties.auto_approval.subscriptions
        subscriptions.Element = AAZStrType()

        fqdns = _schema_network_interface_read.properties.private_link_service.properties.fqdns
        fqdns.Element = AAZStrType()

        ip_configurations = _schema_network_interface_read.properties.private_link_service.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_network_interface_read.properties.private_link_service.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties.private_link_service.properties.ip_configurations.Element.properties
        properties.primary = AAZBoolType()
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        load_balancer_frontend_ip_configurations = _schema_network_interface_read.properties.private_link_service.properties.load_balancer_frontend_ip_configurations
        load_balancer_frontend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_frontend_ip_configuration_read(load_balancer_frontend_ip_configurations.Element)

        network_interfaces = _schema_network_interface_read.properties.private_link_service.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        private_endpoint_connections = _schema_network_interface_read.properties.private_link_service.properties.private_endpoint_connections
        private_endpoint_connections.Element = AAZObjectType()

        _element = _schema_network_interface_read.properties.private_link_service.properties.private_endpoint_connections.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties.private_link_service.properties.private_endpoint_connections.Element.properties
        properties.link_identifier = AAZStrType(
            serialized_name="linkIdentifier",
            flags={"read_only": True},
        )
        properties.private_endpoint = AAZObjectType(
            serialized_name="privateEndpoint",
            flags={"read_only": True},
        )
        cls._build_schema_private_endpoint_read(properties.private_endpoint)
        properties.private_endpoint_location = AAZStrType(
            serialized_name="privateEndpointLocation",
            flags={"read_only": True},
        )
        properties.private_link_service_connection_state = AAZObjectType(
            serialized_name="privateLinkServiceConnectionState",
        )
        cls._build_schema_private_link_service_connection_state_read(properties.private_link_service_connection_state)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        visibility = _schema_network_interface_read.properties.private_link_service.properties.visibility
        visibility.subscriptions = AAZListType()

        subscriptions = _schema_network_interface_read.properties.private_link_service.properties.visibility.subscriptions
        subscriptions.Element = AAZStrType()

        tags = _schema_network_interface_read.properties.private_link_service.tags
        tags.Element = AAZStrType()

        tap_configurations = _schema_network_interface_read.properties.tap_configurations
        tap_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_tap_configuration_read(tap_configurations.Element)

        tags = _schema_network_interface_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_network_interface_read.etag
        _schema.extended_location = cls._schema_network_interface_read.extended_location
        _schema.id = cls._schema_network_interface_read.id
        _schema.location = cls._schema_network_interface_read.location
        _schema.name = cls._schema_network_interface_read.name
        _schema.properties = cls._schema_network_interface_read.properties
        _schema.tags = cls._schema_network_interface_read.tags
        _schema.type = cls._schema_network_interface_read.type

    _schema_network_security_group_read = None

    @classmethod
    def _build_schema_network_security_group_read(cls, _schema):
        if cls._schema_network_security_group_read is not None:
            _schema.etag = cls._schema_network_security_group_read.etag
            _schema.id = cls._schema_network_security_group_read.id
            _schema.location = cls._schema_network_security_group_read.location
            _schema.name = cls._schema_network_security_group_read.name
            _schema.properties = cls._schema_network_security_group_read.properties
            _schema.tags = cls._schema_network_security_group_read.tags
            _schema.type = cls._schema_network_security_group_read.type
            return

        cls._schema_network_security_group_read = _schema_network_security_group_read = AAZObjectType()

        network_security_group_read = _schema_network_security_group_read
        network_security_group_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_security_group_read.id = AAZStrType()
        network_security_group_read.location = AAZStrType()
        network_security_group_read.name = AAZStrType(
            flags={"read_only": True},
        )
        network_security_group_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_security_group_read.tags = AAZDictType()
        network_security_group_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_security_group_read.properties
        properties.default_security_rules = AAZListType(
            serialized_name="defaultSecurityRules",
            flags={"read_only": True},
        )
        properties.flow_logs = AAZListType(
            serialized_name="flowLogs",
            flags={"read_only": True},
        )
        properties.flush_connection = AAZBoolType(
            serialized_name="flushConnection",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.security_rules = AAZListType(
            serialized_name="securityRules",
        )
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        default_security_rules = _schema_network_security_group_read.properties.default_security_rules
        default_security_rules.Element = AAZObjectType()
        cls._build_schema_security_rule_read(default_security_rules.Element)

        flow_logs = _schema_network_security_group_read.properties.flow_logs
        flow_logs.Element = AAZObjectType()

        _element = _schema_network_security_group_read.properties.flow_logs.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.identity = AAZIdentityObjectType()
        cls._build_schema_managed_service_identity_read(_element.identity)
        _element.location = AAZStrType()
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_security_group_read.properties.flow_logs.Element.properties
        properties.enabled = AAZBoolType()
        properties.enabled_filtering_criteria = AAZStrType(
            serialized_name="enabledFilteringCriteria",
        )
        properties.flow_analytics_configuration = AAZObjectType(
            serialized_name="flowAnalyticsConfiguration",
        )
        properties.format = AAZObjectType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.retention_policy = AAZObjectType(
            serialized_name="retentionPolicy",
        )
        properties.storage_id = AAZStrType(
            serialized_name="storageId",
            flags={"required": True},
        )
        properties.target_resource_guid = AAZStrType(
            serialized_name="targetResourceGuid",
            flags={"read_only": True},
        )
        properties.target_resource_id = AAZStrType(
            serialized_name="targetResourceId",
            flags={"required": True},
        )

        flow_analytics_configuration = _schema_network_security_group_read.properties.flow_logs.Element.properties.flow_analytics_configuration
        flow_analytics_configuration.network_watcher_flow_analytics_configuration = AAZObjectType(
            serialized_name="networkWatcherFlowAnalyticsConfiguration",
        )

        network_watcher_flow_analytics_configuration = _schema_network_security_group_read.properties.flow_logs.Element.properties.flow_analytics_configuration.network_watcher_flow_analytics_configuration
        network_watcher_flow_analytics_configuration.enabled = AAZBoolType()
        network_watcher_flow_analytics_configuration.traffic_analytics_interval = AAZIntType(
            serialized_name="trafficAnalyticsInterval",
        )
        network_watcher_flow_analytics_configuration.workspace_id = AAZStrType(
            serialized_name="workspaceId",
        )
        network_watcher_flow_analytics_configuration.workspace_region = AAZStrType(
            serialized_name="workspaceRegion",
        )
        network_watcher_flow_analytics_configuration.workspace_resource_id = AAZStrType(
            serialized_name="workspaceResourceId",
        )

        format = _schema_network_security_group_read.properties.flow_logs.Element.properties.format
        format.type = AAZStrType()
        format.version = AAZIntType()

        retention_policy = _schema_network_security_group_read.properties.flow_logs.Element.properties.retention_policy
        retention_policy.days = AAZIntType()
        retention_policy.enabled = AAZBoolType()

        tags = _schema_network_security_group_read.properties.flow_logs.Element.tags
        tags.Element = AAZStrType()

        network_interfaces = _schema_network_security_group_read.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        security_rules = _schema_network_security_group_read.properties.security_rules
        security_rules.Element = AAZObjectType()
        cls._build_schema_security_rule_read(security_rules.Element)

        subnets = _schema_network_security_group_read.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_network_security_group_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_network_security_group_read.etag
        _schema.id = cls._schema_network_security_group_read.id
        _schema.location = cls._schema_network_security_group_read.location
        _schema.name = cls._schema_network_security_group_read.name
        _schema.properties = cls._schema_network_security_group_read.properties
        _schema.tags = cls._schema_network_security_group_read.tags
        _schema.type = cls._schema_network_security_group_read.type

    _schema_private_endpoint_read = None

    @classmethod
    def _build_schema_private_endpoint_read(cls, _schema):
        if cls._schema_private_endpoint_read is not None:
            _schema.etag = cls._schema_private_endpoint_read.etag
            _schema.extended_location = cls._schema_private_endpoint_read.extended_location
            _schema.id = cls._schema_private_endpoint_read.id
            _schema.location = cls._schema_private_endpoint_read.location
            _schema.name = cls._schema_private_endpoint_read.name
            _schema.properties = cls._schema_private_endpoint_read.properties
            _schema.tags = cls._schema_private_endpoint_read.tags
            _schema.type = cls._schema_private_endpoint_read.type
            return

        cls._schema_private_endpoint_read = _schema_private_endpoint_read = AAZObjectType(
            flags={"read_only": True}
        )

        private_endpoint_read = _schema_private_endpoint_read
        private_endpoint_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_endpoint_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(private_endpoint_read.extended_location)
        private_endpoint_read.id = AAZStrType()
        private_endpoint_read.location = AAZStrType()
        private_endpoint_read.name = AAZStrType(
            flags={"read_only": True},
        )
        private_endpoint_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_endpoint_read.tags = AAZDictType()
        private_endpoint_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_endpoint_read.properties
        properties.application_security_groups = AAZListType(
            serialized_name="applicationSecurityGroups",
        )
        properties.custom_dns_configs = AAZListType(
            serialized_name="customDnsConfigs",
        )
        properties.custom_network_interface_name = AAZStrType(
            serialized_name="customNetworkInterfaceName",
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.manual_private_link_service_connections = AAZListType(
            serialized_name="manualPrivateLinkServiceConnections",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.private_link_service_connections = AAZListType(
            serialized_name="privateLinkServiceConnections",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        application_security_groups = _schema_private_endpoint_read.properties.application_security_groups
        application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(application_security_groups.Element)

        custom_dns_configs = _schema_private_endpoint_read.properties.custom_dns_configs
        custom_dns_configs.Element = AAZObjectType()

        _element = _schema_private_endpoint_read.properties.custom_dns_configs.Element
        _element.fqdn = AAZStrType()
        _element.ip_addresses = AAZListType(
            serialized_name="ipAddresses",
        )

        ip_addresses = _schema_private_endpoint_read.properties.custom_dns_configs.Element.ip_addresses
        ip_addresses.Element = AAZStrType()

        ip_configurations = _schema_private_endpoint_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_private_endpoint_read.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_endpoint_read.properties.ip_configurations.Element.properties
        properties.group_id = AAZStrType(
            serialized_name="groupId",
        )
        properties.member_name = AAZStrType(
            serialized_name="memberName",
        )
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )

        manual_private_link_service_connections = _schema_private_endpoint_read.properties.manual_private_link_service_connections
        manual_private_link_service_connections.Element = AAZObjectType()
        cls._build_schema_private_link_service_connection_read(manual_private_link_service_connections.Element)

        network_interfaces = _schema_private_endpoint_read.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        private_link_service_connections = _schema_private_endpoint_read.properties.private_link_service_connections
        private_link_service_connections.Element = AAZObjectType()
        cls._build_schema_private_link_service_connection_read(private_link_service_connections.Element)

        tags = _schema_private_endpoint_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_private_endpoint_read.etag
        _schema.extended_location = cls._schema_private_endpoint_read.extended_location
        _schema.id = cls._schema_private_endpoint_read.id
        _schema.location = cls._schema_private_endpoint_read.location
        _schema.name = cls._schema_private_endpoint_read.name
        _schema.properties = cls._schema_private_endpoint_read.properties
        _schema.tags = cls._schema_private_endpoint_read.tags
        _schema.type = cls._schema_private_endpoint_read.type

    _schema_private_link_service_connection_state_read = None

    @classmethod
    def _build_schema_private_link_service_connection_state_read(cls, _schema):
        if cls._schema_private_link_service_connection_state_read is not None:
            _schema.actions_required = cls._schema_private_link_service_connection_state_read.actions_required
            _schema.description = cls._schema_private_link_service_connection_state_read.description
            _schema.status = cls._schema_private_link_service_connection_state_read.status
            return

        cls._schema_private_link_service_connection_state_read = _schema_private_link_service_connection_state_read = AAZObjectType()

        private_link_service_connection_state_read = _schema_private_link_service_connection_state_read
        private_link_service_connection_state_read.actions_required = AAZStrType(
            serialized_name="actionsRequired",
        )
        private_link_service_connection_state_read.description = AAZStrType()
        private_link_service_connection_state_read.status = AAZStrType()

        _schema.actions_required = cls._schema_private_link_service_connection_state_read.actions_required
        _schema.description = cls._schema_private_link_service_connection_state_read.description
        _schema.status = cls._schema_private_link_service_connection_state_read.status

    _schema_private_link_service_connection_read = None

    @classmethod
    def _build_schema_private_link_service_connection_read(cls, _schema):
        if cls._schema_private_link_service_connection_read is not None:
            _schema.etag = cls._schema_private_link_service_connection_read.etag
            _schema.id = cls._schema_private_link_service_connection_read.id
            _schema.name = cls._schema_private_link_service_connection_read.name
            _schema.properties = cls._schema_private_link_service_connection_read.properties
            _schema.type = cls._schema_private_link_service_connection_read.type
            return

        cls._schema_private_link_service_connection_read = _schema_private_link_service_connection_read = AAZObjectType()

        private_link_service_connection_read = _schema_private_link_service_connection_read
        private_link_service_connection_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service_connection_read.id = AAZStrType()
        private_link_service_connection_read.name = AAZStrType()
        private_link_service_connection_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_link_service_connection_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_link_service_connection_read.properties
        properties.group_ids = AAZListType(
            serialized_name="groupIds",
        )
        properties.private_link_service_connection_state = AAZObjectType(
            serialized_name="privateLinkServiceConnectionState",
        )
        cls._build_schema_private_link_service_connection_state_read(properties.private_link_service_connection_state)
        properties.private_link_service_id = AAZStrType(
            serialized_name="privateLinkServiceId",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.request_message = AAZStrType(
            serialized_name="requestMessage",
        )

        group_ids = _schema_private_link_service_connection_read.properties.group_ids
        group_ids.Element = AAZStrType()

        _schema.etag = cls._schema_private_link_service_connection_read.etag
        _schema.id = cls._schema_private_link_service_connection_read.id
        _schema.name = cls._schema_private_link_service_connection_read.name
        _schema.properties = cls._schema_private_link_service_connection_read.properties
        _schema.type = cls._schema_private_link_service_connection_read.type

    _schema_public_ip_address_read = None

    @classmethod
    def _build_schema_public_ip_address_read(cls, _schema):
        if cls._schema_public_ip_address_read is not None:
            _schema.etag = cls._schema_public_ip_address_read.etag
            _schema.extended_location = cls._schema_public_ip_address_read.extended_location
            _schema.id = cls._schema_public_ip_address_read.id
            _schema.location = cls._schema_public_ip_address_read.location
            _schema.name = cls._schema_public_ip_address_read.name
            _schema.properties = cls._schema_public_ip_address_read.properties
            _schema.sku = cls._schema_public_ip_address_read.sku
            _schema.tags = cls._schema_public_ip_address_read.tags
            _schema.type = cls._schema_public_ip_address_read.type
            _schema.zones = cls._schema_public_ip_address_read.zones
            return

        cls._schema_public_ip_address_read = _schema_public_ip_address_read = AAZObjectType()

        public_ip_address_read = _schema_public_ip_address_read
        public_ip_address_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(public_ip_address_read.extended_location)
        public_ip_address_read.id = AAZStrType()
        public_ip_address_read.location = AAZStrType()
        public_ip_address_read.name = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        public_ip_address_read.sku = AAZObjectType()
        public_ip_address_read.tags = AAZDictType()
        public_ip_address_read.type = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.zones = AAZListType()

        properties = _schema_public_ip_address_read.properties
        properties.ddos_settings = AAZObjectType(
            serialized_name="ddosSettings",
        )
        properties.delete_option = AAZStrType(
            serialized_name="deleteOption",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )
        properties.ip_configuration = AAZObjectType(
            serialized_name="ipConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_ip_configuration_read(properties.ip_configuration)
        properties.ip_tags = AAZListType(
            serialized_name="ipTags",
        )
        properties.linked_public_ip_address = AAZObjectType(
            serialized_name="linkedPublicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.linked_public_ip_address)
        properties.migration_phase = AAZStrType(
            serialized_name="migrationPhase",
        )
        properties.nat_gateway = AAZObjectType(
            serialized_name="natGateway",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address_version = AAZStrType(
            serialized_name="publicIPAddressVersion",
        )
        properties.public_ip_allocation_method = AAZStrType(
            serialized_name="publicIPAllocationMethod",
        )
        properties.public_ip_prefix = AAZObjectType(
            serialized_name="publicIPPrefix",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_prefix)
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.service_public_ip_address = AAZObjectType(
            serialized_name="servicePublicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.service_public_ip_address)

        ddos_settings = _schema_public_ip_address_read.properties.ddos_settings
        ddos_settings.ddos_protection_plan = AAZObjectType(
            serialized_name="ddosProtectionPlan",
        )
        cls._build_schema_sub_resource_read(ddos_settings.ddos_protection_plan)
        ddos_settings.protection_mode = AAZStrType(
            serialized_name="protectionMode",
        )

        dns_settings = _schema_public_ip_address_read.properties.dns_settings
        dns_settings.domain_name_label = AAZStrType(
            serialized_name="domainNameLabel",
        )
        dns_settings.domain_name_label_scope = AAZStrType(
            serialized_name="domainNameLabelScope",
        )
        dns_settings.fqdn = AAZStrType()
        dns_settings.reverse_fqdn = AAZStrType(
            serialized_name="reverseFqdn",
        )

        ip_tags = _schema_public_ip_address_read.properties.ip_tags
        ip_tags.Element = AAZObjectType()

        _element = _schema_public_ip_address_read.properties.ip_tags.Element
        _element.ip_tag_type = AAZStrType(
            serialized_name="ipTagType",
        )
        _element.tag = AAZStrType()

        nat_gateway = _schema_public_ip_address_read.properties.nat_gateway
        nat_gateway.etag = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.id = AAZStrType()
        nat_gateway.location = AAZStrType()
        nat_gateway.name = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        nat_gateway.sku = AAZObjectType()
        nat_gateway.tags = AAZDictType()
        nat_gateway.type = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.zones = AAZListType()

        properties = _schema_public_ip_address_read.properties.nat_gateway.properties
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_addresses = AAZListType(
            serialized_name="publicIpAddresses",
        )
        properties.public_ip_addresses_v6 = AAZListType(
            serialized_name="publicIpAddressesV6",
        )
        properties.public_ip_prefixes = AAZListType(
            serialized_name="publicIpPrefixes",
        )
        properties.public_ip_prefixes_v6 = AAZListType(
            serialized_name="publicIpPrefixesV6",
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.source_virtual_network = AAZObjectType(
            serialized_name="sourceVirtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.source_virtual_network)
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        public_ip_addresses = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_addresses
        public_ip_addresses.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_addresses.Element)

        public_ip_addresses_v6 = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_addresses_v6
        public_ip_addresses_v6.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_addresses_v6.Element)

        public_ip_prefixes = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_prefixes
        public_ip_prefixes.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_prefixes.Element)

        public_ip_prefixes_v6 = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_prefixes_v6
        public_ip_prefixes_v6.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_prefixes_v6.Element)

        subnets = _schema_public_ip_address_read.properties.nat_gateway.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(subnets.Element)

        sku = _schema_public_ip_address_read.properties.nat_gateway.sku
        sku.name = AAZStrType()

        tags = _schema_public_ip_address_read.properties.nat_gateway.tags
        tags.Element = AAZStrType()

        zones = _schema_public_ip_address_read.properties.nat_gateway.zones
        zones.Element = AAZStrType()

        sku = _schema_public_ip_address_read.sku
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        tags = _schema_public_ip_address_read.tags
        tags.Element = AAZStrType()

        zones = _schema_public_ip_address_read.zones
        zones.Element = AAZStrType()

        _schema.etag = cls._schema_public_ip_address_read.etag
        _schema.extended_location = cls._schema_public_ip_address_read.extended_location
        _schema.id = cls._schema_public_ip_address_read.id
        _schema.location = cls._schema_public_ip_address_read.location
        _schema.name = cls._schema_public_ip_address_read.name
        _schema.properties = cls._schema_public_ip_address_read.properties
        _schema.sku = cls._schema_public_ip_address_read.sku
        _schema.tags = cls._schema_public_ip_address_read.tags
        _schema.type = cls._schema_public_ip_address_read.type
        _schema.zones = cls._schema_public_ip_address_read.zones

    _schema_security_rule_read = None

    @classmethod
    def _build_schema_security_rule_read(cls, _schema):
        if cls._schema_security_rule_read is not None:
            _schema.etag = cls._schema_security_rule_read.etag
            _schema.id = cls._schema_security_rule_read.id
            _schema.name = cls._schema_security_rule_read.name
            _schema.properties = cls._schema_security_rule_read.properties
            _schema.type = cls._schema_security_rule_read.type
            return

        cls._schema_security_rule_read = _schema_security_rule_read = AAZObjectType()

        security_rule_read = _schema_security_rule_read
        security_rule_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        security_rule_read.id = AAZStrType()
        security_rule_read.name = AAZStrType()
        security_rule_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        security_rule_read.type = AAZStrType()

        properties = _schema_security_rule_read.properties
        properties.access = AAZStrType(
            flags={"required": True},
        )
        properties.description = AAZStrType()
        properties.destination_address_prefix = AAZStrType(
            serialized_name="destinationAddressPrefix",
        )
        properties.destination_address_prefixes = AAZListType(
            serialized_name="destinationAddressPrefixes",
        )
        properties.destination_application_security_groups = AAZListType(
            serialized_name="destinationApplicationSecurityGroups",
        )
        properties.destination_port_range = AAZStrType(
            serialized_name="destinationPortRange",
        )
        properties.destination_port_ranges = AAZListType(
            serialized_name="destinationPortRanges",
        )
        properties.direction = AAZStrType(
            flags={"required": True},
        )
        properties.priority = AAZIntType(
            flags={"required": True},
        )
        properties.protocol = AAZStrType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.source_address_prefix = AAZStrType(
            serialized_name="sourceAddressPrefix",
        )
        properties.source_address_prefixes = AAZListType(
            serialized_name="sourceAddressPrefixes",
        )
        properties.source_application_security_groups = AAZListType(
            serialized_name="sourceApplicationSecurityGroups",
        )
        properties.source_port_range = AAZStrType(
            serialized_name="sourcePortRange",
        )
        properties.source_port_ranges = AAZListType(
            serialized_name="sourcePortRanges",
        )

        destination_address_prefixes = _schema_security_rule_read.properties.destination_address_prefixes
        destination_address_prefixes.Element = AAZStrType()

        destination_application_security_groups = _schema_security_rule_read.properties.destination_application_security_groups
        destination_application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(destination_application_security_groups.Element)

        destination_port_ranges = _schema_security_rule_read.properties.destination_port_ranges
        destination_port_ranges.Element = AAZStrType()

        source_address_prefixes = _schema_security_rule_read.properties.source_address_prefixes
        source_address_prefixes.Element = AAZStrType()

        source_application_security_groups = _schema_security_rule_read.properties.source_application_security_groups
        source_application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(source_application_security_groups.Element)

        source_port_ranges = _schema_security_rule_read.properties.source_port_ranges
        source_port_ranges.Element = AAZStrType()

        _schema.etag = cls._schema_security_rule_read.etag
        _schema.id = cls._schema_security_rule_read.id
        _schema.name = cls._schema_security_rule_read.name
        _schema.properties = cls._schema_security_rule_read.properties
        _schema.type = cls._schema_security_rule_read.type

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_subnet_read = None

    @classmethod
    def _build_schema_subnet_read(cls, _schema):
        if cls._schema_subnet_read is not None:
            _schema.etag = cls._schema_subnet_read.etag
            _schema.id = cls._schema_subnet_read.id
            _schema.name = cls._schema_subnet_read.name
            _schema.properties = cls._schema_subnet_read.properties
            _schema.type = cls._schema_subnet_read.type
            return

        cls._schema_subnet_read = _schema_subnet_read = AAZObjectType()

        subnet_read = _schema_subnet_read
        subnet_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        subnet_read.id = AAZStrType()
        subnet_read.name = AAZStrType()
        subnet_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        subnet_read.type = AAZStrType()

        properties = _schema_subnet_read.properties
        properties.address_prefix = AAZStrType(
            serialized_name="addressPrefix",
        )
        properties.address_prefixes = AAZListType(
            serialized_name="addressPrefixes",
        )
        properties.application_gateway_ip_configurations = AAZListType(
            serialized_name="applicationGatewayIPConfigurations",
        )
        properties.default_outbound_access = AAZBoolType(
            serialized_name="defaultOutboundAccess",
        )
        properties.delegations = AAZListType()
        properties.ip_allocations = AAZListType(
            serialized_name="ipAllocations",
        )
        properties.ip_configuration_profiles = AAZListType(
            serialized_name="ipConfigurationProfiles",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
            flags={"read_only": True},
        )
        properties.ipam_pool_prefix_allocations = AAZListType(
            serialized_name="ipamPoolPrefixAllocations",
        )
        properties.nat_gateway = AAZObjectType(
            serialized_name="natGateway",
        )
        cls._build_schema_sub_resource_read(properties.nat_gateway)
        properties.network_security_group = AAZObjectType(
            serialized_name="networkSecurityGroup",
        )
        cls._build_schema_network_security_group_read(properties.network_security_group)
        properties.private_endpoint_network_policies = AAZStrType(
            serialized_name="privateEndpointNetworkPolicies",
        )
        properties.private_endpoints = AAZListType(
            serialized_name="privateEndpoints",
            flags={"read_only": True},
        )
        properties.private_link_service_network_policies = AAZStrType(
            serialized_name="privateLinkServiceNetworkPolicies",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.purpose = AAZStrType(
            flags={"read_only": True},
        )
        properties.resource_navigation_links = AAZListType(
            serialized_name="resourceNavigationLinks",
            flags={"read_only": True},
        )
        properties.route_table = AAZObjectType(
            serialized_name="routeTable",
        )
        properties.service_association_links = AAZListType(
            serialized_name="serviceAssociationLinks",
            flags={"read_only": True},
        )
        properties.service_endpoint_policies = AAZListType(
            serialized_name="serviceEndpointPolicies",
        )
        properties.service_endpoints = AAZListType(
            serialized_name="serviceEndpoints",
        )
        properties.sharing_scope = AAZStrType(
            serialized_name="sharingScope",
        )

        address_prefixes = _schema_subnet_read.properties.address_prefixes
        address_prefixes.Element = AAZStrType()

        application_gateway_ip_configurations = _schema_subnet_read.properties.application_gateway_ip_configurations
        application_gateway_ip_configurations.Element = AAZObjectType()
        cls._build_schema_application_gateway_ip_configuration_read(application_gateway_ip_configurations.Element)

        delegations = _schema_subnet_read.properties.delegations
        delegations.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.delegations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.delegations.Element.properties
        properties.actions = AAZListType(
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.service_name = AAZStrType(
            serialized_name="serviceName",
        )

        actions = _schema_subnet_read.properties.delegations.Element.properties.actions
        actions.Element = AAZStrType()

        ip_allocations = _schema_subnet_read.properties.ip_allocations
        ip_allocations.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(ip_allocations.Element)

        ip_configuration_profiles = _schema_subnet_read.properties.ip_configuration_profiles
        ip_configuration_profiles.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.ip_configuration_profiles.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.ip_configuration_profiles.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        ip_configurations = _schema_subnet_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()
        cls._build_schema_ip_configuration_read(ip_configurations.Element)

        ipam_pool_prefix_allocations = _schema_subnet_read.properties.ipam_pool_prefix_allocations
        ipam_pool_prefix_allocations.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.ipam_pool_prefix_allocations.Element
        _element.allocated_address_prefixes = AAZListType(
            serialized_name="allocatedAddressPrefixes",
            flags={"read_only": True},
        )
        _element.number_of_ip_addresses = AAZStrType(
            serialized_name="numberOfIpAddresses",
        )
        _element.pool = AAZObjectType(
            flags={"client_flatten": True},
        )

        allocated_address_prefixes = _schema_subnet_read.properties.ipam_pool_prefix_allocations.Element.allocated_address_prefixes
        allocated_address_prefixes.Element = AAZStrType()

        pool = _schema_subnet_read.properties.ipam_pool_prefix_allocations.Element.pool
        pool.id = AAZStrType()

        private_endpoints = _schema_subnet_read.properties.private_endpoints
        private_endpoints.Element = AAZObjectType()
        cls._build_schema_private_endpoint_read(private_endpoints.Element)

        resource_navigation_links = _schema_subnet_read.properties.resource_navigation_links
        resource_navigation_links.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.resource_navigation_links.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.resource_navigation_links.Element.properties
        properties.link = AAZStrType()
        properties.linked_resource_type = AAZStrType(
            serialized_name="linkedResourceType",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        route_table = _schema_subnet_read.properties.route_table
        route_table.etag = AAZStrType(
            flags={"read_only": True},
        )
        route_table.id = AAZStrType()
        route_table.location = AAZStrType()
        route_table.name = AAZStrType(
            flags={"read_only": True},
        )
        route_table.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        route_table.tags = AAZDictType()
        route_table.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.route_table.properties
        properties.disable_bgp_route_propagation = AAZBoolType(
            serialized_name="disableBgpRoutePropagation",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.routes = AAZListType()
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        routes = _schema_subnet_read.properties.route_table.properties.routes
        routes.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.route_table.properties.routes.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.route_table.properties.routes.Element.properties
        properties.address_prefix = AAZStrType(
            serialized_name="addressPrefix",
        )
        properties.has_bgp_override = AAZBoolType(
            serialized_name="hasBgpOverride",
            flags={"read_only": True},
        )
        properties.next_hop_ip_address = AAZStrType(
            serialized_name="nextHopIpAddress",
        )
        properties.next_hop_type = AAZStrType(
            serialized_name="nextHopType",
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        subnets = _schema_subnet_read.properties.route_table.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_subnet_read.properties.route_table.tags
        tags.Element = AAZStrType()

        service_association_links = _schema_subnet_read.properties.service_association_links
        service_association_links.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_association_links.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.service_association_links.Element.properties
        properties.allow_delete = AAZBoolType(
            serialized_name="allowDelete",
        )
        properties.link = AAZStrType()
        properties.linked_resource_type = AAZStrType(
            serialized_name="linkedResourceType",
        )
        properties.locations = AAZListType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        locations = _schema_subnet_read.properties.service_association_links.Element.properties.locations
        locations.Element = AAZStrType()

        service_endpoint_policies = _schema_subnet_read.properties.service_endpoint_policies
        service_endpoint_policies.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoint_policies.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.kind = AAZStrType(
            flags={"read_only": True},
        )
        _element.location = AAZStrType()
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.service_endpoint_policies.Element.properties
        properties.contextual_service_endpoint_policies = AAZListType(
            serialized_name="contextualServiceEndpointPolicies",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.service_alias = AAZStrType(
            serialized_name="serviceAlias",
        )
        properties.service_endpoint_policy_definitions = AAZListType(
            serialized_name="serviceEndpointPolicyDefinitions",
        )
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        contextual_service_endpoint_policies = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.contextual_service_endpoint_policies
        contextual_service_endpoint_policies.Element = AAZStrType()

        service_endpoint_policy_definitions = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions
        service_endpoint_policy_definitions.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element.properties
        properties.description = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.service = AAZStrType()
        properties.service_resources = AAZListType(
            serialized_name="serviceResources",
        )

        service_resources = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element.properties.service_resources
        service_resources.Element = AAZStrType()

        subnets = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_subnet_read.properties.service_endpoint_policies.Element.tags
        tags.Element = AAZStrType()

        service_endpoints = _schema_subnet_read.properties.service_endpoints
        service_endpoints.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoints.Element
        _element.locations = AAZListType()
        _element.network_identifier = AAZObjectType(
            serialized_name="networkIdentifier",
        )
        cls._build_schema_sub_resource_read(_element.network_identifier)
        _element.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        _element.service = AAZStrType()

        locations = _schema_subnet_read.properties.service_endpoints.Element.locations
        locations.Element = AAZStrType()

        _schema.etag = cls._schema_subnet_read.etag
        _schema.id = cls._schema_subnet_read.id
        _schema.name = cls._schema_subnet_read.name
        _schema.properties = cls._schema_subnet_read.properties
        _schema.type = cls._schema_subnet_read.type

    _schema_virtual_network_tap_read = None

    @classmethod
    def _build_schema_virtual_network_tap_read(cls, _schema):
        if cls._schema_virtual_network_tap_read is not None:
            _schema.etag = cls._schema_virtual_network_tap_read.etag
            _schema.id = cls._schema_virtual_network_tap_read.id
            _schema.location = cls._schema_virtual_network_tap_read.location
            _schema.name = cls._schema_virtual_network_tap_read.name
            _schema.properties = cls._schema_virtual_network_tap_read.properties
            _schema.tags = cls._schema_virtual_network_tap_read.tags
            _schema.type = cls._schema_virtual_network_tap_read.type
            return

        cls._schema_virtual_network_tap_read = _schema_virtual_network_tap_read = AAZObjectType()

        virtual_network_tap_read = _schema_virtual_network_tap_read
        virtual_network_tap_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_tap_read.id = AAZStrType()
        virtual_network_tap_read.location = AAZStrType()
        virtual_network_tap_read.name = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_tap_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        virtual_network_tap_read.tags = AAZDictType()
        virtual_network_tap_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_tap_read.properties
        properties.destination_load_balancer_front_end_ip_configuration = AAZObjectType(
            serialized_name="destinationLoadBalancerFrontEndIPConfiguration",
        )
        cls._build_schema_frontend_ip_configuration_read(properties.destination_load_balancer_front_end_ip_configuration)
        properties.destination_network_interface_ip_configuration = AAZObjectType(
            serialized_name="destinationNetworkInterfaceIPConfiguration",
        )
        cls._build_schema_network_interface_ip_configuration_read(properties.destination_network_interface_ip_configuration)
        properties.destination_port = AAZIntType(
            serialized_name="destinationPort",
        )
        properties.network_interface_tap_configurations = AAZListType(
            serialized_name="networkInterfaceTapConfigurations",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )

        network_interface_tap_configurations = _schema_virtual_network_tap_read.properties.network_interface_tap_configurations
        network_interface_tap_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_tap_configuration_read(network_interface_tap_configurations.Element)

        tags = _schema_virtual_network_tap_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_virtual_network_tap_read.etag
        _schema.id = cls._schema_virtual_network_tap_read.id
        _schema.location = cls._schema_virtual_network_tap_read.location
        _schema.name = cls._schema_virtual_network_tap_read.name
        _schema.properties = cls._schema_virtual_network_tap_read.properties
        _schema.tags = cls._schema_virtual_network_tap_read.tags
        _schema.type = cls._schema_virtual_network_tap_read.type

    _schema_web_application_firewall_policy_read = None

    @classmethod
    def _build_schema_web_application_firewall_policy_read(cls, _schema):
        if cls._schema_web_application_firewall_policy_read is not None:
            _schema.etag = cls._schema_web_application_firewall_policy_read.etag
            _schema.id = cls._schema_web_application_firewall_policy_read.id
            _schema.location = cls._schema_web_application_firewall_policy_read.location
            _schema.name = cls._schema_web_application_firewall_policy_read.name
            _schema.properties = cls._schema_web_application_firewall_policy_read.properties
            _schema.tags = cls._schema_web_application_firewall_policy_read.tags
            _schema.type = cls._schema_web_application_firewall_policy_read.type
            return

        cls._schema_web_application_firewall_policy_read = _schema_web_application_firewall_policy_read = AAZObjectType()

        web_application_firewall_policy_read = _schema_web_application_firewall_policy_read
        web_application_firewall_policy_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        web_application_firewall_policy_read.id = AAZStrType()
        web_application_firewall_policy_read.location = AAZStrType()
        web_application_firewall_policy_read.name = AAZStrType(
            flags={"read_only": True},
        )
        web_application_firewall_policy_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        web_application_firewall_policy_read.tags = AAZDictType()
        web_application_firewall_policy_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties
        properties.application_gateway_for_containers = AAZListType(
            serialized_name="applicationGatewayForContainers",
            flags={"read_only": True},
        )
        properties.application_gateways = AAZListType(
            serialized_name="applicationGateways",
            flags={"read_only": True},
        )
        properties.custom_rules = AAZListType(
            serialized_name="customRules",
        )
        properties.http_listeners = AAZListType(
            serialized_name="httpListeners",
            flags={"read_only": True},
        )
        properties.managed_rules = AAZObjectType(
            serialized_name="managedRules",
            flags={"required": True},
        )
        properties.path_based_rules = AAZListType(
            serialized_name="pathBasedRules",
            flags={"read_only": True},
        )
        properties.policy_settings = AAZObjectType(
            serialized_name="policySettings",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_state = AAZStrType(
            serialized_name="resourceState",
            flags={"read_only": True},
        )

        application_gateway_for_containers = _schema_web_application_firewall_policy_read.properties.application_gateway_for_containers
        application_gateway_for_containers.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateway_for_containers.Element
        _element.id = AAZStrType(
            flags={"required": True},
        )

        application_gateways = _schema_web_application_firewall_policy_read.properties.application_gateways
        application_gateways.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.identity = AAZIdentityObjectType()
        cls._build_schema_managed_service_identity_read(_element.identity)
        _element.location = AAZStrType()
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )
        _element.zones = AAZListType()

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties
        properties.authentication_certificates = AAZListType(
            serialized_name="authenticationCertificates",
        )
        properties.autoscale_configuration = AAZObjectType(
            serialized_name="autoscaleConfiguration",
        )
        properties.backend_address_pools = AAZListType(
            serialized_name="backendAddressPools",
        )
        properties.backend_http_settings_collection = AAZListType(
            serialized_name="backendHttpSettingsCollection",
        )
        properties.backend_settings_collection = AAZListType(
            serialized_name="backendSettingsCollection",
        )
        properties.custom_error_configurations = AAZListType(
            serialized_name="customErrorConfigurations",
        )
        properties.default_predefined_ssl_policy = AAZStrType(
            serialized_name="defaultPredefinedSslPolicy",
            flags={"read_only": True},
        )
        properties.enable_fips = AAZBoolType(
            serialized_name="enableFips",
        )
        properties.enable_http2 = AAZBoolType(
            serialized_name="enableHttp2",
        )
        properties.firewall_policy = AAZObjectType(
            serialized_name="firewallPolicy",
        )
        cls._build_schema_sub_resource_read(properties.firewall_policy)
        properties.force_firewall_policy_association = AAZBoolType(
            serialized_name="forceFirewallPolicyAssociation",
        )
        properties.frontend_ip_configurations = AAZListType(
            serialized_name="frontendIPConfigurations",
        )
        properties.frontend_ports = AAZListType(
            serialized_name="frontendPorts",
        )
        properties.gateway_ip_configurations = AAZListType(
            serialized_name="gatewayIPConfigurations",
        )
        properties.global_configuration = AAZObjectType(
            serialized_name="globalConfiguration",
        )
        properties.http_listeners = AAZListType(
            serialized_name="httpListeners",
        )
        properties.listeners = AAZListType()
        properties.load_distribution_policies = AAZListType(
            serialized_name="loadDistributionPolicies",
        )
        properties.operational_state = AAZStrType(
            serialized_name="operationalState",
            flags={"read_only": True},
        )
        properties.private_endpoint_connections = AAZListType(
            serialized_name="privateEndpointConnections",
            flags={"read_only": True},
        )
        properties.private_link_configurations = AAZListType(
            serialized_name="privateLinkConfigurations",
        )
        properties.probes = AAZListType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.redirect_configurations = AAZListType(
            serialized_name="redirectConfigurations",
        )
        properties.request_routing_rules = AAZListType(
            serialized_name="requestRoutingRules",
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.rewrite_rule_sets = AAZListType(
            serialized_name="rewriteRuleSets",
        )
        properties.routing_rules = AAZListType(
            serialized_name="routingRules",
        )
        properties.sku = AAZObjectType()
        properties.ssl_certificates = AAZListType(
            serialized_name="sslCertificates",
        )
        properties.ssl_policy = AAZObjectType(
            serialized_name="sslPolicy",
        )
        cls._build_schema_application_gateway_ssl_policy_read(properties.ssl_policy)
        properties.ssl_profiles = AAZListType(
            serialized_name="sslProfiles",
        )
        properties.trusted_client_certificates = AAZListType(
            serialized_name="trustedClientCertificates",
        )
        properties.trusted_root_certificates = AAZListType(
            serialized_name="trustedRootCertificates",
        )
        properties.url_path_maps = AAZListType(
            serialized_name="urlPathMaps",
        )
        properties.web_application_firewall_configuration = AAZObjectType(
            serialized_name="webApplicationFirewallConfiguration",
        )

        authentication_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.authentication_certificates
        authentication_certificates.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.authentication_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.authentication_certificates.Element.properties
        properties.data = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        autoscale_configuration = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.autoscale_configuration
        autoscale_configuration.max_capacity = AAZIntType(
            serialized_name="maxCapacity",
        )
        autoscale_configuration.min_capacity = AAZIntType(
            serialized_name="minCapacity",
            flags={"required": True},
        )

        backend_address_pools = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_address_pools
        backend_address_pools.Element = AAZObjectType()
        cls._build_schema_application_gateway_backend_address_pool_read(backend_address_pools.Element)

        backend_http_settings_collection = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_http_settings_collection
        backend_http_settings_collection.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_http_settings_collection.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_http_settings_collection.Element.properties
        properties.affinity_cookie_name = AAZStrType(
            serialized_name="affinityCookieName",
        )
        properties.authentication_certificates = AAZListType(
            serialized_name="authenticationCertificates",
        )
        properties.connection_draining = AAZObjectType(
            serialized_name="connectionDraining",
        )
        properties.cookie_based_affinity = AAZStrType(
            serialized_name="cookieBasedAffinity",
        )
        properties.dedicated_backend_connection = AAZBoolType(
            serialized_name="dedicatedBackendConnection",
        )
        properties.host_name = AAZStrType(
            serialized_name="hostName",
        )
        properties.path = AAZStrType()
        properties.pick_host_name_from_backend_address = AAZBoolType(
            serialized_name="pickHostNameFromBackendAddress",
        )
        properties.port = AAZIntType()
        properties.probe = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.probe)
        properties.probe_enabled = AAZBoolType(
            serialized_name="probeEnabled",
        )
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.request_timeout = AAZIntType(
            serialized_name="requestTimeout",
        )
        properties.sni_name = AAZStrType(
            serialized_name="sniName",
        )
        properties.trusted_root_certificates = AAZListType(
            serialized_name="trustedRootCertificates",
        )
        properties.validate_cert_chain_and_expiry = AAZBoolType(
            serialized_name="validateCertChainAndExpiry",
        )
        properties.validate_sni = AAZBoolType(
            serialized_name="validateSNI",
        )

        authentication_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_http_settings_collection.Element.properties.authentication_certificates
        authentication_certificates.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(authentication_certificates.Element)

        connection_draining = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_http_settings_collection.Element.properties.connection_draining
        connection_draining.drain_timeout_in_sec = AAZIntType(
            serialized_name="drainTimeoutInSec",
            flags={"required": True},
        )
        connection_draining.enabled = AAZBoolType(
            flags={"required": True},
        )

        trusted_root_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_http_settings_collection.Element.properties.trusted_root_certificates
        trusted_root_certificates.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(trusted_root_certificates.Element)

        backend_settings_collection = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_settings_collection
        backend_settings_collection.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_settings_collection.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_settings_collection.Element.properties
        properties.host_name = AAZStrType(
            serialized_name="hostName",
        )
        properties.pick_host_name_from_backend_address = AAZBoolType(
            serialized_name="pickHostNameFromBackendAddress",
        )
        properties.port = AAZIntType()
        properties.probe = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.probe)
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.timeout = AAZIntType()
        properties.trusted_root_certificates = AAZListType(
            serialized_name="trustedRootCertificates",
        )

        trusted_root_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.backend_settings_collection.Element.properties.trusted_root_certificates
        trusted_root_certificates.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(trusted_root_certificates.Element)

        custom_error_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.custom_error_configurations
        custom_error_configurations.Element = AAZObjectType()
        cls._build_schema_application_gateway_custom_error_read(custom_error_configurations.Element)

        frontend_ip_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.frontend_ip_configurations
        frontend_ip_configurations.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.frontend_ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.frontend_ip_configurations.Element.properties
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.private_link_configuration = AAZObjectType(
            serialized_name="privateLinkConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.private_link_configuration)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)

        frontend_ports = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.frontend_ports
        frontend_ports.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.frontend_ports.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.frontend_ports.Element.properties
        properties.port = AAZIntType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        gateway_ip_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.gateway_ip_configurations
        gateway_ip_configurations.Element = AAZObjectType()
        cls._build_schema_application_gateway_ip_configuration_read(gateway_ip_configurations.Element)

        global_configuration = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.global_configuration
        global_configuration.enable_request_buffering = AAZBoolType(
            serialized_name="enableRequestBuffering",
        )
        global_configuration.enable_response_buffering = AAZBoolType(
            serialized_name="enableResponseBuffering",
        )

        http_listeners = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.http_listeners
        http_listeners.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.http_listeners.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.http_listeners.Element.properties
        properties.custom_error_configurations = AAZListType(
            serialized_name="customErrorConfigurations",
        )
        properties.firewall_policy = AAZObjectType(
            serialized_name="firewallPolicy",
        )
        cls._build_schema_sub_resource_read(properties.firewall_policy)
        properties.frontend_ip_configuration = AAZObjectType(
            serialized_name="frontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.frontend_ip_configuration)
        properties.frontend_port = AAZObjectType(
            serialized_name="frontendPort",
        )
        cls._build_schema_sub_resource_read(properties.frontend_port)
        properties.host_name = AAZStrType(
            serialized_name="hostName",
        )
        properties.host_names = AAZListType(
            serialized_name="hostNames",
        )
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.require_server_name_indication = AAZBoolType(
            serialized_name="requireServerNameIndication",
        )
        properties.ssl_certificate = AAZObjectType(
            serialized_name="sslCertificate",
        )
        cls._build_schema_sub_resource_read(properties.ssl_certificate)
        properties.ssl_profile = AAZObjectType(
            serialized_name="sslProfile",
        )
        cls._build_schema_sub_resource_read(properties.ssl_profile)

        custom_error_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.http_listeners.Element.properties.custom_error_configurations
        custom_error_configurations.Element = AAZObjectType()
        cls._build_schema_application_gateway_custom_error_read(custom_error_configurations.Element)

        host_names = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.http_listeners.Element.properties.host_names
        host_names.Element = AAZStrType()

        listeners = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.listeners
        listeners.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.listeners.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.listeners.Element.properties
        properties.frontend_ip_configuration = AAZObjectType(
            serialized_name="frontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.frontend_ip_configuration)
        properties.frontend_port = AAZObjectType(
            serialized_name="frontendPort",
        )
        cls._build_schema_sub_resource_read(properties.frontend_port)
        properties.host_names = AAZListType(
            serialized_name="hostNames",
        )
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.ssl_certificate = AAZObjectType(
            serialized_name="sslCertificate",
        )
        cls._build_schema_sub_resource_read(properties.ssl_certificate)
        properties.ssl_profile = AAZObjectType(
            serialized_name="sslProfile",
        )
        cls._build_schema_sub_resource_read(properties.ssl_profile)

        host_names = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.listeners.Element.properties.host_names
        host_names.Element = AAZStrType()

        load_distribution_policies = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.load_distribution_policies
        load_distribution_policies.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.load_distribution_policies.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.load_distribution_policies.Element.properties
        properties.load_distribution_algorithm = AAZStrType(
            serialized_name="loadDistributionAlgorithm",
        )
        properties.load_distribution_targets = AAZListType(
            serialized_name="loadDistributionTargets",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        load_distribution_targets = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.load_distribution_policies.Element.properties.load_distribution_targets
        load_distribution_targets.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.load_distribution_policies.Element.properties.load_distribution_targets.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.load_distribution_policies.Element.properties.load_distribution_targets.Element.properties
        properties.backend_address_pool = AAZObjectType(
            serialized_name="backendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.backend_address_pool)
        properties.weight_per_server = AAZIntType(
            serialized_name="weightPerServer",
        )

        private_endpoint_connections = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_endpoint_connections
        private_endpoint_connections.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_endpoint_connections.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_endpoint_connections.Element.properties
        properties.link_identifier = AAZStrType(
            serialized_name="linkIdentifier",
            flags={"read_only": True},
        )
        properties.private_endpoint = AAZObjectType(
            serialized_name="privateEndpoint",
            flags={"read_only": True},
        )
        cls._build_schema_private_endpoint_read(properties.private_endpoint)
        properties.private_link_service_connection_state = AAZObjectType(
            serialized_name="privateLinkServiceConnectionState",
        )
        cls._build_schema_private_link_service_connection_state_read(properties.private_link_service_connection_state)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        private_link_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_link_configurations
        private_link_configurations.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_link_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_link_configurations.Element.properties
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        ip_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_link_configurations.Element.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_link_configurations.Element.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.private_link_configurations.Element.properties.ip_configurations.Element.properties
        properties.primary = AAZBoolType()
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)

        probes = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.probes
        probes.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.probes.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.probes.Element.properties
        properties.host = AAZStrType()
        properties.interval = AAZIntType()
        properties.match = AAZObjectType()
        properties.min_servers = AAZIntType(
            serialized_name="minServers",
        )
        properties.path = AAZStrType()
        properties.pick_host_name_from_backend_http_settings = AAZBoolType(
            serialized_name="pickHostNameFromBackendHttpSettings",
        )
        properties.pick_host_name_from_backend_settings = AAZBoolType(
            serialized_name="pickHostNameFromBackendSettings",
        )
        properties.port = AAZIntType()
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.timeout = AAZIntType()
        properties.unhealthy_threshold = AAZIntType(
            serialized_name="unhealthyThreshold",
        )

        match = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.probes.Element.properties.match
        match.body = AAZStrType()
        match.status_codes = AAZListType(
            serialized_name="statusCodes",
        )

        status_codes = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.probes.Element.properties.match.status_codes
        status_codes.Element = AAZStrType()

        redirect_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.redirect_configurations
        redirect_configurations.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.redirect_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.redirect_configurations.Element.properties
        properties.include_path = AAZBoolType(
            serialized_name="includePath",
        )
        properties.include_query_string = AAZBoolType(
            serialized_name="includeQueryString",
        )
        properties.path_rules = AAZListType(
            serialized_name="pathRules",
        )
        properties.redirect_type = AAZStrType(
            serialized_name="redirectType",
        )
        properties.request_routing_rules = AAZListType(
            serialized_name="requestRoutingRules",
        )
        properties.target_listener = AAZObjectType(
            serialized_name="targetListener",
        )
        cls._build_schema_sub_resource_read(properties.target_listener)
        properties.target_url = AAZStrType(
            serialized_name="targetUrl",
        )
        properties.url_path_maps = AAZListType(
            serialized_name="urlPathMaps",
        )

        path_rules = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.redirect_configurations.Element.properties.path_rules
        path_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(path_rules.Element)

        request_routing_rules = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.redirect_configurations.Element.properties.request_routing_rules
        request_routing_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(request_routing_rules.Element)

        url_path_maps = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.redirect_configurations.Element.properties.url_path_maps
        url_path_maps.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(url_path_maps.Element)

        request_routing_rules = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.request_routing_rules
        request_routing_rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.request_routing_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.request_routing_rules.Element.properties
        properties.backend_address_pool = AAZObjectType(
            serialized_name="backendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.backend_address_pool)
        properties.backend_http_settings = AAZObjectType(
            serialized_name="backendHttpSettings",
        )
        cls._build_schema_sub_resource_read(properties.backend_http_settings)
        properties.http_listener = AAZObjectType(
            serialized_name="httpListener",
        )
        cls._build_schema_sub_resource_read(properties.http_listener)
        properties.load_distribution_policy = AAZObjectType(
            serialized_name="loadDistributionPolicy",
        )
        cls._build_schema_sub_resource_read(properties.load_distribution_policy)
        properties.priority = AAZIntType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.redirect_configuration = AAZObjectType(
            serialized_name="redirectConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.redirect_configuration)
        properties.rewrite_rule_set = AAZObjectType(
            serialized_name="rewriteRuleSet",
        )
        cls._build_schema_sub_resource_read(properties.rewrite_rule_set)
        properties.rule_type = AAZStrType(
            serialized_name="ruleType",
        )
        properties.url_path_map = AAZObjectType(
            serialized_name="urlPathMap",
        )
        cls._build_schema_sub_resource_read(properties.url_path_map)

        rewrite_rule_sets = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets
        rewrite_rule_sets.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.rewrite_rules = AAZListType(
            serialized_name="rewriteRules",
        )

        rewrite_rules = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules
        rewrite_rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules.Element
        _element.action_set = AAZObjectType(
            serialized_name="actionSet",
        )
        _element.conditions = AAZListType()
        _element.name = AAZStrType()
        _element.rule_sequence = AAZIntType(
            serialized_name="ruleSequence",
        )

        action_set = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules.Element.action_set
        action_set.request_header_configurations = AAZListType(
            serialized_name="requestHeaderConfigurations",
        )
        action_set.response_header_configurations = AAZListType(
            serialized_name="responseHeaderConfigurations",
        )
        action_set.url_configuration = AAZObjectType(
            serialized_name="urlConfiguration",
        )

        request_header_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules.Element.action_set.request_header_configurations
        request_header_configurations.Element = AAZObjectType()
        cls._build_schema_application_gateway_header_configuration_read(request_header_configurations.Element)

        response_header_configurations = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules.Element.action_set.response_header_configurations
        response_header_configurations.Element = AAZObjectType()
        cls._build_schema_application_gateway_header_configuration_read(response_header_configurations.Element)

        url_configuration = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules.Element.action_set.url_configuration
        url_configuration.modified_path = AAZStrType(
            serialized_name="modifiedPath",
        )
        url_configuration.modified_query_string = AAZStrType(
            serialized_name="modifiedQueryString",
        )
        url_configuration.reroute = AAZBoolType()

        conditions = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules.Element.conditions
        conditions.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.rewrite_rule_sets.Element.properties.rewrite_rules.Element.conditions.Element
        _element.ignore_case = AAZBoolType(
            serialized_name="ignoreCase",
        )
        _element.negate = AAZBoolType()
        _element.pattern = AAZStrType()
        _element.variable = AAZStrType()

        routing_rules = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.routing_rules
        routing_rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.routing_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.routing_rules.Element.properties
        properties.backend_address_pool = AAZObjectType(
            serialized_name="backendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.backend_address_pool)
        properties.backend_settings = AAZObjectType(
            serialized_name="backendSettings",
        )
        cls._build_schema_sub_resource_read(properties.backend_settings)
        properties.listener = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.listener)
        properties.priority = AAZIntType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.rule_type = AAZStrType(
            serialized_name="ruleType",
        )

        sku = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.sku
        sku.capacity = AAZIntType()
        sku.family = AAZStrType()
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        ssl_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_certificates
        ssl_certificates.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_certificates.Element.properties
        properties.data = AAZStrType()
        properties.key_vault_secret_id = AAZStrType(
            serialized_name="keyVaultSecretId",
        )
        properties.password = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_cert_data = AAZStrType(
            serialized_name="publicCertData",
            flags={"read_only": True},
        )

        ssl_profiles = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_profiles
        ssl_profiles.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_profiles.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_profiles.Element.properties
        properties.client_auth_configuration = AAZObjectType(
            serialized_name="clientAuthConfiguration",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.ssl_policy = AAZObjectType(
            serialized_name="sslPolicy",
        )
        cls._build_schema_application_gateway_ssl_policy_read(properties.ssl_policy)
        properties.trusted_client_certificates = AAZListType(
            serialized_name="trustedClientCertificates",
        )

        client_auth_configuration = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_profiles.Element.properties.client_auth_configuration
        client_auth_configuration.verify_client_cert_issuer_dn = AAZBoolType(
            serialized_name="verifyClientCertIssuerDN",
        )
        client_auth_configuration.verify_client_revocation = AAZStrType(
            serialized_name="verifyClientRevocation",
        )

        trusted_client_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.ssl_profiles.Element.properties.trusted_client_certificates
        trusted_client_certificates.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(trusted_client_certificates.Element)

        trusted_client_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.trusted_client_certificates
        trusted_client_certificates.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.trusted_client_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.trusted_client_certificates.Element.properties
        properties.client_cert_issuer_dn = AAZStrType(
            serialized_name="clientCertIssuerDN",
            flags={"read_only": True},
        )
        properties.data = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.validated_cert_data = AAZStrType(
            serialized_name="validatedCertData",
            flags={"read_only": True},
        )

        trusted_root_certificates = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.trusted_root_certificates
        trusted_root_certificates.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.trusted_root_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.trusted_root_certificates.Element.properties
        properties.data = AAZStrType()
        properties.key_vault_secret_id = AAZStrType(
            serialized_name="keyVaultSecretId",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        url_path_maps = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.url_path_maps
        url_path_maps.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.url_path_maps.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.url_path_maps.Element.properties
        properties.default_backend_address_pool = AAZObjectType(
            serialized_name="defaultBackendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.default_backend_address_pool)
        properties.default_backend_http_settings = AAZObjectType(
            serialized_name="defaultBackendHttpSettings",
        )
        cls._build_schema_sub_resource_read(properties.default_backend_http_settings)
        properties.default_load_distribution_policy = AAZObjectType(
            serialized_name="defaultLoadDistributionPolicy",
        )
        cls._build_schema_sub_resource_read(properties.default_load_distribution_policy)
        properties.default_redirect_configuration = AAZObjectType(
            serialized_name="defaultRedirectConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.default_redirect_configuration)
        properties.default_rewrite_rule_set = AAZObjectType(
            serialized_name="defaultRewriteRuleSet",
        )
        cls._build_schema_sub_resource_read(properties.default_rewrite_rule_set)
        properties.path_rules = AAZListType(
            serialized_name="pathRules",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        path_rules = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.url_path_maps.Element.properties.path_rules
        path_rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.url_path_maps.Element.properties.path_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.url_path_maps.Element.properties.path_rules.Element.properties
        properties.backend_address_pool = AAZObjectType(
            serialized_name="backendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.backend_address_pool)
        properties.backend_http_settings = AAZObjectType(
            serialized_name="backendHttpSettings",
        )
        cls._build_schema_sub_resource_read(properties.backend_http_settings)
        properties.firewall_policy = AAZObjectType(
            serialized_name="firewallPolicy",
        )
        cls._build_schema_sub_resource_read(properties.firewall_policy)
        properties.load_distribution_policy = AAZObjectType(
            serialized_name="loadDistributionPolicy",
        )
        cls._build_schema_sub_resource_read(properties.load_distribution_policy)
        properties.paths = AAZListType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.redirect_configuration = AAZObjectType(
            serialized_name="redirectConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.redirect_configuration)
        properties.rewrite_rule_set = AAZObjectType(
            serialized_name="rewriteRuleSet",
        )
        cls._build_schema_sub_resource_read(properties.rewrite_rule_set)

        paths = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.url_path_maps.Element.properties.path_rules.Element.properties.paths
        paths.Element = AAZStrType()

        web_application_firewall_configuration = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.web_application_firewall_configuration
        web_application_firewall_configuration.disabled_rule_groups = AAZListType(
            serialized_name="disabledRuleGroups",
        )
        web_application_firewall_configuration.enabled = AAZBoolType(
            flags={"required": True},
        )
        web_application_firewall_configuration.exclusions = AAZListType()
        web_application_firewall_configuration.file_upload_limit_in_mb = AAZIntType(
            serialized_name="fileUploadLimitInMb",
        )
        web_application_firewall_configuration.firewall_mode = AAZStrType(
            serialized_name="firewallMode",
            flags={"required": True},
        )
        web_application_firewall_configuration.max_request_body_size = AAZIntType(
            serialized_name="maxRequestBodySize",
        )
        web_application_firewall_configuration.max_request_body_size_in_kb = AAZIntType(
            serialized_name="maxRequestBodySizeInKb",
        )
        web_application_firewall_configuration.request_body_check = AAZBoolType(
            serialized_name="requestBodyCheck",
        )
        web_application_firewall_configuration.rule_set_type = AAZStrType(
            serialized_name="ruleSetType",
            flags={"required": True},
        )
        web_application_firewall_configuration.rule_set_version = AAZStrType(
            serialized_name="ruleSetVersion",
            flags={"required": True},
        )

        disabled_rule_groups = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.web_application_firewall_configuration.disabled_rule_groups
        disabled_rule_groups.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.web_application_firewall_configuration.disabled_rule_groups.Element
        _element.rule_group_name = AAZStrType(
            serialized_name="ruleGroupName",
            flags={"required": True},
        )
        _element.rules = AAZListType()

        rules = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.web_application_firewall_configuration.disabled_rule_groups.Element.rules
        rules.Element = AAZIntType()

        exclusions = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.web_application_firewall_configuration.exclusions
        exclusions.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.properties.web_application_firewall_configuration.exclusions.Element
        _element.match_variable = AAZStrType(
            serialized_name="matchVariable",
            flags={"required": True},
        )
        _element.selector = AAZStrType(
            flags={"required": True},
        )
        _element.selector_match_operator = AAZStrType(
            serialized_name="selectorMatchOperator",
            flags={"required": True},
        )

        tags = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.tags
        tags.Element = AAZStrType()

        zones = _schema_web_application_firewall_policy_read.properties.application_gateways.Element.zones
        zones.Element = AAZStrType()

        custom_rules = _schema_web_application_firewall_policy_read.properties.custom_rules
        custom_rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.custom_rules.Element
        _element.action = AAZStrType(
            flags={"required": True},
        )
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.group_by_user_session = AAZListType(
            serialized_name="groupByUserSession",
        )
        _element.match_conditions = AAZListType(
            serialized_name="matchConditions",
            flags={"required": True},
        )
        _element.name = AAZStrType()
        _element.priority = AAZIntType(
            flags={"required": True},
        )
        _element.rate_limit_duration = AAZStrType(
            serialized_name="rateLimitDuration",
        )
        _element.rate_limit_threshold = AAZIntType(
            serialized_name="rateLimitThreshold",
        )
        _element.rule_type = AAZStrType(
            serialized_name="ruleType",
            flags={"required": True},
        )
        _element.state = AAZStrType()

        group_by_user_session = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.group_by_user_session
        group_by_user_session.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.group_by_user_session.Element
        _element.group_by_variables = AAZListType(
            serialized_name="groupByVariables",
            flags={"required": True},
        )

        group_by_variables = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.group_by_user_session.Element.group_by_variables
        group_by_variables.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.group_by_user_session.Element.group_by_variables.Element
        _element.variable_name = AAZStrType(
            serialized_name="variableName",
            flags={"required": True},
        )

        match_conditions = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.match_conditions
        match_conditions.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.match_conditions.Element
        _element.match_values = AAZListType(
            serialized_name="matchValues",
            flags={"required": True},
        )
        _element.match_variables = AAZListType(
            serialized_name="matchVariables",
            flags={"required": True},
        )
        _element.negation_conditon = AAZBoolType(
            serialized_name="negationConditon",
        )
        _element.operator = AAZStrType(
            flags={"required": True},
        )
        _element.transforms = AAZListType()

        match_values = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.match_conditions.Element.match_values
        match_values.Element = AAZStrType()

        match_variables = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.match_conditions.Element.match_variables
        match_variables.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.match_conditions.Element.match_variables.Element
        _element.selector = AAZStrType()
        _element.variable_name = AAZStrType(
            serialized_name="variableName",
            flags={"required": True},
        )

        transforms = _schema_web_application_firewall_policy_read.properties.custom_rules.Element.match_conditions.Element.transforms
        transforms.Element = AAZStrType()

        http_listeners = _schema_web_application_firewall_policy_read.properties.http_listeners
        http_listeners.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(http_listeners.Element)

        managed_rules = _schema_web_application_firewall_policy_read.properties.managed_rules
        managed_rules.exceptions = AAZListType()
        managed_rules.exclusions = AAZListType()
        managed_rules.managed_rule_sets = AAZListType(
            serialized_name="managedRuleSets",
            flags={"required": True},
        )

        exceptions = _schema_web_application_firewall_policy_read.properties.managed_rules.exceptions
        exceptions.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.managed_rules.exceptions.Element
        _element.exception_managed_rule_sets = AAZListType(
            serialized_name="exceptionManagedRuleSets",
        )
        _element.match_variable = AAZStrType(
            serialized_name="matchVariable",
            flags={"required": True},
        )
        _element.selector = AAZStrType()
        _element.selector_match_operator = AAZStrType(
            serialized_name="selectorMatchOperator",
        )
        _element.value_match_operator = AAZStrType(
            serialized_name="valueMatchOperator",
            flags={"required": True},
        )
        _element.values = AAZListType()

        exception_managed_rule_sets = _schema_web_application_firewall_policy_read.properties.managed_rules.exceptions.Element.exception_managed_rule_sets
        exception_managed_rule_sets.Element = AAZObjectType()
        cls._build_schema_exclusion_managed_rule_set_read(exception_managed_rule_sets.Element)

        values = _schema_web_application_firewall_policy_read.properties.managed_rules.exceptions.Element.values
        values.Element = AAZStrType()

        exclusions = _schema_web_application_firewall_policy_read.properties.managed_rules.exclusions
        exclusions.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.managed_rules.exclusions.Element
        _element.exclusion_managed_rule_sets = AAZListType(
            serialized_name="exclusionManagedRuleSets",
        )
        _element.match_variable = AAZStrType(
            serialized_name="matchVariable",
            flags={"required": True},
        )
        _element.selector = AAZStrType(
            flags={"required": True},
        )
        _element.selector_match_operator = AAZStrType(
            serialized_name="selectorMatchOperator",
            flags={"required": True},
        )

        exclusion_managed_rule_sets = _schema_web_application_firewall_policy_read.properties.managed_rules.exclusions.Element.exclusion_managed_rule_sets
        exclusion_managed_rule_sets.Element = AAZObjectType()
        cls._build_schema_exclusion_managed_rule_set_read(exclusion_managed_rule_sets.Element)

        managed_rule_sets = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets
        managed_rule_sets.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element
        _element.computed_disabled_rules = AAZListType(
            serialized_name="computedDisabledRules",
            flags={"read_only": True},
        )
        _element.rule_group_overrides = AAZListType(
            serialized_name="ruleGroupOverrides",
        )
        _element.rule_set_type = AAZStrType(
            serialized_name="ruleSetType",
            flags={"required": True},
        )
        _element.rule_set_version = AAZStrType(
            serialized_name="ruleSetVersion",
            flags={"required": True},
        )

        computed_disabled_rules = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element.computed_disabled_rules
        computed_disabled_rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element.computed_disabled_rules.Element
        _element.rule_group_name = AAZStrType(
            serialized_name="ruleGroupName",
            flags={"required": True},
        )
        _element.rules = AAZListType()

        rules = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element.computed_disabled_rules.Element.rules
        rules.Element = AAZAnyType()

        rule_group_overrides = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element.rule_group_overrides
        rule_group_overrides.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element.rule_group_overrides.Element
        _element.rule_group_name = AAZStrType(
            serialized_name="ruleGroupName",
            flags={"required": True},
        )
        _element.rules = AAZListType()

        rules = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element.rule_group_overrides.Element.rules
        rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.managed_rules.managed_rule_sets.Element.rule_group_overrides.Element.rules.Element
        _element.action = AAZStrType()
        _element.rule_id = AAZStrType(
            serialized_name="ruleId",
            flags={"required": True},
        )
        _element.sensitivity = AAZStrType()
        _element.state = AAZStrType()

        path_based_rules = _schema_web_application_firewall_policy_read.properties.path_based_rules
        path_based_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(path_based_rules.Element)

        policy_settings = _schema_web_application_firewall_policy_read.properties.policy_settings
        policy_settings.custom_block_response_body = AAZStrType(
            serialized_name="customBlockResponseBody",
        )
        policy_settings.custom_block_response_status_code = AAZIntType(
            serialized_name="customBlockResponseStatusCode",
        )
        policy_settings.file_upload_enforcement = AAZBoolType(
            serialized_name="fileUploadEnforcement",
        )
        policy_settings.file_upload_limit_in_mb = AAZIntType(
            serialized_name="fileUploadLimitInMb",
        )
        policy_settings.js_challenge_cookie_expiration_in_mins = AAZIntType(
            serialized_name="jsChallengeCookieExpirationInMins",
        )
        policy_settings.log_scrubbing = AAZObjectType(
            serialized_name="logScrubbing",
        )
        policy_settings.max_request_body_size_in_kb = AAZIntType(
            serialized_name="maxRequestBodySizeInKb",
        )
        policy_settings.mode = AAZStrType()
        policy_settings.request_body_check = AAZBoolType(
            serialized_name="requestBodyCheck",
        )
        policy_settings.request_body_enforcement = AAZBoolType(
            serialized_name="requestBodyEnforcement",
        )
        policy_settings.request_body_inspect_limit_in_kb = AAZIntType(
            serialized_name="requestBodyInspectLimitInKB",
        )
        policy_settings.state = AAZStrType()

        log_scrubbing = _schema_web_application_firewall_policy_read.properties.policy_settings.log_scrubbing
        log_scrubbing.scrubbing_rules = AAZListType(
            serialized_name="scrubbingRules",
        )
        log_scrubbing.state = AAZStrType()

        scrubbing_rules = _schema_web_application_firewall_policy_read.properties.policy_settings.log_scrubbing.scrubbing_rules
        scrubbing_rules.Element = AAZObjectType()

        _element = _schema_web_application_firewall_policy_read.properties.policy_settings.log_scrubbing.scrubbing_rules.Element
        _element.match_variable = AAZStrType(
            serialized_name="matchVariable",
            flags={"required": True},
        )
        _element.selector = AAZStrType()
        _element.selector_match_operator = AAZStrType(
            serialized_name="selectorMatchOperator",
            flags={"required": True},
        )
        _element.state = AAZStrType()

        tags = _schema_web_application_firewall_policy_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_web_application_firewall_policy_read.etag
        _schema.id = cls._schema_web_application_firewall_policy_read.id
        _schema.location = cls._schema_web_application_firewall_policy_read.location
        _schema.name = cls._schema_web_application_firewall_policy_read.name
        _schema.properties = cls._schema_web_application_firewall_policy_read.properties
        _schema.tags = cls._schema_web_application_firewall_policy_read.tags
        _schema.type = cls._schema_web_application_firewall_policy_read.type


__all__ = ["Create"]
