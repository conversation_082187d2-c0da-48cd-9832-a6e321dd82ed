# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vnet subnet create",
)
class Create(AAZCommand):
    """Create a subnet and associate an existing NSG and route table.

    :example: Create new subnet attached to an NSG with a custom route table.
        az network vnet subnet create -g MyResourceGroup --vnet-name MyVnet -n MySubnet --address-prefixes 10.0.0.0/24 --network-security-group MyNsg --route-table MyRouteTable

    :example: Create new subnet attached to a NAT gateway.
        az network vnet subnet create -n MySubnet --vnet-name MyVnet -g MyResourceGroup --nat-gateway MyNatGateway --address-prefixes "10.0.0.0/21"
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/virtualnetworks/{}/subnets/{}", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The subnet name.",
            required=True,
        )
        _args_schema.vnet_name = AAZStrArg(
            options=["--vnet-name"],
            help="The virtual network (VNet) name.",
            required=True,
        )
        _args_schema.address_prefix = AAZStrArg(
            options=["--address-prefix"],
            help="The address prefix for the subnet.",
        )
        _args_schema.address_prefixes = AAZListArg(
            options=["--address-prefixes"],
            help="Space-separated list of address prefixes in CIDR format. If provided, --ipam-allocations should not be specified.",
        )
        _args_schema.default_outbound_access = AAZBoolArg(
            options=["--default-outbound", "--default-outbound-access"],
            help="Set this property to false to disable default outbound connectivity for all VMs in the subnet.",
        )
        _args_schema.delegated_services = AAZListArg(
            options=["--delegated-services"],
            help="Space-separated list of services to whom the subnet should be delegated, e.g., `Microsoft.Sql/servers`.",
        )
        _args_schema.ipam_pool_prefix_allocations = AAZListArg(
            options=["--ipam-allocations", "--ipam-pool-prefix-allocations"],
            help="A list of IPAM Pools for allocating IP address prefixes. If provided, --address-prefixes would be ignored by CLI and should not be specified.",
        )
        _args_schema.nat_gateway = AAZStrArg(
            options=["--nat-gateway"],
            help="Name or ID of a NAT gateway to attach.",
        )
        _args_schema.network_security_group = AAZResourceIdArg(
            options=["--nsg", "--network-security-group"],
            help="Name or ID of a network security group (NSG).",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/networkSecurityGroups/{}",
            ),
        )
        _args_schema.private_endpoint_network_policies = AAZStrArg(
            options=["--ple-network-policies", "--private-endpoint-network-policies"],
            help="Manage network policies for private endpoint.",
            default="Disabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled", "NetworkSecurityGroupEnabled": "NetworkSecurityGroupEnabled", "RouteTableEnabled": "RouteTableEnabled"},
        )
        _args_schema.private_link_service_network_policies = AAZStrArg(
            options=["--pls-network-policies", "--private-link-service-network-policies"],
            help="Manage network policy for private link service.",
            default="Enabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _args_schema.route_table = AAZResourceIdArg(
            options=["--route-table"],
            help="Name or ID of a route table to associate with the subnet.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/routeTables/{}",
            ),
        )
        _args_schema.policies = AAZListArg(
            options=["--policies"],
            help="An array of service endpoint policies.",
        )
        _args_schema.endpoints = AAZListArg(
            options=["--endpoints"],
            help="An array of service endpoints.",
        )
        _args_schema.sharing_scope = AAZStrArg(
            options=["--sharing-scope"],
            help="Set this property to Tenant to allow sharing subnet with other subscriptions in your AAD tenant. This property can only be set if defaultOutboundAccess is set to false, both properties can only be set if subnet is empty.",
            enum={"DelegatedServices": "DelegatedServices", "Tenant": "Tenant"},
        )

        address_prefixes = cls._args_schema.address_prefixes
        address_prefixes.Element = AAZStrArg()

        delegated_services = cls._args_schema.delegated_services
        delegated_services.Element = AAZObjectArg()

        _element = cls._args_schema.delegated_services.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a subnet. This name can be used to access the resource.",
        )
        _element.service_name = AAZStrArg(
            options=["service-name"],
            help="The name of the service to whom the subnet should be delegated (e.g. Microsoft.Sql/servers).",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="Resource type.",
        )

        ipam_pool_prefix_allocations = cls._args_schema.ipam_pool_prefix_allocations
        ipam_pool_prefix_allocations.Element = AAZObjectArg()

        _element = cls._args_schema.ipam_pool_prefix_allocations.Element
        _element.number_of_ip_addresses = AAZStrArg(
            options=["number-of-ip-addresses"],
            help="Number of IP addresses to allocate.",
        )
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource id of the associated Azure IpamPool resource.",
        )

        policies = cls._args_schema.policies
        policies.Element = AAZObjectArg()

        _element = cls._args_schema.policies.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/serviceEndpointPolicies/{}",
            ),
        )
        _element.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _element.contextual_service_endpoint_policies = AAZListArg(
            options=["contextual-service-endpoint-policies"],
            help="A collection of contextual service endpoint policy.",
        )
        _element.service_alias = AAZStrArg(
            options=["service-alias"],
            help="The alias indicating if the policy belongs to a service",
        )
        _element.service_endpoint_policy_definitions = AAZListArg(
            options=["service-endpoint-policy-definitions"],
            help="A collection of service endpoint policy definitions of the service endpoint policy.",
        )
        _element.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        contextual_service_endpoint_policies = cls._args_schema.policies.Element.contextual_service_endpoint_policies
        contextual_service_endpoint_policies.Element = AAZStrArg()

        service_endpoint_policy_definitions = cls._args_schema.policies.Element.service_endpoint_policy_definitions
        service_endpoint_policy_definitions.Element = AAZObjectArg()

        _element = cls._args_schema.policies.Element.service_endpoint_policy_definitions.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/serviceEndpointPolicies/{}/serviceEndpointPolicyDefinitions/{}",
            ),
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.description = AAZStrArg(
            options=["description"],
            help="A description for this rule. Restricted to 140 chars.",
        )
        _element.service = AAZStrArg(
            options=["service"],
            help="Service endpoint name.",
        )
        _element.service_resources = AAZListArg(
            options=["service-resources"],
            help="A list of service resources.",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of the resource.",
        )

        service_resources = cls._args_schema.policies.Element.service_endpoint_policy_definitions.Element.service_resources
        service_resources.Element = AAZStrArg()

        tags = cls._args_schema.policies.Element.tags
        tags.Element = AAZStrArg()

        endpoints = cls._args_schema.endpoints
        endpoints.Element = AAZObjectArg()

        _element = cls._args_schema.endpoints.Element
        _element.locations = AAZListArg(
            options=["locations"],
            help="A list of locations.",
        )
        _element.network_identifier = AAZStrArg(
            options=["network-identifier"],
            help="SubResource as network identifier.",
        )
        _element.service = AAZStrArg(
            options=["service"],
            help="The type of the endpoint service.",
        )

        locations = cls._args_schema.endpoints.Element.locations
        locations.Element = AAZStrArg()

        # define Arg Group "NetworkSecurityGroup"

        # define Arg Group "Properties"

        # define Arg Group "RouteTable"

        # define Arg Group "SubnetParameters"
        return cls._args_schema

    _args_application_security_group_create = None

    @classmethod
    def _build_args_application_security_group_create(cls, _schema):
        if cls._args_application_security_group_create is not None:
            _schema.location = cls._args_application_security_group_create.location
            _schema.tags = cls._args_application_security_group_create.tags
            return

        cls._args_application_security_group_create = AAZObjectArg()

        application_security_group_create = cls._args_application_security_group_create
        application_security_group_create.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        application_security_group_create.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
        )

        tags = cls._args_application_security_group_create.tags
        tags.Element = AAZStrArg()

        _schema.location = cls._args_application_security_group_create.location
        _schema.tags = cls._args_application_security_group_create.tags

    _args_sub_resource_create = None

    @classmethod
    def _build_args_sub_resource_create(cls, _schema):
        if cls._args_sub_resource_create is not None:
            _schema.id = cls._args_sub_resource_create.id
            return

        cls._args_sub_resource_create = AAZObjectArg()

        sub_resource_create = cls._args_sub_resource_create
        sub_resource_create.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        _schema.id = cls._args_sub_resource_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.SubnetsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class SubnetsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworks/{virtualNetworkName}/subnets/{subnetName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subnetName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkName", self.ctx.args.vnet_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("name", AAZStrType, ".name")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("addressPrefix", AAZStrType, ".address_prefix")
                properties.set_prop("addressPrefixes", AAZListType, ".address_prefixes")
                properties.set_prop("defaultOutboundAccess", AAZBoolType, ".default_outbound_access")
                properties.set_prop("delegations", AAZListType, ".delegated_services")
                properties.set_prop("ipamPoolPrefixAllocations", AAZListType, ".ipam_pool_prefix_allocations")
                properties.set_prop("natGateway", AAZObjectType)
                properties.set_prop("networkSecurityGroup", AAZObjectType)
                properties.set_prop("privateEndpointNetworkPolicies", AAZStrType, ".private_endpoint_network_policies")
                properties.set_prop("privateLinkServiceNetworkPolicies", AAZStrType, ".private_link_service_network_policies")
                properties.set_prop("routeTable", AAZObjectType)
                properties.set_prop("serviceEndpointPolicies", AAZListType, ".policies")
                properties.set_prop("serviceEndpoints", AAZListType, ".endpoints")
                properties.set_prop("sharingScope", AAZStrType, ".sharing_scope")

            address_prefixes = _builder.get(".properties.addressPrefixes")
            if address_prefixes is not None:
                address_prefixes.set_elements(AAZStrType, ".")

            delegations = _builder.get(".properties.delegations")
            if delegations is not None:
                delegations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.delegations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.delegations[].properties")
            if properties is not None:
                properties.set_prop("serviceName", AAZStrType, ".service_name")

            ipam_pool_prefix_allocations = _builder.get(".properties.ipamPoolPrefixAllocations")
            if ipam_pool_prefix_allocations is not None:
                ipam_pool_prefix_allocations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipamPoolPrefixAllocations[]")
            if _elements is not None:
                _elements.set_prop("numberOfIpAddresses", AAZStrType, ".number_of_ip_addresses")
                _elements.set_prop("pool", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            pool = _builder.get(".properties.ipamPoolPrefixAllocations[].pool")
            if pool is not None:
                pool.set_prop("id", AAZStrType, ".id")

            nat_gateway = _builder.get(".properties.natGateway")
            if nat_gateway is not None:
                nat_gateway.set_prop("id", AAZStrType, ".nat_gateway")

            network_security_group = _builder.get(".properties.networkSecurityGroup")
            if network_security_group is not None:
                network_security_group.set_prop("id", AAZStrType, ".network_security_group")
                network_security_group.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            route_table = _builder.get(".properties.routeTable")
            if route_table is not None:
                route_table.set_prop("id", AAZStrType, ".route_table")
                route_table.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            service_endpoint_policies = _builder.get(".properties.serviceEndpointPolicies")
            if service_endpoint_policies is not None:
                service_endpoint_policies.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.serviceEndpointPolicies[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("location", AAZStrType, ".location")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.serviceEndpointPolicies[].properties")
            if properties is not None:
                properties.set_prop("contextualServiceEndpointPolicies", AAZListType, ".contextual_service_endpoint_policies")
                properties.set_prop("serviceAlias", AAZStrType, ".service_alias")
                properties.set_prop("serviceEndpointPolicyDefinitions", AAZListType, ".service_endpoint_policy_definitions")

            contextual_service_endpoint_policies = _builder.get(".properties.serviceEndpointPolicies[].properties.contextualServiceEndpointPolicies")
            if contextual_service_endpoint_policies is not None:
                contextual_service_endpoint_policies.set_elements(AAZStrType, ".")

            service_endpoint_policy_definitions = _builder.get(".properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions")
            if service_endpoint_policy_definitions is not None:
                service_endpoint_policy_definitions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                _elements.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[].properties")
            if properties is not None:
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("service", AAZStrType, ".service")
                properties.set_prop("serviceResources", AAZListType, ".service_resources")

            service_resources = _builder.get(".properties.serviceEndpointPolicies[].properties.serviceEndpointPolicyDefinitions[].properties.serviceResources")
            if service_resources is not None:
                service_resources.set_elements(AAZStrType, ".")

            tags = _builder.get(".properties.serviceEndpointPolicies[].tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            service_endpoints = _builder.get(".properties.serviceEndpoints")
            if service_endpoints is not None:
                service_endpoints.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.serviceEndpoints[]")
            if _elements is not None:
                _elements.set_prop("locations", AAZListType, ".locations")
                _elements.set_prop("networkIdentifier", AAZObjectType)
                _elements.set_prop("service", AAZStrType, ".service")

            locations = _builder.get(".properties.serviceEndpoints[].locations")
            if locations is not None:
                locations.set_elements(AAZStrType, ".")

            network_identifier = _builder.get(".properties.serviceEndpoints[].networkIdentifier")
            if network_identifier is not None:
                network_identifier.set_prop("id", AAZStrType, ".network_identifier")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_subnet_read(cls._schema_on_200_201)

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_application_security_group_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("location", AAZStrType, ".location")
        _builder.set_prop("tags", AAZDictType, ".tags")

        tags = _builder.get(".tags")
        if tags is not None:
            tags.set_elements(AAZStrType, ".")

    @classmethod
    def _build_schema_sub_resource_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_application_security_group_read = None

    @classmethod
    def _build_schema_application_security_group_read(cls, _schema):
        if cls._schema_application_security_group_read is not None:
            _schema.etag = cls._schema_application_security_group_read.etag
            _schema.id = cls._schema_application_security_group_read.id
            _schema.location = cls._schema_application_security_group_read.location
            _schema.name = cls._schema_application_security_group_read.name
            _schema.properties = cls._schema_application_security_group_read.properties
            _schema.tags = cls._schema_application_security_group_read.tags
            _schema.type = cls._schema_application_security_group_read.type
            return

        cls._schema_application_security_group_read = _schema_application_security_group_read = AAZObjectType()

        application_security_group_read = _schema_application_security_group_read
        application_security_group_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        application_security_group_read.id = AAZStrType()
        application_security_group_read.location = AAZStrType()
        application_security_group_read.name = AAZStrType(
            flags={"read_only": True},
        )
        application_security_group_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        application_security_group_read.tags = AAZDictType()
        application_security_group_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_application_security_group_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )

        tags = _schema_application_security_group_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_application_security_group_read.etag
        _schema.id = cls._schema_application_security_group_read.id
        _schema.location = cls._schema_application_security_group_read.location
        _schema.name = cls._schema_application_security_group_read.name
        _schema.properties = cls._schema_application_security_group_read.properties
        _schema.tags = cls._schema_application_security_group_read.tags
        _schema.type = cls._schema_application_security_group_read.type

    _schema_extended_location_read = None

    @classmethod
    def _build_schema_extended_location_read(cls, _schema):
        if cls._schema_extended_location_read is not None:
            _schema.name = cls._schema_extended_location_read.name
            _schema.type = cls._schema_extended_location_read.type
            return

        cls._schema_extended_location_read = _schema_extended_location_read = AAZObjectType()

        extended_location_read = _schema_extended_location_read
        extended_location_read.name = AAZStrType()
        extended_location_read.type = AAZStrType()

        _schema.name = cls._schema_extended_location_read.name
        _schema.type = cls._schema_extended_location_read.type

    _schema_frontend_ip_configuration_read = None

    @classmethod
    def _build_schema_frontend_ip_configuration_read(cls, _schema):
        if cls._schema_frontend_ip_configuration_read is not None:
            _schema.etag = cls._schema_frontend_ip_configuration_read.etag
            _schema.id = cls._schema_frontend_ip_configuration_read.id
            _schema.name = cls._schema_frontend_ip_configuration_read.name
            _schema.properties = cls._schema_frontend_ip_configuration_read.properties
            _schema.type = cls._schema_frontend_ip_configuration_read.type
            _schema.zones = cls._schema_frontend_ip_configuration_read.zones
            return

        cls._schema_frontend_ip_configuration_read = _schema_frontend_ip_configuration_read = AAZObjectType()

        frontend_ip_configuration_read = _schema_frontend_ip_configuration_read
        frontend_ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        frontend_ip_configuration_read.id = AAZStrType()
        frontend_ip_configuration_read.name = AAZStrType()
        frontend_ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        frontend_ip_configuration_read.type = AAZStrType(
            flags={"read_only": True},
        )
        frontend_ip_configuration_read.zones = AAZListType()

        properties = _schema_frontend_ip_configuration_read.properties
        properties.gateway_load_balancer = AAZObjectType(
            serialized_name="gatewayLoadBalancer",
        )
        cls._build_schema_sub_resource_read(properties.gateway_load_balancer)
        properties.inbound_nat_pools = AAZListType(
            serialized_name="inboundNatPools",
            flags={"read_only": True},
        )
        properties.inbound_nat_rules = AAZListType(
            serialized_name="inboundNatRules",
            flags={"read_only": True},
        )
        properties.load_balancing_rules = AAZListType(
            serialized_name="loadBalancingRules",
            flags={"read_only": True},
        )
        properties.outbound_rules = AAZListType(
            serialized_name="outboundRules",
            flags={"read_only": True},
        )
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.public_ip_prefix = AAZObjectType(
            serialized_name="publicIPPrefix",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_prefix)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        inbound_nat_pools = _schema_frontend_ip_configuration_read.properties.inbound_nat_pools
        inbound_nat_pools.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_pools.Element)

        inbound_nat_rules = _schema_frontend_ip_configuration_read.properties.inbound_nat_rules
        inbound_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_rules.Element)

        load_balancing_rules = _schema_frontend_ip_configuration_read.properties.load_balancing_rules
        load_balancing_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancing_rules.Element)

        outbound_rules = _schema_frontend_ip_configuration_read.properties.outbound_rules
        outbound_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(outbound_rules.Element)

        zones = _schema_frontend_ip_configuration_read.zones
        zones.Element = AAZStrType()

        _schema.etag = cls._schema_frontend_ip_configuration_read.etag
        _schema.id = cls._schema_frontend_ip_configuration_read.id
        _schema.name = cls._schema_frontend_ip_configuration_read.name
        _schema.properties = cls._schema_frontend_ip_configuration_read.properties
        _schema.type = cls._schema_frontend_ip_configuration_read.type
        _schema.zones = cls._schema_frontend_ip_configuration_read.zones

    _schema_ip_configuration_read = None

    @classmethod
    def _build_schema_ip_configuration_read(cls, _schema):
        if cls._schema_ip_configuration_read is not None:
            _schema.etag = cls._schema_ip_configuration_read.etag
            _schema.id = cls._schema_ip_configuration_read.id
            _schema.name = cls._schema_ip_configuration_read.name
            _schema.properties = cls._schema_ip_configuration_read.properties
            return

        cls._schema_ip_configuration_read = _schema_ip_configuration_read = AAZObjectType(
            flags={"read_only": True}
        )

        ip_configuration_read = _schema_ip_configuration_read
        ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        ip_configuration_read.id = AAZStrType()
        ip_configuration_read.name = AAZStrType()
        ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_ip_configuration_read.properties
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        _schema.etag = cls._schema_ip_configuration_read.etag
        _schema.id = cls._schema_ip_configuration_read.id
        _schema.name = cls._schema_ip_configuration_read.name
        _schema.properties = cls._schema_ip_configuration_read.properties

    _schema_network_interface_ip_configuration_read = None

    @classmethod
    def _build_schema_network_interface_ip_configuration_read(cls, _schema):
        if cls._schema_network_interface_ip_configuration_read is not None:
            _schema.etag = cls._schema_network_interface_ip_configuration_read.etag
            _schema.id = cls._schema_network_interface_ip_configuration_read.id
            _schema.name = cls._schema_network_interface_ip_configuration_read.name
            _schema.properties = cls._schema_network_interface_ip_configuration_read.properties
            _schema.type = cls._schema_network_interface_ip_configuration_read.type
            return

        cls._schema_network_interface_ip_configuration_read = _schema_network_interface_ip_configuration_read = AAZObjectType()

        network_interface_ip_configuration_read = _schema_network_interface_ip_configuration_read
        network_interface_ip_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_ip_configuration_read.id = AAZStrType()
        network_interface_ip_configuration_read.name = AAZStrType()
        network_interface_ip_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_ip_configuration_read.type = AAZStrType()

        properties = _schema_network_interface_ip_configuration_read.properties
        properties.application_gateway_backend_address_pools = AAZListType(
            serialized_name="applicationGatewayBackendAddressPools",
        )
        properties.application_security_groups = AAZListType(
            serialized_name="applicationSecurityGroups",
        )
        properties.gateway_load_balancer = AAZObjectType(
            serialized_name="gatewayLoadBalancer",
        )
        cls._build_schema_sub_resource_read(properties.gateway_load_balancer)
        properties.load_balancer_backend_address_pools = AAZListType(
            serialized_name="loadBalancerBackendAddressPools",
        )
        properties.load_balancer_inbound_nat_rules = AAZListType(
            serialized_name="loadBalancerInboundNatRules",
        )
        properties.primary = AAZBoolType()
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_prefix_length = AAZIntType(
            serialized_name="privateIPAddressPrefixLength",
            nullable=True,
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.private_link_connection_properties = AAZObjectType(
            serialized_name="privateLinkConnectionProperties",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)
        properties.virtual_network_taps = AAZListType(
            serialized_name="virtualNetworkTaps",
        )

        application_gateway_backend_address_pools = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools
        application_gateway_backend_address_pools.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties
        properties.backend_addresses = AAZListType(
            serialized_name="backendAddresses",
        )
        properties.backend_ip_configurations = AAZListType(
            serialized_name="backendIPConfigurations",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        backend_addresses = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties.backend_addresses
        backend_addresses.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties.backend_addresses.Element
        _element.fqdn = AAZStrType()
        _element.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )

        backend_ip_configurations = _schema_network_interface_ip_configuration_read.properties.application_gateway_backend_address_pools.Element.properties.backend_ip_configurations
        backend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(backend_ip_configurations.Element)

        application_security_groups = _schema_network_interface_ip_configuration_read.properties.application_security_groups
        application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(application_security_groups.Element)

        load_balancer_backend_address_pools = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools
        load_balancer_backend_address_pools.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties
        properties.backend_ip_configurations = AAZListType(
            serialized_name="backendIPConfigurations",
            flags={"read_only": True},
        )
        properties.drain_period_in_seconds = AAZIntType(
            serialized_name="drainPeriodInSeconds",
        )
        properties.inbound_nat_rules = AAZListType(
            serialized_name="inboundNatRules",
            flags={"read_only": True},
        )
        properties.load_balancer_backend_addresses = AAZListType(
            serialized_name="loadBalancerBackendAddresses",
        )
        properties.load_balancing_rules = AAZListType(
            serialized_name="loadBalancingRules",
            flags={"read_only": True},
        )
        properties.location = AAZStrType()
        properties.outbound_rule = AAZObjectType(
            serialized_name="outboundRule",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.outbound_rule)
        properties.outbound_rules = AAZListType(
            serialized_name="outboundRules",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.sync_mode = AAZStrType(
            serialized_name="syncMode",
        )
        properties.tunnel_interfaces = AAZListType(
            serialized_name="tunnelInterfaces",
        )
        properties.virtual_network = AAZObjectType(
            serialized_name="virtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.virtual_network)

        backend_ip_configurations = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.backend_ip_configurations
        backend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(backend_ip_configurations.Element)

        inbound_nat_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.inbound_nat_rules
        inbound_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(inbound_nat_rules.Element)

        load_balancer_backend_addresses = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses
        load_balancer_backend_addresses.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties
        properties.admin_state = AAZStrType(
            serialized_name="adminState",
        )
        properties.inbound_nat_rules_port_mapping = AAZListType(
            serialized_name="inboundNatRulesPortMapping",
            flags={"read_only": True},
        )
        properties.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )
        properties.load_balancer_frontend_ip_configuration = AAZObjectType(
            serialized_name="loadBalancerFrontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.load_balancer_frontend_ip_configuration)
        properties.network_interface_ip_configuration = AAZObjectType(
            serialized_name="networkInterfaceIPConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.network_interface_ip_configuration)
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)
        properties.virtual_network = AAZObjectType(
            serialized_name="virtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.virtual_network)

        inbound_nat_rules_port_mapping = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties.inbound_nat_rules_port_mapping
        inbound_nat_rules_port_mapping.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancer_backend_addresses.Element.properties.inbound_nat_rules_port_mapping.Element
        _element.backend_port = AAZIntType(
            serialized_name="backendPort",
        )
        _element.frontend_port = AAZIntType(
            serialized_name="frontendPort",
        )
        _element.inbound_nat_rule_name = AAZStrType(
            serialized_name="inboundNatRuleName",
        )

        load_balancing_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.load_balancing_rules
        load_balancing_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancing_rules.Element)

        outbound_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.outbound_rules
        outbound_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(outbound_rules.Element)

        tunnel_interfaces = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.tunnel_interfaces
        tunnel_interfaces.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_backend_address_pools.Element.properties.tunnel_interfaces.Element
        _element.identifier = AAZIntType()
        _element.port = AAZIntType()
        _element.protocol = AAZStrType()
        _element.type = AAZStrType()

        load_balancer_inbound_nat_rules = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules
        load_balancer_inbound_nat_rules.Element = AAZObjectType()

        _element = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_ip_configuration_read.properties.load_balancer_inbound_nat_rules.Element.properties
        properties.backend_address_pool = AAZObjectType(
            serialized_name="backendAddressPool",
        )
        cls._build_schema_sub_resource_read(properties.backend_address_pool)
        properties.backend_ip_configuration = AAZObjectType(
            serialized_name="backendIPConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_network_interface_ip_configuration_read(properties.backend_ip_configuration)
        properties.backend_port = AAZIntType(
            serialized_name="backendPort",
        )
        properties.enable_floating_ip = AAZBoolType(
            serialized_name="enableFloatingIP",
        )
        properties.enable_tcp_reset = AAZBoolType(
            serialized_name="enableTcpReset",
        )
        properties.frontend_ip_configuration = AAZObjectType(
            serialized_name="frontendIPConfiguration",
        )
        cls._build_schema_sub_resource_read(properties.frontend_ip_configuration)
        properties.frontend_port = AAZIntType(
            serialized_name="frontendPort",
        )
        properties.frontend_port_range_end = AAZIntType(
            serialized_name="frontendPortRangeEnd",
        )
        properties.frontend_port_range_start = AAZIntType(
            serialized_name="frontendPortRangeStart",
        )
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.protocol = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        private_link_connection_properties = _schema_network_interface_ip_configuration_read.properties.private_link_connection_properties
        private_link_connection_properties.fqdns = AAZListType(
            flags={"read_only": True},
        )
        private_link_connection_properties.group_id = AAZStrType(
            serialized_name="groupId",
            flags={"read_only": True},
        )
        private_link_connection_properties.required_member_name = AAZStrType(
            serialized_name="requiredMemberName",
            flags={"read_only": True},
        )

        fqdns = _schema_network_interface_ip_configuration_read.properties.private_link_connection_properties.fqdns
        fqdns.Element = AAZStrType()

        virtual_network_taps = _schema_network_interface_ip_configuration_read.properties.virtual_network_taps
        virtual_network_taps.Element = AAZObjectType()
        cls._build_schema_virtual_network_tap_read(virtual_network_taps.Element)

        _schema.etag = cls._schema_network_interface_ip_configuration_read.etag
        _schema.id = cls._schema_network_interface_ip_configuration_read.id
        _schema.name = cls._schema_network_interface_ip_configuration_read.name
        _schema.properties = cls._schema_network_interface_ip_configuration_read.properties
        _schema.type = cls._schema_network_interface_ip_configuration_read.type

    _schema_network_interface_tap_configuration_read = None

    @classmethod
    def _build_schema_network_interface_tap_configuration_read(cls, _schema):
        if cls._schema_network_interface_tap_configuration_read is not None:
            _schema.etag = cls._schema_network_interface_tap_configuration_read.etag
            _schema.id = cls._schema_network_interface_tap_configuration_read.id
            _schema.name = cls._schema_network_interface_tap_configuration_read.name
            _schema.properties = cls._schema_network_interface_tap_configuration_read.properties
            _schema.type = cls._schema_network_interface_tap_configuration_read.type
            return

        cls._schema_network_interface_tap_configuration_read = _schema_network_interface_tap_configuration_read = AAZObjectType()

        network_interface_tap_configuration_read = _schema_network_interface_tap_configuration_read
        network_interface_tap_configuration_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_tap_configuration_read.id = AAZStrType()
        network_interface_tap_configuration_read.name = AAZStrType()
        network_interface_tap_configuration_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_tap_configuration_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_tap_configuration_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.virtual_network_tap = AAZObjectType(
            serialized_name="virtualNetworkTap",
        )
        cls._build_schema_virtual_network_tap_read(properties.virtual_network_tap)

        _schema.etag = cls._schema_network_interface_tap_configuration_read.etag
        _schema.id = cls._schema_network_interface_tap_configuration_read.id
        _schema.name = cls._schema_network_interface_tap_configuration_read.name
        _schema.properties = cls._schema_network_interface_tap_configuration_read.properties
        _schema.type = cls._schema_network_interface_tap_configuration_read.type

    _schema_network_interface_read = None

    @classmethod
    def _build_schema_network_interface_read(cls, _schema):
        if cls._schema_network_interface_read is not None:
            _schema.etag = cls._schema_network_interface_read.etag
            _schema.extended_location = cls._schema_network_interface_read.extended_location
            _schema.id = cls._schema_network_interface_read.id
            _schema.location = cls._schema_network_interface_read.location
            _schema.name = cls._schema_network_interface_read.name
            _schema.properties = cls._schema_network_interface_read.properties
            _schema.tags = cls._schema_network_interface_read.tags
            _schema.type = cls._schema_network_interface_read.type
            return

        cls._schema_network_interface_read = _schema_network_interface_read = AAZObjectType()

        network_interface_read = _schema_network_interface_read
        network_interface_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(network_interface_read.extended_location)
        network_interface_read.id = AAZStrType()
        network_interface_read.location = AAZStrType()
        network_interface_read.name = AAZStrType(
            flags={"read_only": True},
        )
        network_interface_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_interface_read.tags = AAZDictType()
        network_interface_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties
        properties.auxiliary_mode = AAZStrType(
            serialized_name="auxiliaryMode",
        )
        properties.auxiliary_sku = AAZStrType(
            serialized_name="auxiliarySku",
        )
        properties.default_outbound_connectivity_enabled = AAZBoolType(
            serialized_name="defaultOutboundConnectivityEnabled",
            flags={"read_only": True},
        )
        properties.disable_tcp_state_tracking = AAZBoolType(
            serialized_name="disableTcpStateTracking",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.dscp_configuration = AAZObjectType(
            serialized_name="dscpConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.dscp_configuration)
        properties.enable_accelerated_networking = AAZBoolType(
            serialized_name="enableAcceleratedNetworking",
        )
        properties.enable_ip_forwarding = AAZBoolType(
            serialized_name="enableIPForwarding",
        )
        properties.hosted_workloads = AAZListType(
            serialized_name="hostedWorkloads",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.mac_address = AAZStrType(
            serialized_name="macAddress",
            flags={"read_only": True},
        )
        properties.migration_phase = AAZStrType(
            serialized_name="migrationPhase",
        )
        properties.network_security_group = AAZObjectType(
            serialized_name="networkSecurityGroup",
        )
        cls._build_schema_network_security_group_read(properties.network_security_group)
        properties.nic_type = AAZStrType(
            serialized_name="nicType",
        )
        properties.primary = AAZBoolType(
            flags={"read_only": True},
        )
        properties.private_endpoint = AAZObjectType(
            serialized_name="privateEndpoint",
            flags={"read_only": True},
        )
        cls._build_schema_private_endpoint_read(properties.private_endpoint)
        properties.private_link_service = AAZObjectType(
            serialized_name="privateLinkService",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.tap_configurations = AAZListType(
            serialized_name="tapConfigurations",
            flags={"read_only": True},
        )
        properties.virtual_machine = AAZObjectType(
            serialized_name="virtualMachine",
            flags={"read_only": True},
        )
        cls._build_schema_sub_resource_read(properties.virtual_machine)
        properties.vnet_encryption_supported = AAZBoolType(
            serialized_name="vnetEncryptionSupported",
            flags={"read_only": True},
        )
        properties.workload_type = AAZStrType(
            serialized_name="workloadType",
        )

        dns_settings = _schema_network_interface_read.properties.dns_settings
        dns_settings.applied_dns_servers = AAZListType(
            serialized_name="appliedDnsServers",
            flags={"read_only": True},
        )
        dns_settings.dns_servers = AAZListType(
            serialized_name="dnsServers",
        )
        dns_settings.internal_dns_name_label = AAZStrType(
            serialized_name="internalDnsNameLabel",
        )
        dns_settings.internal_domain_name_suffix = AAZStrType(
            serialized_name="internalDomainNameSuffix",
            flags={"read_only": True},
        )
        dns_settings.internal_fqdn = AAZStrType(
            serialized_name="internalFqdn",
            flags={"read_only": True},
        )

        applied_dns_servers = _schema_network_interface_read.properties.dns_settings.applied_dns_servers
        applied_dns_servers.Element = AAZStrType()

        dns_servers = _schema_network_interface_read.properties.dns_settings.dns_servers
        dns_servers.Element = AAZStrType()

        hosted_workloads = _schema_network_interface_read.properties.hosted_workloads
        hosted_workloads.Element = AAZStrType()

        ip_configurations = _schema_network_interface_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_ip_configuration_read(ip_configurations.Element)

        private_link_service = _schema_network_interface_read.properties.private_link_service
        private_link_service.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(private_link_service.extended_location)
        private_link_service.id = AAZStrType()
        private_link_service.location = AAZStrType()
        private_link_service.name = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_link_service.tags = AAZDictType()
        private_link_service.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties.private_link_service.properties
        properties.alias = AAZStrType(
            flags={"read_only": True},
        )
        properties.auto_approval = AAZObjectType(
            serialized_name="autoApproval",
        )
        properties.destination_ip_address = AAZStrType(
            serialized_name="destinationIPAddress",
        )
        properties.enable_proxy_protocol = AAZBoolType(
            serialized_name="enableProxyProtocol",
        )
        properties.fqdns = AAZListType()
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.load_balancer_frontend_ip_configurations = AAZListType(
            serialized_name="loadBalancerFrontendIpConfigurations",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.private_endpoint_connections = AAZListType(
            serialized_name="privateEndpointConnections",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.visibility = AAZObjectType()

        auto_approval = _schema_network_interface_read.properties.private_link_service.properties.auto_approval
        auto_approval.subscriptions = AAZListType()

        subscriptions = _schema_network_interface_read.properties.private_link_service.properties.auto_approval.subscriptions
        subscriptions.Element = AAZStrType()

        fqdns = _schema_network_interface_read.properties.private_link_service.properties.fqdns
        fqdns.Element = AAZStrType()

        ip_configurations = _schema_network_interface_read.properties.private_link_service.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_network_interface_read.properties.private_link_service.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties.private_link_service.properties.ip_configurations.Element.properties
        properties.primary = AAZBoolType()
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        load_balancer_frontend_ip_configurations = _schema_network_interface_read.properties.private_link_service.properties.load_balancer_frontend_ip_configurations
        load_balancer_frontend_ip_configurations.Element = AAZObjectType()
        cls._build_schema_frontend_ip_configuration_read(load_balancer_frontend_ip_configurations.Element)

        network_interfaces = _schema_network_interface_read.properties.private_link_service.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        private_endpoint_connections = _schema_network_interface_read.properties.private_link_service.properties.private_endpoint_connections
        private_endpoint_connections.Element = AAZObjectType()

        _element = _schema_network_interface_read.properties.private_link_service.properties.private_endpoint_connections.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_interface_read.properties.private_link_service.properties.private_endpoint_connections.Element.properties
        properties.link_identifier = AAZStrType(
            serialized_name="linkIdentifier",
            flags={"read_only": True},
        )
        properties.private_endpoint = AAZObjectType(
            serialized_name="privateEndpoint",
            flags={"read_only": True},
        )
        cls._build_schema_private_endpoint_read(properties.private_endpoint)
        properties.private_endpoint_location = AAZStrType(
            serialized_name="privateEndpointLocation",
            flags={"read_only": True},
        )
        properties.private_link_service_connection_state = AAZObjectType(
            serialized_name="privateLinkServiceConnectionState",
        )
        cls._build_schema_private_link_service_connection_state_read(properties.private_link_service_connection_state)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        visibility = _schema_network_interface_read.properties.private_link_service.properties.visibility
        visibility.subscriptions = AAZListType()

        subscriptions = _schema_network_interface_read.properties.private_link_service.properties.visibility.subscriptions
        subscriptions.Element = AAZStrType()

        tags = _schema_network_interface_read.properties.private_link_service.tags
        tags.Element = AAZStrType()

        tap_configurations = _schema_network_interface_read.properties.tap_configurations
        tap_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_tap_configuration_read(tap_configurations.Element)

        tags = _schema_network_interface_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_network_interface_read.etag
        _schema.extended_location = cls._schema_network_interface_read.extended_location
        _schema.id = cls._schema_network_interface_read.id
        _schema.location = cls._schema_network_interface_read.location
        _schema.name = cls._schema_network_interface_read.name
        _schema.properties = cls._schema_network_interface_read.properties
        _schema.tags = cls._schema_network_interface_read.tags
        _schema.type = cls._schema_network_interface_read.type

    _schema_network_security_group_read = None

    @classmethod
    def _build_schema_network_security_group_read(cls, _schema):
        if cls._schema_network_security_group_read is not None:
            _schema.etag = cls._schema_network_security_group_read.etag
            _schema.id = cls._schema_network_security_group_read.id
            _schema.location = cls._schema_network_security_group_read.location
            _schema.name = cls._schema_network_security_group_read.name
            _schema.properties = cls._schema_network_security_group_read.properties
            _schema.tags = cls._schema_network_security_group_read.tags
            _schema.type = cls._schema_network_security_group_read.type
            return

        cls._schema_network_security_group_read = _schema_network_security_group_read = AAZObjectType()

        network_security_group_read = _schema_network_security_group_read
        network_security_group_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        network_security_group_read.id = AAZStrType()
        network_security_group_read.location = AAZStrType()
        network_security_group_read.name = AAZStrType(
            flags={"read_only": True},
        )
        network_security_group_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        network_security_group_read.tags = AAZDictType()
        network_security_group_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_network_security_group_read.properties
        properties.default_security_rules = AAZListType(
            serialized_name="defaultSecurityRules",
            flags={"read_only": True},
        )
        properties.flow_logs = AAZListType(
            serialized_name="flowLogs",
            flags={"read_only": True},
        )
        properties.flush_connection = AAZBoolType(
            serialized_name="flushConnection",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.security_rules = AAZListType(
            serialized_name="securityRules",
        )
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        default_security_rules = _schema_network_security_group_read.properties.default_security_rules
        default_security_rules.Element = AAZObjectType()
        cls._build_schema_security_rule_read(default_security_rules.Element)

        flow_logs = _schema_network_security_group_read.properties.flow_logs
        flow_logs.Element = AAZObjectType()

        _element = _schema_network_security_group_read.properties.flow_logs.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.identity = AAZIdentityObjectType()
        _element.location = AAZStrType()
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        identity = _schema_network_security_group_read.properties.flow_logs.Element.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType()
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_network_security_group_read.properties.flow_logs.Element.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_network_security_group_read.properties.flow_logs.Element.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_network_security_group_read.properties.flow_logs.Element.properties
        properties.enabled = AAZBoolType()
        properties.enabled_filtering_criteria = AAZStrType(
            serialized_name="enabledFilteringCriteria",
        )
        properties.flow_analytics_configuration = AAZObjectType(
            serialized_name="flowAnalyticsConfiguration",
        )
        properties.format = AAZObjectType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.retention_policy = AAZObjectType(
            serialized_name="retentionPolicy",
        )
        properties.storage_id = AAZStrType(
            serialized_name="storageId",
            flags={"required": True},
        )
        properties.target_resource_guid = AAZStrType(
            serialized_name="targetResourceGuid",
            flags={"read_only": True},
        )
        properties.target_resource_id = AAZStrType(
            serialized_name="targetResourceId",
            flags={"required": True},
        )

        flow_analytics_configuration = _schema_network_security_group_read.properties.flow_logs.Element.properties.flow_analytics_configuration
        flow_analytics_configuration.network_watcher_flow_analytics_configuration = AAZObjectType(
            serialized_name="networkWatcherFlowAnalyticsConfiguration",
        )

        network_watcher_flow_analytics_configuration = _schema_network_security_group_read.properties.flow_logs.Element.properties.flow_analytics_configuration.network_watcher_flow_analytics_configuration
        network_watcher_flow_analytics_configuration.enabled = AAZBoolType()
        network_watcher_flow_analytics_configuration.traffic_analytics_interval = AAZIntType(
            serialized_name="trafficAnalyticsInterval",
        )
        network_watcher_flow_analytics_configuration.workspace_id = AAZStrType(
            serialized_name="workspaceId",
        )
        network_watcher_flow_analytics_configuration.workspace_region = AAZStrType(
            serialized_name="workspaceRegion",
        )
        network_watcher_flow_analytics_configuration.workspace_resource_id = AAZStrType(
            serialized_name="workspaceResourceId",
        )

        format = _schema_network_security_group_read.properties.flow_logs.Element.properties.format
        format.type = AAZStrType()
        format.version = AAZIntType()

        retention_policy = _schema_network_security_group_read.properties.flow_logs.Element.properties.retention_policy
        retention_policy.days = AAZIntType()
        retention_policy.enabled = AAZBoolType()

        tags = _schema_network_security_group_read.properties.flow_logs.Element.tags
        tags.Element = AAZStrType()

        network_interfaces = _schema_network_security_group_read.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        security_rules = _schema_network_security_group_read.properties.security_rules
        security_rules.Element = AAZObjectType()
        cls._build_schema_security_rule_read(security_rules.Element)

        subnets = _schema_network_security_group_read.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_network_security_group_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_network_security_group_read.etag
        _schema.id = cls._schema_network_security_group_read.id
        _schema.location = cls._schema_network_security_group_read.location
        _schema.name = cls._schema_network_security_group_read.name
        _schema.properties = cls._schema_network_security_group_read.properties
        _schema.tags = cls._schema_network_security_group_read.tags
        _schema.type = cls._schema_network_security_group_read.type

    _schema_private_endpoint_read = None

    @classmethod
    def _build_schema_private_endpoint_read(cls, _schema):
        if cls._schema_private_endpoint_read is not None:
            _schema.etag = cls._schema_private_endpoint_read.etag
            _schema.extended_location = cls._schema_private_endpoint_read.extended_location
            _schema.id = cls._schema_private_endpoint_read.id
            _schema.location = cls._schema_private_endpoint_read.location
            _schema.name = cls._schema_private_endpoint_read.name
            _schema.properties = cls._schema_private_endpoint_read.properties
            _schema.tags = cls._schema_private_endpoint_read.tags
            _schema.type = cls._schema_private_endpoint_read.type
            return

        cls._schema_private_endpoint_read = _schema_private_endpoint_read = AAZObjectType(
            flags={"read_only": True}
        )

        private_endpoint_read = _schema_private_endpoint_read
        private_endpoint_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_endpoint_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(private_endpoint_read.extended_location)
        private_endpoint_read.id = AAZStrType()
        private_endpoint_read.location = AAZStrType()
        private_endpoint_read.name = AAZStrType(
            flags={"read_only": True},
        )
        private_endpoint_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_endpoint_read.tags = AAZDictType()
        private_endpoint_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_endpoint_read.properties
        properties.application_security_groups = AAZListType(
            serialized_name="applicationSecurityGroups",
        )
        properties.custom_dns_configs = AAZListType(
            serialized_name="customDnsConfigs",
        )
        properties.custom_network_interface_name = AAZStrType(
            serialized_name="customNetworkInterfaceName",
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.manual_private_link_service_connections = AAZListType(
            serialized_name="manualPrivateLinkServiceConnections",
        )
        properties.network_interfaces = AAZListType(
            serialized_name="networkInterfaces",
            flags={"read_only": True},
        )
        properties.private_link_service_connections = AAZListType(
            serialized_name="privateLinkServiceConnections",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        application_security_groups = _schema_private_endpoint_read.properties.application_security_groups
        application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(application_security_groups.Element)

        custom_dns_configs = _schema_private_endpoint_read.properties.custom_dns_configs
        custom_dns_configs.Element = AAZObjectType()

        _element = _schema_private_endpoint_read.properties.custom_dns_configs.Element
        _element.fqdn = AAZStrType()
        _element.ip_addresses = AAZListType(
            serialized_name="ipAddresses",
        )

        ip_addresses = _schema_private_endpoint_read.properties.custom_dns_configs.Element.ip_addresses
        ip_addresses.Element = AAZStrType()

        ip_configurations = _schema_private_endpoint_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_private_endpoint_read.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_endpoint_read.properties.ip_configurations.Element.properties
        properties.group_id = AAZStrType(
            serialized_name="groupId",
        )
        properties.member_name = AAZStrType(
            serialized_name="memberName",
        )
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
        )

        manual_private_link_service_connections = _schema_private_endpoint_read.properties.manual_private_link_service_connections
        manual_private_link_service_connections.Element = AAZObjectType()
        cls._build_schema_private_link_service_connection_read(manual_private_link_service_connections.Element)

        network_interfaces = _schema_private_endpoint_read.properties.network_interfaces
        network_interfaces.Element = AAZObjectType()
        cls._build_schema_network_interface_read(network_interfaces.Element)

        private_link_service_connections = _schema_private_endpoint_read.properties.private_link_service_connections
        private_link_service_connections.Element = AAZObjectType()
        cls._build_schema_private_link_service_connection_read(private_link_service_connections.Element)

        tags = _schema_private_endpoint_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_private_endpoint_read.etag
        _schema.extended_location = cls._schema_private_endpoint_read.extended_location
        _schema.id = cls._schema_private_endpoint_read.id
        _schema.location = cls._schema_private_endpoint_read.location
        _schema.name = cls._schema_private_endpoint_read.name
        _schema.properties = cls._schema_private_endpoint_read.properties
        _schema.tags = cls._schema_private_endpoint_read.tags
        _schema.type = cls._schema_private_endpoint_read.type

    _schema_private_link_service_connection_state_read = None

    @classmethod
    def _build_schema_private_link_service_connection_state_read(cls, _schema):
        if cls._schema_private_link_service_connection_state_read is not None:
            _schema.actions_required = cls._schema_private_link_service_connection_state_read.actions_required
            _schema.description = cls._schema_private_link_service_connection_state_read.description
            _schema.status = cls._schema_private_link_service_connection_state_read.status
            return

        cls._schema_private_link_service_connection_state_read = _schema_private_link_service_connection_state_read = AAZObjectType()

        private_link_service_connection_state_read = _schema_private_link_service_connection_state_read
        private_link_service_connection_state_read.actions_required = AAZStrType(
            serialized_name="actionsRequired",
        )
        private_link_service_connection_state_read.description = AAZStrType()
        private_link_service_connection_state_read.status = AAZStrType()

        _schema.actions_required = cls._schema_private_link_service_connection_state_read.actions_required
        _schema.description = cls._schema_private_link_service_connection_state_read.description
        _schema.status = cls._schema_private_link_service_connection_state_read.status

    _schema_private_link_service_connection_read = None

    @classmethod
    def _build_schema_private_link_service_connection_read(cls, _schema):
        if cls._schema_private_link_service_connection_read is not None:
            _schema.etag = cls._schema_private_link_service_connection_read.etag
            _schema.id = cls._schema_private_link_service_connection_read.id
            _schema.name = cls._schema_private_link_service_connection_read.name
            _schema.properties = cls._schema_private_link_service_connection_read.properties
            _schema.type = cls._schema_private_link_service_connection_read.type
            return

        cls._schema_private_link_service_connection_read = _schema_private_link_service_connection_read = AAZObjectType()

        private_link_service_connection_read = _schema_private_link_service_connection_read
        private_link_service_connection_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        private_link_service_connection_read.id = AAZStrType()
        private_link_service_connection_read.name = AAZStrType()
        private_link_service_connection_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        private_link_service_connection_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_private_link_service_connection_read.properties
        properties.group_ids = AAZListType(
            serialized_name="groupIds",
        )
        properties.private_link_service_connection_state = AAZObjectType(
            serialized_name="privateLinkServiceConnectionState",
        )
        cls._build_schema_private_link_service_connection_state_read(properties.private_link_service_connection_state)
        properties.private_link_service_id = AAZStrType(
            serialized_name="privateLinkServiceId",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.request_message = AAZStrType(
            serialized_name="requestMessage",
        )

        group_ids = _schema_private_link_service_connection_read.properties.group_ids
        group_ids.Element = AAZStrType()

        _schema.etag = cls._schema_private_link_service_connection_read.etag
        _schema.id = cls._schema_private_link_service_connection_read.id
        _schema.name = cls._schema_private_link_service_connection_read.name
        _schema.properties = cls._schema_private_link_service_connection_read.properties
        _schema.type = cls._schema_private_link_service_connection_read.type

    _schema_public_ip_address_read = None

    @classmethod
    def _build_schema_public_ip_address_read(cls, _schema):
        if cls._schema_public_ip_address_read is not None:
            _schema.etag = cls._schema_public_ip_address_read.etag
            _schema.extended_location = cls._schema_public_ip_address_read.extended_location
            _schema.id = cls._schema_public_ip_address_read.id
            _schema.location = cls._schema_public_ip_address_read.location
            _schema.name = cls._schema_public_ip_address_read.name
            _schema.properties = cls._schema_public_ip_address_read.properties
            _schema.sku = cls._schema_public_ip_address_read.sku
            _schema.tags = cls._schema_public_ip_address_read.tags
            _schema.type = cls._schema_public_ip_address_read.type
            _schema.zones = cls._schema_public_ip_address_read.zones
            return

        cls._schema_public_ip_address_read = _schema_public_ip_address_read = AAZObjectType()

        public_ip_address_read = _schema_public_ip_address_read
        public_ip_address_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        cls._build_schema_extended_location_read(public_ip_address_read.extended_location)
        public_ip_address_read.id = AAZStrType()
        public_ip_address_read.location = AAZStrType()
        public_ip_address_read.name = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        public_ip_address_read.sku = AAZObjectType()
        public_ip_address_read.tags = AAZDictType()
        public_ip_address_read.type = AAZStrType(
            flags={"read_only": True},
        )
        public_ip_address_read.zones = AAZListType()

        properties = _schema_public_ip_address_read.properties
        properties.ddos_settings = AAZObjectType(
            serialized_name="ddosSettings",
        )
        properties.delete_option = AAZStrType(
            serialized_name="deleteOption",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.ip_address = AAZStrType(
            serialized_name="ipAddress",
        )
        properties.ip_configuration = AAZObjectType(
            serialized_name="ipConfiguration",
            flags={"read_only": True},
        )
        cls._build_schema_ip_configuration_read(properties.ip_configuration)
        properties.ip_tags = AAZListType(
            serialized_name="ipTags",
        )
        properties.linked_public_ip_address = AAZObjectType(
            serialized_name="linkedPublicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.linked_public_ip_address)
        properties.migration_phase = AAZStrType(
            serialized_name="migrationPhase",
        )
        properties.nat_gateway = AAZObjectType(
            serialized_name="natGateway",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address_version = AAZStrType(
            serialized_name="publicIPAddressVersion",
        )
        properties.public_ip_allocation_method = AAZStrType(
            serialized_name="publicIPAllocationMethod",
        )
        properties.public_ip_prefix = AAZObjectType(
            serialized_name="publicIPPrefix",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_prefix)
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.service_public_ip_address = AAZObjectType(
            serialized_name="servicePublicIPAddress",
        )
        cls._build_schema_public_ip_address_read(properties.service_public_ip_address)

        ddos_settings = _schema_public_ip_address_read.properties.ddos_settings
        ddos_settings.ddos_protection_plan = AAZObjectType(
            serialized_name="ddosProtectionPlan",
        )
        cls._build_schema_sub_resource_read(ddos_settings.ddos_protection_plan)
        ddos_settings.protection_mode = AAZStrType(
            serialized_name="protectionMode",
        )

        dns_settings = _schema_public_ip_address_read.properties.dns_settings
        dns_settings.domain_name_label = AAZStrType(
            serialized_name="domainNameLabel",
        )
        dns_settings.domain_name_label_scope = AAZStrType(
            serialized_name="domainNameLabelScope",
        )
        dns_settings.fqdn = AAZStrType()
        dns_settings.reverse_fqdn = AAZStrType(
            serialized_name="reverseFqdn",
        )

        ip_tags = _schema_public_ip_address_read.properties.ip_tags
        ip_tags.Element = AAZObjectType()

        _element = _schema_public_ip_address_read.properties.ip_tags.Element
        _element.ip_tag_type = AAZStrType(
            serialized_name="ipTagType",
        )
        _element.tag = AAZStrType()

        nat_gateway = _schema_public_ip_address_read.properties.nat_gateway
        nat_gateway.etag = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.id = AAZStrType()
        nat_gateway.location = AAZStrType()
        nat_gateway.name = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        nat_gateway.sku = AAZObjectType()
        nat_gateway.tags = AAZDictType()
        nat_gateway.type = AAZStrType(
            flags={"read_only": True},
        )
        nat_gateway.zones = AAZListType()

        properties = _schema_public_ip_address_read.properties.nat_gateway.properties
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_addresses = AAZListType(
            serialized_name="publicIpAddresses",
        )
        properties.public_ip_addresses_v6 = AAZListType(
            serialized_name="publicIpAddressesV6",
        )
        properties.public_ip_prefixes = AAZListType(
            serialized_name="publicIpPrefixes",
        )
        properties.public_ip_prefixes_v6 = AAZListType(
            serialized_name="publicIpPrefixesV6",
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.source_virtual_network = AAZObjectType(
            serialized_name="sourceVirtualNetwork",
        )
        cls._build_schema_sub_resource_read(properties.source_virtual_network)
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        public_ip_addresses = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_addresses
        public_ip_addresses.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_addresses.Element)

        public_ip_addresses_v6 = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_addresses_v6
        public_ip_addresses_v6.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_addresses_v6.Element)

        public_ip_prefixes = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_prefixes
        public_ip_prefixes.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_prefixes.Element)

        public_ip_prefixes_v6 = _schema_public_ip_address_read.properties.nat_gateway.properties.public_ip_prefixes_v6
        public_ip_prefixes_v6.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(public_ip_prefixes_v6.Element)

        subnets = _schema_public_ip_address_read.properties.nat_gateway.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(subnets.Element)

        sku = _schema_public_ip_address_read.properties.nat_gateway.sku
        sku.name = AAZStrType()

        tags = _schema_public_ip_address_read.properties.nat_gateway.tags
        tags.Element = AAZStrType()

        zones = _schema_public_ip_address_read.properties.nat_gateway.zones
        zones.Element = AAZStrType()

        sku = _schema_public_ip_address_read.sku
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        tags = _schema_public_ip_address_read.tags
        tags.Element = AAZStrType()

        zones = _schema_public_ip_address_read.zones
        zones.Element = AAZStrType()

        _schema.etag = cls._schema_public_ip_address_read.etag
        _schema.extended_location = cls._schema_public_ip_address_read.extended_location
        _schema.id = cls._schema_public_ip_address_read.id
        _schema.location = cls._schema_public_ip_address_read.location
        _schema.name = cls._schema_public_ip_address_read.name
        _schema.properties = cls._schema_public_ip_address_read.properties
        _schema.sku = cls._schema_public_ip_address_read.sku
        _schema.tags = cls._schema_public_ip_address_read.tags
        _schema.type = cls._schema_public_ip_address_read.type
        _schema.zones = cls._schema_public_ip_address_read.zones

    _schema_security_rule_read = None

    @classmethod
    def _build_schema_security_rule_read(cls, _schema):
        if cls._schema_security_rule_read is not None:
            _schema.etag = cls._schema_security_rule_read.etag
            _schema.id = cls._schema_security_rule_read.id
            _schema.name = cls._schema_security_rule_read.name
            _schema.properties = cls._schema_security_rule_read.properties
            _schema.type = cls._schema_security_rule_read.type
            return

        cls._schema_security_rule_read = _schema_security_rule_read = AAZObjectType()

        security_rule_read = _schema_security_rule_read
        security_rule_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        security_rule_read.id = AAZStrType()
        security_rule_read.name = AAZStrType()
        security_rule_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        security_rule_read.type = AAZStrType()

        properties = _schema_security_rule_read.properties
        properties.access = AAZStrType(
            flags={"required": True},
        )
        properties.description = AAZStrType()
        properties.destination_address_prefix = AAZStrType(
            serialized_name="destinationAddressPrefix",
        )
        properties.destination_address_prefixes = AAZListType(
            serialized_name="destinationAddressPrefixes",
        )
        properties.destination_application_security_groups = AAZListType(
            serialized_name="destinationApplicationSecurityGroups",
        )
        properties.destination_port_range = AAZStrType(
            serialized_name="destinationPortRange",
        )
        properties.destination_port_ranges = AAZListType(
            serialized_name="destinationPortRanges",
        )
        properties.direction = AAZStrType(
            flags={"required": True},
        )
        properties.priority = AAZIntType(
            flags={"required": True},
        )
        properties.protocol = AAZStrType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.source_address_prefix = AAZStrType(
            serialized_name="sourceAddressPrefix",
        )
        properties.source_address_prefixes = AAZListType(
            serialized_name="sourceAddressPrefixes",
        )
        properties.source_application_security_groups = AAZListType(
            serialized_name="sourceApplicationSecurityGroups",
        )
        properties.source_port_range = AAZStrType(
            serialized_name="sourcePortRange",
        )
        properties.source_port_ranges = AAZListType(
            serialized_name="sourcePortRanges",
        )

        destination_address_prefixes = _schema_security_rule_read.properties.destination_address_prefixes
        destination_address_prefixes.Element = AAZStrType()

        destination_application_security_groups = _schema_security_rule_read.properties.destination_application_security_groups
        destination_application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(destination_application_security_groups.Element)

        destination_port_ranges = _schema_security_rule_read.properties.destination_port_ranges
        destination_port_ranges.Element = AAZStrType()

        source_address_prefixes = _schema_security_rule_read.properties.source_address_prefixes
        source_address_prefixes.Element = AAZStrType()

        source_application_security_groups = _schema_security_rule_read.properties.source_application_security_groups
        source_application_security_groups.Element = AAZObjectType()
        cls._build_schema_application_security_group_read(source_application_security_groups.Element)

        source_port_ranges = _schema_security_rule_read.properties.source_port_ranges
        source_port_ranges.Element = AAZStrType()

        _schema.etag = cls._schema_security_rule_read.etag
        _schema.id = cls._schema_security_rule_read.id
        _schema.name = cls._schema_security_rule_read.name
        _schema.properties = cls._schema_security_rule_read.properties
        _schema.type = cls._schema_security_rule_read.type

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType(
            flags={"read_only": True}
        )

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_subnet_read = None

    @classmethod
    def _build_schema_subnet_read(cls, _schema):
        if cls._schema_subnet_read is not None:
            _schema.etag = cls._schema_subnet_read.etag
            _schema.id = cls._schema_subnet_read.id
            _schema.name = cls._schema_subnet_read.name
            _schema.properties = cls._schema_subnet_read.properties
            _schema.type = cls._schema_subnet_read.type
            return

        cls._schema_subnet_read = _schema_subnet_read = AAZObjectType()

        subnet_read = _schema_subnet_read
        subnet_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        subnet_read.id = AAZStrType()
        subnet_read.name = AAZStrType()
        subnet_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        subnet_read.type = AAZStrType()

        properties = _schema_subnet_read.properties
        properties.address_prefix = AAZStrType(
            serialized_name="addressPrefix",
        )
        properties.address_prefixes = AAZListType(
            serialized_name="addressPrefixes",
        )
        properties.application_gateway_ip_configurations = AAZListType(
            serialized_name="applicationGatewayIPConfigurations",
        )
        properties.default_outbound_access = AAZBoolType(
            serialized_name="defaultOutboundAccess",
        )
        properties.delegations = AAZListType()
        properties.ip_allocations = AAZListType(
            serialized_name="ipAllocations",
        )
        properties.ip_configuration_profiles = AAZListType(
            serialized_name="ipConfigurationProfiles",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
            flags={"read_only": True},
        )
        properties.ipam_pool_prefix_allocations = AAZListType(
            serialized_name="ipamPoolPrefixAllocations",
        )
        properties.nat_gateway = AAZObjectType(
            serialized_name="natGateway",
        )
        cls._build_schema_sub_resource_read(properties.nat_gateway)
        properties.network_security_group = AAZObjectType(
            serialized_name="networkSecurityGroup",
        )
        cls._build_schema_network_security_group_read(properties.network_security_group)
        properties.private_endpoint_network_policies = AAZStrType(
            serialized_name="privateEndpointNetworkPolicies",
        )
        properties.private_endpoints = AAZListType(
            serialized_name="privateEndpoints",
            flags={"read_only": True},
        )
        properties.private_link_service_network_policies = AAZStrType(
            serialized_name="privateLinkServiceNetworkPolicies",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.purpose = AAZStrType(
            flags={"read_only": True},
        )
        properties.resource_navigation_links = AAZListType(
            serialized_name="resourceNavigationLinks",
            flags={"read_only": True},
        )
        properties.route_table = AAZObjectType(
            serialized_name="routeTable",
        )
        properties.service_association_links = AAZListType(
            serialized_name="serviceAssociationLinks",
            flags={"read_only": True},
        )
        properties.service_endpoint_policies = AAZListType(
            serialized_name="serviceEndpointPolicies",
        )
        properties.service_endpoints = AAZListType(
            serialized_name="serviceEndpoints",
        )
        properties.sharing_scope = AAZStrType(
            serialized_name="sharingScope",
        )

        address_prefixes = _schema_subnet_read.properties.address_prefixes
        address_prefixes.Element = AAZStrType()

        application_gateway_ip_configurations = _schema_subnet_read.properties.application_gateway_ip_configurations
        application_gateway_ip_configurations.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.application_gateway_ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.application_gateway_ip_configurations.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)

        delegations = _schema_subnet_read.properties.delegations
        delegations.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.delegations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.delegations.Element.properties
        properties.actions = AAZListType(
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.service_name = AAZStrType(
            serialized_name="serviceName",
        )

        actions = _schema_subnet_read.properties.delegations.Element.properties.actions
        actions.Element = AAZStrType()

        ip_allocations = _schema_subnet_read.properties.ip_allocations
        ip_allocations.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(ip_allocations.Element)

        ip_configuration_profiles = _schema_subnet_read.properties.ip_configuration_profiles
        ip_configuration_profiles.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.ip_configuration_profiles.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.ip_configuration_profiles.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_subnet_read(properties.subnet)

        ip_configurations = _schema_subnet_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()
        cls._build_schema_ip_configuration_read(ip_configurations.Element)

        ipam_pool_prefix_allocations = _schema_subnet_read.properties.ipam_pool_prefix_allocations
        ipam_pool_prefix_allocations.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.ipam_pool_prefix_allocations.Element
        _element.allocated_address_prefixes = AAZListType(
            serialized_name="allocatedAddressPrefixes",
            flags={"read_only": True},
        )
        _element.number_of_ip_addresses = AAZStrType(
            serialized_name="numberOfIpAddresses",
        )
        _element.pool = AAZObjectType(
            flags={"client_flatten": True},
        )

        allocated_address_prefixes = _schema_subnet_read.properties.ipam_pool_prefix_allocations.Element.allocated_address_prefixes
        allocated_address_prefixes.Element = AAZStrType()

        pool = _schema_subnet_read.properties.ipam_pool_prefix_allocations.Element.pool
        pool.id = AAZStrType()

        private_endpoints = _schema_subnet_read.properties.private_endpoints
        private_endpoints.Element = AAZObjectType()
        cls._build_schema_private_endpoint_read(private_endpoints.Element)

        resource_navigation_links = _schema_subnet_read.properties.resource_navigation_links
        resource_navigation_links.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.resource_navigation_links.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.resource_navigation_links.Element.properties
        properties.link = AAZStrType()
        properties.linked_resource_type = AAZStrType(
            serialized_name="linkedResourceType",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        route_table = _schema_subnet_read.properties.route_table
        route_table.etag = AAZStrType(
            flags={"read_only": True},
        )
        route_table.id = AAZStrType()
        route_table.location = AAZStrType()
        route_table.name = AAZStrType(
            flags={"read_only": True},
        )
        route_table.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        route_table.tags = AAZDictType()
        route_table.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.route_table.properties
        properties.disable_bgp_route_propagation = AAZBoolType(
            serialized_name="disableBgpRoutePropagation",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.routes = AAZListType()
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        routes = _schema_subnet_read.properties.route_table.properties.routes
        routes.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.route_table.properties.routes.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.route_table.properties.routes.Element.properties
        properties.address_prefix = AAZStrType(
            serialized_name="addressPrefix",
        )
        properties.has_bgp_override = AAZBoolType(
            serialized_name="hasBgpOverride",
            flags={"read_only": True},
        )
        properties.next_hop_ip_address = AAZStrType(
            serialized_name="nextHopIpAddress",
        )
        properties.next_hop_type = AAZStrType(
            serialized_name="nextHopType",
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        subnets = _schema_subnet_read.properties.route_table.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_subnet_read.properties.route_table.tags
        tags.Element = AAZStrType()

        service_association_links = _schema_subnet_read.properties.service_association_links
        service_association_links.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_association_links.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.service_association_links.Element.properties
        properties.allow_delete = AAZBoolType(
            serialized_name="allowDelete",
        )
        properties.link = AAZStrType()
        properties.linked_resource_type = AAZStrType(
            serialized_name="linkedResourceType",
        )
        properties.locations = AAZListType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        locations = _schema_subnet_read.properties.service_association_links.Element.properties.locations
        locations.Element = AAZStrType()

        service_endpoint_policies = _schema_subnet_read.properties.service_endpoint_policies
        service_endpoint_policies.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoint_policies.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.kind = AAZStrType(
            flags={"read_only": True},
        )
        _element.location = AAZStrType()
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_subnet_read.properties.service_endpoint_policies.Element.properties
        properties.contextual_service_endpoint_policies = AAZListType(
            serialized_name="contextualServiceEndpointPolicies",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.service_alias = AAZStrType(
            serialized_name="serviceAlias",
        )
        properties.service_endpoint_policy_definitions = AAZListType(
            serialized_name="serviceEndpointPolicyDefinitions",
        )
        properties.subnets = AAZListType(
            flags={"read_only": True},
        )

        contextual_service_endpoint_policies = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.contextual_service_endpoint_policies
        contextual_service_endpoint_policies.Element = AAZStrType()

        service_endpoint_policy_definitions = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions
        service_endpoint_policy_definitions.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType()

        properties = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element.properties
        properties.description = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.service = AAZStrType()
        properties.service_resources = AAZListType(
            serialized_name="serviceResources",
        )

        service_resources = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.service_endpoint_policy_definitions.Element.properties.service_resources
        service_resources.Element = AAZStrType()

        subnets = _schema_subnet_read.properties.service_endpoint_policies.Element.properties.subnets
        subnets.Element = AAZObjectType()
        cls._build_schema_subnet_read(subnets.Element)

        tags = _schema_subnet_read.properties.service_endpoint_policies.Element.tags
        tags.Element = AAZStrType()

        service_endpoints = _schema_subnet_read.properties.service_endpoints
        service_endpoints.Element = AAZObjectType()

        _element = _schema_subnet_read.properties.service_endpoints.Element
        _element.locations = AAZListType()
        _element.network_identifier = AAZObjectType(
            serialized_name="networkIdentifier",
        )
        cls._build_schema_sub_resource_read(_element.network_identifier)
        _element.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        _element.service = AAZStrType()

        locations = _schema_subnet_read.properties.service_endpoints.Element.locations
        locations.Element = AAZStrType()

        _schema.etag = cls._schema_subnet_read.etag
        _schema.id = cls._schema_subnet_read.id
        _schema.name = cls._schema_subnet_read.name
        _schema.properties = cls._schema_subnet_read.properties
        _schema.type = cls._schema_subnet_read.type

    _schema_virtual_network_tap_read = None

    @classmethod
    def _build_schema_virtual_network_tap_read(cls, _schema):
        if cls._schema_virtual_network_tap_read is not None:
            _schema.etag = cls._schema_virtual_network_tap_read.etag
            _schema.id = cls._schema_virtual_network_tap_read.id
            _schema.location = cls._schema_virtual_network_tap_read.location
            _schema.name = cls._schema_virtual_network_tap_read.name
            _schema.properties = cls._schema_virtual_network_tap_read.properties
            _schema.tags = cls._schema_virtual_network_tap_read.tags
            _schema.type = cls._schema_virtual_network_tap_read.type
            return

        cls._schema_virtual_network_tap_read = _schema_virtual_network_tap_read = AAZObjectType()

        virtual_network_tap_read = _schema_virtual_network_tap_read
        virtual_network_tap_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_tap_read.id = AAZStrType()
        virtual_network_tap_read.location = AAZStrType()
        virtual_network_tap_read.name = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_tap_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        virtual_network_tap_read.tags = AAZDictType()
        virtual_network_tap_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_tap_read.properties
        properties.destination_load_balancer_front_end_ip_configuration = AAZObjectType(
            serialized_name="destinationLoadBalancerFrontEndIPConfiguration",
        )
        cls._build_schema_frontend_ip_configuration_read(properties.destination_load_balancer_front_end_ip_configuration)
        properties.destination_network_interface_ip_configuration = AAZObjectType(
            serialized_name="destinationNetworkInterfaceIPConfiguration",
        )
        cls._build_schema_network_interface_ip_configuration_read(properties.destination_network_interface_ip_configuration)
        properties.destination_port = AAZIntType(
            serialized_name="destinationPort",
        )
        properties.network_interface_tap_configurations = AAZListType(
            serialized_name="networkInterfaceTapConfigurations",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )

        network_interface_tap_configurations = _schema_virtual_network_tap_read.properties.network_interface_tap_configurations
        network_interface_tap_configurations.Element = AAZObjectType()
        cls._build_schema_network_interface_tap_configuration_read(network_interface_tap_configurations.Element)

        tags = _schema_virtual_network_tap_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_virtual_network_tap_read.etag
        _schema.id = cls._schema_virtual_network_tap_read.id
        _schema.location = cls._schema_virtual_network_tap_read.location
        _schema.name = cls._schema_virtual_network_tap_read.name
        _schema.properties = cls._schema_virtual_network_tap_read.properties
        _schema.tags = cls._schema_virtual_network_tap_read.tags
        _schema.type = cls._schema_virtual_network_tap_read.type


__all__ = ["Create"]
