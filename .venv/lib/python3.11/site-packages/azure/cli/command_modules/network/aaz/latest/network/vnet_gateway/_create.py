# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vnet-gateway create",
)
class Create(AAZCommand):
    """Create a virtual network gateway.

    :example: Create a basic virtual network gateway for site-to-site connectivity.
        az network vnet-gateway create -g MyResourceGroup -n MyVnetGateway --public-ip-address MyGatewayIp --vnet MyVnet --gateway-type Vpn --sku VpnGw1 --vpn-type RouteBased --no-wait

    :example: Create a basic virtual network gateway that provides point-to-site connectivity with a RADIUS secret that matches what is configured on a RADIUS server.
        az network vnet-gateway create -g MyResourceGroup -n MyVnetGateway --public-ip-address MyGatewayIp --vnet MyVnet --gateway-type Vpn --sku VpnGw1 --vpn-type RouteBased --address-prefixes ********/24 --client-protocol IkeV2 SSTP --radius-secret 111_aaa --radius-server ********* --vpn-gateway-generation Generation1

    :example: Create a basic virtual network gateway with multi authentication
        az network vnet-gateway create -g MyResourceGroup -n MyVnetGateway --public-ip-address MyGatewayIp --vnet MyVnet --gateway-type Vpn --sku VpnGw1 --vpn-type RouteBased --address-prefixes ********/24 --client-protocol OpenVPN --radius-secret 111_aaa --radius-server ********* --aad-issuer https://sts.windows.net/00000-000000-00000-0000-000/ --aad-tenant https://login.microsoftonline.com/000 --aad-audience 0000-000 --root-cert-name root-cert --root-cert-data "root-cert.cer" --vpn-auth-type AAD Certificate Radius

    :example: Create a virtual network gateway.
        az network vnet-gateway create --gateway-type Vpn --location westus2 --name MyVnetGateway --no-wait --public-ip-addresses myVGPublicIPAddress --resource-group MyResourceGroup --sku Basic --vnet MyVnet --vpn-type PolicyBased
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/virtualnetworkgateways/{}", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="Name of the VNet gateway.",
            required=True,
        )
        _args_schema.edge_zone = AAZStrArg(
            options=["--edge-zone"],
            help="The name of edge zone.",
        )
        _args_schema.location = AAZResourceLocationArg(
            help="Location. Values from: `az account list-locations`. You can configure the default location using `az configure --defaults location=<location>`.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.allow_remote_vnet_traffic = AAZBoolArg(
            options=["--allow-remote-vnet-traffic"],
            help="Configure this gateway to accept traffic from other Azure Virtual Networks. This configuration does not support connectivity to Azure Virtual WAN.",
        )
        _args_schema.allow_vwan_traffic = AAZBoolArg(
            options=["--allow-vwan-traffic"],
            help="Configures this gateway to accept traffic from remote Virtual WAN networks.",
        )
        _args_schema.max_scale_unit = AAZIntArg(
            options=["--max-scale-unit"],
            help="Maximum scale units for auto-scale configuration.",
        )
        _args_schema.min_scale_unit = AAZIntArg(
            options=["--min-scale-unit"],
            help="Minimum scale units for auto-scale configuration.",
        )
        _args_schema.enable_high_bandwidth_vpn_gateway = AAZBoolArg(
            options=["--enable-high-bandwidth", "--enable-high-bandwidth-vpn-gateway"],
            help="To enable Advanced Connectivity feature for VPN gateway",
        )
        _args_schema.enable_private_ip = AAZBoolArg(
            options=["--enable-private-ip"],
            help="Whether private IP needs to be enabled on this gateway for connections or not.",
        )
        _args_schema.gateway_default_site = AAZStrArg(
            options=["--gateway-default-site"],
            help="Name or ID of a local network gateway representing a local network site with default routes.",
        )
        _args_schema.gateway_type = AAZStrArg(
            options=["--gateway-type"],
            help="The gateway type.",
            default="Vpn",
            enum={"ExpressRoute": "ExpressRoute", "LocalGateway": "LocalGateway", "Vpn": "Vpn"},
        )
        _args_schema.ip_configurations = AAZListArg(
            options=["--ip-configurations"],
            help="IP configurations for virtual network gateway.",
        )
        _args_schema.resiliency_model = AAZStrArg(
            options=["--resiliency-model"],
            help="Indicates if the Express Route Gateway has resiliency model of MultiHomed or SingleHomed",
            enum={"MultiHomed": "MultiHomed", "SingleHomed": "SingleHomed"},
        )
        _args_schema.sku = AAZStrArg(
            options=["--sku"],
            help="VNet gateway SKU.",
            default="Basic",
            enum={"Basic": "Basic", "ErGw1AZ": "ErGw1AZ", "ErGw2AZ": "ErGw2AZ", "ErGw3AZ": "ErGw3AZ", "ErGwScale": "ErGwScale", "HighPerformance": "HighPerformance", "Standard": "Standard", "UltraPerformance": "UltraPerformance", "VpnGw1": "VpnGw1", "VpnGw1AZ": "VpnGw1AZ", "VpnGw2": "VpnGw2", "VpnGw2AZ": "VpnGw2AZ", "VpnGw3": "VpnGw3", "VpnGw3AZ": "VpnGw3AZ", "VpnGw4": "VpnGw4", "VpnGw4AZ": "VpnGw4AZ", "VpnGw5": "VpnGw5", "VpnGw5AZ": "VpnGw5AZ"},
        )
        _args_schema.edge_zone_vnet_id = AAZStrArg(
            options=["--edge-zone-vnet-id"],
            help="The Extended vnet resource id of the local gateway.",
        )
        _args_schema.vpn_auth_type = AAZListArg(
            options=["--vpn-auth-type"],
            help="VPN authentication types enabled for the virtual network gateway.",
        )
        _args_schema.vpn_gateway_generation = AAZStrArg(
            options=["--vpn-gateway-generation"],
            help="The generation for the virtual network gateway. vpn_gateway_generation should not be provided if gateway_type is not Vpn.",
            enum={"Generation1": "Generation1", "Generation2": "Generation2", "None": "None"},
        )
        _args_schema.vpn_type = AAZStrArg(
            options=["--vpn-type"],
            help="VPN routing type.",
            default="RouteBased",
            enum={"PolicyBased": "PolicyBased", "RouteBased": "RouteBased"},
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="Space-separated tags: key[=value] [key[=value] ...]. Use \"\" to clear existing tags.",
        )

        ip_configurations = cls._args_schema.ip_configurations
        ip_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.ip_configurations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.private_ip_allocation_method = AAZStrArg(
            options=["private-ip-allocation-method"],
            help="The private IP address allocation method.",
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )
        _element.public_ip_address = AAZStrArg(
            options=["public-ip-address"],
            help="The reference to the public IP resource.",
        )
        _element.subnet = AAZStrArg(
            options=["subnet"],
            help="test",
        )

        vpn_auth_type = cls._args_schema.vpn_auth_type
        vpn_auth_type.Element = AAZStrArg(
            enum={"AAD": "AAD", "Certificate": "Certificate", "Radius": "Radius"},
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "AAD Authentication"

        _args_schema = cls._args_schema
        _args_schema.aad_audience = AAZStrArg(
            options=["--aad-audience"],
            arg_group="AAD Authentication",
            help="The AADAudience ID of the VirtualNetworkGateway.",
        )
        _args_schema.aad_issuer = AAZStrArg(
            options=["--aad-issuer"],
            arg_group="AAD Authentication",
            help="The AAD Issuer URI of the VirtualNetworkGateway.",
        )
        _args_schema.aad_tenant = AAZStrArg(
            options=["--aad-tenant"],
            arg_group="AAD Authentication",
            help="The AAD Tenant URI of the VirtualNetworkGateway.",
        )

        # define Arg Group "BGP Peering"

        _args_schema = cls._args_schema
        _args_schema.asn = AAZIntArg(
            options=["--asn"],
            arg_group="BGP Peering",
            help="Autonomous System Number to use for the BGP settings.",
            fmt=AAZIntArgFormat(
                maximum=4294967295,
                minimum=0,
            ),
        )
        _args_schema.bgp_peering_address = AAZStrArg(
            options=["--bgp-peering-address"],
            arg_group="BGP Peering",
            help="IP address to use for BGP peering.",
        )
        _args_schema.peer_weight = AAZIntArg(
            options=["--peer-weight"],
            arg_group="BGP Peering",
            help="Weight (0-100) added to routes learned through BGP peering.",
        )
        _args_schema.enable_bgp = AAZBoolArg(
            options=["--enable-bgp"],
            arg_group="BGP Peering",
            help="Enable BGP (Border Gateway Protocol).",
        )

        # define Arg Group "BgpSettings"

        # define Arg Group "Identity"

        _args_schema = cls._args_schema
        _args_schema.mi_system_assigned = AAZStrArg(
            options=["--system-assigned", "--mi-system-assigned"],
            arg_group="Identity",
            help="Set the system managed identity.",
            blank="True",
        )
        _args_schema.mi_user_assigned = AAZListArg(
            options=["--user-assigned", "--mi-user-assigned"],
            arg_group="Identity",
            help="Set the user managed identities.",
            blank=[],
        )

        mi_user_assigned = cls._args_schema.mi_user_assigned
        mi_user_assigned.Element = AAZStrArg()

        # define Arg Group "Nat Rule"

        _args_schema = cls._args_schema
        _args_schema.nat_rules = AAZListArg(
            options=["--nat-rules"],
            singular_options=["--nat-rule"],
            arg_group="Nat Rule",
            help={"short-summary": "VirtualNetworkGatewayNatRule Resource.", "long-summary": "Usage: --nat-rule name=rule type=Static mode=EgressSnat internal-mappings=********/24 external-mappings=************/24 ip-config-id=/subscriptions/subid/resourceGroups/rg1/providers/Microsoft.Network/virtualNetworkGateways/gateway1/ipConfigurations/default\n        name: Required.The name of the resource that is unique within a resource group. This name can be used to access the resource.\n        internal-mappings: Required.The private IP address internal mapping for NAT.\n        external-mappings: Required.The private IP address external mapping for NAT.\n        type: The type of NAT rule for VPN NAT.\n        mode: The Source NAT direction of a VPN NAT.\n        ip-config-id: The IP Configuration ID this NAT rule applies to."},
        )

        nat_rules = cls._args_schema.nat_rules
        nat_rules.Element = AAZObjectArg()

        _element = cls._args_schema.nat_rules.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="Required. The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.external_mappings_ip = AAZListArg(
            options=["external-mappings-ip"],
            help="The private IP address external mapping for NAT.",
        )
        _element.internal_mappings_ip = AAZListArg(
            options=["internal-mappings-ip"],
            help="The private IP address internal mapping for NAT.",
        )
        _element.ip_config_id = AAZStrArg(
            options=["ip-config-id"],
            help="The IP Configuration ID this NAT rule applies to.",
        )
        _element.mode = AAZStrArg(
            options=["mode"],
            help="The Source NAT direction of a VPN NAT.",
            enum={"EgressSnat": "EgressSnat", "IngressSnat": "IngressSnat"},
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The IP Configuration ID this NAT rule applies to.",
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )

        external_mappings_ip = cls._args_schema.nat_rules.Element.external_mappings_ip
        external_mappings_ip.Element = AAZObjectArg()

        _element = cls._args_schema.nat_rules.Element.external_mappings_ip.Element
        _element.address_space = AAZStrArg(
            options=["address-space"],
            help="Address space for Vpn NatRule mapping.",
        )

        internal_mappings_ip = cls._args_schema.nat_rules.Element.internal_mappings_ip
        internal_mappings_ip.Element = AAZObjectArg()

        _element = cls._args_schema.nat_rules.Element.internal_mappings_ip.Element
        _element.address_space = AAZStrArg(
            options=["address-space"],
            help="Address space for Vpn NatRule mapping.",
        )

        # define Arg Group "Parameters"

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.edge_zone_type = AAZStrArg(
            options=["--edge-zone-type"],
            arg_group="Properties",
            help="The type of the extended location.",
            enum={"EdgeZone": "EdgeZone"},
        )
        _args_schema.active = AAZBoolArg(
            options=["--active"],
            arg_group="Properties",
            help="ActiveActive flag.",
        )
        _args_schema.sku_tier = AAZStrArg(
            options=["--sku-tier"],
            arg_group="Properties",
            help="Gateway SKU tier.",
            enum={"Basic": "Basic", "ErGw1AZ": "ErGw1AZ", "ErGw2AZ": "ErGw2AZ", "ErGw3AZ": "ErGw3AZ", "ErGwScale": "ErGwScale", "HighPerformance": "HighPerformance", "Standard": "Standard", "UltraPerformance": "UltraPerformance", "VpnGw1": "VpnGw1", "VpnGw1AZ": "VpnGw1AZ", "VpnGw2": "VpnGw2", "VpnGw2AZ": "VpnGw2AZ", "VpnGw3": "VpnGw3", "VpnGw3AZ": "VpnGw3AZ", "VpnGw4": "VpnGw4", "VpnGw4AZ": "VpnGw4AZ", "VpnGw5": "VpnGw5", "VpnGw5AZ": "VpnGw5AZ"},
        )
        _args_schema.virtual_network_gateway_migration_status = AAZObjectArg(
            options=["--virtual-network-gateway-migration-status"],
            arg_group="Properties",
            help="The reference to the VirtualNetworkGatewayMigrationStatus which represents the status of migration.",
        )

        virtual_network_gateway_migration_status = cls._args_schema.virtual_network_gateway_migration_status
        virtual_network_gateway_migration_status.error_message = AAZStrArg(
            options=["error-message"],
            help="Error if any occurs during migration.",
        )
        virtual_network_gateway_migration_status.phase = AAZStrArg(
            options=["phase"],
            help="Represent the current migration phase of gateway.",
            enum={"Abort": "Abort", "AbortSucceeded": "AbortSucceeded", "Commit": "Commit", "CommitSucceeded": "CommitSucceeded", "Execute": "Execute", "ExecuteSucceeded": "ExecuteSucceeded", "None": "None", "Prepare": "Prepare", "PrepareSucceeded": "PrepareSucceeded"},
        )
        virtual_network_gateway_migration_status.state = AAZStrArg(
            options=["state"],
            help="Represent the current state of gateway migration.",
            enum={"Failed": "Failed", "InProgress": "InProgress", "None": "None", "Succeeded": "Succeeded"},
        )

        # define Arg Group "Root Cert Authentication"

        _args_schema = cls._args_schema
        _args_schema.vpn_client_root_certificates = AAZListArg(
            options=["--vpn-client-root-certificates"],
            arg_group="Root Cert Authentication",
            help="VpnClientRootCertificate for virtual network gateway.",
        )

        vpn_client_root_certificates = cls._args_schema.vpn_client_root_certificates
        vpn_client_root_certificates.Element = AAZObjectArg()

        _element = cls._args_schema.vpn_client_root_certificates.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
        )
        _element.public_cert_data = AAZStrArg(
            options=["public-cert-data"],
            help="The certificate public data.",
            required=True,
        )

        # define Arg Group "VPN Client"

        _args_schema = cls._args_schema
        _args_schema.custom_routes = AAZListArg(
            options=["--custom-routes"],
            arg_group="VPN Client",
            help="Space-separated list of CIDR prefixes representing the custom routes address space specified by the customer for VpnClient.",
        )
        _args_schema.radius_server = AAZStrArg(
            options=["--radius-server"],
            arg_group="VPN Client",
            help="Radius server address to connect to.",
        )
        _args_schema.radius_secret = AAZStrArg(
            options=["--radius-secret"],
            arg_group="VPN Client",
            help="Radius secret to use for authentication.",
        )
        _args_schema.address_prefixes = AAZListArg(
            options=["--address-prefixes"],
            singular_options=["--address-prefix"],
            arg_group="VPN Client",
            help="Space-separated list of CIDR prefixes representing the address space for the P2S Vpnclient.",
        )
        _args_schema.client_protocol = AAZListArg(
            options=["--client-protocol"],
            arg_group="VPN Client",
            help="Protocols to use for connecting.  Allowed values: IkeV2, OpenVPN, SSTP.",
        )

        custom_routes = cls._args_schema.custom_routes
        custom_routes.Element = AAZStrArg()

        address_prefixes = cls._args_schema.address_prefixes
        address_prefixes.Element = AAZStrArg()

        client_protocol = cls._args_schema.client_protocol
        client_protocol.Element = AAZStrArg(
            enum={"IkeV2": "IkeV2", "OpenVPN": "OpenVPN", "SSTP": "SSTP"},
        )

        # define Arg Group "VpnClientConfiguration"
        return cls._args_schema

    _args_address_space_create = None

    @classmethod
    def _build_args_address_space_create(cls, _schema):
        if cls._args_address_space_create is not None:
            _schema.address_prefixes = cls._args_address_space_create.address_prefixes
            return

        cls._args_address_space_create = AAZObjectArg()

        address_space_create = cls._args_address_space_create
        address_space_create.address_prefixes = AAZListArg(
            options=["address-prefixes"],
            help="A list of address blocks reserved for this virtual network in CIDR notation.",
        )

        address_prefixes = cls._args_address_space_create.address_prefixes
        address_prefixes.Element = AAZStrArg()

        _schema.address_prefixes = cls._args_address_space_create.address_prefixes

    _args_sub_resource_create = None

    @classmethod
    def _build_args_sub_resource_create(cls, _schema):
        if cls._args_sub_resource_create is not None:
            _schema.id = cls._args_sub_resource_create.id
            return

        cls._args_sub_resource_create = AAZObjectArg()

        sub_resource_create = cls._args_sub_resource_create
        sub_resource_create.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        _schema.id = cls._args_sub_resource_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualNetworkGatewaysCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualNetworkGatewaysCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworkGateways/{virtualNetworkGatewayName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayName", self.ctx.args.name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("extendedLocation", AAZObjectType)
            _builder.set_prop("identity", AAZIdentityObjectType)
            _builder.set_prop("location", AAZStrType, ".location")
            _builder.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            extended_location = _builder.get(".extendedLocation")
            if extended_location is not None:
                extended_location.set_prop("name", AAZStrType, ".edge_zone")
                extended_location.set_prop("type", AAZStrType, ".edge_zone_type")

            identity = _builder.get(".identity")
            if identity is not None:
                identity.set_prop("userAssigned", AAZListType, ".mi_user_assigned", typ_kwargs={"flags": {"action": "create"}})
                identity.set_prop("systemAssigned", AAZStrType, ".mi_system_assigned", typ_kwargs={"flags": {"action": "create"}})

            user_assigned = _builder.get(".identity.userAssigned")
            if user_assigned is not None:
                user_assigned.set_elements(AAZStrType, ".")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("activeActive", AAZBoolType, ".active")
                properties.set_prop("allowRemoteVnetTraffic", AAZBoolType, ".allow_remote_vnet_traffic")
                properties.set_prop("allowVirtualWanTraffic", AAZBoolType, ".allow_vwan_traffic")
                properties.set_prop("autoScaleConfiguration", AAZObjectType)
                properties.set_prop("bgpSettings", AAZObjectType)
                properties.set_prop("customRoutes", AAZObjectType)
                properties.set_prop("enableBgp", AAZBoolType, ".enable_bgp")
                properties.set_prop("enableHighBandwidthVpnGateway", AAZBoolType, ".enable_high_bandwidth_vpn_gateway")
                properties.set_prop("enablePrivateIpAddress", AAZBoolType, ".enable_private_ip")
                properties.set_prop("gatewayDefaultSite", AAZObjectType)
                properties.set_prop("gatewayType", AAZStrType, ".gateway_type")
                properties.set_prop("ipConfigurations", AAZListType, ".ip_configurations")
                properties.set_prop("natRules", AAZListType, ".nat_rules")
                properties.set_prop("resiliencyModel", AAZStrType, ".resiliency_model")
                properties.set_prop("sku", AAZObjectType)
                properties.set_prop("vNetExtendedLocationResourceId", AAZStrType, ".edge_zone_vnet_id")
                properties.set_prop("virtualNetworkGatewayMigrationStatus", AAZObjectType, ".virtual_network_gateway_migration_status")
                properties.set_prop("vpnClientConfiguration", AAZObjectType)
                properties.set_prop("vpnGatewayGeneration", AAZStrType, ".vpn_gateway_generation")
                properties.set_prop("vpnType", AAZStrType, ".vpn_type")

            auto_scale_configuration = _builder.get(".properties.autoScaleConfiguration")
            if auto_scale_configuration is not None:
                auto_scale_configuration.set_prop("bounds", AAZObjectType)

            bounds = _builder.get(".properties.autoScaleConfiguration.bounds")
            if bounds is not None:
                bounds.set_prop("max", AAZIntType, ".max_scale_unit")
                bounds.set_prop("min", AAZIntType, ".min_scale_unit")

            bgp_settings = _builder.get(".properties.bgpSettings")
            if bgp_settings is not None:
                bgp_settings.set_prop("asn", AAZIntType, ".asn")
                bgp_settings.set_prop("bgpPeeringAddress", AAZStrType, ".bgp_peering_address")
                bgp_settings.set_prop("peerWeight", AAZIntType, ".peer_weight")

            custom_routes = _builder.get(".properties.customRoutes")
            if custom_routes is not None:
                custom_routes.set_prop("addressPrefixes", AAZListType, ".custom_routes")

            address_prefixes = _builder.get(".properties.customRoutes.addressPrefixes")
            if address_prefixes is not None:
                address_prefixes.set_elements(AAZStrType, ".")

            gateway_default_site = _builder.get(".properties.gatewayDefaultSite")
            if gateway_default_site is not None:
                gateway_default_site.set_prop("id", AAZStrType, ".gateway_default_site")

            ip_configurations = _builder.get(".properties.ipConfigurations")
            if ip_configurations is not None:
                ip_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipConfigurations[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.ipConfigurations[].properties")
            if properties is not None:
                properties.set_prop("privateIPAllocationMethod", AAZStrType, ".private_ip_allocation_method")
                properties.set_prop("publicIPAddress", AAZObjectType)
                properties.set_prop("subnet", AAZObjectType)

            public_ip_address = _builder.get(".properties.ipConfigurations[].properties.publicIPAddress")
            if public_ip_address is not None:
                public_ip_address.set_prop("id", AAZStrType, ".public_ip_address")

            subnet = _builder.get(".properties.ipConfigurations[].properties.subnet")
            if subnet is not None:
                subnet.set_prop("id", AAZStrType, ".subnet")

            nat_rules = _builder.get(".properties.natRules")
            if nat_rules is not None:
                nat_rules.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.natRules[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.natRules[].properties")
            if properties is not None:
                properties.set_prop("externalMappings", AAZListType, ".external_mappings_ip")
                properties.set_prop("internalMappings", AAZListType, ".internal_mappings_ip")
                properties.set_prop("ipConfigurationId", AAZStrType, ".ip_config_id")
                properties.set_prop("mode", AAZStrType, ".mode")
                properties.set_prop("type", AAZStrType, ".type")

            external_mappings = _builder.get(".properties.natRules[].properties.externalMappings")
            if external_mappings is not None:
                external_mappings.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.natRules[].properties.externalMappings[]")
            if _elements is not None:
                _elements.set_prop("addressSpace", AAZStrType, ".address_space")

            internal_mappings = _builder.get(".properties.natRules[].properties.internalMappings")
            if internal_mappings is not None:
                internal_mappings.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.natRules[].properties.internalMappings[]")
            if _elements is not None:
                _elements.set_prop("addressSpace", AAZStrType, ".address_space")

            sku = _builder.get(".properties.sku")
            if sku is not None:
                sku.set_prop("name", AAZStrType, ".sku")
                sku.set_prop("tier", AAZStrType, ".sku_tier")

            virtual_network_gateway_migration_status = _builder.get(".properties.virtualNetworkGatewayMigrationStatus")
            if virtual_network_gateway_migration_status is not None:
                virtual_network_gateway_migration_status.set_prop("errorMessage", AAZStrType, ".error_message")
                virtual_network_gateway_migration_status.set_prop("phase", AAZStrType, ".phase")
                virtual_network_gateway_migration_status.set_prop("state", AAZStrType, ".state")

            vpn_client_configuration = _builder.get(".properties.vpnClientConfiguration")
            if vpn_client_configuration is not None:
                vpn_client_configuration.set_prop("aadAudience", AAZStrType, ".aad_audience")
                vpn_client_configuration.set_prop("aadIssuer", AAZStrType, ".aad_issuer")
                vpn_client_configuration.set_prop("aadTenant", AAZStrType, ".aad_tenant")
                vpn_client_configuration.set_prop("radiusServerAddress", AAZStrType, ".radius_server")
                vpn_client_configuration.set_prop("radiusServerSecret", AAZStrType, ".radius_secret")
                vpn_client_configuration.set_prop("vpnAuthenticationTypes", AAZListType, ".vpn_auth_type")
                vpn_client_configuration.set_prop("vpnClientAddressPool", AAZObjectType)
                vpn_client_configuration.set_prop("vpnClientProtocols", AAZListType, ".client_protocol")
                vpn_client_configuration.set_prop("vpnClientRootCertificates", AAZListType, ".vpn_client_root_certificates")

            vpn_authentication_types = _builder.get(".properties.vpnClientConfiguration.vpnAuthenticationTypes")
            if vpn_authentication_types is not None:
                vpn_authentication_types.set_elements(AAZStrType, ".")

            vpn_client_address_pool = _builder.get(".properties.vpnClientConfiguration.vpnClientAddressPool")
            if vpn_client_address_pool is not None:
                vpn_client_address_pool.set_prop("addressPrefixes", AAZListType, ".address_prefixes")

            address_prefixes = _builder.get(".properties.vpnClientConfiguration.vpnClientAddressPool.addressPrefixes")
            if address_prefixes is not None:
                address_prefixes.set_elements(AAZStrType, ".")

            vpn_client_protocols = _builder.get(".properties.vpnClientConfiguration.vpnClientProtocols")
            if vpn_client_protocols is not None:
                vpn_client_protocols.set_elements(AAZStrType, ".")

            vpn_client_root_certificates = _builder.get(".properties.vpnClientConfiguration.vpnClientRootCertificates")
            if vpn_client_root_certificates is not None:
                vpn_client_root_certificates.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.vpnClientConfiguration.vpnClientRootCertificates[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})

            properties = _builder.get(".properties.vpnClientConfiguration.vpnClientRootCertificates[].properties")
            if properties is not None:
                properties.set_prop("publicCertData", AAZStrType, ".public_cert_data", typ_kwargs={"flags": {"required": True}})

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.extended_location = AAZObjectType(
                serialized_name="extendedLocation",
            )
            _schema_on_200_201.id = AAZStrType()
            _schema_on_200_201.identity = AAZIdentityObjectType()
            _schema_on_200_201.location = AAZStrType()
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _schema_on_200_201.tags = AAZDictType()
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )

            extended_location = cls._schema_on_200_201.extended_location
            extended_location.name = AAZStrType()
            extended_location.type = AAZStrType()

            identity = cls._schema_on_200_201.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200_201.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200_201.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            properties = cls._schema_on_200_201.properties
            properties.active_active = AAZBoolType(
                serialized_name="activeActive",
            )
            properties.admin_state = AAZStrType(
                serialized_name="adminState",
            )
            properties.allow_remote_vnet_traffic = AAZBoolType(
                serialized_name="allowRemoteVnetTraffic",
            )
            properties.allow_virtual_wan_traffic = AAZBoolType(
                serialized_name="allowVirtualWanTraffic",
            )
            properties.auto_scale_configuration = AAZObjectType(
                serialized_name="autoScaleConfiguration",
            )
            properties.bgp_settings = AAZObjectType(
                serialized_name="bgpSettings",
            )
            properties.custom_routes = AAZObjectType(
                serialized_name="customRoutes",
            )
            _CreateHelper._build_schema_address_space_read(properties.custom_routes)
            properties.disable_ip_sec_replay_protection = AAZBoolType(
                serialized_name="disableIPSecReplayProtection",
            )
            properties.enable_bgp = AAZBoolType(
                serialized_name="enableBgp",
            )
            properties.enable_bgp_route_translation_for_nat = AAZBoolType(
                serialized_name="enableBgpRouteTranslationForNat",
            )
            properties.enable_dns_forwarding = AAZBoolType(
                serialized_name="enableDnsForwarding",
            )
            properties.enable_high_bandwidth_vpn_gateway = AAZBoolType(
                serialized_name="enableHighBandwidthVpnGateway",
            )
            properties.enable_private_ip_address = AAZBoolType(
                serialized_name="enablePrivateIpAddress",
            )
            properties.gateway_default_site = AAZObjectType(
                serialized_name="gatewayDefaultSite",
            )
            _CreateHelper._build_schema_sub_resource_read(properties.gateway_default_site)
            properties.gateway_type = AAZStrType(
                serialized_name="gatewayType",
            )
            properties.inbound_dns_forwarding_endpoint = AAZStrType(
                serialized_name="inboundDnsForwardingEndpoint",
                flags={"read_only": True},
            )
            properties.ip_configurations = AAZListType(
                serialized_name="ipConfigurations",
            )
            properties.nat_rules = AAZListType(
                serialized_name="natRules",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.resiliency_model = AAZStrType(
                serialized_name="resiliencyModel",
            )
            properties.resource_guid = AAZStrType(
                serialized_name="resourceGuid",
                flags={"read_only": True},
            )
            properties.sku = AAZObjectType()
            properties.v_net_extended_location_resource_id = AAZStrType(
                serialized_name="vNetExtendedLocationResourceId",
            )
            properties.virtual_network_gateway_migration_status = AAZObjectType(
                serialized_name="virtualNetworkGatewayMigrationStatus",
            )
            properties.virtual_network_gateway_policy_groups = AAZListType(
                serialized_name="virtualNetworkGatewayPolicyGroups",
            )
            properties.vpn_client_configuration = AAZObjectType(
                serialized_name="vpnClientConfiguration",
            )
            properties.vpn_gateway_generation = AAZStrType(
                serialized_name="vpnGatewayGeneration",
            )
            properties.vpn_type = AAZStrType(
                serialized_name="vpnType",
            )

            auto_scale_configuration = cls._schema_on_200_201.properties.auto_scale_configuration
            auto_scale_configuration.bounds = AAZObjectType()

            bounds = cls._schema_on_200_201.properties.auto_scale_configuration.bounds
            bounds.max = AAZIntType()
            bounds.min = AAZIntType()

            bgp_settings = cls._schema_on_200_201.properties.bgp_settings
            bgp_settings.asn = AAZIntType()
            bgp_settings.bgp_peering_address = AAZStrType(
                serialized_name="bgpPeeringAddress",
            )
            bgp_settings.bgp_peering_addresses = AAZListType(
                serialized_name="bgpPeeringAddresses",
            )
            bgp_settings.peer_weight = AAZIntType(
                serialized_name="peerWeight",
            )

            bgp_peering_addresses = cls._schema_on_200_201.properties.bgp_settings.bgp_peering_addresses
            bgp_peering_addresses.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.bgp_settings.bgp_peering_addresses.Element
            _element.custom_bgp_ip_addresses = AAZListType(
                serialized_name="customBgpIpAddresses",
            )
            _element.default_bgp_ip_addresses = AAZListType(
                serialized_name="defaultBgpIpAddresses",
                flags={"read_only": True},
            )
            _element.ipconfiguration_id = AAZStrType(
                serialized_name="ipconfigurationId",
            )
            _element.tunnel_ip_addresses = AAZListType(
                serialized_name="tunnelIpAddresses",
                flags={"read_only": True},
            )

            custom_bgp_ip_addresses = cls._schema_on_200_201.properties.bgp_settings.bgp_peering_addresses.Element.custom_bgp_ip_addresses
            custom_bgp_ip_addresses.Element = AAZStrType()

            default_bgp_ip_addresses = cls._schema_on_200_201.properties.bgp_settings.bgp_peering_addresses.Element.default_bgp_ip_addresses
            default_bgp_ip_addresses.Element = AAZStrType()

            tunnel_ip_addresses = cls._schema_on_200_201.properties.bgp_settings.bgp_peering_addresses.Element.tunnel_ip_addresses
            tunnel_ip_addresses.Element = AAZStrType()

            ip_configurations = cls._schema_on_200_201.properties.ip_configurations
            ip_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.ip_configurations.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType()
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200_201.properties.ip_configurations.Element.properties
            properties.private_ip_address = AAZStrType(
                serialized_name="privateIPAddress",
                flags={"read_only": True},
            )
            properties.private_ip_allocation_method = AAZStrType(
                serialized_name="privateIPAllocationMethod",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.public_ip_address = AAZObjectType(
                serialized_name="publicIPAddress",
            )
            _CreateHelper._build_schema_sub_resource_read(properties.public_ip_address)
            properties.subnet = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(properties.subnet)

            nat_rules = cls._schema_on_200_201.properties.nat_rules
            nat_rules.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.nat_rules.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType()
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200_201.properties.nat_rules.Element.properties
            properties.external_mappings = AAZListType(
                serialized_name="externalMappings",
            )
            properties.internal_mappings = AAZListType(
                serialized_name="internalMappings",
            )
            properties.ip_configuration_id = AAZStrType(
                serialized_name="ipConfigurationId",
            )
            properties.mode = AAZStrType()
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.type = AAZStrType()

            external_mappings = cls._schema_on_200_201.properties.nat_rules.Element.properties.external_mappings
            external_mappings.Element = AAZObjectType()
            _CreateHelper._build_schema_vpn_nat_rule_mapping_read(external_mappings.Element)

            internal_mappings = cls._schema_on_200_201.properties.nat_rules.Element.properties.internal_mappings
            internal_mappings.Element = AAZObjectType()
            _CreateHelper._build_schema_vpn_nat_rule_mapping_read(internal_mappings.Element)

            sku = cls._schema_on_200_201.properties.sku
            sku.capacity = AAZIntType(
                flags={"read_only": True},
            )
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            virtual_network_gateway_migration_status = cls._schema_on_200_201.properties.virtual_network_gateway_migration_status
            virtual_network_gateway_migration_status.error_message = AAZStrType(
                serialized_name="errorMessage",
            )
            virtual_network_gateway_migration_status.phase = AAZStrType()
            virtual_network_gateway_migration_status.state = AAZStrType()

            virtual_network_gateway_policy_groups = cls._schema_on_200_201.properties.virtual_network_gateway_policy_groups
            virtual_network_gateway_policy_groups.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.virtual_network_gateway_policy_groups.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType()
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200_201.properties.virtual_network_gateway_policy_groups.Element.properties
            properties.is_default = AAZBoolType(
                serialized_name="isDefault",
                flags={"required": True},
            )
            properties.policy_members = AAZListType(
                serialized_name="policyMembers",
                flags={"required": True},
            )
            properties.priority = AAZIntType(
                flags={"required": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.vng_client_connection_configurations = AAZListType(
                serialized_name="vngClientConnectionConfigurations",
                flags={"read_only": True},
            )

            policy_members = cls._schema_on_200_201.properties.virtual_network_gateway_policy_groups.Element.properties.policy_members
            policy_members.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.virtual_network_gateway_policy_groups.Element.properties.policy_members.Element
            _element.attribute_type = AAZStrType(
                serialized_name="attributeType",
            )
            _element.attribute_value = AAZStrType(
                serialized_name="attributeValue",
            )
            _element.name = AAZStrType()

            vng_client_connection_configurations = cls._schema_on_200_201.properties.virtual_network_gateway_policy_groups.Element.properties.vng_client_connection_configurations
            vng_client_connection_configurations.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(vng_client_connection_configurations.Element)

            vpn_client_configuration = cls._schema_on_200_201.properties.vpn_client_configuration
            vpn_client_configuration.aad_audience = AAZStrType(
                serialized_name="aadAudience",
            )
            vpn_client_configuration.aad_issuer = AAZStrType(
                serialized_name="aadIssuer",
            )
            vpn_client_configuration.aad_tenant = AAZStrType(
                serialized_name="aadTenant",
            )
            vpn_client_configuration.radius_server_address = AAZStrType(
                serialized_name="radiusServerAddress",
            )
            vpn_client_configuration.radius_server_secret = AAZStrType(
                serialized_name="radiusServerSecret",
            )
            vpn_client_configuration.radius_servers = AAZListType(
                serialized_name="radiusServers",
            )
            vpn_client_configuration.vng_client_connection_configurations = AAZListType(
                serialized_name="vngClientConnectionConfigurations",
            )
            vpn_client_configuration.vpn_authentication_types = AAZListType(
                serialized_name="vpnAuthenticationTypes",
            )
            vpn_client_configuration.vpn_client_address_pool = AAZObjectType(
                serialized_name="vpnClientAddressPool",
            )
            _CreateHelper._build_schema_address_space_read(vpn_client_configuration.vpn_client_address_pool)
            vpn_client_configuration.vpn_client_ipsec_policies = AAZListType(
                serialized_name="vpnClientIpsecPolicies",
            )
            vpn_client_configuration.vpn_client_protocols = AAZListType(
                serialized_name="vpnClientProtocols",
            )
            vpn_client_configuration.vpn_client_revoked_certificates = AAZListType(
                serialized_name="vpnClientRevokedCertificates",
            )
            vpn_client_configuration.vpn_client_root_certificates = AAZListType(
                serialized_name="vpnClientRootCertificates",
            )

            radius_servers = cls._schema_on_200_201.properties.vpn_client_configuration.radius_servers
            radius_servers.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.vpn_client_configuration.radius_servers.Element
            _element.radius_server_address = AAZStrType(
                serialized_name="radiusServerAddress",
                flags={"required": True},
            )
            _element.radius_server_score = AAZIntType(
                serialized_name="radiusServerScore",
            )
            _element.radius_server_secret = AAZStrType(
                serialized_name="radiusServerSecret",
            )

            vng_client_connection_configurations = cls._schema_on_200_201.properties.vpn_client_configuration.vng_client_connection_configurations
            vng_client_connection_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.vpn_client_configuration.vng_client_connection_configurations.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType()
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200_201.properties.vpn_client_configuration.vng_client_connection_configurations.Element.properties
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.virtual_network_gateway_policy_groups = AAZListType(
                serialized_name="virtualNetworkGatewayPolicyGroups",
                flags={"required": True},
            )
            properties.vpn_client_address_pool = AAZObjectType(
                serialized_name="vpnClientAddressPool",
                flags={"required": True},
            )
            _CreateHelper._build_schema_address_space_read(properties.vpn_client_address_pool)

            virtual_network_gateway_policy_groups = cls._schema_on_200_201.properties.vpn_client_configuration.vng_client_connection_configurations.Element.properties.virtual_network_gateway_policy_groups
            virtual_network_gateway_policy_groups.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(virtual_network_gateway_policy_groups.Element)

            vpn_authentication_types = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_authentication_types
            vpn_authentication_types.Element = AAZStrType()

            vpn_client_ipsec_policies = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_ipsec_policies
            vpn_client_ipsec_policies.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_ipsec_policies.Element
            _element.dh_group = AAZStrType(
                serialized_name="dhGroup",
                flags={"required": True},
            )
            _element.ike_encryption = AAZStrType(
                serialized_name="ikeEncryption",
                flags={"required": True},
            )
            _element.ike_integrity = AAZStrType(
                serialized_name="ikeIntegrity",
                flags={"required": True},
            )
            _element.ipsec_encryption = AAZStrType(
                serialized_name="ipsecEncryption",
                flags={"required": True},
            )
            _element.ipsec_integrity = AAZStrType(
                serialized_name="ipsecIntegrity",
                flags={"required": True},
            )
            _element.pfs_group = AAZStrType(
                serialized_name="pfsGroup",
                flags={"required": True},
            )
            _element.sa_data_size_kilobytes = AAZIntType(
                serialized_name="saDataSizeKilobytes",
                flags={"required": True},
            )
            _element.sa_life_time_seconds = AAZIntType(
                serialized_name="saLifeTimeSeconds",
                flags={"required": True},
            )

            vpn_client_protocols = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_protocols
            vpn_client_protocols.Element = AAZStrType()

            vpn_client_revoked_certificates = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_revoked_certificates
            vpn_client_revoked_certificates.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_revoked_certificates.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType()
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_revoked_certificates.Element.properties
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.thumbprint = AAZStrType()

            vpn_client_root_certificates = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_root_certificates
            vpn_client_root_certificates.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_root_certificates.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType()
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )

            properties = cls._schema_on_200_201.properties.vpn_client_configuration.vpn_client_root_certificates.Element.properties
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.public_cert_data = AAZStrType(
                serialized_name="publicCertData",
                flags={"required": True},
            )

            tags = cls._schema_on_200_201.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_address_space_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("addressPrefixes", AAZListType, ".address_prefixes")

        address_prefixes = _builder.get(".addressPrefixes")
        if address_prefixes is not None:
            address_prefixes.set_elements(AAZStrType, ".")

    @classmethod
    def _build_schema_sub_resource_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_address_space_read = None

    @classmethod
    def _build_schema_address_space_read(cls, _schema):
        if cls._schema_address_space_read is not None:
            _schema.address_prefixes = cls._schema_address_space_read.address_prefixes
            return

        cls._schema_address_space_read = _schema_address_space_read = AAZObjectType()

        address_space_read = _schema_address_space_read
        address_space_read.address_prefixes = AAZListType(
            serialized_name="addressPrefixes",
        )

        address_prefixes = _schema_address_space_read.address_prefixes
        address_prefixes.Element = AAZStrType()

        _schema.address_prefixes = cls._schema_address_space_read.address_prefixes

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_vpn_nat_rule_mapping_read = None

    @classmethod
    def _build_schema_vpn_nat_rule_mapping_read(cls, _schema):
        if cls._schema_vpn_nat_rule_mapping_read is not None:
            _schema.address_space = cls._schema_vpn_nat_rule_mapping_read.address_space
            _schema.port_range = cls._schema_vpn_nat_rule_mapping_read.port_range
            return

        cls._schema_vpn_nat_rule_mapping_read = _schema_vpn_nat_rule_mapping_read = AAZObjectType()

        vpn_nat_rule_mapping_read = _schema_vpn_nat_rule_mapping_read
        vpn_nat_rule_mapping_read.address_space = AAZStrType(
            serialized_name="addressSpace",
        )
        vpn_nat_rule_mapping_read.port_range = AAZStrType(
            serialized_name="portRange",
        )

        _schema.address_space = cls._schema_vpn_nat_rule_mapping_read.address_space
        _schema.port_range = cls._schema_vpn_nat_rule_mapping_read.port_range


__all__ = ["Create"]
