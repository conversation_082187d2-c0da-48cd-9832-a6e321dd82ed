# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vnet-gateway get-routes-information",
)
class GetRoutesInformation(AAZCommand):
    """This operation retrieves the route set information for an Express Route Gateway based on their resiliency

    :example: GetVirtualNetworkGatewayRoutesInformation
        az network vnet-gateway get-routes-information --resource-group rg1 --virtual-network-gateway-name vpngw --attempt-refresh False
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/virtualnetworkgateways/{}/getroutesinformation", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.virtual_network_gateway_name = AAZStrArg(
            options=["--virtual-network-gateway-name", "--name"],
            help="The name of the virtual network gateway.",
            required=True,
            id_part="name",
        )
        _args_schema.attempt_refresh = AAZBoolArg(
            options=["--attempt-refresh"],
            help="Attempt to recalculate the Route Sets Information for the gateway",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualNetworkGatewaysGetRoutesInformation(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualNetworkGatewaysGetRoutesInformation(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworkGateways/{virtualNetworkGatewayName}/getRoutesInformation",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayName", self.ctx.args.virtual_network_gateway_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "attemptRefresh", self.ctx.args.attempt_refresh,
                ),
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.circuits_metadata_map = AAZDictType(
                serialized_name="circuitsMetadataMap",
            )
            _schema_on_200.last_computed_time = AAZStrType(
                serialized_name="lastComputedTime",
            )
            _schema_on_200.next_eligible_compute_time = AAZStrType(
                serialized_name="nextEligibleComputeTime",
            )
            _schema_on_200.route_set_version = AAZStrType(
                serialized_name="routeSetVersion",
            )
            _schema_on_200.route_sets = AAZListType(
                serialized_name="routeSets",
            )

            circuits_metadata_map = cls._schema_on_200.circuits_metadata_map
            circuits_metadata_map.Element = AAZObjectType()

            _element = cls._schema_on_200.circuits_metadata_map.Element
            _element.link = AAZStrType()
            _element.location = AAZStrType()
            _element.name = AAZStrType()

            route_sets = cls._schema_on_200.route_sets
            route_sets.Element = AAZObjectType()

            _element = cls._schema_on_200.route_sets.Element
            _element.details = AAZDictType()
            _element.locations = AAZListType()
            _element.name = AAZStrType()

            details = cls._schema_on_200.route_sets.Element.details
            details.Element = AAZListType()

            _element = cls._schema_on_200.route_sets.Element.details.Element
            _element.Element = AAZObjectType()

            _element = cls._schema_on_200.route_sets.Element.details.Element.Element
            _element.circuit = AAZStrType()
            _element.pri = AAZStrType()
            _element.sec = AAZStrType()

            locations = cls._schema_on_200.route_sets.Element.locations
            locations.Element = AAZStrType()

            return cls._schema_on_200


class _GetRoutesInformationHelper:
    """Helper class for GetRoutesInformation"""


__all__ = ["GetRoutesInformation"]
