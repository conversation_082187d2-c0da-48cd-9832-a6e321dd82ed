# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network network-watcher packet-capture query-status",
)
class QueryStatu(AAZCommand):
    """Query the status of a running packet capture session.

    :example: Query a status of packet capture 
        az network network-watcher packet-capture query-status --network-watcher-name "NetworkWatcher_eastus2euap" --packet-capture-name "clitestpacp3" --resource-group "NetworkWatcherRG"
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/networkwatchers/{}/packetcaptures/{}/querystatus", "2024-05-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.network_watcher_name = AAZStrArg(
            options=["--network-watcher-name"],
            help="The name of the Network Watcher resource.",
            required=True,
            id_part="name",
        )
        _args_schema.packet_capture_name = AAZStrArg(
            options=["--packet-capture-name"],
            help="The name given to the packet capture session.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.PacketCapturesGetStatus(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class PacketCapturesGetStatus(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/networkWatchers/{networkWatcherName}/packetCaptures/{packetCaptureName}/queryStatus",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "networkWatcherName", self.ctx.args.network_watcher_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "packetCaptureName", self.ctx.args.packet_capture_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _QueryStatuHelper._build_schema_packet_capture_query_status_result_read(cls._schema_on_200)

            return cls._schema_on_200


class _QueryStatuHelper:
    """Helper class for QueryStatu"""

    _schema_packet_capture_query_status_result_read = None

    @classmethod
    def _build_schema_packet_capture_query_status_result_read(cls, _schema):
        if cls._schema_packet_capture_query_status_result_read is not None:
            _schema.capture_start_time = cls._schema_packet_capture_query_status_result_read.capture_start_time
            _schema.id = cls._schema_packet_capture_query_status_result_read.id
            _schema.name = cls._schema_packet_capture_query_status_result_read.name
            _schema.packet_capture_error = cls._schema_packet_capture_query_status_result_read.packet_capture_error
            _schema.packet_capture_status = cls._schema_packet_capture_query_status_result_read.packet_capture_status
            _schema.stop_reason = cls._schema_packet_capture_query_status_result_read.stop_reason
            return

        cls._schema_packet_capture_query_status_result_read = _schema_packet_capture_query_status_result_read = AAZObjectType()

        packet_capture_query_status_result_read = _schema_packet_capture_query_status_result_read
        packet_capture_query_status_result_read.capture_start_time = AAZStrType(
            serialized_name="captureStartTime",
        )
        packet_capture_query_status_result_read.id = AAZStrType()
        packet_capture_query_status_result_read.name = AAZStrType()
        packet_capture_query_status_result_read.packet_capture_error = AAZListType(
            serialized_name="packetCaptureError",
        )
        packet_capture_query_status_result_read.packet_capture_status = AAZStrType(
            serialized_name="packetCaptureStatus",
        )
        packet_capture_query_status_result_read.stop_reason = AAZStrType(
            serialized_name="stopReason",
        )

        packet_capture_error = _schema_packet_capture_query_status_result_read.packet_capture_error
        packet_capture_error.Element = AAZStrType()

        _schema.capture_start_time = cls._schema_packet_capture_query_status_result_read.capture_start_time
        _schema.id = cls._schema_packet_capture_query_status_result_read.id
        _schema.name = cls._schema_packet_capture_query_status_result_read.name
        _schema.packet_capture_error = cls._schema_packet_capture_query_status_result_read.packet_capture_error
        _schema.packet_capture_status = cls._schema_packet_capture_query_status_result_read.packet_capture_status
        _schema.stop_reason = cls._schema_packet_capture_query_status_result_read.stop_reason


__all__ = ["QueryStatu"]
