# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network nat gateway create",
)
class Create(AAZCommand):
    """Create a NAT gateway.

    :example: Create a NAT gateway.
        az network nat gateway create --resource-group MyResourceGroup --name MyNatGateway --location MyLocation --public-ip-addresses  MyPublicIp --public-ip-prefixes MyPublicIpPrefix --idle-timeout 4 --zone 2
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/natgateways/{}", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="Name of the NAT gateway.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.location = AAZResourceLocationArg(
            help="Location. Values from: `az account list-locations`. You can configure the default location using `az configure --defaults location=<location>`.",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.idle_timeout = AAZIntArg(
            options=["--idle-timeout"],
            help="Idle timeout in minutes.",
        )
        _args_schema.pip_addresses = AAZListArg(
            options=["--pip-addresses"],
            help="Space-separated list of public IP addresses (Names or IDs).",
        )
        _args_schema.pip_addresses_v6 = AAZListArg(
            options=["--pip-addresses-v6"],
            help="An array of public ip addresses V6 associated with the nat gateway resource.",
        )
        _args_schema.pip_prefixes = AAZListArg(
            options=["--pip-prefixes"],
            help="Space-separated list of public IP prefixes (Names or IDs).",
        )
        _args_schema.pip_prefixes_v6 = AAZListArg(
            options=["--pip-prefixes-v6"],
            help="An array of public ip prefixes V6 associated with the nat gateway resource.",
        )
        _args_schema.source_vnet = AAZObjectArg(
            options=["--source-vnet"],
            help="A reference to the source virtual network using this nat gateway resource.",
        )
        cls._build_args_sub_resource_create(_args_schema.source_vnet)
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="Space-separated tags: key[=value] [key[=value] ...].",
        )
        _args_schema.zone = AAZListArg(
            options=["-z", "--zone"],
            help="Availability zone into which to provision the resource. Allowed values: 1, 2, 3.",
        )

        pip_addresses = cls._args_schema.pip_addresses
        pip_addresses.Element = AAZObjectArg()

        _element = cls._args_schema.pip_addresses.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        pip_addresses_v6 = cls._args_schema.pip_addresses_v6
        pip_addresses_v6.Element = AAZObjectArg()
        cls._build_args_sub_resource_create(pip_addresses_v6.Element)

        pip_prefixes = cls._args_schema.pip_prefixes
        pip_prefixes.Element = AAZObjectArg()

        _element = cls._args_schema.pip_prefixes.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        pip_prefixes_v6 = cls._args_schema.pip_prefixes_v6
        pip_prefixes_v6.Element = AAZObjectArg()
        cls._build_args_sub_resource_create(pip_prefixes_v6.Element)

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        zone = cls._args_schema.zone
        zone.Element = AAZStrArg()

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.sku = AAZObjectArg(
            options=["--sku"],
            arg_group="Parameters",
            help="The nat gateway SKU.",
        )

        sku = cls._args_schema.sku
        sku.name = AAZStrArg(
            options=["name"],
            help="Name of Nat Gateway SKU.",
            enum={"Standard": "Standard", "StandardV2": "StandardV2"},
        )
        return cls._args_schema

    _args_sub_resource_create = None

    @classmethod
    def _build_args_sub_resource_create(cls, _schema):
        if cls._args_sub_resource_create is not None:
            _schema.id = cls._args_sub_resource_create.id
            return

        cls._args_sub_resource_create = AAZObjectArg()

        sub_resource_create = cls._args_sub_resource_create
        sub_resource_create.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        _schema.id = cls._args_sub_resource_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.NatGatewaysCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class NatGatewaysCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/natGateways/{natGatewayName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "natGatewayName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("location", AAZStrType, ".location")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("sku", AAZObjectType, ".sku")
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zone")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("idleTimeoutInMinutes", AAZIntType, ".idle_timeout")
                properties.set_prop("publicIpAddresses", AAZListType, ".pip_addresses")
                properties.set_prop("publicIpAddressesV6", AAZListType, ".pip_addresses_v6")
                properties.set_prop("publicIpPrefixes", AAZListType, ".pip_prefixes")
                properties.set_prop("publicIpPrefixesV6", AAZListType, ".pip_prefixes_v6")
                _CreateHelper._build_schema_sub_resource_create(properties.set_prop("sourceVirtualNetwork", AAZObjectType, ".source_vnet"))

            public_ip_addresses = _builder.get(".properties.publicIpAddresses")
            if public_ip_addresses is not None:
                public_ip_addresses.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.publicIpAddresses[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")

            public_ip_addresses_v6 = _builder.get(".properties.publicIpAddressesV6")
            if public_ip_addresses_v6 is not None:
                _CreateHelper._build_schema_sub_resource_create(public_ip_addresses_v6.set_elements(AAZObjectType, "."))

            public_ip_prefixes = _builder.get(".properties.publicIpPrefixes")
            if public_ip_prefixes is not None:
                public_ip_prefixes.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.publicIpPrefixes[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")

            public_ip_prefixes_v6 = _builder.get(".properties.publicIpPrefixesV6")
            if public_ip_prefixes_v6 is not None:
                _CreateHelper._build_schema_sub_resource_create(public_ip_prefixes_v6.set_elements(AAZObjectType, "."))

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("name", AAZStrType, ".name")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.id = AAZStrType()
            _schema_on_200_201.location = AAZStrType()
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200_201.sku = AAZObjectType()
            _schema_on_200_201.tags = AAZDictType()
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.zones = AAZListType()

            properties = cls._schema_on_200_201.properties
            properties.idle_timeout_in_minutes = AAZIntType(
                serialized_name="idleTimeoutInMinutes",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.public_ip_addresses = AAZListType(
                serialized_name="publicIpAddresses",
            )
            properties.public_ip_addresses_v6 = AAZListType(
                serialized_name="publicIpAddressesV6",
            )
            properties.public_ip_prefixes = AAZListType(
                serialized_name="publicIpPrefixes",
            )
            properties.public_ip_prefixes_v6 = AAZListType(
                serialized_name="publicIpPrefixesV6",
            )
            properties.resource_guid = AAZStrType(
                serialized_name="resourceGuid",
                flags={"read_only": True},
            )
            properties.source_virtual_network = AAZObjectType(
                serialized_name="sourceVirtualNetwork",
            )
            _CreateHelper._build_schema_sub_resource_read(properties.source_virtual_network)
            properties.subnets = AAZListType(
                flags={"read_only": True},
            )

            public_ip_addresses = cls._schema_on_200_201.properties.public_ip_addresses
            public_ip_addresses.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_addresses.Element)

            public_ip_addresses_v6 = cls._schema_on_200_201.properties.public_ip_addresses_v6
            public_ip_addresses_v6.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_addresses_v6.Element)

            public_ip_prefixes = cls._schema_on_200_201.properties.public_ip_prefixes
            public_ip_prefixes.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_prefixes.Element)

            public_ip_prefixes_v6 = cls._schema_on_200_201.properties.public_ip_prefixes_v6
            public_ip_prefixes_v6.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(public_ip_prefixes_v6.Element)

            subnets = cls._schema_on_200_201.properties.subnets
            subnets.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read(subnets.Element)

            sku = cls._schema_on_200_201.sku
            sku.name = AAZStrType()

            tags = cls._schema_on_200_201.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200_201.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_sub_resource_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id


__all__ = ["Create"]
