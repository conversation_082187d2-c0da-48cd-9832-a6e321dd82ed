# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network network-watcher packet-capture list",
)
class List(AAZCommand):
    """List all packet capture sessions within the specified resource group.
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/networkwatchers/{}/packetcaptures", "2024-05-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.network_watcher_name = AAZStrArg(
            options=["--network-watcher-name"],
            help="The name of the Network Watcher resource.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.PacketCapturesList(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        return result

    class PacketCapturesList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/networkWatchers/{networkWatcherName}/packetCaptures",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "networkWatcherName", self.ctx.args.network_watcher_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.value = AAZListType()

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.bytes_to_capture_per_packet = AAZIntType(
                serialized_name="bytesToCapturePerPacket",
            )
            properties.capture_settings = AAZObjectType(
                serialized_name="captureSettings",
            )
            properties.continuous_capture = AAZBoolType(
                serialized_name="continuousCapture",
            )
            properties.filters = AAZListType()
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.scope = AAZObjectType()
            properties.storage_location = AAZObjectType(
                serialized_name="storageLocation",
                flags={"required": True},
            )
            properties.target = AAZStrType(
                flags={"required": True},
            )
            properties.target_type = AAZStrType(
                serialized_name="targetType",
            )
            properties.time_limit_in_seconds = AAZIntType(
                serialized_name="timeLimitInSeconds",
            )
            properties.total_bytes_per_session = AAZIntType(
                serialized_name="totalBytesPerSession",
            )

            capture_settings = cls._schema_on_200.value.Element.properties.capture_settings
            capture_settings.file_count = AAZIntType(
                serialized_name="fileCount",
            )
            capture_settings.file_size_in_bytes = AAZIntType(
                serialized_name="fileSizeInBytes",
            )
            capture_settings.session_time_limit_in_seconds = AAZIntType(
                serialized_name="sessionTimeLimitInSeconds",
            )

            filters = cls._schema_on_200.value.Element.properties.filters
            filters.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.filters.Element
            _element.local_ip_address = AAZStrType(
                serialized_name="localIPAddress",
            )
            _element.local_port = AAZStrType(
                serialized_name="localPort",
            )
            _element.protocol = AAZStrType()
            _element.remote_ip_address = AAZStrType(
                serialized_name="remoteIPAddress",
            )
            _element.remote_port = AAZStrType(
                serialized_name="remotePort",
            )

            scope = cls._schema_on_200.value.Element.properties.scope
            scope.exclude = AAZListType()
            scope.include = AAZListType()

            exclude = cls._schema_on_200.value.Element.properties.scope.exclude
            exclude.Element = AAZStrType()

            include = cls._schema_on_200.value.Element.properties.scope.include
            include.Element = AAZStrType()

            storage_location = cls._schema_on_200.value.Element.properties.storage_location
            storage_location.file_path = AAZStrType(
                serialized_name="filePath",
            )
            storage_location.local_path = AAZStrType(
                serialized_name="localPath",
            )
            storage_location.storage_id = AAZStrType(
                serialized_name="storageId",
            )
            storage_location.storage_path = AAZStrType(
                serialized_name="storagePath",
            )

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""


__all__ = ["List"]
