# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vpn-connection wait",
)
class Wait(AAZWaitCommand):
    """Place the CLI in a waiting state until a condition is met.
    """

    _aaz_info = {
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/connections/{}", "2024-07-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="Connection name.",
            required=True,
            id_part="name",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.VirtualNetworkGatewayConnectionsGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=False)
        return result

    class VirtualNetworkGatewayConnectionsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/connections/{virtualNetworkGatewayConnectionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayConnectionName", self.ctx.args.name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.id = AAZStrType()
            _schema_on_200.location = AAZStrType()
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.authorization_key = AAZStrType(
                serialized_name="authorizationKey",
            )
            properties.connection_mode = AAZStrType(
                serialized_name="connectionMode",
            )
            properties.connection_protocol = AAZStrType(
                serialized_name="connectionProtocol",
            )
            properties.connection_status = AAZStrType(
                serialized_name="connectionStatus",
                flags={"read_only": True},
            )
            properties.connection_type = AAZStrType(
                serialized_name="connectionType",
                flags={"required": True},
            )
            properties.dpd_timeout_seconds = AAZIntType(
                serialized_name="dpdTimeoutSeconds",
            )
            properties.egress_bytes_transferred = AAZIntType(
                serialized_name="egressBytesTransferred",
                flags={"read_only": True},
            )
            properties.egress_nat_rules = AAZListType(
                serialized_name="egressNatRules",
            )
            properties.enable_bgp = AAZBoolType(
                serialized_name="enableBgp",
            )
            properties.enable_private_link_fast_path = AAZBoolType(
                serialized_name="enablePrivateLinkFastPath",
            )
            properties.express_route_gateway_bypass = AAZBoolType(
                serialized_name="expressRouteGatewayBypass",
            )
            properties.gateway_custom_bgp_ip_addresses = AAZListType(
                serialized_name="gatewayCustomBgpIpAddresses",
            )
            properties.ingress_bytes_transferred = AAZIntType(
                serialized_name="ingressBytesTransferred",
                flags={"read_only": True},
            )
            properties.ingress_nat_rules = AAZListType(
                serialized_name="ingressNatRules",
            )
            properties.ipsec_policies = AAZListType(
                serialized_name="ipsecPolicies",
            )
            properties.local_network_gateway2 = AAZObjectType(
                serialized_name="localNetworkGateway2",
            )
            properties.peer = AAZObjectType()
            _WaitHelper._build_schema_sub_resource_read(properties.peer)
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.resource_guid = AAZStrType(
                serialized_name="resourceGuid",
                flags={"read_only": True},
            )
            properties.routing_weight = AAZIntType(
                serialized_name="routingWeight",
            )
            properties.shared_key = AAZStrType(
                serialized_name="sharedKey",
            )
            properties.traffic_selector_policies = AAZListType(
                serialized_name="trafficSelectorPolicies",
            )
            properties.tunnel_connection_status = AAZListType(
                serialized_name="tunnelConnectionStatus",
                flags={"read_only": True},
            )
            properties.tunnel_properties = AAZListType(
                serialized_name="tunnelProperties",
            )
            properties.use_local_azure_ip_address = AAZBoolType(
                serialized_name="useLocalAzureIpAddress",
            )
            properties.use_policy_based_traffic_selectors = AAZBoolType(
                serialized_name="usePolicyBasedTrafficSelectors",
            )
            properties.virtual_network_gateway1 = AAZObjectType(
                serialized_name="virtualNetworkGateway1",
                flags={"required": True},
            )
            _WaitHelper._build_schema_virtual_network_gateway_read(properties.virtual_network_gateway1)
            properties.virtual_network_gateway2 = AAZObjectType(
                serialized_name="virtualNetworkGateway2",
            )
            _WaitHelper._build_schema_virtual_network_gateway_read(properties.virtual_network_gateway2)

            egress_nat_rules = cls._schema_on_200.properties.egress_nat_rules
            egress_nat_rules.Element = AAZObjectType()
            _WaitHelper._build_schema_sub_resource_read(egress_nat_rules.Element)

            gateway_custom_bgp_ip_addresses = cls._schema_on_200.properties.gateway_custom_bgp_ip_addresses
            gateway_custom_bgp_ip_addresses.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.gateway_custom_bgp_ip_addresses.Element
            _element.custom_bgp_ip_address = AAZStrType(
                serialized_name="customBgpIpAddress",
                flags={"required": True},
            )
            _element.ip_configuration_id = AAZStrType(
                serialized_name="ipConfigurationId",
                flags={"required": True},
            )

            ingress_nat_rules = cls._schema_on_200.properties.ingress_nat_rules
            ingress_nat_rules.Element = AAZObjectType()
            _WaitHelper._build_schema_sub_resource_read(ingress_nat_rules.Element)

            ipsec_policies = cls._schema_on_200.properties.ipsec_policies
            ipsec_policies.Element = AAZObjectType()
            _WaitHelper._build_schema_ipsec_policy_read(ipsec_policies.Element)

            local_network_gateway2 = cls._schema_on_200.properties.local_network_gateway2
            local_network_gateway2.etag = AAZStrType(
                flags={"read_only": True},
            )
            local_network_gateway2.id = AAZStrType()
            local_network_gateway2.location = AAZStrType()
            local_network_gateway2.name = AAZStrType(
                flags={"read_only": True},
            )
            local_network_gateway2.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            local_network_gateway2.tags = AAZDictType()
            local_network_gateway2.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties.local_network_gateway2.properties
            properties.bgp_settings = AAZObjectType(
                serialized_name="bgpSettings",
            )
            _WaitHelper._build_schema_bgp_settings_read(properties.bgp_settings)
            properties.fqdn = AAZStrType()
            properties.gateway_ip_address = AAZStrType(
                serialized_name="gatewayIpAddress",
            )
            properties.local_network_address_space = AAZObjectType(
                serialized_name="localNetworkAddressSpace",
            )
            _WaitHelper._build_schema_address_space_read(properties.local_network_address_space)
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.resource_guid = AAZStrType(
                serialized_name="resourceGuid",
                flags={"read_only": True},
            )

            tags = cls._schema_on_200.properties.local_network_gateway2.tags
            tags.Element = AAZStrType()

            traffic_selector_policies = cls._schema_on_200.properties.traffic_selector_policies
            traffic_selector_policies.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.traffic_selector_policies.Element
            _element.local_address_ranges = AAZListType(
                serialized_name="localAddressRanges",
                flags={"required": True},
            )
            _element.remote_address_ranges = AAZListType(
                serialized_name="remoteAddressRanges",
                flags={"required": True},
            )

            local_address_ranges = cls._schema_on_200.properties.traffic_selector_policies.Element.local_address_ranges
            local_address_ranges.Element = AAZStrType()

            remote_address_ranges = cls._schema_on_200.properties.traffic_selector_policies.Element.remote_address_ranges
            remote_address_ranges.Element = AAZStrType()

            tunnel_connection_status = cls._schema_on_200.properties.tunnel_connection_status
            tunnel_connection_status.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.tunnel_connection_status.Element
            _element.connection_status = AAZStrType(
                serialized_name="connectionStatus",
                flags={"read_only": True},
            )
            _element.egress_bytes_transferred = AAZIntType(
                serialized_name="egressBytesTransferred",
                flags={"read_only": True},
            )
            _element.ingress_bytes_transferred = AAZIntType(
                serialized_name="ingressBytesTransferred",
                flags={"read_only": True},
            )
            _element.last_connection_established_utc_time = AAZStrType(
                serialized_name="lastConnectionEstablishedUtcTime",
                flags={"read_only": True},
            )
            _element.tunnel = AAZStrType(
                flags={"read_only": True},
            )

            tunnel_properties = cls._schema_on_200.properties.tunnel_properties
            tunnel_properties.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.tunnel_properties.Element
            _element.bgp_peering_address = AAZStrType(
                serialized_name="bgpPeeringAddress",
            )
            _element.tunnel_ip_address = AAZStrType(
                serialized_name="tunnelIpAddress",
            )

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _WaitHelper:
    """Helper class for Wait"""

    _schema_address_space_read = None

    @classmethod
    def _build_schema_address_space_read(cls, _schema):
        if cls._schema_address_space_read is not None:
            _schema.address_prefixes = cls._schema_address_space_read.address_prefixes
            return

        cls._schema_address_space_read = _schema_address_space_read = AAZObjectType()

        address_space_read = _schema_address_space_read
        address_space_read.address_prefixes = AAZListType(
            serialized_name="addressPrefixes",
        )

        address_prefixes = _schema_address_space_read.address_prefixes
        address_prefixes.Element = AAZStrType()

        _schema.address_prefixes = cls._schema_address_space_read.address_prefixes

    _schema_bgp_settings_read = None

    @classmethod
    def _build_schema_bgp_settings_read(cls, _schema):
        if cls._schema_bgp_settings_read is not None:
            _schema.asn = cls._schema_bgp_settings_read.asn
            _schema.bgp_peering_address = cls._schema_bgp_settings_read.bgp_peering_address
            _schema.bgp_peering_addresses = cls._schema_bgp_settings_read.bgp_peering_addresses
            _schema.peer_weight = cls._schema_bgp_settings_read.peer_weight
            return

        cls._schema_bgp_settings_read = _schema_bgp_settings_read = AAZObjectType()

        bgp_settings_read = _schema_bgp_settings_read
        bgp_settings_read.asn = AAZIntType()
        bgp_settings_read.bgp_peering_address = AAZStrType(
            serialized_name="bgpPeeringAddress",
        )
        bgp_settings_read.bgp_peering_addresses = AAZListType(
            serialized_name="bgpPeeringAddresses",
        )
        bgp_settings_read.peer_weight = AAZIntType(
            serialized_name="peerWeight",
        )

        bgp_peering_addresses = _schema_bgp_settings_read.bgp_peering_addresses
        bgp_peering_addresses.Element = AAZObjectType()

        _element = _schema_bgp_settings_read.bgp_peering_addresses.Element
        _element.custom_bgp_ip_addresses = AAZListType(
            serialized_name="customBgpIpAddresses",
        )
        _element.default_bgp_ip_addresses = AAZListType(
            serialized_name="defaultBgpIpAddresses",
            flags={"read_only": True},
        )
        _element.ipconfiguration_id = AAZStrType(
            serialized_name="ipconfigurationId",
        )
        _element.tunnel_ip_addresses = AAZListType(
            serialized_name="tunnelIpAddresses",
            flags={"read_only": True},
        )

        custom_bgp_ip_addresses = _schema_bgp_settings_read.bgp_peering_addresses.Element.custom_bgp_ip_addresses
        custom_bgp_ip_addresses.Element = AAZStrType()

        default_bgp_ip_addresses = _schema_bgp_settings_read.bgp_peering_addresses.Element.default_bgp_ip_addresses
        default_bgp_ip_addresses.Element = AAZStrType()

        tunnel_ip_addresses = _schema_bgp_settings_read.bgp_peering_addresses.Element.tunnel_ip_addresses
        tunnel_ip_addresses.Element = AAZStrType()

        _schema.asn = cls._schema_bgp_settings_read.asn
        _schema.bgp_peering_address = cls._schema_bgp_settings_read.bgp_peering_address
        _schema.bgp_peering_addresses = cls._schema_bgp_settings_read.bgp_peering_addresses
        _schema.peer_weight = cls._schema_bgp_settings_read.peer_weight

    _schema_ipsec_policy_read = None

    @classmethod
    def _build_schema_ipsec_policy_read(cls, _schema):
        if cls._schema_ipsec_policy_read is not None:
            _schema.dh_group = cls._schema_ipsec_policy_read.dh_group
            _schema.ike_encryption = cls._schema_ipsec_policy_read.ike_encryption
            _schema.ike_integrity = cls._schema_ipsec_policy_read.ike_integrity
            _schema.ipsec_encryption = cls._schema_ipsec_policy_read.ipsec_encryption
            _schema.ipsec_integrity = cls._schema_ipsec_policy_read.ipsec_integrity
            _schema.pfs_group = cls._schema_ipsec_policy_read.pfs_group
            _schema.sa_data_size_kilobytes = cls._schema_ipsec_policy_read.sa_data_size_kilobytes
            _schema.sa_life_time_seconds = cls._schema_ipsec_policy_read.sa_life_time_seconds
            return

        cls._schema_ipsec_policy_read = _schema_ipsec_policy_read = AAZObjectType()

        ipsec_policy_read = _schema_ipsec_policy_read
        ipsec_policy_read.dh_group = AAZStrType(
            serialized_name="dhGroup",
            flags={"required": True},
        )
        ipsec_policy_read.ike_encryption = AAZStrType(
            serialized_name="ikeEncryption",
            flags={"required": True},
        )
        ipsec_policy_read.ike_integrity = AAZStrType(
            serialized_name="ikeIntegrity",
            flags={"required": True},
        )
        ipsec_policy_read.ipsec_encryption = AAZStrType(
            serialized_name="ipsecEncryption",
            flags={"required": True},
        )
        ipsec_policy_read.ipsec_integrity = AAZStrType(
            serialized_name="ipsecIntegrity",
            flags={"required": True},
        )
        ipsec_policy_read.pfs_group = AAZStrType(
            serialized_name="pfsGroup",
            flags={"required": True},
        )
        ipsec_policy_read.sa_data_size_kilobytes = AAZIntType(
            serialized_name="saDataSizeKilobytes",
            flags={"required": True},
        )
        ipsec_policy_read.sa_life_time_seconds = AAZIntType(
            serialized_name="saLifeTimeSeconds",
            flags={"required": True},
        )

        _schema.dh_group = cls._schema_ipsec_policy_read.dh_group
        _schema.ike_encryption = cls._schema_ipsec_policy_read.ike_encryption
        _schema.ike_integrity = cls._schema_ipsec_policy_read.ike_integrity
        _schema.ipsec_encryption = cls._schema_ipsec_policy_read.ipsec_encryption
        _schema.ipsec_integrity = cls._schema_ipsec_policy_read.ipsec_integrity
        _schema.pfs_group = cls._schema_ipsec_policy_read.pfs_group
        _schema.sa_data_size_kilobytes = cls._schema_ipsec_policy_read.sa_data_size_kilobytes
        _schema.sa_life_time_seconds = cls._schema_ipsec_policy_read.sa_life_time_seconds

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_virtual_network_gateway_read = None

    @classmethod
    def _build_schema_virtual_network_gateway_read(cls, _schema):
        if cls._schema_virtual_network_gateway_read is not None:
            _schema.etag = cls._schema_virtual_network_gateway_read.etag
            _schema.extended_location = cls._schema_virtual_network_gateway_read.extended_location
            _schema.id = cls._schema_virtual_network_gateway_read.id
            _schema.identity = cls._schema_virtual_network_gateway_read.identity
            _schema.location = cls._schema_virtual_network_gateway_read.location
            _schema.name = cls._schema_virtual_network_gateway_read.name
            _schema.properties = cls._schema_virtual_network_gateway_read.properties
            _schema.tags = cls._schema_virtual_network_gateway_read.tags
            _schema.type = cls._schema_virtual_network_gateway_read.type
            return

        cls._schema_virtual_network_gateway_read = _schema_virtual_network_gateway_read = AAZObjectType()

        virtual_network_gateway_read = _schema_virtual_network_gateway_read
        virtual_network_gateway_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_gateway_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        virtual_network_gateway_read.id = AAZStrType()
        virtual_network_gateway_read.identity = AAZIdentityObjectType()
        virtual_network_gateway_read.location = AAZStrType()
        virtual_network_gateway_read.name = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_gateway_read.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )
        virtual_network_gateway_read.tags = AAZDictType()
        virtual_network_gateway_read.type = AAZStrType(
            flags={"read_only": True},
        )

        extended_location = _schema_virtual_network_gateway_read.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        identity = _schema_virtual_network_gateway_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType()
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_virtual_network_gateway_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_gateway_read.properties
        properties.active_active = AAZBoolType(
            serialized_name="activeActive",
        )
        properties.admin_state = AAZStrType(
            serialized_name="adminState",
        )
        properties.allow_remote_vnet_traffic = AAZBoolType(
            serialized_name="allowRemoteVnetTraffic",
        )
        properties.allow_virtual_wan_traffic = AAZBoolType(
            serialized_name="allowVirtualWanTraffic",
        )
        properties.auto_scale_configuration = AAZObjectType(
            serialized_name="autoScaleConfiguration",
        )
        properties.bgp_settings = AAZObjectType(
            serialized_name="bgpSettings",
        )
        cls._build_schema_bgp_settings_read(properties.bgp_settings)
        properties.custom_routes = AAZObjectType(
            serialized_name="customRoutes",
        )
        cls._build_schema_address_space_read(properties.custom_routes)
        properties.disable_ip_sec_replay_protection = AAZBoolType(
            serialized_name="disableIPSecReplayProtection",
        )
        properties.enable_bgp = AAZBoolType(
            serialized_name="enableBgp",
        )
        properties.enable_bgp_route_translation_for_nat = AAZBoolType(
            serialized_name="enableBgpRouteTranslationForNat",
        )
        properties.enable_dns_forwarding = AAZBoolType(
            serialized_name="enableDnsForwarding",
        )
        properties.enable_high_bandwidth_vpn_gateway = AAZBoolType(
            serialized_name="enableHighBandwidthVpnGateway",
        )
        properties.enable_private_ip_address = AAZBoolType(
            serialized_name="enablePrivateIpAddress",
        )
        properties.gateway_default_site = AAZObjectType(
            serialized_name="gatewayDefaultSite",
        )
        cls._build_schema_sub_resource_read(properties.gateway_default_site)
        properties.gateway_type = AAZStrType(
            serialized_name="gatewayType",
        )
        properties.inbound_dns_forwarding_endpoint = AAZStrType(
            serialized_name="inboundDnsForwardingEndpoint",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.nat_rules = AAZListType(
            serialized_name="natRules",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resiliency_model = AAZStrType(
            serialized_name="resiliencyModel",
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.sku = AAZObjectType()
        properties.v_net_extended_location_resource_id = AAZStrType(
            serialized_name="vNetExtendedLocationResourceId",
        )
        properties.virtual_network_gateway_migration_status = AAZObjectType(
            serialized_name="virtualNetworkGatewayMigrationStatus",
        )
        properties.virtual_network_gateway_policy_groups = AAZListType(
            serialized_name="virtualNetworkGatewayPolicyGroups",
        )
        properties.vpn_client_configuration = AAZObjectType(
            serialized_name="vpnClientConfiguration",
        )
        properties.vpn_gateway_generation = AAZStrType(
            serialized_name="vpnGatewayGeneration",
        )
        properties.vpn_type = AAZStrType(
            serialized_name="vpnType",
        )

        auto_scale_configuration = _schema_virtual_network_gateway_read.properties.auto_scale_configuration
        auto_scale_configuration.bounds = AAZObjectType()

        bounds = _schema_virtual_network_gateway_read.properties.auto_scale_configuration.bounds
        bounds.max = AAZIntType()
        bounds.min = AAZIntType()

        ip_configurations = _schema_virtual_network_gateway_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.ip_configurations.Element.properties
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
            flags={"read_only": True},
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)

        nat_rules = _schema_virtual_network_gateway_read.properties.nat_rules
        nat_rules.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.nat_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.nat_rules.Element.properties
        properties.external_mappings = AAZListType(
            serialized_name="externalMappings",
        )
        properties.internal_mappings = AAZListType(
            serialized_name="internalMappings",
        )
        properties.ip_configuration_id = AAZStrType(
            serialized_name="ipConfigurationId",
        )
        properties.mode = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.type = AAZStrType()

        external_mappings = _schema_virtual_network_gateway_read.properties.nat_rules.Element.properties.external_mappings
        external_mappings.Element = AAZObjectType()
        cls._build_schema_vpn_nat_rule_mapping_read(external_mappings.Element)

        internal_mappings = _schema_virtual_network_gateway_read.properties.nat_rules.Element.properties.internal_mappings
        internal_mappings.Element = AAZObjectType()
        cls._build_schema_vpn_nat_rule_mapping_read(internal_mappings.Element)

        sku = _schema_virtual_network_gateway_read.properties.sku
        sku.capacity = AAZIntType(
            flags={"read_only": True},
        )
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        virtual_network_gateway_migration_status = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_migration_status
        virtual_network_gateway_migration_status.error_message = AAZStrType(
            serialized_name="errorMessage",
        )
        virtual_network_gateway_migration_status.phase = AAZStrType()
        virtual_network_gateway_migration_status.state = AAZStrType()

        virtual_network_gateway_policy_groups = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups
        virtual_network_gateway_policy_groups.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties
        properties.is_default = AAZBoolType(
            serialized_name="isDefault",
            flags={"required": True},
        )
        properties.policy_members = AAZListType(
            serialized_name="policyMembers",
            flags={"required": True},
        )
        properties.priority = AAZIntType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.vng_client_connection_configurations = AAZListType(
            serialized_name="vngClientConnectionConfigurations",
            flags={"read_only": True},
        )

        policy_members = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties.policy_members
        policy_members.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties.policy_members.Element
        _element.attribute_type = AAZStrType(
            serialized_name="attributeType",
        )
        _element.attribute_value = AAZStrType(
            serialized_name="attributeValue",
        )
        _element.name = AAZStrType()

        vng_client_connection_configurations = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties.vng_client_connection_configurations
        vng_client_connection_configurations.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(vng_client_connection_configurations.Element)

        vpn_client_configuration = _schema_virtual_network_gateway_read.properties.vpn_client_configuration
        vpn_client_configuration.aad_audience = AAZStrType(
            serialized_name="aadAudience",
        )
        vpn_client_configuration.aad_issuer = AAZStrType(
            serialized_name="aadIssuer",
        )
        vpn_client_configuration.aad_tenant = AAZStrType(
            serialized_name="aadTenant",
        )
        vpn_client_configuration.radius_server_address = AAZStrType(
            serialized_name="radiusServerAddress",
        )
        vpn_client_configuration.radius_server_secret = AAZStrType(
            serialized_name="radiusServerSecret",
        )
        vpn_client_configuration.radius_servers = AAZListType(
            serialized_name="radiusServers",
        )
        vpn_client_configuration.vng_client_connection_configurations = AAZListType(
            serialized_name="vngClientConnectionConfigurations",
        )
        vpn_client_configuration.vpn_authentication_types = AAZListType(
            serialized_name="vpnAuthenticationTypes",
        )
        vpn_client_configuration.vpn_client_address_pool = AAZObjectType(
            serialized_name="vpnClientAddressPool",
        )
        cls._build_schema_address_space_read(vpn_client_configuration.vpn_client_address_pool)
        vpn_client_configuration.vpn_client_ipsec_policies = AAZListType(
            serialized_name="vpnClientIpsecPolicies",
        )
        vpn_client_configuration.vpn_client_protocols = AAZListType(
            serialized_name="vpnClientProtocols",
        )
        vpn_client_configuration.vpn_client_revoked_certificates = AAZListType(
            serialized_name="vpnClientRevokedCertificates",
        )
        vpn_client_configuration.vpn_client_root_certificates = AAZListType(
            serialized_name="vpnClientRootCertificates",
        )

        radius_servers = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.radius_servers
        radius_servers.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.radius_servers.Element
        _element.radius_server_address = AAZStrType(
            serialized_name="radiusServerAddress",
            flags={"required": True},
        )
        _element.radius_server_score = AAZIntType(
            serialized_name="radiusServerScore",
        )
        _element.radius_server_secret = AAZStrType(
            serialized_name="radiusServerSecret",
        )

        vng_client_connection_configurations = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations
        vng_client_connection_configurations.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.virtual_network_gateway_policy_groups = AAZListType(
            serialized_name="virtualNetworkGatewayPolicyGroups",
            flags={"required": True},
        )
        properties.vpn_client_address_pool = AAZObjectType(
            serialized_name="vpnClientAddressPool",
            flags={"required": True},
        )
        cls._build_schema_address_space_read(properties.vpn_client_address_pool)

        virtual_network_gateway_policy_groups = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations.Element.properties.virtual_network_gateway_policy_groups
        virtual_network_gateway_policy_groups.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(virtual_network_gateway_policy_groups.Element)

        vpn_authentication_types = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_authentication_types
        vpn_authentication_types.Element = AAZStrType()

        vpn_client_ipsec_policies = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_ipsec_policies
        vpn_client_ipsec_policies.Element = AAZObjectType()
        cls._build_schema_ipsec_policy_read(vpn_client_ipsec_policies.Element)

        vpn_client_protocols = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_protocols
        vpn_client_protocols.Element = AAZStrType()

        vpn_client_revoked_certificates = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_revoked_certificates
        vpn_client_revoked_certificates.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_revoked_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_revoked_certificates.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.thumbprint = AAZStrType()

        vpn_client_root_certificates = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_root_certificates
        vpn_client_root_certificates.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_root_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_root_certificates.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_cert_data = AAZStrType(
            serialized_name="publicCertData",
            flags={"required": True},
        )

        tags = _schema_virtual_network_gateway_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_virtual_network_gateway_read.etag
        _schema.extended_location = cls._schema_virtual_network_gateway_read.extended_location
        _schema.id = cls._schema_virtual_network_gateway_read.id
        _schema.identity = cls._schema_virtual_network_gateway_read.identity
        _schema.location = cls._schema_virtual_network_gateway_read.location
        _schema.name = cls._schema_virtual_network_gateway_read.name
        _schema.properties = cls._schema_virtual_network_gateway_read.properties
        _schema.tags = cls._schema_virtual_network_gateway_read.tags
        _schema.type = cls._schema_virtual_network_gateway_read.type

    _schema_vpn_nat_rule_mapping_read = None

    @classmethod
    def _build_schema_vpn_nat_rule_mapping_read(cls, _schema):
        if cls._schema_vpn_nat_rule_mapping_read is not None:
            _schema.address_space = cls._schema_vpn_nat_rule_mapping_read.address_space
            _schema.port_range = cls._schema_vpn_nat_rule_mapping_read.port_range
            return

        cls._schema_vpn_nat_rule_mapping_read = _schema_vpn_nat_rule_mapping_read = AAZObjectType()

        vpn_nat_rule_mapping_read = _schema_vpn_nat_rule_mapping_read
        vpn_nat_rule_mapping_read.address_space = AAZStrType(
            serialized_name="addressSpace",
        )
        vpn_nat_rule_mapping_read.port_range = AAZStrType(
            serialized_name="portRange",
        )

        _schema.address_space = cls._schema_vpn_nat_rule_mapping_read.address_space
        _schema.port_range = cls._schema_vpn_nat_rule_mapping_read.port_range


__all__ = ["Wait"]
