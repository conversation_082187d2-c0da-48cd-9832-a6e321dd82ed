# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vnet-gateway get-resiliency-information",
)
class GetResiliencyInformation(AAZCommand):
    """This operation retrieves the resiliency information for an Express Route Gateway, including the gateway's current resiliency score and recommendations to further improve the score

    :example: GetVirtualNetworkGatewayResiliencyInformation
        az network vnet-gateway get-resiliency-information --resource-group rg1 --virtual-network-gateway-name vpngw --attempt-refresh True
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/virtualnetworkgateways/{}/getresiliencyinformation", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.virtual_network_gateway_name = AAZStrArg(
            options=["--virtual-network-gateway-name", "--name"],
            help="The name of the virtual network gateway.",
            required=True,
            id_part="name",
        )
        _args_schema.attempt_refresh = AAZBoolArg(
            options=["--attempt-refresh"],
            help="Attempt to recalculate the Resiliency Information for the gateway",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualNetworkGatewaysGetResiliencyInformation(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualNetworkGatewaysGetResiliencyInformation(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworkGateways/{virtualNetworkGatewayName}/getResiliencyInformation",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayName", self.ctx.args.virtual_network_gateway_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "attemptRefresh", self.ctx.args.attempt_refresh,
                ),
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.components = AAZListType()
            _schema_on_200.last_computed_time = AAZStrType(
                serialized_name="lastComputedTime",
            )
            _schema_on_200.max_score_from_recommendations = AAZStrType(
                serialized_name="maxScoreFromRecommendations",
            )
            _schema_on_200.min_score_from_recommendations = AAZStrType(
                serialized_name="minScoreFromRecommendations",
            )
            _schema_on_200.next_eligible_compute_time = AAZStrType(
                serialized_name="nextEligibleComputeTime",
            )
            _schema_on_200.overall_score = AAZStrType(
                serialized_name="overallScore",
            )
            _schema_on_200.score_change = AAZStrType(
                serialized_name="scoreChange",
            )

            components = cls._schema_on_200.components
            components.Element = AAZObjectType()

            _element = cls._schema_on_200.components.Element
            _element.current_score = AAZStrType(
                serialized_name="currentScore",
            )
            _element.max_score = AAZStrType(
                serialized_name="maxScore",
            )
            _element.name = AAZStrType()
            _element.recommendations = AAZListType()

            recommendations = cls._schema_on_200.components.Element.recommendations
            recommendations.Element = AAZObjectType()

            _element = cls._schema_on_200.components.Element.recommendations.Element
            _element.call_to_action_link = AAZStrType(
                serialized_name="callToActionLink",
            )
            _element.call_to_action_text = AAZStrType(
                serialized_name="callToActionText",
            )
            _element.recommendation_id = AAZStrType(
                serialized_name="recommendationId",
            )
            _element.recommendation_text = AAZStrType(
                serialized_name="recommendationText",
            )
            _element.recommendation_title = AAZStrType(
                serialized_name="recommendationTitle",
            )
            _element.severity = AAZStrType()

            return cls._schema_on_200


class _GetResiliencyInformationHelper:
    """Helper class for GetResiliencyInformation"""


__all__ = ["GetResiliencyInformation"]
