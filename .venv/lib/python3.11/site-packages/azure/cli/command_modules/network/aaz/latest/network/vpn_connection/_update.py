# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "network vpn-connection update",
)
class Update(AAZCommand):
    """Update a VPN connection.

    :example: Add BGP to an existing connection.
        az network vpn-connection update -g MyResourceGroup -n MyConnection --enable-bgp True

    :example: Update a VPN connection.
        az network vpn-connection update --name MyConnection --resource-group MyResourceGroup --use-policy-based-traffic-selectors true
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.network/connections/{}", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="Connection name.",
            required=True,
            id_part="name",
        )
        _args_schema.enable_bgp = AAZBoolArg(
            options=["--enable-bgp"],
            help="Enable BGP (Border Gateway Protocol).",
            nullable=True,
        )
        _args_schema.express_route_gateway_bypass = AAZBoolArg(
            options=["--express-route-gateway-bypass"],
            help="Bypass ExpressRoute gateway for data forwarding.",
            nullable=True,
        )
        _args_schema.routing_weight = AAZIntArg(
            options=["--routing-weight"],
            help="Connection routing weight.",
            nullable=True,
        )
        _args_schema.shared_key = AAZStrArg(
            options=["--shared-key"],
            help="Shared IPSec key.",
            nullable=True,
        )
        _args_schema.use_policy_based_traffic_selectors = AAZBoolArg(
            options=["--use-policy-based-traffic-selectors"],
            help="Enable policy-based traffic selectors.",
            nullable=True,
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="Space-separated tags: key[=value] [key[=value] ...]. Use \"\" to clear existing tags.",
            nullable=True,
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg(
            nullable=True,
        )

        # define Arg Group "Parameters"

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.ipsec_policies = AAZListArg(
            options=["--ipsec-policies"],
            arg_group="Properties",
            help="The IPSec Policies to be considered by this connection.",
            nullable=True,
        )

        ipsec_policies = cls._args_schema.ipsec_policies
        ipsec_policies.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.ipsec_policies.Element
        _element.dh_group = AAZStrArg(
            options=["dh-group"],
            help="The DH Group used in IKE Phase 1 for initial SA.",
            enum={"DHGroup1": "DHGroup1", "DHGroup14": "DHGroup14", "DHGroup2": "DHGroup2", "DHGroup2048": "DHGroup2048", "DHGroup24": "DHGroup24", "ECP256": "ECP256", "ECP384": "ECP384", "None": "None"},
        )
        _element.ike_encryption = AAZStrArg(
            options=["ike-encryption"],
            help="The IKE encryption algorithm (IKE phase 2).",
            enum={"AES128": "AES128", "AES192": "AES192", "AES256": "AES256", "DES": "DES", "DES3": "DES3", "GCMAES128": "GCMAES128", "GCMAES256": "GCMAES256"},
        )
        _element.ike_integrity = AAZStrArg(
            options=["ike-integrity"],
            help="The IKE integrity algorithm (IKE phase 2).",
            enum={"GCMAES128": "GCMAES128", "GCMAES256": "GCMAES256", "MD5": "MD5", "SHA1": "SHA1", "SHA256": "SHA256", "SHA384": "SHA384"},
        )
        _element.ipsec_encryption = AAZStrArg(
            options=["ipsec-encryption"],
            help="The IPSec encryption algorithm (IKE phase 1).",
            enum={"AES128": "AES128", "AES192": "AES192", "AES256": "AES256", "DES": "DES", "DES3": "DES3", "GCMAES128": "GCMAES128", "GCMAES192": "GCMAES192", "GCMAES256": "GCMAES256", "None": "None"},
        )
        _element.ipsec_integrity = AAZStrArg(
            options=["ipsec-integrity"],
            help="The IPSec integrity algorithm (IKE phase 1).",
            enum={"GCMAES128": "GCMAES128", "GCMAES192": "GCMAES192", "GCMAES256": "GCMAES256", "MD5": "MD5", "SHA1": "SHA1", "SHA256": "SHA256"},
        )
        _element.pfs_group = AAZStrArg(
            options=["pfs-group"],
            help="The Pfs Group used in IKE Phase 2 for new child SA.",
            enum={"ECP256": "ECP256", "ECP384": "ECP384", "None": "None", "PFS1": "PFS1", "PFS14": "PFS14", "PFS2": "PFS2", "PFS2048": "PFS2048", "PFS24": "PFS24", "PFSMM": "PFSMM"},
        )
        _element.sa_data_size_kilobytes = AAZIntArg(
            options=["sa-data-size-kilobytes"],
            help="The IPSec Security Association (also called Quick Mode or Phase 2 SA) payload size in KB for a site to site VPN tunnel.",
        )
        _element.sa_life_time_seconds = AAZIntArg(
            options=["sa-life-time-seconds"],
            help="The IPSec Security Association (also called Quick Mode or Phase 2 SA) lifetime in seconds for a site to site VPN tunnel.",
        )
        return cls._args_schema

    _args_address_space_update = None

    @classmethod
    def _build_args_address_space_update(cls, _schema):
        if cls._args_address_space_update is not None:
            _schema.address_prefixes = cls._args_address_space_update.address_prefixes
            return

        cls._args_address_space_update = AAZObjectArg(
            nullable=True,
        )

        address_space_update = cls._args_address_space_update
        address_space_update.address_prefixes = AAZListArg(
            options=["address-prefixes"],
            help="A list of address blocks reserved for this virtual network in CIDR notation.",
            nullable=True,
        )

        address_prefixes = cls._args_address_space_update.address_prefixes
        address_prefixes.Element = AAZStrArg(
            nullable=True,
        )

        _schema.address_prefixes = cls._args_address_space_update.address_prefixes

    _args_bgp_settings_update = None

    @classmethod
    def _build_args_bgp_settings_update(cls, _schema):
        if cls._args_bgp_settings_update is not None:
            _schema.asn = cls._args_bgp_settings_update.asn
            _schema.bgp_peering_address = cls._args_bgp_settings_update.bgp_peering_address
            _schema.bgp_peering_addresses = cls._args_bgp_settings_update.bgp_peering_addresses
            _schema.peer_weight = cls._args_bgp_settings_update.peer_weight
            return

        cls._args_bgp_settings_update = AAZObjectArg(
            nullable=True,
        )

        bgp_settings_update = cls._args_bgp_settings_update
        bgp_settings_update.asn = AAZIntArg(
            options=["asn"],
            help="The BGP speaker's ASN.",
            nullable=True,
            fmt=AAZIntArgFormat(
                maximum=4294967295,
                minimum=0,
            ),
        )
        bgp_settings_update.bgp_peering_address = AAZStrArg(
            options=["bgp-peering-address"],
            help="The BGP peering address and BGP identifier of this BGP speaker.",
            nullable=True,
        )
        bgp_settings_update.bgp_peering_addresses = AAZListArg(
            options=["bgp-peering-addresses"],
            help="BGP peering address with IP configuration ID for virtual network gateway.",
            nullable=True,
        )
        bgp_settings_update.peer_weight = AAZIntArg(
            options=["peer-weight"],
            help="The weight added to routes learned from this BGP speaker.",
            nullable=True,
        )

        bgp_peering_addresses = cls._args_bgp_settings_update.bgp_peering_addresses
        bgp_peering_addresses.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_bgp_settings_update.bgp_peering_addresses.Element
        _element.custom_bgp_ip_addresses = AAZListArg(
            options=["custom-bgp-ip-addresses"],
            help="The list of custom BGP peering addresses which belong to IP configuration.",
            nullable=True,
        )
        _element.ipconfiguration_id = AAZStrArg(
            options=["ipconfiguration-id"],
            help="The ID of IP configuration which belongs to gateway.",
            nullable=True,
        )

        custom_bgp_ip_addresses = cls._args_bgp_settings_update.bgp_peering_addresses.Element.custom_bgp_ip_addresses
        custom_bgp_ip_addresses.Element = AAZStrArg(
            nullable=True,
        )

        _schema.asn = cls._args_bgp_settings_update.asn
        _schema.bgp_peering_address = cls._args_bgp_settings_update.bgp_peering_address
        _schema.bgp_peering_addresses = cls._args_bgp_settings_update.bgp_peering_addresses
        _schema.peer_weight = cls._args_bgp_settings_update.peer_weight

    _args_ipsec_policy_update = None

    @classmethod
    def _build_args_ipsec_policy_update(cls, _schema):
        if cls._args_ipsec_policy_update is not None:
            _schema.dh_group = cls._args_ipsec_policy_update.dh_group
            _schema.ike_encryption = cls._args_ipsec_policy_update.ike_encryption
            _schema.ike_integrity = cls._args_ipsec_policy_update.ike_integrity
            _schema.ipsec_encryption = cls._args_ipsec_policy_update.ipsec_encryption
            _schema.ipsec_integrity = cls._args_ipsec_policy_update.ipsec_integrity
            _schema.pfs_group = cls._args_ipsec_policy_update.pfs_group
            _schema.sa_data_size_kilobytes = cls._args_ipsec_policy_update.sa_data_size_kilobytes
            _schema.sa_life_time_seconds = cls._args_ipsec_policy_update.sa_life_time_seconds
            return

        cls._args_ipsec_policy_update = AAZObjectArg(
            nullable=True,
        )

        ipsec_policy_update = cls._args_ipsec_policy_update
        ipsec_policy_update.dh_group = AAZStrArg(
            options=["dh-group"],
            help="The DH Group used in IKE Phase 1 for initial SA.",
            enum={"DHGroup1": "DHGroup1", "DHGroup14": "DHGroup14", "DHGroup2": "DHGroup2", "DHGroup2048": "DHGroup2048", "DHGroup24": "DHGroup24", "ECP256": "ECP256", "ECP384": "ECP384", "None": "None"},
        )
        ipsec_policy_update.ike_encryption = AAZStrArg(
            options=["ike-encryption"],
            help="The IKE encryption algorithm (IKE phase 2).",
            enum={"AES128": "AES128", "AES192": "AES192", "AES256": "AES256", "DES": "DES", "DES3": "DES3", "GCMAES128": "GCMAES128", "GCMAES256": "GCMAES256"},
        )
        ipsec_policy_update.ike_integrity = AAZStrArg(
            options=["ike-integrity"],
            help="The IKE integrity algorithm (IKE phase 2).",
            enum={"GCMAES128": "GCMAES128", "GCMAES256": "GCMAES256", "MD5": "MD5", "SHA1": "SHA1", "SHA256": "SHA256", "SHA384": "SHA384"},
        )
        ipsec_policy_update.ipsec_encryption = AAZStrArg(
            options=["ipsec-encryption"],
            help="The IPSec encryption algorithm (IKE phase 1).",
            enum={"AES128": "AES128", "AES192": "AES192", "AES256": "AES256", "DES": "DES", "DES3": "DES3", "GCMAES128": "GCMAES128", "GCMAES192": "GCMAES192", "GCMAES256": "GCMAES256", "None": "None"},
        )
        ipsec_policy_update.ipsec_integrity = AAZStrArg(
            options=["ipsec-integrity"],
            help="The IPSec integrity algorithm (IKE phase 1).",
            enum={"GCMAES128": "GCMAES128", "GCMAES192": "GCMAES192", "GCMAES256": "GCMAES256", "MD5": "MD5", "SHA1": "SHA1", "SHA256": "SHA256"},
        )
        ipsec_policy_update.pfs_group = AAZStrArg(
            options=["pfs-group"],
            help="The Pfs Group used in IKE Phase 2 for new child SA.",
            enum={"ECP256": "ECP256", "ECP384": "ECP384", "None": "None", "PFS1": "PFS1", "PFS14": "PFS14", "PFS2": "PFS2", "PFS2048": "PFS2048", "PFS24": "PFS24", "PFSMM": "PFSMM"},
        )
        ipsec_policy_update.sa_data_size_kilobytes = AAZIntArg(
            options=["sa-data-size-kilobytes"],
            help="The IPSec Security Association (also called Quick Mode or Phase 2 SA) payload size in KB for a site to site VPN tunnel.",
        )
        ipsec_policy_update.sa_life_time_seconds = AAZIntArg(
            options=["sa-life-time-seconds"],
            help="The IPSec Security Association (also called Quick Mode or Phase 2 SA) lifetime in seconds for a site to site VPN tunnel.",
        )

        _schema.dh_group = cls._args_ipsec_policy_update.dh_group
        _schema.ike_encryption = cls._args_ipsec_policy_update.ike_encryption
        _schema.ike_integrity = cls._args_ipsec_policy_update.ike_integrity
        _schema.ipsec_encryption = cls._args_ipsec_policy_update.ipsec_encryption
        _schema.ipsec_integrity = cls._args_ipsec_policy_update.ipsec_integrity
        _schema.pfs_group = cls._args_ipsec_policy_update.pfs_group
        _schema.sa_data_size_kilobytes = cls._args_ipsec_policy_update.sa_data_size_kilobytes
        _schema.sa_life_time_seconds = cls._args_ipsec_policy_update.sa_life_time_seconds

    _args_sub_resource_update = None

    @classmethod
    def _build_args_sub_resource_update(cls, _schema):
        if cls._args_sub_resource_update is not None:
            _schema.id = cls._args_sub_resource_update.id
            return

        cls._args_sub_resource_update = AAZObjectArg(
            nullable=True,
        )

        sub_resource_update = cls._args_sub_resource_update
        sub_resource_update.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
            nullable=True,
        )

        _schema.id = cls._args_sub_resource_update.id

    _args_virtual_network_gateway_update = None

    @classmethod
    def _build_args_virtual_network_gateway_update(cls, _schema):
        if cls._args_virtual_network_gateway_update is not None:
            _schema.active_active = cls._args_virtual_network_gateway_update.active_active
            _schema.admin_state = cls._args_virtual_network_gateway_update.admin_state
            _schema.allow_remote_vnet_traffic = cls._args_virtual_network_gateway_update.allow_remote_vnet_traffic
            _schema.allow_virtual_wan_traffic = cls._args_virtual_network_gateway_update.allow_virtual_wan_traffic
            _schema.auto_scale_configuration = cls._args_virtual_network_gateway_update.auto_scale_configuration
            _schema.bgp_settings = cls._args_virtual_network_gateway_update.bgp_settings
            _schema.custom_routes = cls._args_virtual_network_gateway_update.custom_routes
            _schema.disable_ip_sec_replay_protection = cls._args_virtual_network_gateway_update.disable_ip_sec_replay_protection
            _schema.enable_bgp = cls._args_virtual_network_gateway_update.enable_bgp
            _schema.enable_bgp_route_translation_for_nat = cls._args_virtual_network_gateway_update.enable_bgp_route_translation_for_nat
            _schema.enable_dns_forwarding = cls._args_virtual_network_gateway_update.enable_dns_forwarding
            _schema.enable_high_bandwidth_vpn_gateway = cls._args_virtual_network_gateway_update.enable_high_bandwidth_vpn_gateway
            _schema.enable_private_ip_address = cls._args_virtual_network_gateway_update.enable_private_ip_address
            _schema.extended_location = cls._args_virtual_network_gateway_update.extended_location
            _schema.gateway_default_site = cls._args_virtual_network_gateway_update.gateway_default_site
            _schema.gateway_type = cls._args_virtual_network_gateway_update.gateway_type
            _schema.ip_configurations = cls._args_virtual_network_gateway_update.ip_configurations
            _schema.location = cls._args_virtual_network_gateway_update.location
            _schema.nat_rules = cls._args_virtual_network_gateway_update.nat_rules
            _schema.resiliency_model = cls._args_virtual_network_gateway_update.resiliency_model
            _schema.sku = cls._args_virtual_network_gateway_update.sku
            _schema.tags = cls._args_virtual_network_gateway_update.tags
            _schema.v_net_extended_location_resource_id = cls._args_virtual_network_gateway_update.v_net_extended_location_resource_id
            _schema.virtual_network_gateway_migration_status = cls._args_virtual_network_gateway_update.virtual_network_gateway_migration_status
            _schema.virtual_network_gateway_policy_groups = cls._args_virtual_network_gateway_update.virtual_network_gateway_policy_groups
            _schema.vpn_client_configuration = cls._args_virtual_network_gateway_update.vpn_client_configuration
            _schema.vpn_gateway_generation = cls._args_virtual_network_gateway_update.vpn_gateway_generation
            _schema.vpn_type = cls._args_virtual_network_gateway_update.vpn_type
            return

        cls._args_virtual_network_gateway_update = AAZObjectArg()

        virtual_network_gateway_update = cls._args_virtual_network_gateway_update
        virtual_network_gateway_update.extended_location = AAZObjectArg(
            options=["extended-location"],
            help="The extended location of type local virtual network gateway.",
            nullable=True,
        )
        virtual_network_gateway_update.location = AAZResourceLocationArg(
            options=["l", "location"],
            help="Resource location.",
            nullable=True,
        )
        virtual_network_gateway_update.active_active = AAZBoolArg(
            options=["active-active"],
            help="ActiveActive flag.",
            nullable=True,
        )
        virtual_network_gateway_update.admin_state = AAZStrArg(
            options=["admin-state"],
            help="Property to indicate if the Express Route Gateway serves traffic when there are multiple Express Route Gateways in the vnet",
            nullable=True,
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        virtual_network_gateway_update.allow_remote_vnet_traffic = AAZBoolArg(
            options=["allow-remote-vnet-traffic"],
            help="Configure this gateway to accept traffic from other Azure Virtual Networks. This configuration does not support connectivity to Azure Virtual WAN.",
            nullable=True,
        )
        virtual_network_gateway_update.allow_virtual_wan_traffic = AAZBoolArg(
            options=["allow-virtual-wan-traffic"],
            help="Configures this gateway to accept traffic from remote Virtual WAN networks.",
            nullable=True,
        )
        virtual_network_gateway_update.auto_scale_configuration = AAZObjectArg(
            options=["auto-scale-configuration"],
            help="Autoscale configuration for virutal network gateway",
            nullable=True,
        )
        virtual_network_gateway_update.bgp_settings = AAZObjectArg(
            options=["bgp-settings"],
            help="Virtual network gateway's BGP speaker settings.",
            nullable=True,
        )
        cls._build_args_bgp_settings_update(virtual_network_gateway_update.bgp_settings)
        virtual_network_gateway_update.custom_routes = AAZObjectArg(
            options=["custom-routes"],
            help="The reference to the address space resource which represents the custom routes address space specified by the customer for virtual network gateway and VpnClient.",
            nullable=True,
        )
        cls._build_args_address_space_update(virtual_network_gateway_update.custom_routes)
        virtual_network_gateway_update.disable_ip_sec_replay_protection = AAZBoolArg(
            options=["disable-ip-sec-replay-protection"],
            help="disableIPSecReplayProtection flag.",
            nullable=True,
        )
        virtual_network_gateway_update.enable_bgp = AAZBoolArg(
            options=["enable-bgp"],
            help="Whether BGP is enabled for this virtual network gateway or not.",
            nullable=True,
        )
        virtual_network_gateway_update.enable_bgp_route_translation_for_nat = AAZBoolArg(
            options=["enable-bgp-route-translation-for-nat"],
            help="EnableBgpRouteTranslationForNat flag.",
            nullable=True,
        )
        virtual_network_gateway_update.enable_dns_forwarding = AAZBoolArg(
            options=["enable-dns-forwarding"],
            help="Whether dns forwarding is enabled or not.",
            nullable=True,
        )
        virtual_network_gateway_update.enable_high_bandwidth_vpn_gateway = AAZBoolArg(
            options=["enable-high-bandwidth-vpn-gateway"],
            help="To enable Advanced Connectivity feature for VPN gateway",
            nullable=True,
        )
        virtual_network_gateway_update.enable_private_ip_address = AAZBoolArg(
            options=["enable-private-ip-address"],
            help="Whether private IP needs to be enabled on this gateway for connections or not.",
            nullable=True,
        )
        virtual_network_gateway_update.gateway_default_site = AAZObjectArg(
            options=["gateway-default-site"],
            help="The reference to the LocalNetworkGateway resource which represents local network site having default routes. Assign Null value in case of removing existing default site setting.",
            nullable=True,
        )
        cls._build_args_sub_resource_update(virtual_network_gateway_update.gateway_default_site)
        virtual_network_gateway_update.gateway_type = AAZStrArg(
            options=["gateway-type"],
            help="The type of this virtual network gateway.",
            nullable=True,
            enum={"ExpressRoute": "ExpressRoute", "LocalGateway": "LocalGateway", "Vpn": "Vpn"},
        )
        virtual_network_gateway_update.ip_configurations = AAZListArg(
            options=["ip-configurations"],
            help="IP configurations for virtual network gateway.",
            nullable=True,
        )
        virtual_network_gateway_update.nat_rules = AAZListArg(
            options=["nat-rules"],
            help="NatRules for virtual network gateway.",
            nullable=True,
        )
        virtual_network_gateway_update.resiliency_model = AAZStrArg(
            options=["resiliency-model"],
            help="Property to indicate if the Express Route Gateway has resiliency model of MultiHomed or SingleHomed",
            nullable=True,
            enum={"MultiHomed": "MultiHomed", "SingleHomed": "SingleHomed"},
        )
        virtual_network_gateway_update.sku = AAZObjectArg(
            options=["sku"],
            help="The reference to the VirtualNetworkGatewaySku resource which represents the SKU selected for Virtual network gateway.",
            nullable=True,
        )
        virtual_network_gateway_update.v_net_extended_location_resource_id = AAZStrArg(
            options=["v-net-extended-location-resource-id"],
            help="Customer vnet resource id. VirtualNetworkGateway of type local gateway is associated with the customer vnet.",
            nullable=True,
        )
        virtual_network_gateway_update.virtual_network_gateway_migration_status = AAZObjectArg(
            options=["virtual-network-gateway-migration-status"],
            help="The reference to the VirtualNetworkGatewayMigrationStatus which represents the status of migration.",
            nullable=True,
        )
        virtual_network_gateway_update.virtual_network_gateway_policy_groups = AAZListArg(
            options=["virtual-network-gateway-policy-groups"],
            help="The reference to the VirtualNetworkGatewayPolicyGroup resource which represents the available VirtualNetworkGatewayPolicyGroup for the gateway.",
            nullable=True,
        )
        virtual_network_gateway_update.vpn_client_configuration = AAZObjectArg(
            options=["vpn-client-configuration"],
            help="The reference to the VpnClientConfiguration resource which represents the P2S VpnClient configurations.",
            nullable=True,
        )
        virtual_network_gateway_update.vpn_gateway_generation = AAZStrArg(
            options=["vpn-gateway-generation"],
            help="The generation for this VirtualNetworkGateway. Must be None if gatewayType is not VPN.",
            nullable=True,
            enum={"Generation1": "Generation1", "Generation2": "Generation2", "None": "None"},
        )
        virtual_network_gateway_update.vpn_type = AAZStrArg(
            options=["vpn-type"],
            help="The type of this virtual network gateway.",
            nullable=True,
            enum={"PolicyBased": "PolicyBased", "RouteBased": "RouteBased"},
        )
        virtual_network_gateway_update.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags.",
            nullable=True,
        )

        extended_location = cls._args_virtual_network_gateway_update.extended_location
        extended_location.name = AAZStrArg(
            options=["name"],
            help="The name of the extended location.",
            nullable=True,
        )
        extended_location.type = AAZStrArg(
            options=["type"],
            help="The type of the extended location.",
            nullable=True,
            enum={"EdgeZone": "EdgeZone"},
        )

        auto_scale_configuration = cls._args_virtual_network_gateway_update.auto_scale_configuration
        auto_scale_configuration.bounds = AAZObjectArg(
            options=["bounds"],
            help="The bounds of the autoscale configuration",
            nullable=True,
        )

        bounds = cls._args_virtual_network_gateway_update.auto_scale_configuration.bounds
        bounds.max = AAZIntArg(
            options=["max"],
            help="Maximum Scale Units for Autoscale configuration",
            nullable=True,
        )
        bounds.min = AAZIntArg(
            options=["min"],
            help="Minimum scale Units for Autoscale configuration",
            nullable=True,
        )

        ip_configurations = cls._args_virtual_network_gateway_update.ip_configurations
        ip_configurations.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.ip_configurations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
            nullable=True,
        )
        _element.private_ip_allocation_method = AAZStrArg(
            options=["private-ip-allocation-method"],
            help="The private IP address allocation method.",
            nullable=True,
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )
        _element.public_ip_address = AAZObjectArg(
            options=["public-ip-address"],
            help="The reference to the public IP resource.",
            nullable=True,
        )
        cls._build_args_sub_resource_update(_element.public_ip_address)
        _element.subnet = AAZObjectArg(
            options=["subnet"],
            help="The reference to the subnet resource.",
            nullable=True,
        )
        cls._build_args_sub_resource_update(_element.subnet)

        nat_rules = cls._args_virtual_network_gateway_update.nat_rules
        nat_rules.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.nat_rules.Element
        _element.id = AAZResourceIdArg(
            options=["id"],
            help="Resource ID.",
            nullable=True,
            fmt=AAZResourceIdArgFormat(
                template="/subscriptions/{}/resourceGroups/{}/providers/Microsoft.Network/virtualNetworkGateways/{}/natRules/{}",
            ),
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
            nullable=True,
        )
        _element.external_mappings = AAZListArg(
            options=["external-mappings"],
            help="The private IP address external mapping for NAT.",
            nullable=True,
        )
        _element.internal_mappings = AAZListArg(
            options=["internal-mappings"],
            help="The private IP address internal mapping for NAT.",
            nullable=True,
        )
        _element.ip_configuration_id = AAZStrArg(
            options=["ip-configuration-id"],
            help="The IP Configuration ID this NAT rule applies to.",
            nullable=True,
        )
        _element.mode = AAZStrArg(
            options=["mode"],
            help="The Source NAT direction of a VPN NAT.",
            nullable=True,
            enum={"EgressSnat": "EgressSnat", "IngressSnat": "IngressSnat"},
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The type of NAT rule for VPN NAT.",
            nullable=True,
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )

        external_mappings = cls._args_virtual_network_gateway_update.nat_rules.Element.external_mappings
        external_mappings.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_vpn_nat_rule_mapping_update(external_mappings.Element)

        internal_mappings = cls._args_virtual_network_gateway_update.nat_rules.Element.internal_mappings
        internal_mappings.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_vpn_nat_rule_mapping_update(internal_mappings.Element)

        sku = cls._args_virtual_network_gateway_update.sku
        sku.name = AAZStrArg(
            options=["name"],
            help="Gateway SKU name.",
            nullable=True,
            enum={"Basic": "Basic", "ErGw1AZ": "ErGw1AZ", "ErGw2AZ": "ErGw2AZ", "ErGw3AZ": "ErGw3AZ", "ErGwScale": "ErGwScale", "HighPerformance": "HighPerformance", "Standard": "Standard", "UltraPerformance": "UltraPerformance", "VpnGw1": "VpnGw1", "VpnGw1AZ": "VpnGw1AZ", "VpnGw2": "VpnGw2", "VpnGw2AZ": "VpnGw2AZ", "VpnGw3": "VpnGw3", "VpnGw3AZ": "VpnGw3AZ", "VpnGw4": "VpnGw4", "VpnGw4AZ": "VpnGw4AZ", "VpnGw5": "VpnGw5", "VpnGw5AZ": "VpnGw5AZ"},
        )
        sku.tier = AAZStrArg(
            options=["tier"],
            help="Gateway SKU tier.",
            nullable=True,
            enum={"Basic": "Basic", "ErGw1AZ": "ErGw1AZ", "ErGw2AZ": "ErGw2AZ", "ErGw3AZ": "ErGw3AZ", "ErGwScale": "ErGwScale", "HighPerformance": "HighPerformance", "Standard": "Standard", "UltraPerformance": "UltraPerformance", "VpnGw1": "VpnGw1", "VpnGw1AZ": "VpnGw1AZ", "VpnGw2": "VpnGw2", "VpnGw2AZ": "VpnGw2AZ", "VpnGw3": "VpnGw3", "VpnGw3AZ": "VpnGw3AZ", "VpnGw4": "VpnGw4", "VpnGw4AZ": "VpnGw4AZ", "VpnGw5": "VpnGw5", "VpnGw5AZ": "VpnGw5AZ"},
        )

        virtual_network_gateway_migration_status = cls._args_virtual_network_gateway_update.virtual_network_gateway_migration_status
        virtual_network_gateway_migration_status.error_message = AAZStrArg(
            options=["error-message"],
            help="Error if any occurs during migration.",
            nullable=True,
        )
        virtual_network_gateway_migration_status.phase = AAZStrArg(
            options=["phase"],
            help="Represent the current migration phase of gateway.",
            nullable=True,
            enum={"Abort": "Abort", "AbortSucceeded": "AbortSucceeded", "Commit": "Commit", "CommitSucceeded": "CommitSucceeded", "Execute": "Execute", "ExecuteSucceeded": "ExecuteSucceeded", "None": "None", "Prepare": "Prepare", "PrepareSucceeded": "PrepareSucceeded"},
        )
        virtual_network_gateway_migration_status.state = AAZStrArg(
            options=["state"],
            help="Represent the current state of gateway migration.",
            nullable=True,
            enum={"Failed": "Failed", "InProgress": "InProgress", "None": "None", "Succeeded": "Succeeded"},
        )

        virtual_network_gateway_policy_groups = cls._args_virtual_network_gateway_update.virtual_network_gateway_policy_groups
        virtual_network_gateway_policy_groups.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.virtual_network_gateway_policy_groups.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
            nullable=True,
        )
        _element.is_default = AAZBoolArg(
            options=["is-default"],
            help="Shows if this is a Default VirtualNetworkGatewayPolicyGroup or not.",
        )
        _element.policy_members = AAZListArg(
            options=["policy-members"],
            help="Multiple PolicyMembers for VirtualNetworkGatewayPolicyGroup.",
        )
        _element.priority = AAZIntArg(
            options=["priority"],
            help="Priority for VirtualNetworkGatewayPolicyGroup.",
        )

        policy_members = cls._args_virtual_network_gateway_update.virtual_network_gateway_policy_groups.Element.policy_members
        policy_members.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.virtual_network_gateway_policy_groups.Element.policy_members.Element
        _element.attribute_type = AAZStrArg(
            options=["attribute-type"],
            help="The Vpn Policy member attribute type.",
            nullable=True,
            enum={"AADGroupId": "AADGroupId", "CertificateGroupId": "CertificateGroupId", "RadiusAzureGroupId": "RadiusAzureGroupId"},
        )
        _element.attribute_value = AAZStrArg(
            options=["attribute-value"],
            help="The value of Attribute used for this VirtualNetworkGatewayPolicyGroupMember.",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="Name of the VirtualNetworkGatewayPolicyGroupMember.",
            nullable=True,
        )

        vpn_client_configuration = cls._args_virtual_network_gateway_update.vpn_client_configuration
        vpn_client_configuration.aad_audience = AAZStrArg(
            options=["aad-audience"],
            help="The AADAudience property of the VirtualNetworkGateway resource for vpn client connection used for AAD authentication.",
            nullable=True,
        )
        vpn_client_configuration.aad_issuer = AAZStrArg(
            options=["aad-issuer"],
            help="The AADIssuer property of the VirtualNetworkGateway resource for vpn client connection used for AAD authentication.",
            nullable=True,
        )
        vpn_client_configuration.aad_tenant = AAZStrArg(
            options=["aad-tenant"],
            help="The AADTenant property of the VirtualNetworkGateway resource for vpn client connection used for AAD authentication.",
            nullable=True,
        )
        vpn_client_configuration.radius_server_address = AAZStrArg(
            options=["radius-server-address"],
            help="The radius server address property of the VirtualNetworkGateway resource for vpn client connection.",
            nullable=True,
        )
        vpn_client_configuration.radius_server_secret = AAZStrArg(
            options=["radius-server-secret"],
            help="The radius secret property of the VirtualNetworkGateway resource for vpn client connection.",
            nullable=True,
        )
        vpn_client_configuration.radius_servers = AAZListArg(
            options=["radius-servers"],
            help="The radiusServers property for multiple radius server configuration.",
            nullable=True,
        )
        vpn_client_configuration.vng_client_connection_configurations = AAZListArg(
            options=["vng-client-connection-configurations"],
            help="per ip address pool connection policy for virtual network gateway P2S client.",
            nullable=True,
        )
        vpn_client_configuration.vpn_authentication_types = AAZListArg(
            options=["vpn-authentication-types"],
            help="VPN authentication types for the virtual network gateway..",
            nullable=True,
        )
        vpn_client_configuration.vpn_client_address_pool = AAZObjectArg(
            options=["vpn-client-address-pool"],
            help="The reference to the address space resource which represents Address space for P2S VpnClient.",
            nullable=True,
        )
        cls._build_args_address_space_update(vpn_client_configuration.vpn_client_address_pool)
        vpn_client_configuration.vpn_client_ipsec_policies = AAZListArg(
            options=["vpn-client-ipsec-policies"],
            help="VpnClientIpsecPolicies for virtual network gateway P2S client.",
            nullable=True,
        )
        vpn_client_configuration.vpn_client_protocols = AAZListArg(
            options=["vpn-client-protocols"],
            help="VpnClientProtocols for Virtual network gateway.",
            nullable=True,
        )
        vpn_client_configuration.vpn_client_revoked_certificates = AAZListArg(
            options=["vpn-client-revoked-certificates"],
            help="VpnClientRevokedCertificate for Virtual network gateway.",
            nullable=True,
        )
        vpn_client_configuration.vpn_client_root_certificates = AAZListArg(
            options=["vpn-client-root-certificates"],
            help="VpnClientRootCertificate for virtual network gateway.",
            nullable=True,
        )

        radius_servers = cls._args_virtual_network_gateway_update.vpn_client_configuration.radius_servers
        radius_servers.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.vpn_client_configuration.radius_servers.Element
        _element.radius_server_address = AAZStrArg(
            options=["radius-server-address"],
            help="The address of this radius server.",
        )
        _element.radius_server_score = AAZIntArg(
            options=["radius-server-score"],
            help="The initial score assigned to this radius server.",
            nullable=True,
        )
        _element.radius_server_secret = AAZStrArg(
            options=["radius-server-secret"],
            help="The secret used for this radius server.",
            nullable=True,
        )

        vng_client_connection_configurations = cls._args_virtual_network_gateway_update.vpn_client_configuration.vng_client_connection_configurations
        vng_client_connection_configurations.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.vpn_client_configuration.vng_client_connection_configurations.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
            nullable=True,
        )
        _element.virtual_network_gateway_policy_groups = AAZListArg(
            options=["virtual-network-gateway-policy-groups"],
            help="List of references to virtualNetworkGatewayPolicyGroups",
        )
        _element.vpn_client_address_pool = AAZObjectArg(
            options=["vpn-client-address-pool"],
            help="The reference to the address space resource which represents Address space for P2S VpnClient.",
            nullable=True,
        )
        cls._build_args_address_space_update(_element.vpn_client_address_pool)

        virtual_network_gateway_policy_groups = cls._args_virtual_network_gateway_update.vpn_client_configuration.vng_client_connection_configurations.Element.virtual_network_gateway_policy_groups
        virtual_network_gateway_policy_groups.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_sub_resource_update(virtual_network_gateway_policy_groups.Element)

        vpn_authentication_types = cls._args_virtual_network_gateway_update.vpn_client_configuration.vpn_authentication_types
        vpn_authentication_types.Element = AAZStrArg(
            nullable=True,
            enum={"AAD": "AAD", "Certificate": "Certificate", "Radius": "Radius"},
        )

        vpn_client_ipsec_policies = cls._args_virtual_network_gateway_update.vpn_client_configuration.vpn_client_ipsec_policies
        vpn_client_ipsec_policies.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_ipsec_policy_update(vpn_client_ipsec_policies.Element)

        vpn_client_protocols = cls._args_virtual_network_gateway_update.vpn_client_configuration.vpn_client_protocols
        vpn_client_protocols.Element = AAZStrArg(
            nullable=True,
            enum={"IkeV2": "IkeV2", "OpenVPN": "OpenVPN", "SSTP": "SSTP"},
        )

        vpn_client_revoked_certificates = cls._args_virtual_network_gateway_update.vpn_client_configuration.vpn_client_revoked_certificates
        vpn_client_revoked_certificates.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.vpn_client_configuration.vpn_client_revoked_certificates.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
            nullable=True,
        )
        _element.thumbprint = AAZStrArg(
            options=["thumbprint"],
            help="The revoked VPN client certificate thumbprint.",
            nullable=True,
        )

        vpn_client_root_certificates = cls._args_virtual_network_gateway_update.vpn_client_configuration.vpn_client_root_certificates
        vpn_client_root_certificates.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_virtual_network_gateway_update.vpn_client_configuration.vpn_client_root_certificates.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource that is unique within a resource group. This name can be used to access the resource.",
            nullable=True,
        )
        _element.public_cert_data = AAZStrArg(
            options=["public-cert-data"],
            help="The certificate public data.",
        )

        tags = cls._args_virtual_network_gateway_update.tags
        tags.Element = AAZStrArg(
            nullable=True,
        )

        _schema.active_active = cls._args_virtual_network_gateway_update.active_active
        _schema.admin_state = cls._args_virtual_network_gateway_update.admin_state
        _schema.allow_remote_vnet_traffic = cls._args_virtual_network_gateway_update.allow_remote_vnet_traffic
        _schema.allow_virtual_wan_traffic = cls._args_virtual_network_gateway_update.allow_virtual_wan_traffic
        _schema.auto_scale_configuration = cls._args_virtual_network_gateway_update.auto_scale_configuration
        _schema.bgp_settings = cls._args_virtual_network_gateway_update.bgp_settings
        _schema.custom_routes = cls._args_virtual_network_gateway_update.custom_routes
        _schema.disable_ip_sec_replay_protection = cls._args_virtual_network_gateway_update.disable_ip_sec_replay_protection
        _schema.enable_bgp = cls._args_virtual_network_gateway_update.enable_bgp
        _schema.enable_bgp_route_translation_for_nat = cls._args_virtual_network_gateway_update.enable_bgp_route_translation_for_nat
        _schema.enable_dns_forwarding = cls._args_virtual_network_gateway_update.enable_dns_forwarding
        _schema.enable_high_bandwidth_vpn_gateway = cls._args_virtual_network_gateway_update.enable_high_bandwidth_vpn_gateway
        _schema.enable_private_ip_address = cls._args_virtual_network_gateway_update.enable_private_ip_address
        _schema.extended_location = cls._args_virtual_network_gateway_update.extended_location
        _schema.gateway_default_site = cls._args_virtual_network_gateway_update.gateway_default_site
        _schema.gateway_type = cls._args_virtual_network_gateway_update.gateway_type
        _schema.ip_configurations = cls._args_virtual_network_gateway_update.ip_configurations
        _schema.location = cls._args_virtual_network_gateway_update.location
        _schema.nat_rules = cls._args_virtual_network_gateway_update.nat_rules
        _schema.resiliency_model = cls._args_virtual_network_gateway_update.resiliency_model
        _schema.sku = cls._args_virtual_network_gateway_update.sku
        _schema.tags = cls._args_virtual_network_gateway_update.tags
        _schema.v_net_extended_location_resource_id = cls._args_virtual_network_gateway_update.v_net_extended_location_resource_id
        _schema.virtual_network_gateway_migration_status = cls._args_virtual_network_gateway_update.virtual_network_gateway_migration_status
        _schema.virtual_network_gateway_policy_groups = cls._args_virtual_network_gateway_update.virtual_network_gateway_policy_groups
        _schema.vpn_client_configuration = cls._args_virtual_network_gateway_update.vpn_client_configuration
        _schema.vpn_gateway_generation = cls._args_virtual_network_gateway_update.vpn_gateway_generation
        _schema.vpn_type = cls._args_virtual_network_gateway_update.vpn_type

    _args_vpn_nat_rule_mapping_update = None

    @classmethod
    def _build_args_vpn_nat_rule_mapping_update(cls, _schema):
        if cls._args_vpn_nat_rule_mapping_update is not None:
            _schema.address_space = cls._args_vpn_nat_rule_mapping_update.address_space
            _schema.port_range = cls._args_vpn_nat_rule_mapping_update.port_range
            return

        cls._args_vpn_nat_rule_mapping_update = AAZObjectArg(
            nullable=True,
        )

        vpn_nat_rule_mapping_update = cls._args_vpn_nat_rule_mapping_update
        vpn_nat_rule_mapping_update.address_space = AAZStrArg(
            options=["address-space"],
            help="Address space for Vpn NatRule mapping.",
            nullable=True,
        )
        vpn_nat_rule_mapping_update.port_range = AAZStrArg(
            options=["port-range"],
            help="Port range for Vpn NatRule mapping.",
            nullable=True,
        )

        _schema.address_space = cls._args_vpn_nat_rule_mapping_update.address_space
        _schema.port_range = cls._args_vpn_nat_rule_mapping_update.port_range

    def _execute_operations(self):
        self.pre_operations()
        self.VirtualNetworkGatewayConnectionsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.VirtualNetworkGatewayConnectionsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualNetworkGatewayConnectionsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/connections/{virtualNetworkGatewayConnectionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayConnectionName", self.ctx.args.name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_virtual_network_gateway_connection_read(cls._schema_on_200)

            return cls._schema_on_200

    class VirtualNetworkGatewayConnectionsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/connections/{virtualNetworkGatewayConnectionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "virtualNetworkGatewayConnectionName", self.ctx.args.name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_virtual_network_gateway_connection_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("enableBgp", AAZBoolType, ".enable_bgp")
                properties.set_prop("expressRouteGatewayBypass", AAZBoolType, ".express_route_gateway_bypass")
                properties.set_prop("ipsecPolicies", AAZListType, ".ipsec_policies")
                properties.set_prop("routingWeight", AAZIntType, ".routing_weight")
                properties.set_prop("sharedKey", AAZStrType, ".shared_key")
                properties.set_prop("usePolicyBasedTrafficSelectors", AAZBoolType, ".use_policy_based_traffic_selectors")

            ipsec_policies = _builder.get(".properties.ipsecPolicies")
            if ipsec_policies is not None:
                ipsec_policies.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.ipsecPolicies[]")
            if _elements is not None:
                _elements.set_prop("dhGroup", AAZStrType, ".dh_group", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("ikeEncryption", AAZStrType, ".ike_encryption", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("ikeIntegrity", AAZStrType, ".ike_integrity", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("ipsecEncryption", AAZStrType, ".ipsec_encryption", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("ipsecIntegrity", AAZStrType, ".ipsec_integrity", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("pfsGroup", AAZStrType, ".pfs_group", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("saDataSizeKilobytes", AAZIntType, ".sa_data_size_kilobytes", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("saLifeTimeSeconds", AAZIntType, ".sa_life_time_seconds", typ_kwargs={"flags": {"required": True}})

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    @classmethod
    def _build_schema_address_space_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("addressPrefixes", AAZListType, ".address_prefixes")

        address_prefixes = _builder.get(".addressPrefixes")
        if address_prefixes is not None:
            address_prefixes.set_elements(AAZStrType, ".")

    @classmethod
    def _build_schema_bgp_settings_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("asn", AAZIntType, ".asn")
        _builder.set_prop("bgpPeeringAddress", AAZStrType, ".bgp_peering_address")
        _builder.set_prop("bgpPeeringAddresses", AAZListType, ".bgp_peering_addresses")
        _builder.set_prop("peerWeight", AAZIntType, ".peer_weight")

        bgp_peering_addresses = _builder.get(".bgpPeeringAddresses")
        if bgp_peering_addresses is not None:
            bgp_peering_addresses.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".bgpPeeringAddresses[]")
        if _elements is not None:
            _elements.set_prop("customBgpIpAddresses", AAZListType, ".custom_bgp_ip_addresses")
            _elements.set_prop("ipconfigurationId", AAZStrType, ".ipconfiguration_id")

        custom_bgp_ip_addresses = _builder.get(".bgpPeeringAddresses[].customBgpIpAddresses")
        if custom_bgp_ip_addresses is not None:
            custom_bgp_ip_addresses.set_elements(AAZStrType, ".")

    @classmethod
    def _build_schema_ipsec_policy_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("dhGroup", AAZStrType, ".dh_group", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("ikeEncryption", AAZStrType, ".ike_encryption", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("ikeIntegrity", AAZStrType, ".ike_integrity", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("ipsecEncryption", AAZStrType, ".ipsec_encryption", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("ipsecIntegrity", AAZStrType, ".ipsec_integrity", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("pfsGroup", AAZStrType, ".pfs_group", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("saDataSizeKilobytes", AAZIntType, ".sa_data_size_kilobytes", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("saLifeTimeSeconds", AAZIntType, ".sa_life_time_seconds", typ_kwargs={"flags": {"required": True}})

    @classmethod
    def _build_schema_sub_resource_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    @classmethod
    def _build_schema_virtual_network_gateway_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("extendedLocation", AAZObjectType, ".extended_location")
        _builder.set_prop("identity", AAZIdentityObjectType)
        _builder.set_prop("location", AAZStrType, ".location")
        _builder.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})
        _builder.set_prop("tags", AAZDictType, ".tags")

        extended_location = _builder.get(".extendedLocation")
        if extended_location is not None:
            extended_location.set_prop("name", AAZStrType, ".name")
            extended_location.set_prop("type", AAZStrType, ".type")

        properties = _builder.get(".properties")
        if properties is not None:
            properties.set_prop("activeActive", AAZBoolType, ".active_active")
            properties.set_prop("adminState", AAZStrType, ".admin_state")
            properties.set_prop("allowRemoteVnetTraffic", AAZBoolType, ".allow_remote_vnet_traffic")
            properties.set_prop("allowVirtualWanTraffic", AAZBoolType, ".allow_virtual_wan_traffic")
            properties.set_prop("autoScaleConfiguration", AAZObjectType, ".auto_scale_configuration")
            cls._build_schema_bgp_settings_update(properties.set_prop("bgpSettings", AAZObjectType, ".bgp_settings"))
            cls._build_schema_address_space_update(properties.set_prop("customRoutes", AAZObjectType, ".custom_routes"))
            properties.set_prop("disableIPSecReplayProtection", AAZBoolType, ".disable_ip_sec_replay_protection")
            properties.set_prop("enableBgp", AAZBoolType, ".enable_bgp")
            properties.set_prop("enableBgpRouteTranslationForNat", AAZBoolType, ".enable_bgp_route_translation_for_nat")
            properties.set_prop("enableDnsForwarding", AAZBoolType, ".enable_dns_forwarding")
            properties.set_prop("enableHighBandwidthVpnGateway", AAZBoolType, ".enable_high_bandwidth_vpn_gateway")
            properties.set_prop("enablePrivateIpAddress", AAZBoolType, ".enable_private_ip_address")
            cls._build_schema_sub_resource_update(properties.set_prop("gatewayDefaultSite", AAZObjectType, ".gateway_default_site"))
            properties.set_prop("gatewayType", AAZStrType, ".gateway_type")
            properties.set_prop("ipConfigurations", AAZListType, ".ip_configurations")
            properties.set_prop("natRules", AAZListType, ".nat_rules")
            properties.set_prop("resiliencyModel", AAZStrType, ".resiliency_model")
            properties.set_prop("sku", AAZObjectType, ".sku")
            properties.set_prop("vNetExtendedLocationResourceId", AAZStrType, ".v_net_extended_location_resource_id")
            properties.set_prop("virtualNetworkGatewayMigrationStatus", AAZObjectType, ".virtual_network_gateway_migration_status")
            properties.set_prop("virtualNetworkGatewayPolicyGroups", AAZListType, ".virtual_network_gateway_policy_groups")
            properties.set_prop("vpnClientConfiguration", AAZObjectType, ".vpn_client_configuration")
            properties.set_prop("vpnGatewayGeneration", AAZStrType, ".vpn_gateway_generation")
            properties.set_prop("vpnType", AAZStrType, ".vpn_type")

        auto_scale_configuration = _builder.get(".properties.autoScaleConfiguration")
        if auto_scale_configuration is not None:
            auto_scale_configuration.set_prop("bounds", AAZObjectType, ".bounds")

        bounds = _builder.get(".properties.autoScaleConfiguration.bounds")
        if bounds is not None:
            bounds.set_prop("max", AAZIntType, ".max")
            bounds.set_prop("min", AAZIntType, ".min")

        ip_configurations = _builder.get(".properties.ipConfigurations")
        if ip_configurations is not None:
            ip_configurations.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.ipConfigurations[]")
        if _elements is not None:
            _elements.set_prop("id", AAZStrType, ".id")
            _elements.set_prop("name", AAZStrType, ".name")
            _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

        properties = _builder.get(".properties.ipConfigurations[].properties")
        if properties is not None:
            properties.set_prop("privateIPAllocationMethod", AAZStrType, ".private_ip_allocation_method")
            cls._build_schema_sub_resource_update(properties.set_prop("publicIPAddress", AAZObjectType, ".public_ip_address"))
            cls._build_schema_sub_resource_update(properties.set_prop("subnet", AAZObjectType, ".subnet"))

        nat_rules = _builder.get(".properties.natRules")
        if nat_rules is not None:
            nat_rules.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.natRules[]")
        if _elements is not None:
            _elements.set_prop("id", AAZStrType, ".id")
            _elements.set_prop("name", AAZStrType, ".name")
            _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

        properties = _builder.get(".properties.natRules[].properties")
        if properties is not None:
            properties.set_prop("externalMappings", AAZListType, ".external_mappings")
            properties.set_prop("internalMappings", AAZListType, ".internal_mappings")
            properties.set_prop("ipConfigurationId", AAZStrType, ".ip_configuration_id")
            properties.set_prop("mode", AAZStrType, ".mode")
            properties.set_prop("type", AAZStrType, ".type")

        external_mappings = _builder.get(".properties.natRules[].properties.externalMappings")
        if external_mappings is not None:
            cls._build_schema_vpn_nat_rule_mapping_update(external_mappings.set_elements(AAZObjectType, "."))

        internal_mappings = _builder.get(".properties.natRules[].properties.internalMappings")
        if internal_mappings is not None:
            cls._build_schema_vpn_nat_rule_mapping_update(internal_mappings.set_elements(AAZObjectType, "."))

        sku = _builder.get(".properties.sku")
        if sku is not None:
            sku.set_prop("name", AAZStrType, ".name")
            sku.set_prop("tier", AAZStrType, ".tier")

        virtual_network_gateway_migration_status = _builder.get(".properties.virtualNetworkGatewayMigrationStatus")
        if virtual_network_gateway_migration_status is not None:
            virtual_network_gateway_migration_status.set_prop("errorMessage", AAZStrType, ".error_message")
            virtual_network_gateway_migration_status.set_prop("phase", AAZStrType, ".phase")
            virtual_network_gateway_migration_status.set_prop("state", AAZStrType, ".state")

        virtual_network_gateway_policy_groups = _builder.get(".properties.virtualNetworkGatewayPolicyGroups")
        if virtual_network_gateway_policy_groups is not None:
            virtual_network_gateway_policy_groups.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.virtualNetworkGatewayPolicyGroups[]")
        if _elements is not None:
            _elements.set_prop("id", AAZStrType, ".id")
            _elements.set_prop("name", AAZStrType, ".name")
            _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

        properties = _builder.get(".properties.virtualNetworkGatewayPolicyGroups[].properties")
        if properties is not None:
            properties.set_prop("isDefault", AAZBoolType, ".is_default", typ_kwargs={"flags": {"required": True}})
            properties.set_prop("policyMembers", AAZListType, ".policy_members", typ_kwargs={"flags": {"required": True}})
            properties.set_prop("priority", AAZIntType, ".priority", typ_kwargs={"flags": {"required": True}})

        policy_members = _builder.get(".properties.virtualNetworkGatewayPolicyGroups[].properties.policyMembers")
        if policy_members is not None:
            policy_members.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.virtualNetworkGatewayPolicyGroups[].properties.policyMembers[]")
        if _elements is not None:
            _elements.set_prop("attributeType", AAZStrType, ".attribute_type")
            _elements.set_prop("attributeValue", AAZStrType, ".attribute_value")
            _elements.set_prop("name", AAZStrType, ".name")

        vpn_client_configuration = _builder.get(".properties.vpnClientConfiguration")
        if vpn_client_configuration is not None:
            vpn_client_configuration.set_prop("aadAudience", AAZStrType, ".aad_audience")
            vpn_client_configuration.set_prop("aadIssuer", AAZStrType, ".aad_issuer")
            vpn_client_configuration.set_prop("aadTenant", AAZStrType, ".aad_tenant")
            vpn_client_configuration.set_prop("radiusServerAddress", AAZStrType, ".radius_server_address")
            vpn_client_configuration.set_prop("radiusServerSecret", AAZStrType, ".radius_server_secret")
            vpn_client_configuration.set_prop("radiusServers", AAZListType, ".radius_servers")
            vpn_client_configuration.set_prop("vngClientConnectionConfigurations", AAZListType, ".vng_client_connection_configurations")
            vpn_client_configuration.set_prop("vpnAuthenticationTypes", AAZListType, ".vpn_authentication_types")
            cls._build_schema_address_space_update(vpn_client_configuration.set_prop("vpnClientAddressPool", AAZObjectType, ".vpn_client_address_pool"))
            vpn_client_configuration.set_prop("vpnClientIpsecPolicies", AAZListType, ".vpn_client_ipsec_policies")
            vpn_client_configuration.set_prop("vpnClientProtocols", AAZListType, ".vpn_client_protocols")
            vpn_client_configuration.set_prop("vpnClientRevokedCertificates", AAZListType, ".vpn_client_revoked_certificates")
            vpn_client_configuration.set_prop("vpnClientRootCertificates", AAZListType, ".vpn_client_root_certificates")

        radius_servers = _builder.get(".properties.vpnClientConfiguration.radiusServers")
        if radius_servers is not None:
            radius_servers.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.vpnClientConfiguration.radiusServers[]")
        if _elements is not None:
            _elements.set_prop("radiusServerAddress", AAZStrType, ".radius_server_address", typ_kwargs={"flags": {"required": True}})
            _elements.set_prop("radiusServerScore", AAZIntType, ".radius_server_score")
            _elements.set_prop("radiusServerSecret", AAZStrType, ".radius_server_secret")

        vng_client_connection_configurations = _builder.get(".properties.vpnClientConfiguration.vngClientConnectionConfigurations")
        if vng_client_connection_configurations is not None:
            vng_client_connection_configurations.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.vpnClientConfiguration.vngClientConnectionConfigurations[]")
        if _elements is not None:
            _elements.set_prop("id", AAZStrType, ".id")
            _elements.set_prop("name", AAZStrType, ".name")
            _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

        properties = _builder.get(".properties.vpnClientConfiguration.vngClientConnectionConfigurations[].properties")
        if properties is not None:
            properties.set_prop("virtualNetworkGatewayPolicyGroups", AAZListType, ".virtual_network_gateway_policy_groups", typ_kwargs={"flags": {"required": True}})
            cls._build_schema_address_space_update(properties.set_prop("vpnClientAddressPool", AAZObjectType, ".vpn_client_address_pool", typ_kwargs={"flags": {"required": True}}))

        virtual_network_gateway_policy_groups = _builder.get(".properties.vpnClientConfiguration.vngClientConnectionConfigurations[].properties.virtualNetworkGatewayPolicyGroups")
        if virtual_network_gateway_policy_groups is not None:
            cls._build_schema_sub_resource_update(virtual_network_gateway_policy_groups.set_elements(AAZObjectType, "."))

        vpn_authentication_types = _builder.get(".properties.vpnClientConfiguration.vpnAuthenticationTypes")
        if vpn_authentication_types is not None:
            vpn_authentication_types.set_elements(AAZStrType, ".")

        vpn_client_ipsec_policies = _builder.get(".properties.vpnClientConfiguration.vpnClientIpsecPolicies")
        if vpn_client_ipsec_policies is not None:
            cls._build_schema_ipsec_policy_update(vpn_client_ipsec_policies.set_elements(AAZObjectType, "."))

        vpn_client_protocols = _builder.get(".properties.vpnClientConfiguration.vpnClientProtocols")
        if vpn_client_protocols is not None:
            vpn_client_protocols.set_elements(AAZStrType, ".")

        vpn_client_revoked_certificates = _builder.get(".properties.vpnClientConfiguration.vpnClientRevokedCertificates")
        if vpn_client_revoked_certificates is not None:
            vpn_client_revoked_certificates.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.vpnClientConfiguration.vpnClientRevokedCertificates[]")
        if _elements is not None:
            _elements.set_prop("id", AAZStrType, ".id")
            _elements.set_prop("name", AAZStrType, ".name")
            _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

        properties = _builder.get(".properties.vpnClientConfiguration.vpnClientRevokedCertificates[].properties")
        if properties is not None:
            properties.set_prop("thumbprint", AAZStrType, ".thumbprint")

        vpn_client_root_certificates = _builder.get(".properties.vpnClientConfiguration.vpnClientRootCertificates")
        if vpn_client_root_certificates is not None:
            vpn_client_root_certificates.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".properties.vpnClientConfiguration.vpnClientRootCertificates[]")
        if _elements is not None:
            _elements.set_prop("id", AAZStrType, ".id")
            _elements.set_prop("name", AAZStrType, ".name")
            _elements.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})

        properties = _builder.get(".properties.vpnClientConfiguration.vpnClientRootCertificates[].properties")
        if properties is not None:
            properties.set_prop("publicCertData", AAZStrType, ".public_cert_data", typ_kwargs={"flags": {"required": True}})

        tags = _builder.get(".tags")
        if tags is not None:
            tags.set_elements(AAZStrType, ".")

    @classmethod
    def _build_schema_vpn_nat_rule_mapping_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("addressSpace", AAZStrType, ".address_space")
        _builder.set_prop("portRange", AAZStrType, ".port_range")

    _schema_address_space_read = None

    @classmethod
    def _build_schema_address_space_read(cls, _schema):
        if cls._schema_address_space_read is not None:
            _schema.address_prefixes = cls._schema_address_space_read.address_prefixes
            return

        cls._schema_address_space_read = _schema_address_space_read = AAZObjectType()

        address_space_read = _schema_address_space_read
        address_space_read.address_prefixes = AAZListType(
            serialized_name="addressPrefixes",
        )

        address_prefixes = _schema_address_space_read.address_prefixes
        address_prefixes.Element = AAZStrType()

        _schema.address_prefixes = cls._schema_address_space_read.address_prefixes

    _schema_bgp_settings_read = None

    @classmethod
    def _build_schema_bgp_settings_read(cls, _schema):
        if cls._schema_bgp_settings_read is not None:
            _schema.asn = cls._schema_bgp_settings_read.asn
            _schema.bgp_peering_address = cls._schema_bgp_settings_read.bgp_peering_address
            _schema.bgp_peering_addresses = cls._schema_bgp_settings_read.bgp_peering_addresses
            _schema.peer_weight = cls._schema_bgp_settings_read.peer_weight
            return

        cls._schema_bgp_settings_read = _schema_bgp_settings_read = AAZObjectType()

        bgp_settings_read = _schema_bgp_settings_read
        bgp_settings_read.asn = AAZIntType()
        bgp_settings_read.bgp_peering_address = AAZStrType(
            serialized_name="bgpPeeringAddress",
        )
        bgp_settings_read.bgp_peering_addresses = AAZListType(
            serialized_name="bgpPeeringAddresses",
        )
        bgp_settings_read.peer_weight = AAZIntType(
            serialized_name="peerWeight",
        )

        bgp_peering_addresses = _schema_bgp_settings_read.bgp_peering_addresses
        bgp_peering_addresses.Element = AAZObjectType()

        _element = _schema_bgp_settings_read.bgp_peering_addresses.Element
        _element.custom_bgp_ip_addresses = AAZListType(
            serialized_name="customBgpIpAddresses",
        )
        _element.default_bgp_ip_addresses = AAZListType(
            serialized_name="defaultBgpIpAddresses",
            flags={"read_only": True},
        )
        _element.ipconfiguration_id = AAZStrType(
            serialized_name="ipconfigurationId",
        )
        _element.tunnel_ip_addresses = AAZListType(
            serialized_name="tunnelIpAddresses",
            flags={"read_only": True},
        )

        custom_bgp_ip_addresses = _schema_bgp_settings_read.bgp_peering_addresses.Element.custom_bgp_ip_addresses
        custom_bgp_ip_addresses.Element = AAZStrType()

        default_bgp_ip_addresses = _schema_bgp_settings_read.bgp_peering_addresses.Element.default_bgp_ip_addresses
        default_bgp_ip_addresses.Element = AAZStrType()

        tunnel_ip_addresses = _schema_bgp_settings_read.bgp_peering_addresses.Element.tunnel_ip_addresses
        tunnel_ip_addresses.Element = AAZStrType()

        _schema.asn = cls._schema_bgp_settings_read.asn
        _schema.bgp_peering_address = cls._schema_bgp_settings_read.bgp_peering_address
        _schema.bgp_peering_addresses = cls._schema_bgp_settings_read.bgp_peering_addresses
        _schema.peer_weight = cls._schema_bgp_settings_read.peer_weight

    _schema_ipsec_policy_read = None

    @classmethod
    def _build_schema_ipsec_policy_read(cls, _schema):
        if cls._schema_ipsec_policy_read is not None:
            _schema.dh_group = cls._schema_ipsec_policy_read.dh_group
            _schema.ike_encryption = cls._schema_ipsec_policy_read.ike_encryption
            _schema.ike_integrity = cls._schema_ipsec_policy_read.ike_integrity
            _schema.ipsec_encryption = cls._schema_ipsec_policy_read.ipsec_encryption
            _schema.ipsec_integrity = cls._schema_ipsec_policy_read.ipsec_integrity
            _schema.pfs_group = cls._schema_ipsec_policy_read.pfs_group
            _schema.sa_data_size_kilobytes = cls._schema_ipsec_policy_read.sa_data_size_kilobytes
            _schema.sa_life_time_seconds = cls._schema_ipsec_policy_read.sa_life_time_seconds
            return

        cls._schema_ipsec_policy_read = _schema_ipsec_policy_read = AAZObjectType()

        ipsec_policy_read = _schema_ipsec_policy_read
        ipsec_policy_read.dh_group = AAZStrType(
            serialized_name="dhGroup",
            flags={"required": True},
        )
        ipsec_policy_read.ike_encryption = AAZStrType(
            serialized_name="ikeEncryption",
            flags={"required": True},
        )
        ipsec_policy_read.ike_integrity = AAZStrType(
            serialized_name="ikeIntegrity",
            flags={"required": True},
        )
        ipsec_policy_read.ipsec_encryption = AAZStrType(
            serialized_name="ipsecEncryption",
            flags={"required": True},
        )
        ipsec_policy_read.ipsec_integrity = AAZStrType(
            serialized_name="ipsecIntegrity",
            flags={"required": True},
        )
        ipsec_policy_read.pfs_group = AAZStrType(
            serialized_name="pfsGroup",
            flags={"required": True},
        )
        ipsec_policy_read.sa_data_size_kilobytes = AAZIntType(
            serialized_name="saDataSizeKilobytes",
            flags={"required": True},
        )
        ipsec_policy_read.sa_life_time_seconds = AAZIntType(
            serialized_name="saLifeTimeSeconds",
            flags={"required": True},
        )

        _schema.dh_group = cls._schema_ipsec_policy_read.dh_group
        _schema.ike_encryption = cls._schema_ipsec_policy_read.ike_encryption
        _schema.ike_integrity = cls._schema_ipsec_policy_read.ike_integrity
        _schema.ipsec_encryption = cls._schema_ipsec_policy_read.ipsec_encryption
        _schema.ipsec_integrity = cls._schema_ipsec_policy_read.ipsec_integrity
        _schema.pfs_group = cls._schema_ipsec_policy_read.pfs_group
        _schema.sa_data_size_kilobytes = cls._schema_ipsec_policy_read.sa_data_size_kilobytes
        _schema.sa_life_time_seconds = cls._schema_ipsec_policy_read.sa_life_time_seconds

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_virtual_network_gateway_connection_read = None

    @classmethod
    def _build_schema_virtual_network_gateway_connection_read(cls, _schema):
        if cls._schema_virtual_network_gateway_connection_read is not None:
            _schema.etag = cls._schema_virtual_network_gateway_connection_read.etag
            _schema.id = cls._schema_virtual_network_gateway_connection_read.id
            _schema.location = cls._schema_virtual_network_gateway_connection_read.location
            _schema.name = cls._schema_virtual_network_gateway_connection_read.name
            _schema.properties = cls._schema_virtual_network_gateway_connection_read.properties
            _schema.tags = cls._schema_virtual_network_gateway_connection_read.tags
            _schema.type = cls._schema_virtual_network_gateway_connection_read.type
            return

        cls._schema_virtual_network_gateway_connection_read = _schema_virtual_network_gateway_connection_read = AAZObjectType()

        virtual_network_gateway_connection_read = _schema_virtual_network_gateway_connection_read
        virtual_network_gateway_connection_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_gateway_connection_read.id = AAZStrType()
        virtual_network_gateway_connection_read.location = AAZStrType()
        virtual_network_gateway_connection_read.name = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_gateway_connection_read.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )
        virtual_network_gateway_connection_read.tags = AAZDictType()
        virtual_network_gateway_connection_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_gateway_connection_read.properties
        properties.authorization_key = AAZStrType(
            serialized_name="authorizationKey",
        )
        properties.connection_mode = AAZStrType(
            serialized_name="connectionMode",
        )
        properties.connection_protocol = AAZStrType(
            serialized_name="connectionProtocol",
        )
        properties.connection_status = AAZStrType(
            serialized_name="connectionStatus",
            flags={"read_only": True},
        )
        properties.connection_type = AAZStrType(
            serialized_name="connectionType",
            flags={"required": True},
        )
        properties.dpd_timeout_seconds = AAZIntType(
            serialized_name="dpdTimeoutSeconds",
        )
        properties.egress_bytes_transferred = AAZIntType(
            serialized_name="egressBytesTransferred",
            flags={"read_only": True},
        )
        properties.egress_nat_rules = AAZListType(
            serialized_name="egressNatRules",
        )
        properties.enable_bgp = AAZBoolType(
            serialized_name="enableBgp",
        )
        properties.enable_private_link_fast_path = AAZBoolType(
            serialized_name="enablePrivateLinkFastPath",
        )
        properties.express_route_gateway_bypass = AAZBoolType(
            serialized_name="expressRouteGatewayBypass",
        )
        properties.gateway_custom_bgp_ip_addresses = AAZListType(
            serialized_name="gatewayCustomBgpIpAddresses",
        )
        properties.ingress_bytes_transferred = AAZIntType(
            serialized_name="ingressBytesTransferred",
            flags={"read_only": True},
        )
        properties.ingress_nat_rules = AAZListType(
            serialized_name="ingressNatRules",
        )
        properties.ipsec_policies = AAZListType(
            serialized_name="ipsecPolicies",
        )
        properties.local_network_gateway2 = AAZObjectType(
            serialized_name="localNetworkGateway2",
        )
        properties.peer = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.peer)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.routing_weight = AAZIntType(
            serialized_name="routingWeight",
        )
        properties.shared_key = AAZStrType(
            serialized_name="sharedKey",
        )
        properties.traffic_selector_policies = AAZListType(
            serialized_name="trafficSelectorPolicies",
        )
        properties.tunnel_connection_status = AAZListType(
            serialized_name="tunnelConnectionStatus",
            flags={"read_only": True},
        )
        properties.tunnel_properties = AAZListType(
            serialized_name="tunnelProperties",
        )
        properties.use_local_azure_ip_address = AAZBoolType(
            serialized_name="useLocalAzureIpAddress",
        )
        properties.use_policy_based_traffic_selectors = AAZBoolType(
            serialized_name="usePolicyBasedTrafficSelectors",
        )
        properties.virtual_network_gateway1 = AAZObjectType(
            serialized_name="virtualNetworkGateway1",
            flags={"required": True},
        )
        cls._build_schema_virtual_network_gateway_read(properties.virtual_network_gateway1)
        properties.virtual_network_gateway2 = AAZObjectType(
            serialized_name="virtualNetworkGateway2",
        )
        cls._build_schema_virtual_network_gateway_read(properties.virtual_network_gateway2)

        egress_nat_rules = _schema_virtual_network_gateway_connection_read.properties.egress_nat_rules
        egress_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(egress_nat_rules.Element)

        gateway_custom_bgp_ip_addresses = _schema_virtual_network_gateway_connection_read.properties.gateway_custom_bgp_ip_addresses
        gateway_custom_bgp_ip_addresses.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_connection_read.properties.gateway_custom_bgp_ip_addresses.Element
        _element.custom_bgp_ip_address = AAZStrType(
            serialized_name="customBgpIpAddress",
            flags={"required": True},
        )
        _element.ip_configuration_id = AAZStrType(
            serialized_name="ipConfigurationId",
            flags={"required": True},
        )

        ingress_nat_rules = _schema_virtual_network_gateway_connection_read.properties.ingress_nat_rules
        ingress_nat_rules.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(ingress_nat_rules.Element)

        ipsec_policies = _schema_virtual_network_gateway_connection_read.properties.ipsec_policies
        ipsec_policies.Element = AAZObjectType()
        cls._build_schema_ipsec_policy_read(ipsec_policies.Element)

        local_network_gateway2 = _schema_virtual_network_gateway_connection_read.properties.local_network_gateway2
        local_network_gateway2.etag = AAZStrType(
            flags={"read_only": True},
        )
        local_network_gateway2.id = AAZStrType()
        local_network_gateway2.location = AAZStrType()
        local_network_gateway2.name = AAZStrType(
            flags={"read_only": True},
        )
        local_network_gateway2.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )
        local_network_gateway2.tags = AAZDictType()
        local_network_gateway2.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_gateway_connection_read.properties.local_network_gateway2.properties
        properties.bgp_settings = AAZObjectType(
            serialized_name="bgpSettings",
        )
        cls._build_schema_bgp_settings_read(properties.bgp_settings)
        properties.fqdn = AAZStrType()
        properties.gateway_ip_address = AAZStrType(
            serialized_name="gatewayIpAddress",
        )
        properties.local_network_address_space = AAZObjectType(
            serialized_name="localNetworkAddressSpace",
        )
        cls._build_schema_address_space_read(properties.local_network_address_space)
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )

        tags = _schema_virtual_network_gateway_connection_read.properties.local_network_gateway2.tags
        tags.Element = AAZStrType()

        traffic_selector_policies = _schema_virtual_network_gateway_connection_read.properties.traffic_selector_policies
        traffic_selector_policies.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_connection_read.properties.traffic_selector_policies.Element
        _element.local_address_ranges = AAZListType(
            serialized_name="localAddressRanges",
            flags={"required": True},
        )
        _element.remote_address_ranges = AAZListType(
            serialized_name="remoteAddressRanges",
            flags={"required": True},
        )

        local_address_ranges = _schema_virtual_network_gateway_connection_read.properties.traffic_selector_policies.Element.local_address_ranges
        local_address_ranges.Element = AAZStrType()

        remote_address_ranges = _schema_virtual_network_gateway_connection_read.properties.traffic_selector_policies.Element.remote_address_ranges
        remote_address_ranges.Element = AAZStrType()

        tunnel_connection_status = _schema_virtual_network_gateway_connection_read.properties.tunnel_connection_status
        tunnel_connection_status.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_connection_read.properties.tunnel_connection_status.Element
        _element.connection_status = AAZStrType(
            serialized_name="connectionStatus",
            flags={"read_only": True},
        )
        _element.egress_bytes_transferred = AAZIntType(
            serialized_name="egressBytesTransferred",
            flags={"read_only": True},
        )
        _element.ingress_bytes_transferred = AAZIntType(
            serialized_name="ingressBytesTransferred",
            flags={"read_only": True},
        )
        _element.last_connection_established_utc_time = AAZStrType(
            serialized_name="lastConnectionEstablishedUtcTime",
            flags={"read_only": True},
        )
        _element.tunnel = AAZStrType(
            flags={"read_only": True},
        )

        tunnel_properties = _schema_virtual_network_gateway_connection_read.properties.tunnel_properties
        tunnel_properties.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_connection_read.properties.tunnel_properties.Element
        _element.bgp_peering_address = AAZStrType(
            serialized_name="bgpPeeringAddress",
        )
        _element.tunnel_ip_address = AAZStrType(
            serialized_name="tunnelIpAddress",
        )

        tags = _schema_virtual_network_gateway_connection_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_virtual_network_gateway_connection_read.etag
        _schema.id = cls._schema_virtual_network_gateway_connection_read.id
        _schema.location = cls._schema_virtual_network_gateway_connection_read.location
        _schema.name = cls._schema_virtual_network_gateway_connection_read.name
        _schema.properties = cls._schema_virtual_network_gateway_connection_read.properties
        _schema.tags = cls._schema_virtual_network_gateway_connection_read.tags
        _schema.type = cls._schema_virtual_network_gateway_connection_read.type

    _schema_virtual_network_gateway_read = None

    @classmethod
    def _build_schema_virtual_network_gateway_read(cls, _schema):
        if cls._schema_virtual_network_gateway_read is not None:
            _schema.etag = cls._schema_virtual_network_gateway_read.etag
            _schema.extended_location = cls._schema_virtual_network_gateway_read.extended_location
            _schema.id = cls._schema_virtual_network_gateway_read.id
            _schema.identity = cls._schema_virtual_network_gateway_read.identity
            _schema.location = cls._schema_virtual_network_gateway_read.location
            _schema.name = cls._schema_virtual_network_gateway_read.name
            _schema.properties = cls._schema_virtual_network_gateway_read.properties
            _schema.tags = cls._schema_virtual_network_gateway_read.tags
            _schema.type = cls._schema_virtual_network_gateway_read.type
            return

        cls._schema_virtual_network_gateway_read = _schema_virtual_network_gateway_read = AAZObjectType()

        virtual_network_gateway_read = _schema_virtual_network_gateway_read
        virtual_network_gateway_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_gateway_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        virtual_network_gateway_read.id = AAZStrType()
        virtual_network_gateway_read.identity = AAZIdentityObjectType()
        virtual_network_gateway_read.location = AAZStrType()
        virtual_network_gateway_read.name = AAZStrType(
            flags={"read_only": True},
        )
        virtual_network_gateway_read.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )
        virtual_network_gateway_read.tags = AAZDictType()
        virtual_network_gateway_read.type = AAZStrType(
            flags={"read_only": True},
        )

        extended_location = _schema_virtual_network_gateway_read.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        identity = _schema_virtual_network_gateway_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType()
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_virtual_network_gateway_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_gateway_read.properties
        properties.active_active = AAZBoolType(
            serialized_name="activeActive",
        )
        properties.admin_state = AAZStrType(
            serialized_name="adminState",
        )
        properties.allow_remote_vnet_traffic = AAZBoolType(
            serialized_name="allowRemoteVnetTraffic",
        )
        properties.allow_virtual_wan_traffic = AAZBoolType(
            serialized_name="allowVirtualWanTraffic",
        )
        properties.auto_scale_configuration = AAZObjectType(
            serialized_name="autoScaleConfiguration",
        )
        properties.bgp_settings = AAZObjectType(
            serialized_name="bgpSettings",
        )
        cls._build_schema_bgp_settings_read(properties.bgp_settings)
        properties.custom_routes = AAZObjectType(
            serialized_name="customRoutes",
        )
        cls._build_schema_address_space_read(properties.custom_routes)
        properties.disable_ip_sec_replay_protection = AAZBoolType(
            serialized_name="disableIPSecReplayProtection",
        )
        properties.enable_bgp = AAZBoolType(
            serialized_name="enableBgp",
        )
        properties.enable_bgp_route_translation_for_nat = AAZBoolType(
            serialized_name="enableBgpRouteTranslationForNat",
        )
        properties.enable_dns_forwarding = AAZBoolType(
            serialized_name="enableDnsForwarding",
        )
        properties.enable_high_bandwidth_vpn_gateway = AAZBoolType(
            serialized_name="enableHighBandwidthVpnGateway",
        )
        properties.enable_private_ip_address = AAZBoolType(
            serialized_name="enablePrivateIpAddress",
        )
        properties.gateway_default_site = AAZObjectType(
            serialized_name="gatewayDefaultSite",
        )
        cls._build_schema_sub_resource_read(properties.gateway_default_site)
        properties.gateway_type = AAZStrType(
            serialized_name="gatewayType",
        )
        properties.inbound_dns_forwarding_endpoint = AAZStrType(
            serialized_name="inboundDnsForwardingEndpoint",
            flags={"read_only": True},
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
        )
        properties.nat_rules = AAZListType(
            serialized_name="natRules",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resiliency_model = AAZStrType(
            serialized_name="resiliencyModel",
        )
        properties.resource_guid = AAZStrType(
            serialized_name="resourceGuid",
            flags={"read_only": True},
        )
        properties.sku = AAZObjectType()
        properties.v_net_extended_location_resource_id = AAZStrType(
            serialized_name="vNetExtendedLocationResourceId",
        )
        properties.virtual_network_gateway_migration_status = AAZObjectType(
            serialized_name="virtualNetworkGatewayMigrationStatus",
        )
        properties.virtual_network_gateway_policy_groups = AAZListType(
            serialized_name="virtualNetworkGatewayPolicyGroups",
        )
        properties.vpn_client_configuration = AAZObjectType(
            serialized_name="vpnClientConfiguration",
        )
        properties.vpn_gateway_generation = AAZStrType(
            serialized_name="vpnGatewayGeneration",
        )
        properties.vpn_type = AAZStrType(
            serialized_name="vpnType",
        )

        auto_scale_configuration = _schema_virtual_network_gateway_read.properties.auto_scale_configuration
        auto_scale_configuration.bounds = AAZObjectType()

        bounds = _schema_virtual_network_gateway_read.properties.auto_scale_configuration.bounds
        bounds.max = AAZIntType()
        bounds.min = AAZIntType()

        ip_configurations = _schema_virtual_network_gateway_read.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.ip_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.ip_configurations.Element.properties
        properties.private_ip_address = AAZStrType(
            serialized_name="privateIPAddress",
            flags={"read_only": True},
        )
        properties.private_ip_allocation_method = AAZStrType(
            serialized_name="privateIPAllocationMethod",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_ip_address = AAZObjectType(
            serialized_name="publicIPAddress",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_address)
        properties.subnet = AAZObjectType()
        cls._build_schema_sub_resource_read(properties.subnet)

        nat_rules = _schema_virtual_network_gateway_read.properties.nat_rules
        nat_rules.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.nat_rules.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.nat_rules.Element.properties
        properties.external_mappings = AAZListType(
            serialized_name="externalMappings",
        )
        properties.internal_mappings = AAZListType(
            serialized_name="internalMappings",
        )
        properties.ip_configuration_id = AAZStrType(
            serialized_name="ipConfigurationId",
        )
        properties.mode = AAZStrType()
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.type = AAZStrType()

        external_mappings = _schema_virtual_network_gateway_read.properties.nat_rules.Element.properties.external_mappings
        external_mappings.Element = AAZObjectType()
        cls._build_schema_vpn_nat_rule_mapping_read(external_mappings.Element)

        internal_mappings = _schema_virtual_network_gateway_read.properties.nat_rules.Element.properties.internal_mappings
        internal_mappings.Element = AAZObjectType()
        cls._build_schema_vpn_nat_rule_mapping_read(internal_mappings.Element)

        sku = _schema_virtual_network_gateway_read.properties.sku
        sku.capacity = AAZIntType(
            flags={"read_only": True},
        )
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        virtual_network_gateway_migration_status = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_migration_status
        virtual_network_gateway_migration_status.error_message = AAZStrType(
            serialized_name="errorMessage",
        )
        virtual_network_gateway_migration_status.phase = AAZStrType()
        virtual_network_gateway_migration_status.state = AAZStrType()

        virtual_network_gateway_policy_groups = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups
        virtual_network_gateway_policy_groups.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties
        properties.is_default = AAZBoolType(
            serialized_name="isDefault",
            flags={"required": True},
        )
        properties.policy_members = AAZListType(
            serialized_name="policyMembers",
            flags={"required": True},
        )
        properties.priority = AAZIntType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.vng_client_connection_configurations = AAZListType(
            serialized_name="vngClientConnectionConfigurations",
            flags={"read_only": True},
        )

        policy_members = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties.policy_members
        policy_members.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties.policy_members.Element
        _element.attribute_type = AAZStrType(
            serialized_name="attributeType",
        )
        _element.attribute_value = AAZStrType(
            serialized_name="attributeValue",
        )
        _element.name = AAZStrType()

        vng_client_connection_configurations = _schema_virtual_network_gateway_read.properties.virtual_network_gateway_policy_groups.Element.properties.vng_client_connection_configurations
        vng_client_connection_configurations.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(vng_client_connection_configurations.Element)

        vpn_client_configuration = _schema_virtual_network_gateway_read.properties.vpn_client_configuration
        vpn_client_configuration.aad_audience = AAZStrType(
            serialized_name="aadAudience",
        )
        vpn_client_configuration.aad_issuer = AAZStrType(
            serialized_name="aadIssuer",
        )
        vpn_client_configuration.aad_tenant = AAZStrType(
            serialized_name="aadTenant",
        )
        vpn_client_configuration.radius_server_address = AAZStrType(
            serialized_name="radiusServerAddress",
        )
        vpn_client_configuration.radius_server_secret = AAZStrType(
            serialized_name="radiusServerSecret",
        )
        vpn_client_configuration.radius_servers = AAZListType(
            serialized_name="radiusServers",
        )
        vpn_client_configuration.vng_client_connection_configurations = AAZListType(
            serialized_name="vngClientConnectionConfigurations",
        )
        vpn_client_configuration.vpn_authentication_types = AAZListType(
            serialized_name="vpnAuthenticationTypes",
        )
        vpn_client_configuration.vpn_client_address_pool = AAZObjectType(
            serialized_name="vpnClientAddressPool",
        )
        cls._build_schema_address_space_read(vpn_client_configuration.vpn_client_address_pool)
        vpn_client_configuration.vpn_client_ipsec_policies = AAZListType(
            serialized_name="vpnClientIpsecPolicies",
        )
        vpn_client_configuration.vpn_client_protocols = AAZListType(
            serialized_name="vpnClientProtocols",
        )
        vpn_client_configuration.vpn_client_revoked_certificates = AAZListType(
            serialized_name="vpnClientRevokedCertificates",
        )
        vpn_client_configuration.vpn_client_root_certificates = AAZListType(
            serialized_name="vpnClientRootCertificates",
        )

        radius_servers = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.radius_servers
        radius_servers.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.radius_servers.Element
        _element.radius_server_address = AAZStrType(
            serialized_name="radiusServerAddress",
            flags={"required": True},
        )
        _element.radius_server_score = AAZIntType(
            serialized_name="radiusServerScore",
        )
        _element.radius_server_secret = AAZStrType(
            serialized_name="radiusServerSecret",
        )

        vng_client_connection_configurations = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations
        vng_client_connection_configurations.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.virtual_network_gateway_policy_groups = AAZListType(
            serialized_name="virtualNetworkGatewayPolicyGroups",
            flags={"required": True},
        )
        properties.vpn_client_address_pool = AAZObjectType(
            serialized_name="vpnClientAddressPool",
            flags={"required": True},
        )
        cls._build_schema_address_space_read(properties.vpn_client_address_pool)

        virtual_network_gateway_policy_groups = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vng_client_connection_configurations.Element.properties.virtual_network_gateway_policy_groups
        virtual_network_gateway_policy_groups.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(virtual_network_gateway_policy_groups.Element)

        vpn_authentication_types = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_authentication_types
        vpn_authentication_types.Element = AAZStrType()

        vpn_client_ipsec_policies = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_ipsec_policies
        vpn_client_ipsec_policies.Element = AAZObjectType()
        cls._build_schema_ipsec_policy_read(vpn_client_ipsec_policies.Element)

        vpn_client_protocols = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_protocols
        vpn_client_protocols.Element = AAZStrType()

        vpn_client_revoked_certificates = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_revoked_certificates
        vpn_client_revoked_certificates.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_revoked_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_revoked_certificates.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.thumbprint = AAZStrType()

        vpn_client_root_certificates = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_root_certificates
        vpn_client_root_certificates.Element = AAZObjectType()

        _element = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_root_certificates.Element
        _element.etag = AAZStrType(
            flags={"read_only": True},
        )
        _element.id = AAZStrType()
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )

        properties = _schema_virtual_network_gateway_read.properties.vpn_client_configuration.vpn_client_root_certificates.Element.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_cert_data = AAZStrType(
            serialized_name="publicCertData",
            flags={"required": True},
        )

        tags = _schema_virtual_network_gateway_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_virtual_network_gateway_read.etag
        _schema.extended_location = cls._schema_virtual_network_gateway_read.extended_location
        _schema.id = cls._schema_virtual_network_gateway_read.id
        _schema.identity = cls._schema_virtual_network_gateway_read.identity
        _schema.location = cls._schema_virtual_network_gateway_read.location
        _schema.name = cls._schema_virtual_network_gateway_read.name
        _schema.properties = cls._schema_virtual_network_gateway_read.properties
        _schema.tags = cls._schema_virtual_network_gateway_read.tags
        _schema.type = cls._schema_virtual_network_gateway_read.type

    _schema_vpn_nat_rule_mapping_read = None

    @classmethod
    def _build_schema_vpn_nat_rule_mapping_read(cls, _schema):
        if cls._schema_vpn_nat_rule_mapping_read is not None:
            _schema.address_space = cls._schema_vpn_nat_rule_mapping_read.address_space
            _schema.port_range = cls._schema_vpn_nat_rule_mapping_read.port_range
            return

        cls._schema_vpn_nat_rule_mapping_read = _schema_vpn_nat_rule_mapping_read = AAZObjectType()

        vpn_nat_rule_mapping_read = _schema_vpn_nat_rule_mapping_read
        vpn_nat_rule_mapping_read.address_space = AAZStrType(
            serialized_name="addressSpace",
        )
        vpn_nat_rule_mapping_read.port_range = AAZStrType(
            serialized_name="portRange",
        )

        _schema.address_space = cls._schema_vpn_nat_rule_mapping_read.address_space
        _schema.port_range = cls._schema_vpn_nat_rule_mapping_read.port_range


__all__ = ["Update"]
