# coding=utf-8
# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# --------------------------------------------------------------------------------------------

from knack.help_files import helps  # pylint: disable=unused-import
# pylint: disable=line-too-long, too-many-lines

helps['cosmosdb'] = """
type: group
short-summary: Manage Azure Cosmos DB database accounts.
"""

helps['cosmosdb cassandra'] = """
type: group
short-summary: Manage Cassandra resources of Azure Cosmos DB account.
"""

helps['cosmosdb cassandra keyspace'] = """
type: group
short-summary: Manage Azure Cosmos DB Cassandra keyspaces.
"""

helps['cosmosdb cassandra keyspace create'] = """
type: command
short-summary: Create an Cassandra keyspace under an Azure Cosmos DB account.
"""

helps['cosmosdb cassandra keyspace delete'] = """
type: command
short-summary: Delete the Cassandra keyspace under an Azure Cosmos DB account.
examples:
  - name: Delete the Cassandra keyspace under an Azure Cosmos DB account. (autogenerated)
    text: az cosmosdb cassandra keyspace delete --account-name MyAccount --name MyKeyspace --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb cassandra keyspace list'] = """
type: command
short-summary: List the Cassandra keyspaces under an Azure Cosmos DB account.
examples:
  - name: List the Cassandra keyspaces under an Azure Cosmos DB account. (autogenerated)
    text: az cosmosdb cassandra keyspace list --account-name MyAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb cassandra keyspace show'] = """
type: command
short-summary: Show the details of a Cassandra keyspace under an Azure Cosmos DB account.
"""

helps['cosmosdb cassandra keyspace throughput'] = """
type: group
short-summary: Manage throughput of Cassandra keyspace under an Azure Cosmos DB account.
"""

helps['cosmosdb cassandra keyspace throughput show'] = """
type: command
short-summary: Get the throughput of the Cassandra keyspace under an Azure Cosmos DB account.
"""

helps['cosmosdb cassandra keyspace throughput update'] = """
type: command
short-summary: Update the throughput of the Cassandra keyspace under an Azure Cosmos DB account.
"""

helps['cosmosdb cassandra keyspace throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the Cassandra keyspace between autoscale and manually provisioned.
"""

helps['cosmosdb cassandra table'] = """
type: group
short-summary: Manage Azure Cosmos DB Cassandra tables.
"""

helps['cosmosdb cassandra table create'] = """
type: command
short-summary: Create an Cassandra table under an Azure Cosmos DB Cassandra keyspace.
examples:
  - name: Create an Azure Cosmos DB Cassandra table.
    text: az cosmosdb cassandra table create -g MyResourceGroup -a MyAccount -k MyKeyspace -n MyTable --schema @indexes-file.json --throughput "500" --ttl 1000
    crafted: true
"""

helps['cosmosdb cassandra table delete'] = """
type: command
short-summary: Delete the Cassandra table under an Azure Cosmos DB Cassandra keyspace.
"""

helps['cosmosdb cassandra table list'] = """
type: command
short-summary: List the Cassandra tables under an Azure Cosmos DB Cassandra keyspace.
examples:
  - name: List the Cassandra tables under an Azure Cosmos DB Cassandra keyspace. (autogenerated)
    text: az cosmosdb cassandra table list --account-name MyAccount --keyspace-name MyKeyspace --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb cassandra table show'] = """
type: command
short-summary: Show the details of a Cassandra table under an Azure Cosmos DB Cassandra keyspace.
"""

helps['cosmosdb cassandra table throughput'] = """
type: group
short-summary: Manage throughput of Cassandra table under an Azure Cosmos DB account.
"""

helps['cosmosdb cassandra table throughput show'] = """
type: command
short-summary: Get the throughput of the Cassandra table under an Azure Cosmos DB Cassandra keyspace.
examples:
  - name: Get the throughput of the Cassandra table under an Azure Cosmos DB Cassandra keyspace. (autogenerated)
    text: az cosmosdb cassandra table throughput show --account-name MyAccount --keyspace-name MyKeyspace --name MyTable --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb cassandra table throughput update'] = """
type: command
short-summary: Update the throughput of the Cassandra table under an Azure Cosmos DB Cassandra keyspace.
examples:
  - name: Update the throughput of the Cassandra table under an Azure Cosmos DB Cassandra keyspace. (autogenerated)
    text: |
        az cosmosdb cassandra table throughput update --account-name MyAccount --keyspace-name MyKeyspace --name MyTable --resource-group MyResourceGroup --throughput "500"
    crafted: true
"""

helps['cosmosdb cassandra table throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the Cassandra table between autoscale and manually provisioned.
"""

helps['cosmosdb cassandra table update'] = """
type: command
short-summary: Update an Cassandra table under an Azure Cosmos DB Cassandra keyspace.
examples:
  - name: Update an Cassandra table under an Azure Cosmos DB Cassandra keyspace. (autogenerated)
    text: az cosmosdb cassandra table update --account-name MyAccount --keyspace-name MyKeyspace --name MyTable --resource-group MyResourceGroup --schema @indexes-file.json
    crafted: true
"""

helps['cosmosdb check-name-exists'] = """
type: command
short-summary: Checks if an Azure Cosmos DB account name exists.
examples:
  - name: Checks if an Azure Cosmos DB account name exists. (autogenerated)
    text: az cosmosdb check-name-exists --name MyCosmosDBDatabaseAccount
    crafted: true
"""

helps['cosmosdb collection'] = """
type: group
short-summary: Manage Azure Cosmos DB collections.
"""

helps['cosmosdb create'] = """
type: command
short-summary: Creates a new Azure Cosmos DB database account.
parameters:
  - name: --locations
    short-summary: Add a location to the Cosmos DB database account
    long-summary: |
        Usage:          --locations KEY=VALUE [KEY=VALUE ...]
        Required Keys:  regionName, failoverPriority
        Optional Key:   isZoneRedundant
        Default:        single region account in the location of the specified resource group.
        Failover priority values are 0 for write regions and greater than 0 for read regions. A failover priority value must be unique and less than the total number of regions.
        Multiple locations can be specified by using more than one `--locations` argument.
  - name: --databases-to-restore
    short-summary: Add a database and its collection names to restore
    long-summary: |
        Usage:          --databases-to-restore name=DatabaseName collections=collection1 [collection2 ...]
  - name: --gremlin-databases-to-restore
    short-summary: Add a gremlin database and its graph names to restore
    long-summary: |
        Usage:          --gremlin-databases-to-restore name=DatabaseName graphs=graph1 [graph2 ...]
  - name: --tables-to-restore
    short-summary: Add table names to restore
    long-summary: |
        Usage:          --tables-to-restore tables=table1 [table2 ...]
  - name: --minimal-tls-version
    short-summary: Indicate the minimum allowed TLS version
    long-summary: |
      Usage:    --minimal-tls-version TLSVersion
      Default:  Tls, except for Cassandra and Mongo APIs, which only work with Tls12
      The accepted values for the minimal TLS version are 'Tls', 'Tls11', and 'Tls12', which correspond to the
      TLS versions 1.0, 1.1, and 1.2.
  - name: --enable-burst-capacity
    short-summary: Flag to Enable/Disable burst capacity feature
    long-summary: |
      Usage:    --enable-burst-capacity true
      Default:  false
      The accepted values for the enable-burst-capacity are true and false.
  - name: --enable-prpp-autoscale
    short-summary: Flag to Enable/Disable burst capacity feature
    long-summary: |
      Usage:    --enable-prpp-autoscale true
      Default:  false
      The accepted values for the --enable-prpp-autoscale are true and false.
examples:
  - name: Creates a new Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb create --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --subscription MySubscription
    crafted: true
  - name: Creates a new Azure Cosmos DB database account with two regions. UK South is zone redundant.
    text: az cosmosdb create -n myaccount -g mygroup --locations regionName=eastus failoverPriority=0 isZoneRedundant=False --locations regionName=uksouth failoverPriority=1 isZoneRedundant=True --enable-multiple-write-locations --network-acl-bypass AzureServices --network-acl-bypass-resource-ids /subscriptions/subId/resourceGroups/rgName/providers/Microsoft.Synapse/workspaces/wsName
  - name: Create a new Azure Cosmos DB database account by restoring from an existing account in the given location
    text: az cosmosdb create -n restoredaccount -g mygroup --is-restore-request true --restore-source /subscriptions/2296c272-5d55-40d9-bc05-4d56dc2d7588/providers/Microsoft.DocumentDB/locations/westus/restorableDatabaseAccounts/d056a4f8-044a-436f-80c8-cd3edbc94c68 --restore-timestamp 2020-07-13T16:03:41+0000 --locations regionName=westus failoverPriority=0 isZoneRedundant=False
"""

helps['cosmosdb restore'] = """
type: command
short-summary: Create a new Azure Cosmos DB database account by restoring from an existing database account.
parameters:
  - name: --databases-to-restore
    short-summary: Add a database and its collection names to restore
    long-summary: |
        Usage:          --databases-to-restore name=DatabaseName collections=collection1 [collection2 ...]
        Multiple databases can be specified by using more than one `--databases-to-restore` argument.
  - name: --gremlin-databases-to-restore
    short-summary: Add a gremlin database and its graph names to restore
    long-summary: |
        Usage:          --gremlin-databases-to-restore name=DatabaseName graphs=graph1 [graph2 ...]
  - name: --tables-to-restore
    short-summary: Add table names to restore
    long-summary: |
        Usage:          --tables-to-restore table1 [table2 ...]
examples:
  - name: Create a new Azure Cosmos DB database account by restoring from an existing database account.
    text: az cosmosdb restore --target-database-account-name MyRestoredCosmosDBDatabaseAccount --account-name MySourceAccount --restore-timestamp 2020-07-13T16:03:41+0000 -g MyResourceGroup --location westus
  - name: Create a new Azure Cosmos DB Sql or MongoDB database account by restoring only the selected databases and collections from an existing database account.
    text: az cosmosdb restore -g MyResourceGroup --target-database-account-name MyRestoredCosmosDBDatabaseAccount --account-name MySourceAccount --restore-timestamp 2020-07-13T16:03:41+0000 --location westus --databases-to-restore name=MyDB1 collections=collection1 collection2 --databases-to-restore name=MyDB2 collections=collection3 collection4
  - name: Create a new Azure Cosmos DB Gremlin database account by restoring only the selected databases or graphs from an existing database account.
    text: az cosmosdb restore -g MyResourceGroup --target-database-account-name MyRestoredCosmosDBDatabaseAccount --account-name MySourceAccount --restore-timestamp 2020-07-13T16:03:41+0000 --location westus --gremlin-databases-to-restore name=graphdb1 graphs=graph1 graph2
  - name: Create a new Azure Cosmos DB Table database account by restoring only the selected tables from an existing database account.
    text: az cosmosdb restore -g MyResourceGroup --target-database-account-name MyRestoredCosmosDBDatabaseAccount --account-name MySourceAccount --restore-timestamp 2020-07-13T16:03:41+0000 --location westus --tables-to-restore table1,table2
  - name: Create a new Azure Cosmos DB Table database account by restoring with Time-To-Live disabled.
    text: az cosmosdb restore -g MyResourceGroup --target-database-account-name MyRestoredCosmosDBDatabaseAccount --account-name MySourceAccount --restore-timestamp 2020-07-13T16:03:41+0000 --location westus --disable-ttl True
"""

helps['cosmosdb database'] = """
type: group
short-summary: Manage Azure Cosmos DB databases.
"""

helps['cosmosdb database create'] = """
type: command
short-summary: Creates an Azure Cosmos DB database
examples:
  - name: Creates an Azure Cosmos DB database.
    text: az cosmosdb database create --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --db-name MyDatabase
    crafted: true
"""

helps['cosmosdb database delete'] = """
type: command
short-summary: Deletes an Azure Cosmos DB database
examples:
  - name: Deletes an Azure Cosmos DB database (autogenerated)
    text: az cosmosdb database delete --db-name MyDatabase --name MyCosmosDBDatabaseAccount --resource-group-name MyResourceGroup
    crafted: true
"""

helps['cosmosdb database exists'] = """
type: command
short-summary: Returns a boolean indicating whether the database exists
examples:
  - name: Returns a boolean indicating whether the database exists (autogenerated)
    text: az cosmosdb database exists --db-name MyDatabase --name MyCosmosDBDatabaseAccount --resource-group-name MyResourceGroup
    crafted: true
"""

helps['cosmosdb database list'] = """
type: command
short-summary: Lists all Azure Cosmos DB databases
examples:
  - name: Lists all Azure Cosmos DB databases (autogenerated)
    text: az cosmosdb database list --name MyCosmosDBDatabaseAccount --resource-group-name MyResourceGroup
    crafted: true
"""

helps['cosmosdb database show'] = """
type: command
short-summary: Shows an Azure Cosmos DB database
examples:
  - name: Shows an Azure Cosmos DB database (autogenerated)
    text: az cosmosdb database show --db-name MyDatabase --name MyCosmosDBDatabaseAccount --resource-group-name MyResourceGroup
    crafted: true
"""

helps['cosmosdb delete'] = """
type: command
short-summary: Deletes an Azure Cosmos DB database account.
examples:
  - name: Deletes an Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb delete --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
    crafted: true
  - name: an Azure Cosmos DB database account without waiting for the long-running operation to finish.
    text: az cosmosdb delete --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --no-wait
"""

helps['cosmosdb failover-priority-change'] = """
type: command
short-summary: Changes the failover priority for the Azure Cosmos DB database account.
examples:
  - name: Changes the failover priority for the Azure Cosmos DB database account.
    text: az cosmosdb failover-priority-change --failover-policies southafricanorth=0 westus=8 northeurope=3 --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb offline-region'] = """
type: command
short-summary: Offline the specified region for the specified Azure Cosmos DB database account.
examples:
  - name: Offlines North Europe regional account for the Azure Cosmos DB database account MyCosmosDBDatabaseAccount.
    text: az cosmosdb offline-region --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --region NorthEurope
    crafted: true
"""

helps['cosmosdb gremlin'] = """
type: group
short-summary: Manage Gremlin resources of Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database'] = """
type: group
short-summary: Manage Azure Cosmos DB Gremlin databases.
"""

helps['cosmosdb gremlin database create'] = """
type: command
short-summary: Create an Gremlin database under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database delete'] = """
type: command
short-summary: Delete the Gremlin database under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database list'] = """
type: command
short-summary: List the Gremlin databases under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database show'] = """
type: command
short-summary: Show the details of a Gremlin database under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database throughput'] = """
type: group
short-summary: Manage throughput of Gremlin database under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database throughput show'] = """
type: command
short-summary: Get the throughput of the Gremlin database under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database throughput update'] = """
type: command
short-summary: Update the throughput of the Gremlin database under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin database throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the Gremlin database between autoscale and manually provisioned.
"""

helps['cosmosdb gremlin graph'] = """
type: group
short-summary: Manage Azure Cosmos DB Gremlin graphs.
"""

helps['cosmosdb gremlin graph create'] = """
type: command
short-summary: Create an Gremlin graph under an Azure Cosmos DB Gremlin database.
examples:
  - name: Create an Azure Cosmos DB Gremlin graph.
    text: az cosmosdb gremlin graph create -g MyResourceGroup -a MyAccount -d MyDatabase -n MyGraph --partition-key-path "/my/path" --idx @policy-file.json --ttl 1000 --throughput "700"
    crafted: true
"""

helps['cosmosdb gremlin graph delete'] = """
type: command
short-summary: Delete the Gremlin graph under an Azure Cosmos DB Gremlin database.
"""

helps['cosmosdb gremlin graph list'] = """
type: command
short-summary: List the Gremlin graphs under an Azure Cosmos DB Gremlin database.
"""

helps['cosmosdb gremlin graph show'] = """
type: command
short-summary: Show the details of a Gremlin graph under an Azure Cosmos DB Gremlin database.
"""

helps['cosmosdb gremlin graph throughput'] = """
type: group
short-summary: Manage throughput of Gremlin graph under an Azure Cosmos DB account.
"""

helps['cosmosdb gremlin graph throughput show'] = """
type: command
short-summary: Get the throughput of the Gremlin graph under an Azure Cosmos DB Gremlin database.
"""

helps['cosmosdb gremlin graph throughput update'] = """
type: command
short-summary: Update the throughput of the Gremlin graph under an Azure Cosmos DB Gremlin database.
"""

helps['cosmosdb gremlin graph throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the Gremlin Graph between autoscale and manually provisioned.
"""

helps['cosmosdb gremlin graph update'] = """
type: command
short-summary: Update an Gremlin graph under an Azure Cosmos DB Gremlin database.
"""

helps['cosmosdb identity'] = """
type: group
short-summary: Manage Azure Cosmos DB managed service identities.
"""

helps['cosmosdb identity show'] = """
type: command
short-summary: Show the identities for a Azure Cosmos DB database account.
examples:
  - name: Show the identities for a Azure Cosmos DB database account.
    text: az cosmosdb identity show --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
"""

helps['cosmosdb identity assign'] = """
type: command
short-summary: Assign SystemAssigned identity for a Azure Cosmos DB database account.
examples:
  - name: Assign SystemAssigned identity for a Azure Cosmos DB database account.
    text: az cosmosdb identity assign --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
  - name: Assign one UserAssigned identity for a Azure Cosmos DB database account.
    text: az cosmosdb identity assign --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --identities /subscriptions/********-0000-0000-0000-********/resourcegroups/MyRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/MyID
"""

helps['cosmosdb identity remove'] = """
type: command
short-summary: Remove SystemAssigned identity for a Azure Cosmos DB database account.
examples:
  - name: Remove SystemAssigned identity for a Azure Cosmos DB database account.
    text: az cosmosdb identity remove --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
  - name: Remove a UserAssigned identity for a Azure Cosmos DB database account.
    text: az cosmosdb identity remove --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --identities /subscriptions/********-0000-0000-0000-********/resourcegroups/MyRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/MyID

"""

helps['cosmosdb keys'] = """
type: group
short-summary: Manage Azure Cosmos DB keys.
"""

helps['cosmosdb keys list'] = """
type: command
short-summary: List the access keys or connection strings for a Azure Cosmos DB database account.
examples:
  - name: List the access keys or connection strings for a Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb keys list --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --subscription MySubscription --type keys
    crafted: true
"""

helps['cosmosdb keys regenerate'] = """
type: command
short-summary: Regenerate an access key for a Azure Cosmos DB database account.
examples:
  - name: Regenerate primaryReadonly access key for a Azure Cosmos DB database account.
    text: az cosmosdb keys regenerate --key-kind primaryReadonly --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --subscription MySubscription
"""

helps['cosmosdb list'] = """
type: command
short-summary: List Azure Cosmos DB database accounts.
"""

helps['cosmosdb list-connection-strings'] = """
type: command
short-summary: List the connection strings for a Azure Cosmos DB database account.
examples:
  - name: List the connection strings for a Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb list-connection-strings --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb list-keys'] = """
type: command
short-summary: List the access keys for a Azure Cosmos DB database account.
examples:
  - name: List the access keys for a Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb list-keys --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup --subscription MySubscription
    crafted: true
"""

helps['cosmosdb list-read-only-keys'] = """
type: command
short-summary: List the read-only access keys for a Azure Cosmos DB database account.
examples:
  - name: List the read-only access keys for a Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb list-read-only-keys --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb mongodb'] = """
type: group
short-summary: Manage MongoDB resources of Azure Cosmos DB account.
"""

helps['cosmosdb mongodb collection'] = """
type: group
short-summary: Manage Azure Cosmos DB MongoDB collections.
"""

helps['cosmosdb mongodb collection create'] = """
type: command
short-summary: Create an MongoDB collection under an Azure Cosmos DB MongoDB database.
examples:
  - name: Create an Azure Cosmos DB MongoDB collection.
    text: az cosmosdb mongodb collection create -g MyResourceGroup -a MyAccount -d MyDatabase -n MyCollection --shard "ShardingKey" --idx @indexes-file.json --throughput "500"
    crafted: true
"""

helps['cosmosdb mongodb collection delete'] = """
type: command
short-summary: Delete the MongoDB collection under an Azure Cosmos DB MongoDB database.
"""

helps['cosmosdb mongodb collection list'] = """
type: command
short-summary: List the MongoDB collections under an Azure Cosmos DB MongoDB database.
"""

helps['cosmosdb mongodb collection show'] = """
type: command
short-summary: Show the details of a MongoDB collection under an Azure Cosmos DB MongoDB database.
"""

helps['cosmosdb mongodb collection throughput'] = """
type: group
short-summary: Manage throughput of MongoDB collection under an Azure Cosmos DB account.
"""

helps['cosmosdb mongodb collection throughput show'] = """
type: command
short-summary: Get the throughput of the MongoDB collection under an Azure Cosmos DB MongoDB database.
"""

helps['cosmosdb mongodb collection throughput update'] = """
type: command
short-summary: Update the throughput of the MongoDB collection under an Azure Cosmos DB MongoDB database.
"""

helps['cosmosdb mongodb collection throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the MongoDB collection between autoscale and manually provisioned.
"""

helps['cosmosdb mongodb collection update'] = """
type: command
short-summary: Update an MongoDB collection under an Azure Cosmos DB MongoDB database.
"""

helps['cosmosdb mongodb database'] = """
type: group
short-summary: Manage Azure Cosmos DB MongoDB databases.
"""

helps['cosmosdb mongodb database create'] = """
type: command
short-summary: Create an MongoDB database under an Azure Cosmos DB account.
"""

helps['cosmosdb mongodb database delete'] = """
type: command
short-summary: Delete the MongoDB database under an Azure Cosmos DB account.
examples:
  - name: Delete the MongoDB database under an Azure Cosmos DB account. (autogenerated)
    text: az cosmosdb mongodb database delete --account-name MyAccount --name MyDatabase --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb mongodb database list'] = """
type: command
short-summary: List the MongoDB databases under an Azure Cosmos DB account.
examples:
  - name: List the MongoDB databases under an Azure Cosmos DB account. (autogenerated)
    text: az cosmosdb mongodb database list --account-name MyAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb mongodb database show'] = """
type: command
short-summary: Show the details of a MongoDB database under an Azure Cosmos DB account.
"""

helps['cosmosdb mongodb database throughput'] = """
type: group
short-summary: Manage throughput of MongoDB database under an Azure Cosmos DB account.
"""

helps['cosmosdb mongodb database throughput show'] = """
type: command
short-summary: Get the throughput of the MongoDB database under an Azure Cosmos DB account.
"""

helps['cosmosdb mongodb database throughput update'] = """
type: command
short-summary: Update the throughput of the MongoDB database under an Azure Cosmos DB account.
examples:
  - name: Update the throughput of the MongoDB database under an Azure Cosmos DB account. (autogenerated)
    text: |
        az cosmosdb mongodb database throughput update --account-name MyAccount --name MyDatabase --resource-group MyResourceGroup --throughput "500"
    crafted: true
"""

helps['cosmosdb mongodb database throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the MongoDB database between autoscale and manually provisioned.
"""

helps['cosmosdb network-rule'] = """
type: group
short-summary: Manage Azure Cosmos DB network rules.
"""

helps['cosmosdb private-endpoint-connection'] = """
type: group
short-summary: Manage Azure Cosmos DB private endpoint connections.
"""

helps['cosmosdb private-endpoint-connection approve'] = """
type: command
short-summary: Approve the specified private endpoint connection associated with Azure Cosmos DB.
examples:
  - name: Approve the specified private endpoint connection associated with Azure Cosmos DB.
    text: az cosmosdb private-endpoint-connection approve --account-name MyAccount --name MyPrivateEndpoint --resource-group MyResourceGroup --description "Approved"
"""

helps['cosmosdb private-endpoint-connection delete'] = """
type: command
short-summary: Delete the specified private endpoint connection associated with Azure Cosmos DB.
examples:
  - name: Delete the specified private endpoint connection associated with Azure Cosmos DB.
    text: az cosmosdb private-endpoint-connection delete --account-name MyAccount --name MyPrivateEndpoint --resource-group MyResourceGroup

"""

helps['cosmosdb private-endpoint-connection reject'] = """
type: command
short-summary: Reject the specified private endpoint connection associated with Azure Cosmos DB.
examples:
  - name: Reject the specified private endpoint connection associated with Azure Cosmos DB.
    text: az cosmosdb private-endpoint-connection reject --account-name MyAccount --name MyPrivateEndpoint --resource-group MyResourceGroup --description "Rejected"
"""

helps['cosmosdb private-endpoint-connection show'] = """
type: command
short-summary: Show details of a private endpoint connection associated with Azure Cosmos DB.
examples:
  - name: Show details of a private endpoint connection associated with Azure Cosmos DB.
    text: az cosmosdb private-endpoint-connection show --account-name MyAccount --name MyPrivateEndpoint --resource-group MyResourceGroup
"""

helps['cosmosdb private-link-resource'] = """
type: group
short-summary: Manage Azure Cosmos DB private link resources.
"""

helps['cosmosdb private-link-resource list'] = """
type: command
short-summary: List the private link resources supported for Azure Cosmos DB.
example:
  - name: List the private link resources supported for Azure Cosmos DB.
    text: cosmosdb private-link-resource list --account-name MyAccount --resource-group MyResourceGroup
"""

helps['cosmosdb regenerate-key'] = """
type: command
short-summary: Regenerate an access key for a Azure Cosmos DB database account.
examples:
  - name: Regenerate an access key for a Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb regenerate-key --key-kind primary --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb show'] = """
type: command
short-summary: Get the details of an Azure Cosmos DB database account.
examples:
  - name: Get the details of an Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb show --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb sql'] = """
type: group
short-summary: Manage SQL resources of Azure Cosmos DB account.
"""

helps['cosmosdb sql stored-procedure'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL stored procedures.
"""

helps['cosmosdb sql stored-procedure create'] = """
type: command
short-summary: Create an SQL stored procedure under an Azure Cosmos DB SQL container.
examples:
  - name: Create an Azure Cosmos DB SQL stored procedure.
    text: az cosmosdb sql stored-procedure create -g MyResourceGroup -a MyAccount -d MyDatabase -c MyContainer -n MyStoredProcedure -b StoredProcedureBody
    crafted: true
"""

helps['cosmosdb sql stored-procedure delete'] = """
type: command
short-summary: Delete the SQL stored procedure under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql stored-procedure list'] = """
type: command
short-summary: List the SQL stored procedures under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql stored-procedure show'] = """
type: command
short-summary: Show the details of a SQL stored procedure under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql'] = """
type: group
short-summary: Manage SQL resources of Azure Cosmos DB account.
"""

helps['cosmosdb sql trigger'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL triggers.
"""

helps['cosmosdb sql trigger create'] = """
type: command
short-summary: Create an SQL trigger under an Azure Cosmos DB SQL container.
examples:
  - name: Create an Azure Cosmos DB SQL trigger.
    text: az cosmosdb sql trigger create -g MyResourceGroup -a MyAccount -d MyDatabase -c MyContainer -n MyTrigger -b TriggerBody
    crafted: true
"""

helps['cosmosdb sql trigger delete'] = """
type: command
short-summary: Delete the SQL trigger under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql trigger list'] = """
type: command
short-summary: List the SQL triggers under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql trigger show'] = """
type: command
short-summary: Show the details of a SQL trigger under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql user-defined-function'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL user defined functions.
"""

helps['cosmosdb sql user-defined-function create'] = """
type: command
short-summary: Create an SQL user defined function under an Azure Cosmos DB SQL container.
examples:
  - name: Create an Azure Cosmos DB SQL user defined function.
    text: az cosmosdb sql user-defined-function create -g MyResourceGroup -a MyAccount -d MyDatabase -c MyContainer -n MyUserDefinedFunction -b UserDefinedFunctionBody
    crafted: true
"""

helps['cosmosdb sql user-defined-function delete'] = """
type: command
short-summary: Delete the SQL user defined function under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql user-defined-function list'] = """
type: command
short-summary: List the SQL user defined functions under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql user-defined-function show'] = """
type: command
short-summary: Show the details of a SQL user defined function under an Azure Cosmos DB SQL container.
"""

helps['cosmosdb sql container'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL containers.
"""

helps['cosmosdb sql container create'] = """
type: command
short-summary: Create an SQL container under an Azure Cosmos DB SQL database.
examples:
  - name: Create an Azure Cosmos DB SQL container.
    text: az cosmosdb sql container create -g MyResourceGroup -a MyAccount -d MyDatabase -n MyContainer --partition-key-path "/my/path" --idx @policy-file.json --ttl 1000 --throughput "700"
    crafted: true
"""

helps['cosmosdb sql container delete'] = """
type: command
short-summary: Delete the SQL container under an Azure Cosmos DB SQL database.
"""

helps['cosmosdb sql container list'] = """
type: command
short-summary: List the SQL containers under an Azure Cosmos DB SQL database.
"""

helps['cosmosdb sql container show'] = """
type: command
short-summary: Show the details of a SQL container under an Azure Cosmos DB SQL database.
"""

helps['cosmosdb sql container throughput'] = """
type: group
short-summary: Manage throughput of SQL container under an Azure Cosmos DB account.
"""

helps['cosmosdb sql container throughput show'] = """
type: command
short-summary: Get the throughput of the SQL container under an Azure Cosmos DB SQL database.
"""

helps['cosmosdb sql container throughput update'] = """
type: command
short-summary: Update the throughput of the SQL container under an Azure Cosmos DB SQL database.
"""

helps['cosmosdb sql container throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the SQL container between autoscale and manually provisioned.
"""

helps['cosmosdb sql container update'] = """
type: command
short-summary: Update an SQL container under an Azure Cosmos DB SQL database.
"""

helps['cosmosdb sql database'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL databases.
"""

helps['cosmosdb sql database create'] = """
type: command
short-summary: Create an SQL database under an Azure Cosmos DB account.
"""

helps['cosmosdb sql database delete'] = """
type: command
short-summary: Delete the SQL database under an Azure Cosmos DB account.
"""

helps['cosmosdb sql database list'] = """
type: command
short-summary: List the SQL databases under an Azure Cosmos DB account.
examples:
  - name: List the SQL databases under an Azure Cosmos DB account (autogenerated)
    text: az cosmosdb sql database list --account-name MyAccount --resource-group MyResourceGroup
    crafted: true
"""

helps['cosmosdb sql database show'] = """
type: command
short-summary: Show the details of a SQL database under an Azure Cosmos DB account.
"""

helps['cosmosdb sql database throughput'] = """
type: group
short-summary: Manage throughput of SQL database under an Azure Cosmos DB account.
"""

helps['cosmosdb sql database throughput show'] = """
type: command
short-summary: Get the throughput of the SQL database under an Azure Cosmos DB account.
"""

helps['cosmosdb sql database throughput update'] = """
type: command
short-summary: Update the throughput of the SQL database under an Azure Cosmos DB account.
"""

helps['cosmosdb sql database throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the SQL database between autoscale and manually provisioned.
"""

helps['cosmosdb table'] = """
type: group
short-summary: Manage Table resources of Azure Cosmos DB account.
"""

helps['cosmosdb table create'] = """
type: command
short-summary: Create an Table under an Azure Cosmos DB account.
"""

helps['cosmosdb table delete'] = """
type: command
short-summary: Delete the Table under an Azure Cosmos DB account.
"""

helps['cosmosdb table list'] = """
type: command
short-summary: List the Tables under an Azure Cosmos DB account.
"""

helps['cosmosdb table show'] = """
type: command
short-summary: Show the details of a Table under an Azure Cosmos DB account.
"""

helps['cosmosdb table throughput'] = """
type: group
short-summary: Manage throughput of Table under an Azure Cosmos DB account.
"""

helps['cosmosdb table throughput show'] = """
type: command
short-summary: Get the throughput of the Table under an Azure Cosmos DB account.
"""

helps['cosmosdb table throughput update'] = """
type: command
short-summary: Update the throughput of the Table under an Azure Cosmos DB account.
"""

helps['cosmosdb table throughput migrate'] = """
type: command
short-summary: Migrate the throughput of the Table between autoscale and manually provisioned.
"""

helps['cosmosdb update'] = """
type: command
short-summary: Update an Azure Cosmos DB database account.
parameters:
  - name: --locations
    short-summary: Add a location to the Cosmos DB database account
    long-summary: |
        Usage:          --locations KEY=VALUE [KEY=VALUE ...]
        Required Keys:  regionName, failoverPriority
        Optional Key:   isZoneRedundant
        Default:        single region account in the location of the specified resource group.
        Failover priority values are 0 for write regions and greater than 0 for read regions. A failover priority value must be unique and less than the total number of regions.
        Multiple locations can be specified by using more than one `--locations` argument.
  - name: --minimal-tls-version
    short-summary: Indicate the minimum allowed TLS version
    long-summary: |
      Usage:    --minimal-tls-version TLSVersion
      Default:  Tls, except for Cassandra and Mongo APIs, which only work with Tls12
      The accepted values for the minimal TLS version are 'Tls', 'Tls11', and 'Tls12', which correspond to the
      TLS versions 1.0, 1.1, and 1.2.
  - name: --enable-burst-capacity
    short-summary: Flag to Enable/Disable burst capacity feature
    long-summary: |
      Usage:    --enable-burst-capacity true
      Default:  false
      The accepted values for the enable-burst-capacity are true and false.
  - name: --enable-prpp-autoscale
    short-summary: Flag to Enable/Disable burst capacity feature
    long-summary: |
      Usage:    --enable-prpp-autoscale true
      Default:  false
      The accepted values for the --enable-prpp-autoscale are true and false.
examples:
  - name: Update an Azure Cosmos DB database account. (autogenerated)
    text: az cosmosdb update --capabilities EnableGremlin --name MyCosmosDBDatabaseAccount --resource-group MyResourceGroup
    crafted: true
  - name: Creates a new Azure Cosmos DB database account with two regions. UK South is zone redundant.
    text: az cosmosdb update -n myaccount -g mygroup --locations regionName=eastus failoverPriority=0 isZoneRedundant=False --locations regionName=uksouth failoverPriority=1 isZoneRedundant=True --enable-multiple-write-locations --network-acl-bypass AzureServices --network-acl-bypass-resource-ids /subscriptions/subId/resourceGroups/rgName/providers/Microsoft.Synapse/workspaces/wsName
"""

helps['cosmosdb mongodb role'] = """
type: group
short-summary: Manage Azure Cosmos DB Mongo role resources.
"""

helps['cosmosdb mongodb role definition'] = """
type: group
short-summary: Manage Azure Cosmos DB Mongo role definitions.
"""

helps['cosmosdb mongodb role definition create'] = """
type: command
short-summary: Create a Mongo DB role definition under an Azure Cosmos DB account.
examples:
  - name: Create a Mongo DB role definition under an Azure Cosmos DB account using a JSON string.
    text: |
      az cosmosdb mongodb role definition create --account-name MyAccount --resource-group MyResourceGroup --body '{
        "Id": "MyDB.My_Read_Only_Role",
        "RoleName": "My_Read_Only_Role",
        "Type": "CustomRole",
        "DatabaseName": "MyDB",
        "Privileges": [{
          "Resource": {
              "Db": "MyDB",
              "Collection": "MyCol"
            },
            "Actions": [
              "insert",
              "find"
            ]
        }],
        "Roles": [
          {
            "Role": "myInheritedRole",
            "Db": "MyTestDb"
          }
        ]
      }'
  - name: Create a Mongo DB role definition under an Azure Cosmos DB account using a JSON file.
    text: az cosmosdb mongodb role definition create --account-name MyAccount --resource-group MyResourceGroup --body @mongo-role-definition.json
"""

helps['cosmosdb mongodb role definition delete'] = """
type: command
short-summary: Delete a CosmosDb MongoDb role definition under an Azure Cosmos DB account.
examples:
  - name: Delete a Mongo role definition under an Azure Cosmos DB account.
    text: az cosmosdb mongodb role definition delete --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb mongodb role definition exists'] = """
type: command
short-summary: Check if an Azure Cosmos DB MongoDb role definition exists.
examples:
  - name: Check if an Azure Cosmos DB MongoDb role definition exists.
    text: az cosmosdb mongodb role definition exists --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb mongodb role definition list'] = """
type: command
short-summary: List all MongoDb role definitions under an Azure Cosmos DB account.
examples:
  - name: List all Mongodb role definitions under an Azure Cosmos DB account.
    text: az cosmosdb mongodb role definition list --account-name MyAccount --resource-group MyResourceGroup
"""

helps['cosmosdb mongodb role definition show'] = """
type: command
short-summary: Show the properties of a MongoDb role definition under an Azure Cosmos DB account.
examples:
  - name: Show the properties of a MongoDb role definition under an Azure Cosmos DB account.
    text: az cosmosdb mongodb role definition show --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb mongodb role definition update'] = """
type: command
short-summary: Update a MongoDb role definition under an Azure Cosmos DB account.
examples:
  - name: Update a MongoDb role definition under an Azure Cosmos DB account.
    text: az cosmosdb mongodb role definition update --account-name MyAccount --resource-group MyResourceGroup --body @mongo-role-definition.json
"""

helps['cosmosdb mongodb user'] = """
type: group
short-summary: Manage Azure Cosmos DB Mongo user resources.
"""

helps['cosmosdb mongodb user definition'] = """
type: group
short-summary: Manage Azure Cosmos DB Mongo user definitions.
"""

helps['cosmosdb mongodb user definition create'] = """
type: command
short-summary: Create a Mongo DB user definition under an Azure Cosmos DB account.
examples:
  - name: Create a Mongo DB user definition under an Azure Cosmos DB account using a JSON string.
    text: |
      az cosmosdb mongodb user definition create --account-name MyAccount --resource-group MyResourceGroup --body '{
        "Id": "MyDB.MyUName",
        "UserName": "MyUName",
        "Password": "MyPass",
        "DatabaseName": "MyDB",
        "CustomData": "TestCustomData",
        "Mechanisms": "SCRAM-SHA-256",
        "Roles": [
          {
            "Role": "myReadRole",
            "Db": "MyDB"
          }
        ]
      }'
  - name: Create a Mongo DB user definition under an Azure Cosmos DB account using a JSON file.
    text: az cosmosdb mongodb user definition create --account-name MyAccount --resource-group MyResourceGroup --body @mongo-user-definition.json
"""

helps['cosmosdb mongodb user definition delete'] = """
type: command
short-summary: Delete a CosmosDb MongoDb user definition under an Azure Cosmos DB account.
examples:
  - name: Delete a Mongo user definition under an Azure Cosmos DB account.
    text: az cosmosdb mongodb user definition delete --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb mongodb user definition exists'] = """
type: command
short-summary: Check if an Azure Cosmos DB MongoDb user definition exists.
examples:
  - name: Check if an Azure Cosmos DB MongoDb user definition exists.
    text: az cosmosdb mongodb user definition exists --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb mongodb user definition list'] = """
type: command
short-summary: List all MongoDb user definitions under an Azure Cosmos DB account.
examples:
  - name: List all Mongodb user definitions under an Azure Cosmos DB account.
    text: az cosmosdb mongodb user definition list --account-name MyAccount --resource-group MyResourceGroup
"""

helps['cosmosdb mongodb user definition show'] = """
type: command
short-summary: Show the properties of a MongoDb user definition under an Azure Cosmos DB account.
examples:
  - name: Show the properties of a MongoDb user definition under an Azure Cosmos DB account.
    text: az cosmosdb mongodb user definition show --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb mongodb user definition update'] = """
type: command
short-summary: Update a MongoDb user definition under an Azure Cosmos DB account.
examples:
  - name: Update a MongoDb user definition under an Azure Cosmos DB account.
    text: az cosmosdb mongodb user definition update --account-name MyAccount --resource-group MyResourceGroup --body @mongo-user-definition.json
"""

helps['cosmosdb sql role'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL role resources.
"""

helps['cosmosdb sql role definition'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL role definitions.
"""

helps['cosmosdb sql role definition create'] = """
type: command
short-summary: Create a SQL role definition under an Azure Cosmos DB account.
examples:
  - name: Create a SQL role definition under an Azure Cosmos DB account using a JSON string.
    text: |
      az cosmosdb sql role definition create --account-name MyAccount --resource-group MyResourceGroup --body '{
        "Id": "be79875a-2cc4-40d5-8958-566017875b39",
        "RoleName": "My Read Only Role",
        "Type": "CustomRole",
        "AssignableScopes": ["/dbs/mydb/colls/mycontainer"],
        "Permissions": [{
          "DataActions": [
            "Microsoft.DocumentDB/databaseAccounts/readMetadata",
            "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/items/read",
            "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/executeQuery",
            "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/readChangeFeed"
          ]
        }]
      }'
  - name: Create a SQL role definition under an Azure Cosmos DB account using a JSON file.
    text: az cosmosdb sql role definition create --account-name MyAccount --resource-group MyResourceGroup --body @role-definition.json
"""

helps['cosmosdb sql role definition delete'] = """
type: command
short-summary: Delete a SQL role definition under an Azure Cosmos DB account.
examples:
  - name: Delete a SQL role definition under an Azure Cosmos DB account.
    text: az cosmosdb sql role definition delete --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb sql role definition exists'] = """
type: command
short-summary: Check if an Azure Cosmos DB role definition exists.
examples:
  - name: Check if an Azure Cosmos DB role definition exists.
    text: az cosmosdb sql role definition exists --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb sql role definition list'] = """
type: command
short-summary: List all SQL role definitions under an Azure Cosmos DB account.
examples:
  - name: List all SQL role definitions under an Azure Cosmos DB account.
    text: az cosmosdb sql role definition list --account-name MyAccount --resource-group MyResourceGroup
"""

helps['cosmosdb sql role definition show'] = """
type: command
short-summary: Show the properties of a SQL role definition under an Azure Cosmos DB account.
examples:
  - name: Show the properties of a SQL role definition under an Azure Cosmos DB account.
    text: az cosmosdb sql role definition show --account-name MyAccount --resource-group MyResourceGroup --id be79875a-2cc4-40d5-8958-566017875b39
"""

helps['cosmosdb sql role definition update'] = """
type: command
short-summary: Update a SQL role definition under an Azure Cosmos DB account.
examples:
  - name: Update a SQL role definition under an Azure Cosmos DB account.
    text: az cosmosdb sql role definition update --account-name MyAccount --resource-group MyResourceGroup --body @role-definition.json
"""

helps['cosmosdb sql role definition wait'] = """
type: command
short-summary: Poll on a SQL role definition until a specific condition is met.
examples:
  - name: Poll on a SQL role definition until it is deleted.
    text: az cosmosdb sql role definition wait --account-name MyAccount --resource-group MyResourceGroup --id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8 --deleted
"""

helps['cosmosdb sql role assignment'] = """
type: group
short-summary: Manage Azure Cosmos DB SQL role assignments.
"""

helps['cosmosdb sql role assignment create'] = """
type: command
short-summary: Create a SQL role assignment under an Azure Cosmos DB account.
examples:
  - name: Create a SQL role assignment under an Azure Cosmos DB account using Role Definition Name.
    text: |
      az cosmosdb sql role assignment create --account-name MyAccount --resource-group MyResourceGroup \\
        --role-assignment-id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8 \\
        --role-definition-name "My Read Only Role" \\
        --scope "/dbs/mydb/colls/mycontainer" \\
        --principal-id 6328f5f7-dbf7-4244-bba8-fbb9d8066506
  - name: Create a SQL role assignment under an Azure Cosmos DB account using Role Definition ID.
    text: |
      az cosmosdb sql role assignment create --account-name MyAccount --resource-group MyResourceGroup \\
        --role-assignment-id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8 \\
        --role-definition-id be79875a-2cc4-40d5-8958-566017875b39 \\
        --scope "/dbs/mydb/colls/mycontainer" \\
        --principal-id 6328f5f7-dbf7-4244-bba8-fbb9d8066506
"""

helps['cosmosdb sql role assignment delete'] = """
type: command
short-summary: Delete a SQL role assignment under an Azure Cosmos DB account.
examples:
  - name: Delete a SQL role assignment under an Azure Cosmos DB account.
    text: az cosmosdb sql role assignment delete --account-name MyAccount --resource-group MyResourceGroup --role-assignment-id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8
"""

helps['cosmosdb sql role assignment exists'] = """
type: command
short-summary: Check if an Azure Cosmos DB role assignment exists.
examples:
  - name: Check if an Azure Cosmos DB role assignment exists.
    text: az cosmosdb sql role assignment exists --account-name MyAccount --resource-group MyResourceGroup --role-assignment-id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8
"""

helps['cosmosdb sql role assignment list'] = """
type: command
short-summary: List all SQL role assignments under an Azure Cosmos DB account.
examples:
  - name: List all SQL role assignments under an Azure Cosmos DB account.
    text: az cosmosdb sql role assignment list --account-name MyAccount --resource-group MyResourceGroup
"""

helps['cosmosdb sql role assignment show'] = """
type: command
short-summary: Show the properties of a SQL role assignment under an Azure Cosmos DB account.
examples:
  - name: Show the properties of a SQL role assignment under an Azure Cosmos DB account.
    text: az cosmosdb sql role assignment show --account-name MyAccount --resource-group MyResourceGroup --role-assignment-id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8
"""

helps['cosmosdb sql role assignment update'] = """
type: command
short-summary: Update a SQL role assignment under an Azure Cosmos DB account.
examples:
  - name: Update a SQL role assignment under an Azure Cosmos DB account.
    text: |
      az cosmosdb sql role assignment update --account-name MyAccount --resource-group MyResourceGroup \\
        --role-assignment-id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8 \\
        --role-definition-id updated-role-definition-id
"""

helps['cosmosdb sql role assignment wait'] = """
type: command
short-summary: Poll on a SQL role assignment until a specific condition is met.
examples:
  - name: Poll on a SQL role assignment until it is deleted.
    text: az cosmosdb sql role assignment wait --account-name MyAccount --resource-group MyResourceGroup --role-assignment-id cb8ed2d7-2371-4e3c-bd31-6cc1560e84f8 --deleted
"""

helps['cosmosdb restorable-database-account'] = """
type: group
short-summary: Manage restorable Azure Cosmos DB accounts.
"""

helps['cosmosdb restorable-database-account list'] = """
type: command
short-summary: List all the database accounts that can be restored.
"""

helps['cosmosdb restorable-database-account show'] = """
type: command
short-summary: Show the details of a database account that can be restored.
"""

helps['cosmosdb sql restorable-database'] = """
type: group
short-summary: Manage different versions of sql databases that are restorable in a Azure Cosmos DB account.
"""

helps['cosmosdb sql restorable-database list'] = """
type: command
short-summary: List all the versions of all the sql databases that were created / modified / deleted in the given restorable account.
"""

helps['cosmosdb sql restorable-container'] = """
type: group
short-summary: Manage different versions of sql containers that are restorable in a database of a Azure Cosmos DB account.
"""

helps['cosmosdb sql restorable-container list'] = """
type: command
short-summary: List all the versions of all the sql containers that were created / modified / deleted in the given database and restorable account.
"""

helps['cosmosdb sql restorable-resource'] = """
type: group
short-summary: Manage the databases and its containers that can be restored in the given account at the given timesamp and region.
"""

helps['cosmosdb sql restorable-resource list'] = """
type: command
short-summary: List all the databases and its containers that can be restored in the given account at the given timesamp and region.
"""

helps['cosmosdb mongodb restorable-database'] = """
type: group
short-summary: Manage different versions of mongodb databases that are restorable in a Azure Cosmos DB account.
"""

helps['cosmosdb mongodb restorable-database list'] = """
type: command
short-summary: List all the versions of all the mongodb databases that were created / modified / deleted in the given restorable account.
"""

helps['cosmosdb mongodb restorable-collection'] = """
type: group
short-summary: Manage different versions of mongodb collections that are restorable in a database of a Azure Cosmos DB account.
"""

helps['cosmosdb mongodb restorable-collection list'] = """
type: command
short-summary: List all the versions of all the mongodb collections that were created / modified / deleted in the given database and restorable account.
"""

helps['cosmosdb mongodb restorable-resource'] = """
type: group
short-summary: Manage the databases and its collections that can be restored in the given account at the given timesamp and region.
"""

helps['cosmosdb mongodb restorable-resource list'] = """
type: command
short-summary: List all the databases and its collections that can be restored in the given account at the given timesamp and region.
"""

helps['cosmosdb sql retrieve-latest-backup-time'] = """
type: command
short-summary: Retrieves latest restorable timestamp for the given sql container in given region.
"""

# gremlin restorable resources
helps['cosmosdb gremlin restorable-database'] = """
type: group
short-summary: Manage different versions of gremlin databases that are restorable in a Azure Cosmos DB account.
"""

helps['cosmosdb gremlin restorable-database list'] = """
type: command
short-summary: List all the versions of all the gremlin databases that were created / modified / deleted in the given restorable account.
"""

helps['cosmosdb gremlin restorable-graph'] = """
type: group
short-summary: Manage different versions of gremlin graphs that are restorable in a database of a Azure Cosmos DB account.
"""

helps['cosmosdb gremlin restorable-graph list'] = """
type: command
short-summary: List all the versions of all the gremlin graphs that were created / modified / deleted in the given database and restorable account.
"""

helps['cosmosdb gremlin restorable-resource'] = """
type: group
short-summary: Manage the databases and its graphs that can be restored in the given account at the given timestamp and region.
"""

helps['cosmosdb gremlin restorable-resource list'] = """
type: command
short-summary: List all the databases and its graphs that can be restored in the given account at the given timestamp and region.
"""

# table restorable resources
helps['cosmosdb table restorable-table'] = """
type: group
short-summary: Manage different versions of tables that are restorable in Azure Cosmos DB account.
"""

helps['cosmosdb table restorable-table list'] = """
type: command
short-summary: List all the versions of all the tables that were created / modified / deleted in the given restorable account.
"""

helps['cosmosdb table restorable-resource'] = """
type: group
short-summary: Manage the tables that can be restored in the given account at the given timestamp and region.
"""

helps['cosmosdb table restorable-resource list'] = """
type: command
short-summary: List all the tables that can be restored in the given account at the given timestamp and region.
"""

# gremlin graph latest backup time.
helps['cosmosdb gremlin retrieve-latest-backup-time'] = """
type: command
short-summary: Retrieves latest restorable timestamp for the given gremlin graph in given region.
"""

# table latest backup time.
helps['cosmosdb table retrieve-latest-backup-time'] = """
type: command
short-summary: Retrieves latest restorable timestamp for the given table in given region.
"""

helps['cosmosdb mongodb retrieve-latest-backup-time'] = """
type: command
short-summary: Retrieves latest restorable timestamp for the given mongodb collection in given region.
"""

helps['cosmosdb locations'] = """
type: group
short-summary: Manage Azure Cosmos DB location properties.
"""

helps['cosmosdb locations list'] = """
type: command
short-summary: Retrieves the list of cosmosdb locations and their properties.
"""

helps['cosmosdb locations show'] = """
type: command
short-summary: Show the Azure Cosmos DB location properties in the given location.
examples:
  - name: Shows the Azure Cosmos DB location properties in the given location.
    text: az cosmosdb locations show --location 'East US'
"""

helps['managed-cassandra'] = """
type: group
short-summary: Azure Managed Cassandra.
"""

helps['managed-cassandra cluster'] = """
type: group
short-summary: Azure Managed Cassandra Cluster.
"""

helps['managed-cassandra cluster create'] = """
type: command
short-summary: Create a Managed Cassandra Cluster.
examples:
  - name: Create a Managed Cassandra Cluster in a given Subscription and ResourceGroup. Either a cassandra admin password or external seed needs are required.
    text: |
      az managed-cassandra cluster create \\
      --resource-group MyResourceGroup \\
      --cluster-name MyCluster \\
      --location MyLocation \\
      --initial-cassandra-admin-password password \\
      --delegated-management-subnet-id /subscriptions/94d9b402-77b4-4049-b4c1-947bc6b7729b/resourceGroups/My-vnet/providers/Microsoft.Network/virtualNetworks/test-vnet/subnets/test-subnet
"""

helps['managed-cassandra cluster update'] = """
type: command
short-summary: Update a Managed Cassandra Cluster.
examples:
  - name: Update External Seed Nodes of a given cluster.
    text: |
      az managed-cassandra cluster update --resource-group MyResourceGroup --cluster-name MyCluster --external-seed-nodes 127.0.0.1 *********
  - name: Update External Gossip Certificates of a given cluster. Certs can be passed in as strings or the file locations.
    text: |
      az managed-cassandra cluster update --resource-group MyResourceGroup --cluster-name MyCluster --external-gossip-certificates C:/MyFolder/test.pem BeginCert-MLXCF-EndCert
"""

helps['managed-cassandra cluster delete'] = """
type: command
short-summary: Deletes a Managed Cassandra Cluster.
examples:
  - name: Deletes a Managed Cassandra Cluster in the given Subscription and ResourceGroup.
    text: |
      az managed-cassandra cluster delete --resource-group MyResourceGroup --cluster-name MyCluster
"""

helps['managed-cassandra cluster show'] = """
type: command
short-summary: Get a Managed Cassandra Cluster Resource.
examples:
  - name: Gets a Managed Cassandra Cluster Resource. ProvisioningState tells the state of this cluster. If the cluster doesnot exist a NotFound response is returned.
    text: |
      az managed-cassandra cluster show --resource-group MyResourceGroup --cluster-name MyCluster
"""

helps['managed-cassandra cluster list'] = """
type: command
short-summary: List the Managed Cassandra Clusters in a ResourceGroup and Subscription. If the ResourceGroup is not specified all the clusters in this Subscription are returned.
examples:
  - name: List all Managed Cassandra Clusters in a given Subscription and ResourceGroup.
    text: |
      az managed-cassandra cluster list --resource-group MyResourceGroup
  - name: List all Managed Cassandra Clusters in a given Subscription.
    text: |
      az managed-cassandra cluster list
"""

helps['managed-cassandra cluster invoke-command'] = """
type: command
short-summary: Invoke a command like nodetool for cassandra maintenance.
examples:
  - name: This command runs nodetool with these arguments in a host node of the cluster.
    text: |
      az managed-cassandra cluster invoke-command --resource-group MyResourceGroup --cluster-name MyCluster --host "*********" --command-name "nodetool" --arguments arg1="value1" arg2="value2" arg3="value3"
"""

helps['managed-cassandra cluster deallocate'] = """
type: command
short-summary: Deallocate the Managed Cassandra Cluster and Associated Data Centers. Deallocation will deallocate the host virtual machine of this cluster, and reserved the data disk. This won't do anything on an already deallocated cluster. Use Start to restart the cluster.
examples:
  - name: This command deallocates this cluster.
    text: |
      az managed-cassandra cluster deallocate --resource-group MyResourceGroup --cluster-name MyCluster
"""

helps['managed-cassandra cluster start'] = """
type: command
short-summary: Start the Managed Cassandra Cluster and Associated Data Centers. Start will start the host virtual machine of this cluster with reserved data disk. This won't do anything on an already running cluster. Use Deallocate to deallocate the cluster.
examples:
  - name: This command starts this cluster.
    text: |
      az managed-cassandra cluster start --resource-group MyResourceGroup --cluster-name MyCluster
"""

helps['managed-cassandra cluster status'] = """
type: command
short-summary: Gets the CPU, memory, and disk usage statistics for each Cassandra node in a cluster.
examples:
  - name: Gets the CPU, memory, and disk usage statistics for each Cassandra node in a cluster.
    text: |
      az managed-cassandra cluster status --resource-group MyResourceGroup --cluster-name MyCluster
"""

helps['managed-cassandra datacenter'] = """
type: group
short-summary: Azure Managed Cassandra DataCenter.
"""

helps['managed-cassandra datacenter create'] = """
type: command
short-summary: Create a Datacenter in an Azure Managed Cassandra Cluster.
examples:
  - name: Create a Managed Cassandra Datacenter in a Cassandra Cluster. Each datacenter should atleast have 3 nodes.
    text: |
      az managed-cassandra datacenter create \\
      --resource-group MyResourceGroup \\
      --cluster-name MyCluster \\
      --data-center-name MyDataCenter \\
      --data-center-location westus2 \\
      --node-count 3 \\
      --delegated-subnet-id /subscriptions/94d9b402-77b4-4049-b4c1-947bc6b7729b/resourceGroups/My-vnet/providers/Microsoft.Network/virtualNetworks/test-vnet/subnets/test-subnet
"""

helps['managed-cassandra datacenter update'] = """
type: command
short-summary: Update a Datacenter in an Azure Managed Cassandra Cluster.
examples:
  - name: Scale the number of nodes in a datacenter. This is a scale up operation assuming that the create datacenter was done with 3 nodes. Each datacenter should atleast have 3 nodes.
    text: |
      az managed-cassandra datacenter update --resource-group MyResourceGroup --cluster-name MyCluster --data-center-name MyDataCenter --node-count 6
  - name: Scale the number of nodes in a datacenter. This is a scale down operation assuming that the create datacenter was done with 3 nodes, followed by a scale up to 6 nodes. Each datacenter should atleast have 3 nodes.
    text: |
      az managed-cassandra datacenter update --resource-group MyResourceGroup --cluster-name MyCluster --data-center-name MyDataCenter --node-count 4
"""

helps['managed-cassandra datacenter delete'] = """
type: command
short-summary: Deletes a Managed Cassandra Datacenter.
examples:
  - name: Deletes a Managed Cassandra Datacenter in the given Cluster.
    text: |
      az managed-cassandra datacenter delete --resource-group MyResourceGroup --cluster-name MyCluster --data-center-name MyDataCenter
  - name: Deletes a Managed Cassandra Datacenter in the given Cluster without waiting for the long-running operation to finish.
    text: |
      az managed-cassandra datacenter delete --resource-group MyResourceGroup --cluster-name MyCluster --data-center-name MyDataCenter --no-wait
"""

helps['managed-cassandra datacenter show'] = """
type: command
short-summary: Get a Managed Cassandra DataCenter Resource.
examples:
  - name: Gets a Managed Cassandra Datacenter Resource. ProvisioningState tells the state of this datacenter. If the datacenter does not exist a NotFound response is returned.
    text: |
      az managed-cassandra datacenter show --resource-group MyResourceGroup --cluster-name MyCluster --data-center-name MyDataCenter
"""

helps['managed-cassandra datacenter list'] = """
type: command
short-summary: List the Managed Cassandra Datacenters in a given Cluster.
examples:
  - name: List all Managed Cassandra DataCenters in a given Cluster.
    text: |
      az managed-cassandra datacenter list --resource-group MyResourceGroup --cluster-name MyCluster
"""

helps['cosmosdb service'] = """
type: group
short-summary: Commands to perform operations on Service.
"""

helps['cosmosdb service create'] = """
type: command
short-summary: Create a cosmosdb service resource.
examples:
  - name: Create a cosmosdb service resource.
    text: |
      az cosmosdb service create --resource-group MyResourceGroup --account-name MyAccount --name "sqlDedicatedGateway" --count 3 --size "Cosmos.D4s"
"""

helps['cosmosdb service update'] = """
type: command
short-summary: Update a cosmosdb service resource.
examples:
  - name: Update a cosmosdb service resource.
    text: |
      az cosmosdb service update --resource-group MyResourceGroup --account-name MyAccount --name "sqlDedicatedGateway" --count 3
"""

helps['cosmosdb service show'] = """
type: command
short-summary: Get cosmosdb service resource under an account.
examples:
  - name: Get cosmosdb service resource under an account.
    text: |
      az cosmosdb service show --resource-group MyResourceGroup --account-name MyAccount --name "sqlDedicatedGateway"
"""

helps['cosmosdb service list'] = """
type: command
short-summary: List all cosmosdb service resource under an account.
examples:
  - name: List all cosmosdb service resource under an account.
    text: |
      az cosmosdb service list --resource-group MyResourceGroup --account-name MyAccount
"""

helps['cosmosdb service delete'] = """
type: command
short-summary: Delete the given cosmosdb service resource.
examples:
  - name: Delete the given cosmosdb service resource.
    text: |
      az cosmosdb service delete --resource-group MyResourceGroup --account-name MyAccount --name "sqlDedicatedGateway"
"""

helps['cosmosdb sql database restore'] = """
type: command
short-summary: Restore a deleted sql database within the same account.
parameters:
  - name: --disable-ttl
    short-summary: Flag to restore with TTL disabled
    long-summary: |
        Usage:          --disable-ttl True
        Default: false
examples:
  - name: Restore a deleted sql database within the same account.
    text: |
      az cosmosdb sql database restore --resource-group resource_group --account-name database_account_name --name name_of_database_needs_to_be_restored --restore-timestamp 2020-07-13T16:03:41+0000
"""

helps['cosmosdb sql container restore'] = """
type: command
short-summary: Restore a deleted sql container within the same account.
parameters:
  - name: --disable-ttl
    short-summary: Flag to restore with TTL disabled
    long-summary: |
        Usage:          --disable-ttl True
        Default: false
examples:
  - name: Restore a deleted sql container within the same account.
    text: |
      az cosmosdb sql container restore --resource-group resource_group --account-name database_account_name --database-name parent_database_name --name name_of_container_needs_to_be_restored --restore-timestamp 2020-07-13T16:03:41+0000
"""

helps['cosmosdb mongodb database restore'] = """
type: command
short-summary: Restore a deleted mongodb database within the same account.
parameters:
  - name: --disable-ttl
    short-summary: Flag to restore with TTL disabled
    long-summary: |
        Usage:          --disable-ttl True
        Default: false
examples:
  - name: Restore a deleted mongodb database within the same account.
    text: |
      az cosmosdb mongodb database restore --resource-group resource_group --account-name database_account_name --name name_of_database_needs_to_be_restored --restore-timestamp 2020-07-13T16:03:41+0000
"""

helps['cosmosdb mongodb collection restore'] = """
type: command
short-summary: Restore a deleted mongodb collection within the same account.
parameters:
  - name: --disable-ttl
    short-summary: Flag to restore with TTL disabled
    long-summary: |
        Usage:          --disable-ttl True
        Default: false
examples:
  - name: Restore a deleted mongodb collection within the same account.
    text: |
      az cosmosdb mongodb collection restore --resource-group resource_group --account-name database_account_name --database-name parent_database_name --name name_of_collection_needs_to_be_restored --restore-timestamp 2020-07-13T16:03:41+0000
"""

helps['cosmosdb gremlin database restore'] = """
type: command
short-summary: Restore a deleted gremlin database within the same account.
parameters:
  - name: --disable-ttl
    short-summary: Flag to restore with TTL disabled
    long-summary: |
        Usage:          --disable-ttl True
        Default: false
examples:
  - name: Restore a deleted gremlin database within the same account.
    text: |
      az cosmosdb gremlin database restore --resource-group resource_group --account-name database_account_name --name name_of_database_needs_to_be_restored --restore-timestamp 2020-07-13T16:03:41+0000
"""

helps['cosmosdb gremlin graph restore'] = """
type: command
short-summary: Restore a deleted gremlin graph within the same account.
parameters:
  - name: --disable-ttl
    short-summary: Flag to restore with TTL disabled
    long-summary: |
        Usage:          --disable-ttl True
        Default: false
examples:
  - name: Restore a deleted gremlin graph within the same account.
    text: |
      az cosmosdb gremlin graph restore --resource-group resource_group --account-name database_account_name --database-name parent_database_name --name name_of_graph_needs_to_be_restored --restore-timestamp 2020-07-13T16:03:41+0000
"""

helps['cosmosdb table restore'] = """
type: command
short-summary: Restore a deleted table within the same account.
parameters:
  - name: --disable-ttl
    short-summary: Flag to restore with TTL disabled
    long-summary: |
        Usage:          --disable-ttl True
        Default: false
examples:
  - name: Restore a deleted table within the same account.
    text: |
      az cosmosdb table restore --resource-group resource_group --account-name database_account_name --table-name name_of_table_needs_to_be_restored --restore-timestamp 2020-07-13T16:03:41+0000
"""
