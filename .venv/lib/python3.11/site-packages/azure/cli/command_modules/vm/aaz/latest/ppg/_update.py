# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "ppg update",
)
class Update(AAZCommand):
    """Update a proximity placement group.

    :example: Update a proximity placement group with specifying VM sizes that can be created.
        az ppg update --name MyProximityPlacementGroup --resource-group MyResourceGroup --intent-vm-sizes Standard_E64s_v4 Standard_M416ms_v2
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/proximityplacementgroups/{}", "2024-07-01"],
        ]
    }

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.proximity_placement_group_name = AAZStrArg(
            options=["-n", "--name", "--proximity-placement-group-name"],
            help="The name of the proximity placement group.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.colocation_status = AAZObjectArg(
            options=["--colocation-status"],
            help="Describes colocation status of the Proximity Placement Group.",
            nullable=True,
        )
        _args_schema.intent_vm_sizes = AAZListArg(
            options=["--intent-vm-sizes"],
            help="Specifies possible sizes of virtual machines that can be created in the proximity placement group.",
            nullable=True,
        )
        _args_schema.ppg_type = AAZStrArg(
            options=["-t", "--type", "--ppg-type"],
            help="The type of the proximity placement group. Allowed values: Standard.",
            nullable=True,
            enum={"Standard": "Standard", "Ultra": "Ultra"},
        )

        colocation_status = cls._args_schema.colocation_status
        colocation_status.code = AAZStrArg(
            options=["code"],
            help="The status code.",
            nullable=True,
        )
        colocation_status.display_status = AAZStrArg(
            options=["display-status"],
            help="The short localizable label for the status.",
            nullable=True,
        )
        colocation_status.level = AAZStrArg(
            options=["level"],
            help="The level code.",
            nullable=True,
            enum={"Error": "Error", "Info": "Info", "Warning": "Warning"},
        )
        colocation_status.message = AAZStrArg(
            options=["message"],
            help="The detailed status message, including for alerts and error messages.",
            nullable=True,
        )
        colocation_status.time = AAZDateTimeArg(
            options=["time"],
            help="The time of the status.",
            nullable=True,
        )

        intent_vm_sizes = cls._args_schema.intent_vm_sizes
        intent_vm_sizes.Element = AAZStrArg(
            nullable=True,
        )

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Parameters",
            help="Resource location",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Parameters",
            help="Space-separated tags: key[=value] [key[=value] ...].",
            nullable=True,
        )
        _args_schema.zone = AAZListArg(
            options=["-z", "--zone"],
            arg_group="Parameters",
            help="Specifies the Availability Zone where virtual machine, virtual machine scale set or availability set associated with the  proximity placement group can be created.",
            nullable=True,
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg(
            nullable=True,
        )

        zone = cls._args_schema.zone
        zone.Element = AAZStrArg(
            nullable=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.ProximityPlacementGroupsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        self.ProximityPlacementGroupsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class ProximityPlacementGroupsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/proximityPlacementGroups/{proximityPlacementGroupName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "proximityPlacementGroupName", self.ctx.args.proximity_placement_group_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_proximity_placement_group_read(cls._schema_on_200)

            return cls._schema_on_200

    class ProximityPlacementGroupsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200, 201]:
                return self.on_200_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/proximityPlacementGroups/{proximityPlacementGroupName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "proximityPlacementGroupName", self.ctx.args.proximity_placement_group_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_proximity_placement_group_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zone")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("colocationStatus", AAZObjectType, ".colocation_status")
                properties.set_prop("intent", AAZObjectType)
                properties.set_prop("proximityPlacementGroupType", AAZStrType, ".ppg_type")

            colocation_status = _builder.get(".properties.colocationStatus")
            if colocation_status is not None:
                colocation_status.set_prop("code", AAZStrType, ".code")
                colocation_status.set_prop("displayStatus", AAZStrType, ".display_status")
                colocation_status.set_prop("level", AAZStrType, ".level")
                colocation_status.set_prop("message", AAZStrType, ".message")
                colocation_status.set_prop("time", AAZStrType, ".time")

            intent = _builder.get(".properties.intent")
            if intent is not None:
                intent.set_prop("vmSizes", AAZListType, ".intent_vm_sizes")

            vm_sizes = _builder.get(".properties.intent.vmSizes")
            if vm_sizes is not None:
                vm_sizes.set_elements(AAZStrType, ".")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_instance_view_status_read = None

    @classmethod
    def _build_schema_instance_view_status_read(cls, _schema):
        if cls._schema_instance_view_status_read is not None:
            _schema.code = cls._schema_instance_view_status_read.code
            _schema.display_status = cls._schema_instance_view_status_read.display_status
            _schema.level = cls._schema_instance_view_status_read.level
            _schema.message = cls._schema_instance_view_status_read.message
            _schema.time = cls._schema_instance_view_status_read.time
            return

        cls._schema_instance_view_status_read = _schema_instance_view_status_read = AAZObjectType()

        instance_view_status_read = _schema_instance_view_status_read
        instance_view_status_read.code = AAZStrType()
        instance_view_status_read.display_status = AAZStrType(
            serialized_name="displayStatus",
        )
        instance_view_status_read.level = AAZStrType()
        instance_view_status_read.message = AAZStrType()
        instance_view_status_read.time = AAZStrType()

        _schema.code = cls._schema_instance_view_status_read.code
        _schema.display_status = cls._schema_instance_view_status_read.display_status
        _schema.level = cls._schema_instance_view_status_read.level
        _schema.message = cls._schema_instance_view_status_read.message
        _schema.time = cls._schema_instance_view_status_read.time

    _schema_proximity_placement_group_read = None

    @classmethod
    def _build_schema_proximity_placement_group_read(cls, _schema):
        if cls._schema_proximity_placement_group_read is not None:
            _schema.id = cls._schema_proximity_placement_group_read.id
            _schema.location = cls._schema_proximity_placement_group_read.location
            _schema.name = cls._schema_proximity_placement_group_read.name
            _schema.properties = cls._schema_proximity_placement_group_read.properties
            _schema.tags = cls._schema_proximity_placement_group_read.tags
            _schema.type = cls._schema_proximity_placement_group_read.type
            _schema.zones = cls._schema_proximity_placement_group_read.zones
            return

        cls._schema_proximity_placement_group_read = _schema_proximity_placement_group_read = AAZObjectType()

        proximity_placement_group_read = _schema_proximity_placement_group_read
        proximity_placement_group_read.id = AAZStrType(
            flags={"read_only": True},
        )
        proximity_placement_group_read.location = AAZStrType(
            flags={"required": True},
        )
        proximity_placement_group_read.name = AAZStrType(
            flags={"read_only": True},
        )
        proximity_placement_group_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        proximity_placement_group_read.tags = AAZDictType()
        proximity_placement_group_read.type = AAZStrType(
            flags={"read_only": True},
        )
        proximity_placement_group_read.zones = AAZListType()

        properties = _schema_proximity_placement_group_read.properties
        properties.availability_sets = AAZListType(
            serialized_name="availabilitySets",
            flags={"read_only": True},
        )
        properties.colocation_status = AAZObjectType(
            serialized_name="colocationStatus",
        )
        cls._build_schema_instance_view_status_read(properties.colocation_status)
        properties.intent = AAZObjectType()
        properties.proximity_placement_group_type = AAZStrType(
            serialized_name="proximityPlacementGroupType",
        )
        properties.virtual_machine_scale_sets = AAZListType(
            serialized_name="virtualMachineScaleSets",
            flags={"read_only": True},
        )
        properties.virtual_machines = AAZListType(
            serialized_name="virtualMachines",
            flags={"read_only": True},
        )

        availability_sets = _schema_proximity_placement_group_read.properties.availability_sets
        availability_sets.Element = AAZObjectType()
        cls._build_schema_sub_resource_with_colocation_status_read(availability_sets.Element)

        intent = _schema_proximity_placement_group_read.properties.intent
        intent.vm_sizes = AAZListType(
            serialized_name="vmSizes",
        )

        vm_sizes = _schema_proximity_placement_group_read.properties.intent.vm_sizes
        vm_sizes.Element = AAZStrType()

        virtual_machine_scale_sets = _schema_proximity_placement_group_read.properties.virtual_machine_scale_sets
        virtual_machine_scale_sets.Element = AAZObjectType()
        cls._build_schema_sub_resource_with_colocation_status_read(virtual_machine_scale_sets.Element)

        virtual_machines = _schema_proximity_placement_group_read.properties.virtual_machines
        virtual_machines.Element = AAZObjectType()
        cls._build_schema_sub_resource_with_colocation_status_read(virtual_machines.Element)

        tags = _schema_proximity_placement_group_read.tags
        tags.Element = AAZStrType()

        zones = _schema_proximity_placement_group_read.zones
        zones.Element = AAZStrType()

        _schema.id = cls._schema_proximity_placement_group_read.id
        _schema.location = cls._schema_proximity_placement_group_read.location
        _schema.name = cls._schema_proximity_placement_group_read.name
        _schema.properties = cls._schema_proximity_placement_group_read.properties
        _schema.tags = cls._schema_proximity_placement_group_read.tags
        _schema.type = cls._schema_proximity_placement_group_read.type
        _schema.zones = cls._schema_proximity_placement_group_read.zones

    _schema_sub_resource_with_colocation_status_read = None

    @classmethod
    def _build_schema_sub_resource_with_colocation_status_read(cls, _schema):
        if cls._schema_sub_resource_with_colocation_status_read is not None:
            _schema.colocation_status = cls._schema_sub_resource_with_colocation_status_read.colocation_status
            _schema.id = cls._schema_sub_resource_with_colocation_status_read.id
            return

        cls._schema_sub_resource_with_colocation_status_read = _schema_sub_resource_with_colocation_status_read = AAZObjectType()

        sub_resource_with_colocation_status_read = _schema_sub_resource_with_colocation_status_read
        sub_resource_with_colocation_status_read.colocation_status = AAZObjectType(
            serialized_name="colocationStatus",
        )
        cls._build_schema_instance_view_status_read(sub_resource_with_colocation_status_read.colocation_status)
        sub_resource_with_colocation_status_read.id = AAZStrType()

        _schema.colocation_status = cls._schema_sub_resource_with_colocation_status_read.colocation_status
        _schema.id = cls._schema_sub_resource_with_colocation_status_read.id


__all__ = ["Update"]
