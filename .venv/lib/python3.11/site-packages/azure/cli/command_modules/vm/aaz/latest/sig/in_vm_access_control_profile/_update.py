# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "sig in-vm-access-control-profile update",
)
class Update(AAZCommand):
    """Update a gallery in VM access control profile.

    :example: Update a gallery in VM access control profile.
        az sig in-vm-access-control-profile update --resource-group myResourceGroup --gallery-name myGalleryName --name myInVMAccessControlProfileName --description test
    """

    _aaz_info = {
        "version": "2024-03-03",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/galleries/{}/invmaccesscontrolprofiles/{}", "2024-03-03"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.gallery_name = AAZStrArg(
            options=["--gallery-name"],
            help="The name of the Shared Image Gallery from which the in VM access control profiles are to be retrieved.",
            required=True,
            id_part="name",
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The name of the gallery in VM access control profile to be deleted.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "GalleryInVMAccessControlProfile"

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.applicable_host_endpoint = AAZStrArg(
            options=["--applicable-host-endpoint"],
            arg_group="Properties",
            help="This property allows you to specify the Endpoint type for which this profile is defining the access control for. Possible values are: 'WireServer' or 'IMDS'",
            enum={"IMDS": "IMDS", "WireServer": "WireServer"},
        )
        _args_schema.description = AAZStrArg(
            options=["--description"],
            arg_group="Properties",
            help="The description of this gallery in VM access control profile resources. This property is updatable.",
            nullable=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.GalleryInVMAccessControlProfilesGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.GalleryInVMAccessControlProfilesCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class GalleryInVMAccessControlProfilesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/inVMAccessControlProfiles/{inVMAccessControlProfileName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-03-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_gallery_in_vm_access_control_profile_read(cls._schema_on_200)

            return cls._schema_on_200

    class GalleryInVMAccessControlProfilesCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/inVMAccessControlProfiles/{inVMAccessControlProfileName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-03-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_gallery_in_vm_access_control_profile_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType)

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("applicableHostEndpoint", AAZStrType, ".applicable_host_endpoint", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("description", AAZStrType, ".description")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_gallery_in_vm_access_control_profile_read = None

    @classmethod
    def _build_schema_gallery_in_vm_access_control_profile_read(cls, _schema):
        if cls._schema_gallery_in_vm_access_control_profile_read is not None:
            _schema.id = cls._schema_gallery_in_vm_access_control_profile_read.id
            _schema.location = cls._schema_gallery_in_vm_access_control_profile_read.location
            _schema.name = cls._schema_gallery_in_vm_access_control_profile_read.name
            _schema.properties = cls._schema_gallery_in_vm_access_control_profile_read.properties
            _schema.tags = cls._schema_gallery_in_vm_access_control_profile_read.tags
            _schema.type = cls._schema_gallery_in_vm_access_control_profile_read.type
            return

        cls._schema_gallery_in_vm_access_control_profile_read = _schema_gallery_in_vm_access_control_profile_read = AAZObjectType()

        gallery_in_vm_access_control_profile_read = _schema_gallery_in_vm_access_control_profile_read
        gallery_in_vm_access_control_profile_read.id = AAZStrType(
            flags={"read_only": True},
        )
        gallery_in_vm_access_control_profile_read.location = AAZStrType(
            flags={"required": True},
        )
        gallery_in_vm_access_control_profile_read.name = AAZStrType(
            flags={"read_only": True},
        )
        gallery_in_vm_access_control_profile_read.properties = AAZObjectType()
        gallery_in_vm_access_control_profile_read.tags = AAZDictType()
        gallery_in_vm_access_control_profile_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_gallery_in_vm_access_control_profile_read.properties
        properties.applicable_host_endpoint = AAZStrType(
            serialized_name="applicableHostEndpoint",
            flags={"required": True},
        )
        properties.description = AAZStrType()
        properties.os_type = AAZStrType(
            serialized_name="osType",
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        tags = _schema_gallery_in_vm_access_control_profile_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_gallery_in_vm_access_control_profile_read.id
        _schema.location = cls._schema_gallery_in_vm_access_control_profile_read.location
        _schema.name = cls._schema_gallery_in_vm_access_control_profile_read.name
        _schema.properties = cls._schema_gallery_in_vm_access_control_profile_read.properties
        _schema.tags = cls._schema_gallery_in_vm_access_control_profile_read.tags
        _schema.type = cls._schema_gallery_in_vm_access_control_profile_read.type


__all__ = ["Update"]
