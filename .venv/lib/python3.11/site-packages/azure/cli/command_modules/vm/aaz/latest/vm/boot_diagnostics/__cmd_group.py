# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "vm boot-diagnostics",
)
class __CMDGroup(AAZCommandGroup):
    """Troubleshoot the startup of an Azure Virtual Machine.         Use this feature to troubleshoot boot failures for custom or platform images.
    """
    pass


__all__ = ["__CMDGroup"]
