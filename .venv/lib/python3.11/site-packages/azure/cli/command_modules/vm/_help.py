# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
#
# Generation mode: Incremental
# --------------------------------------------------------------------------
from knack.help_files import helps  # pylint: disable=unused-import

from .generated._help import helps
try:
    from .manual._help import helps  # pylint: disable=reimported
except ImportError:
    pass


# pylint: disable=line-too-long, too-many-lines

helps['disk create'] = """
type: command
short-summary: Create a managed disk.
examples:
  - name: Create a managed disk by importing from a blob uri.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --source https://vhd1234.blob.core.windows.net/vhds/osdisk1234.vhd
  - name: Create an empty managed disk.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10
  - name: Create an empty managed disk with bursting enabled.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --size-gb 1024 --location centraluseuap --enable-bursting
  - name: Create a managed disk by copying an existing disk or snapshot.
    text: >
        az disk create -g MyResourceGroup -n MyDisk2 --source MyDisk
  - name: Create a disk in an availability zone in the region of "East US 2"
    text: >
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --location eastus2 --zone 1
  - name: Create a disk from image.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --image-reference Canonical:0001-com-ubuntu-server-jammy:22_04-lts-gen2:latest
  - name: Create a disk from the OS Disk of a compute gallery image version
    text: >
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0
  - name: Create a disk from the OS Disk of the latest version in a compute gallery image
    text: >
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage
  - name: Create a disk from the OS Disk of a shared gallery image version
    text: >
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /SharedGalleries/sharedGalleryUniqueName/Images/imageName/Versions/1.0.0
  - name: Create a disk from the OS Disk of a community gallery image version
    text: >
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /CommunityGalleries/communityGalleryPublicGalleryName/Images/imageName/Versions/1.0.0
  - name: Create a disk from the Data Disk of a gallery image
    text: >
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0 --gallery-image-reference-lun 0
  - name: Create a disk with total number of IOPS and total throughput (MBps) limitation.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --sku UltraSSD_LRS --disk-iops-read-only 200 --disk-mbps-read-only 30
  - name: Create a disk and specify maximum number of VMs that can attach to the disk at the same time.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --size-gb 256 --max-shares 2 -l centraluseuap
  - name: Create a disk and associate it with a disk access resource.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --network-access-policy AllowPrivate --disk-access MyDiskAccessID
  - name: Create a disk from the blob URI for VM guest state VHD.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --security-data-uri GuestStateDiskVhdUri --security-type TrustedLaunch --hyper-v-generation V2
  - name: Create a standard disk for uploading blobs.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --upload-size-bytes 20972032 --upload-type Upload
  - name: Create an OS disk for uploading along with VM guest state.
    text: >
        az disk create -g MyResourceGroup -n MyDisk --upload-size-bytes 20972032 --upload-type UploadWithSecurityData --security-type TrustedLaunch --hyper-v-generation V2
"""

helps['disk wait'] = """
type: command
short-summary: Place the CLI in a waiting state until a condition of a managed disk is met.
examples:
  - name: Place the CLI in a waiting state until a condition of a managed disk is met. (autogenerated)
    text: |
        az disk wait --created --name MyManagedDisk --resource-group MyResourceGroup
    crafted: true
  - name: Place the CLI in a waiting state until a condition of a managed disk is met. (autogenerated)
    text: |
        az disk wait --deleted --name MyManagedDisk --resource-group MyResourceGroup --subscription mysubscription
    crafted: true
"""

helps['disk-encryption-set identity'] = """
type: group
short-summary: Manage identities of a disk encryption set.
"""

helps['disk-encryption-set identity show'] = """
type: command
short-summary: Display managed identities of a disk encryption set.
examples:
  - name: Display managed identities of a disk encryption set.
    text: |
        az disk-encryption-set identity show --name MyDiskEncryptionSet --resource-group MyResourceGroup
"""

helps['image create'] = """
type: command
short-summary: Create a custom Virtual Machine Image from managed disks or snapshots.
examples:
  - name: Create an image from an existing disk.
    text: |
        az image create -g MyResourceGroup -n image1 --os-type Linux \\
            --source /subscriptions/db5eb68e-73e2-4fa8-b18a-0123456789999/resourceGroups/rg1/providers/Microsoft.Compute/snapshots/s1
  - name: Create an image by capturing an existing generalized virtual machine in the same resource group.
    text: az image create -g MyResourceGroup -n image1 --source MyVm1
"""

helps['image builder'] = """
type: group
short-summary: Manage and build image builder templates.
"""

helps['image builder create'] = """
type: command
short-summary: Create an image builder template.
parameters:
  - name: --image-source -i
    populator-commands:
      - az vm image list
      - az vm image show
examples:
  - name: Create an image builder template from an Ubuntu2204 image. Distribute it as a managed image and a shared image gallery image version. Specify the staging resource group id as the image template that will be used to build the image.
    text: |
        scripts="https://my-script-url.net/customize_script.sh"
        imagesource="Canonical:0001-com-ubuntu-server-jammy:22_04-lts-gen2:latest"

        az image builder create --image-source $imagesource -n myTemplate -g myGroup \\
            --scripts $scripts --managed-image-destinations image_1=westus \\
            --shared-image-destinations my_shared_gallery/linux_image_def=westus,brazilsouth \\
            --identity myIdentity --staging-resource-group myStagingResourceGroup

  - name: Create an image builder template using an image template file.
    text: |
        az image builder create -g my-group -n myTemplate --image-template filename

  - name: >
        [Advanced] Create an image template with multiple customizers and distributors using the CLI's object cache via --defer. Supports features such as: customizer and output names, powershell exit codes, inline scripts, windows restart, file customizers, artifact tags and vhd output distributors.
    text: |
        script="https://my-script-url.com/customize_script.ps1"
        imagesource="MicrosoftWindowsServer:WindowsServer:2019-Datacenter:2019.0.20190214"

        # create and update template object in local cli cache. Defers put request to ARM
        # Cache object ttl set via az configure.
        az image builder create --image-source $imagesource -n myTemplate \\
            -g myGroup --scripts $script --identity myIdentity --defer

        # add customizers
        az image builder customizer add -n myTemplate -g myGroup  \\
            --customizer-name myPwshScript --exit-codes 0 1 --inline-script \\
            "mkdir c:\\buildActions" "echo Azure-Image-Builder-Was-Here \\
             > c:\\buildActions\\Output.txt" --type powershell --defer

        az image builder customizer add -n myTemplate -g myGroup \\
            --customizer-name myFileCustomizer --type file \\
            --file-source "https://my-file-source.net/file.txt"  \\
            --dest-path "c:\\buildArtifacts\\file.txt" --defer

        # add distributors
        az image builder output add -n myTemplate -g myGroup --is-vhd \\
            --output-name myWinImageVhd --artifact-tags "is_vhd=True" --defer

        az image builder output add -n myTemplate -g myGroup \\
            --output-name myWinImageManaged --managed-image winImage \\
            --managed-image-location eastus \\
            --artifact-tags "is_vhd=False" --defer

        # Stop deferring put request to ARM. Create the template from the object cache.
        # Cache object will be deleted.
        az image builder update -n myTemplate -g myGroup
"""

helps['image builder customizer'] = """
type: group
short-summary: Manage image builder template customizers.
"""

helps['image builder customizer add'] = """
type: command
short-summary: Add an image builder customizer to an image builder template.
long-summary: Must be used with --defer
examples:
  - name: Add an inline shell customizer to an image template in the cli object cache
    text: |
        az image builder customizer add -n myTemplate -g myGroup \\
            --inline-script "sudo mkdir /buildArtifacts" \\
                            "sudo cp /tmp/index.html /buildArtifacts/index.html" \\
            --customizer-name shellScriptInline --type shell --defer

  - name: Add a file customizer to an image template in the cli object cache
    text: |
        az image builder customizer add -n myTemplate -g myGroup \\
            --customizer-name myFile --type file \\
            --file-source "https://my-remote-file.html" --dest-path "/tmp/index.html" --defer

  - name: Add a windows restart customizer to an image template in the cli object cache
    text: |
        az image builder customizer add -n myTemplate -g myGroup \\
        --customizer-name shellScriptUrl \\
        --restart-check-command "echo Azure-Image-Builder-Restarted-the-VM  > \\
                                c:\\buildArtifacts\\restart.txt" \\
            --type windows-restart --restart-timeout 10m --defer

  - name: Add a windows update customizer to an image template in the cli object cache.
    text: |
        az image builder customizer add -n myTemplate -g myGroup --customizer-name winUpdate --type windows-update --search-criteria IsInstalled=0 --filters "exclude:\\$_.Title -like \\'*Preview*\\'" "include:\\$true" --update-limit 20 --defer
"""

helps['image builder customizer clear'] = """
type: command
short-summary: Remove all image builder customizers from an image builder template.
long-summary: Must be used with --defer
"""

helps['image builder customizer remove'] = """
type: command
short-summary: Remove an image builder customizer from an image builder template.
long-summary: Must be used with --defer
"""

helps['image builder validator'] = """
type: group
short-summary: Manage image builder template validate.
"""

helps['image builder validator add'] = """
type: command
short-summary: Add validate to an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Add validate with continue distribute on failure set to true. If not specified, the default value of continue distribute on failure is false.
        If validation fails and this field is set to false, output image(s) will not be distributed.
    text: |
        az image builder validator add -n myTemplate -g myGroup --continue-distribute-on-failure true --defer

  - name: Add validate with source validation only set to true. If not specified, the default value of source validation only is false.
        If this field is set to true, the image specified in the source section will directly be validated.
    text: |
        az image builder validator add -n myTemplate -g myGroup --source-validation-only true --defer

  - name: Add validate with source validation only and continue distribute on failure set to false.
    text: |
        az image builder validator add -n myTemplate -g myGroup --defer
"""

helps['image builder validator remove'] = """
type: command
short-summary: Remove validate from an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Remove validate from an existing image builder template.
    text: |
        az image builder validator remove -n myTemplate -g myGroup --defer
"""

helps['image builder validator show'] = """
type: command
short-summary: Show validate of an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Show validate of an existing image builder template.
    text: |
        az image builder validator show -n myTemplate -g myGroup --defer
"""

helps['image builder optimizer'] = """
type: group
short-summary: Manage image builder template optimizer.
"""

helps['image builder optimizer add'] = """
type: command
short-summary: Add optimizer to an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Add optimizer for improving VM boot time by optimizing the final customized image output.
    text: |
        az image builder optimizer add -n myTemplate -g myGroup --enable-vm-boot true --defer
"""

helps['image builder optimizer update'] = """
type: command
short-summary: Update an optimizer from an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Update an optimizer from an existing image builder template.
    text: |
        az image builder optimizer update -n myTemplate -g myGroup --enable-vm-boot true --defer
"""

helps['image builder optimizer remove'] = """
type: command
short-summary: Remove optimizer from an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Remove optimizer from an existing image builder template.
    text: |
        az image builder optimizer remove -n myTemplate -g myGroup --defer
"""

helps['image builder optimizer show'] = """
type: command
short-summary: Show optimizer of an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Show optimizer of an existing image builder template.
    text: |
        az image builder optimizer show -n myTemplate -g myGroup --defer
"""

helps['image builder error-handler'] = """
type: group
short-summary: Manage image builder template error handler.
"""

helps['image builder error-handler add'] = """
type: command
short-summary: Add error handler to an existing image builder template.
long-summary: Must be used with --defer
"""

helps['image builder error-handler remove'] = """
type: command
short-summary: Remove error handler from an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Remove error handler from an existing image builder template.
    text: |
        az image builder error-handler remove -n myTemplate -g myGroup --defer
"""

helps['image builder error-handler show'] = """
type: command
short-summary: Show error handler of an existing image builder template.
long-summary: Must be used with --defer
examples:
  - name: Show error handler of an existing image builder template.
    text: |
        az image builder error-handler show -n myTemplate -g myGroup --defer
"""

helps['image builder identity'] = """
type: group
short-summary: Manage identities of an image builder template.
"""

helps['image builder identity assign'] = """
type: command
short-summary: Add managed identities to an existing image builder template. Currently, only one user identity is supported.
examples:
  - name: Add a user assigned managed identity to an existing image builder template.
    text: >
        az image builder identity assign --name MyImageBuilderTemplate --resource-group MyResourceGroup --user-assigned MyAssignedId
"""

helps['image builder identity remove'] = """
type: command
short-summary: Remove managed identities from an existing image builder template.
examples:
  - name: Remove a user assigned managed identity from an existing image builder template.
    text: >
        az image builder identity remove --name MyImageBuilderTemplate --resource-group MyResourceGroup --user-assigned MyAssignedId
  - name: Remove all user assigned managed identities from an existing image builder.
    text: >
        az image builder identity remove --name MyImageBuilderTemplate --resource-group MyResourceGroup --user-assigned
"""

helps['image builder identity show'] = """
type: command
short-summary: Display managed identities of a image builder template.
examples:
  - name: Display managed identities of a image builder template.
    text: |
        az image builder identity show --name MyImageBuilderTemplate --resource-group MyResourceGroup
"""

helps['image builder delete'] = """
type: command
short-summary: Delete image builder template.
examples:
  - name: Delete image builder template. (autogenerated)
    text: |
        az image builder delete --name MyImageTemplate --resource-group MyResourceGroup
    crafted: true
"""

helps['image builder list'] = """
type: command
short-summary: List image builder templates.
"""

helps['image builder output'] = """
type: group
short-summary: Manage image builder template output distributors.
long-summary: >
    A customized image can be distributed as a managed image,
    a shared image in a shared image gallery (SIG), or as a VHD blob.
"""

helps['image builder output add'] = """
type: command
short-summary: Add an image builder output distributor to an image builder template.
long-summary: Must be used with --defer. The output distributor can be a managed image, a gallery image, or as a VHD blob.
examples:
  - name: Add a managed image distributor to an image template in the cli object cache. Specify a run output name.
    text: |
        az image builder output add -n mytemplate -g my-group \\
            --managed-image my_desired_image_name --output-name managed_image_run_01 --defer

  - name: Add a shared image gallery distributor to an image template in the cli object cache. Specify its replication regions.
    text: |
        az image builder output add -n mytemplate -g my-group --gallery-name my_shared_gallery \\
            --gallery-replication-regions westus brazilsouth \\
            --gallery-image-definition linux_image_def --defer

  - name: Add a VHD distributor to an image template in the cli object cache.
    text: |
        az image builder output add -n mytemplate -g my-group \\
            --output-name my_vhd_image --is-vhd  --defer

  - name: Add a VHD distributor with specifying storage uri to an image template in the cli object cache.
    text: |
        az image builder output add -n mytemplate -g my-group \\
            --output-name my_vhd_image --is-vhd --vhd-uri https://mystorageaccount.blob.core.windows.net/container/path_to_vhd_file --defer
"""

helps['image builder output clear'] = """
type: command
short-summary: Remove all image builder output distributors from an image builder template.
long-summary: Must be used with --defer
"""

helps['image builder output remove'] = """
type: command
short-summary: Remove an image builder output distributor from an image builder template.
long-summary: Must be used with --defer
"""

helps['image builder output versioning'] = """
type: group
short-summary: Manage image builder template output versioner.
long-summary: >
    Describe how to generate new x.y.z version number for distribution.
"""

helps['image builder output versioning set'] = """
type: command
short-summary: Set the image builder output versioner of an image builder template.
long-summary: Must be used with --defer.
examples:
  - name: Set the image builder output versioner generating version number that will be latest based on existing version numbers.
    text: |
        az image builder output versioning set -n MyTemplate -g MyResourceGroup --output-name MyVhdImage --scheme Latest --defer

  - name: Set the image builder output versioner generating version number that will be latest based on specified major version.
    text: |
        az image builder output versioning set -n MyTemplate -g MyResourceGroup --output-name MyVhdImage --scheme Latest --major 1 --defer

  - name: Set the image builder output versioner generating version number based on version number of source image.
    text: |
        az image builder output versioning set -n MyTemplate -g MyResourceGroup --output-name MyVhdImage --scheme Source --defer
"""

helps['image builder output versioning remove'] = """
type: command
short-summary: Remove all versioning options on specified outputs.
long-summary: Must be used with --defer
examples:
  - name: Remove the image builder output versioner of specified outputs.
    text: |
        az image builder output versioning remove -n MyTemplate -g MyResourceGroup --output-name MyVhdImage --defer
"""

helps['image builder output versioning show'] = """
type: command
short-summary: Show versioning options on specified outputs.
long-summary: Must be used with --defer
examples:
  - name: Show the image builder output versioner of specified outputs.
    text: |
        az image builder output versioning show -n MyTemplate -g MyResourceGroup --output-name MyVhdImage --defer
"""

helps['image builder run'] = """
type: command
short-summary: Build an image builder template.
examples:
  - name: Start a template build run and then wait for it to finish.
    text: |
        az image builder run -n mytemplate -g my-group --no-wait

        az image builder wait -n mytemplate -g aibmdi \\
            --custom "lastRunStatus.runState!='Running'"

        az image builder show -n mytemplate -g my-group
"""

helps['image builder cancel'] = """
type: command
short-summary: Cancel the long running image build based on the image template.
examples:
  - name: Cancel an image build.
    text: |
        az image builder cancel -n mytemplate -g my-group
"""

helps['image builder show'] = """
type: command
short-summary: Show an image builder template.
examples:
  - name: Show an image builder template (autogenerated)
    text: |
        az image builder show --name mytemplate  --resource-group my-group
    crafted: true
"""

helps['image builder show-runs'] = """
type: command
short-summary: Show an image builder template's run outputs.
examples:
  - name: Run a template build run and then view its run outputs.
    text: |
        az image builder run -n mytemplate -g my-group --no-wait

        az image builder wait -n mytemplate -g aibmdi \\
            --custom "lastRunStatus.runState!='Running'"

        az image builder show-runs -n mytemplate -g my-group
"""

helps['image builder update'] = """
type: command
short-summary: Update an image builder template.
long-summary: >
    Updating an image builder templates is currently unsupported. This command can be used in conjunction with --defer
    to update an image template object within the CLI cache. Without --defer it retrieves the specified image template
    from the cache and sends a request to Azure to create the image template.

examples:
  - name: |
        Create a template resource from a template object in the cli cache.
        See "az image builder create / output add / customizer add --help" and "az cache -h" for more information
    text: |
        # create and write template object to local cli cache
        az image builder create --image-source {image_source} -n mytemplate -g my-group \\
            --scripts {script} --managed-image-destinations image_1=westus --identity myidentity --defer

        # add customizers and outputs to local cache template object via az image template output / customizer add
        # one can also update cache object properties through generic update options, such as: --set
        az image builder output add -n mytemplate -g my-group --output-name my-win-image-managed \\
            --artifact-tags "is_vhd=False"  --managed-image winImage --managed-image-location eastus --defer

        # send template create request to azure to create template resource
        az image builder update -n mytemplate -g my-group
"""

helps['image builder wait'] = """
type: command
short-summary: Place the CLI in a waiting state until a condition of the template is met.
examples:
  - name: Start a template build run and then wait for it to finish.
    text: |
        az image builder run -n mytemplate -g my-group --no-wait

        az image builder wait -n mytemplate -g aibmdi \\
            --custom "lastRunStatus.runState!='Running'"

        az image builder show -n mytemplate -g my-group
"""

helps['sig image-definition create'] = """
type: command
short-summary: create a gallery image definition
examples:
  - name: Create an image definition for specialized linux images
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized
  - name: Create an image definition for generalized linux images
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Generalized
  - name: Create an image definition for specialized windows images
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type windows --os-state Specialized
  - name: Create an image definition for generalized windows images
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type windows --os-state Generalized
  - name: Create an image definition with plan information
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized --plan-name PlanName \\
        --plan-product PlanProduct --plan-publisher PlanPublisher
  - name: Create an image definition for images that support hibernate feature
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --features IsHibernateSupported=true
  - name: Create an image definition for images that support accelerated networking
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --features IsAcceleratedNetworkSupported=true
  - name: Create an image definition for images that can only be used to create Trusted VMs. Only Trusted VMs can be created from this image.
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --features SecurityType=TrustedLaunch
  - name: Create an image definition for images that can be used to create Confidential VMs.
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --features SecurityType=ConfidentialVmSupported
  - name: Create an image definition for images that can only be used to create Confidential VMs. Only Confidential VMs can be created from this image.
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --features SecurityType=ConfidentialVM
  - name: Create an image definition for images that can be used to create Gen2 or TrustedLaunchSupported VMs.
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --features SecurityType=TrustedLaunchSupported
  - name: Create an image definition for images that can be used to create Gen2, TrustedLaunch, or Confidential VMs.
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --features SecurityType=TrustedLaunchAndConfidentialVmSupported
  - name: Create an image definition and indicate end of life date
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --end-of-life-date YYYY-MM-DDTHH:MM:SS+00:00
  - name: Create an image definition and recommend minimum and maximum CPU and memory (GB)
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --minimum-cpu-core myMinCPU --maximum-cpu-core myMaxCPU \\
        --minimum-memory myMinMemory --maximum-memory myMaxMemory
  - name: Create an image definition and indicate which OS disk types are not recommended for the image
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --disallowed-disk-types Standard_LRS
  - name: Create an image definition and provide the EULA, privacy statement URI, and release notes URI
    text: |
        az sig image-definition create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --publisher GreatPublisher --offer GreatOffer --sku GreatSku \\
        --os-type linux --os-state Specialized \\
        --eula path_to_eula --privacy-statement-uri path_to_statement \\
        --release-note-uri path_to_release_notes
"""

helps['sig image-version create'] = """
type: command
short-summary: create a new image version
long-summary: this operation might take a long time depending on the replicate region number. Use "--no-wait" is advised.
examples:
  - name: Add a new image version from a virtual machine
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --virtual-machine /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/virtualMachines/MyVM
  - name: Add a new image version from a managed image
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --managed-image /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/images/MyManagedImage
  - name: Add a new image version from another image version
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --image-version /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/galleries/MyGallery/images/MyImageDefinition/versions/1.0.0
  - name: Add a new image version from a managed disk
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --os-snapshot /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/disks/MyOSDisk
  - name: Add a new image version from a managed disk and add additional data disks
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --os-snapshot /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/disks/MyOSDisk \\
        --data-snapshots /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/disks/MyDataDisk \\
        --data-snapshot-luns 0
  - name: Add a new image version from a snapshot of an OS disk.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --os-snapshot /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/snapshots/MyOsDiskSnapshot
  - name: Add a new image version from a snapshot of an OS disk and add additional snapshots as data disks
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --os-snapshot /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/snapshots/MyOsDiskSnapshot \\
        --data-snapshots /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/snapshots/MyDiskSnapshot \\
        --data-snapshot-luns 0
  - name: Add a new image version from a VHD of an OS disk.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --os-vhd-storage-account /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Storage/storageAccounts/mystorageaccount \\
        --os-vhd-uri https://mystorageaccount.blob.core.windows.net/container/path_to_vhd_file
  - name: Add a new image version from a VHD of an OS disk and add additional VHDs as data disks
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --os-vhd-storage-account /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Storage/storageAccounts/mystorageaccount \\
        --os-vhd-uri https://mystorageaccount.blob.core.windows.net/container/path_to_vhd_file \\
        --data-vhds-sa /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Storage/storageAccounts/myotherstorageaccount \\
        --data-vhds-uris https://myotherstorageaccount.blob.core.windows.net/container/path_to_vhd_file \\
        --data-vhds-luns 0
  - name: You can combine snapshots, managed disks, and VHDs to create a new image version. Add a new image version using a VHD as the OS disk and a managed disk and a snapshot as data disks.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --os-vhd-storage-account /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Storage/storageAccounts/mystorageaccount \\
        --os-vhd-uri https://mystorageaccount.blob.core.windows.net/container/path_to_vhd_file \\
        --data-snapshots /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/disks/MyDataDisk subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/snapshots/MyDiskSnapshot \\
        --data-snapshot-luns 0 1
  - name: Add a new image version and copy it to additional regions. The home location for the source of the image version must be included in the list of target regions. For each additional region, you can specify a different replica count and storage account type. Otherwise, the region will inherit from the global. The default replica count is 1 and the default storage account type is Standard LRS. In this example, eastus2 will have one replica stored on Standard ZRS storage, ukwest will have 3 replicas stored on Standard ZRS storage, southindia will have one replica stored on Standard LRS storage, and brazilsouth will have 2 replicas stored on Standard LRS storage.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 --replica-count 1 \\
        --storage-account-type Standard_ZRS --managed-image image-name \\
        --target-regions eastus2 ukwest=3 southindia=standard_lrs \\
        brazilsouth=2=standard_lrs
  - name: Add a new image version with encryption using a disk encryption set. Encryption is applied to each disk that is a part of the image version.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --virtual-machine /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/virtualMachines/MyVM \\
        --target-regions westus=2=standard eastus \\
        --target-region-encryption WestUSDiskEncryptionSet1,0,WestUSDiskEncryptionSet2 \\
        EastUSDiskEncryptionSet1,0,EastUSDiskEncryptionSet2
  - name: Add a new image version and copy it to extended locations.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 --replica-count 1 \\
        --storage-account-type Standard_ZRS --managed-image image-name \\
        --target-edge-zones westus=microsoftlosangeles1 eastus=microsoftlosangeles2=1 \\
        brazilsouth=2=standard_lrs
  - name: Add a new image version and copy it to extended locations with encryption using a disk encryption set.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --virtual-machine /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/virtualMachines/MyVM \\
        --target-edge-zones westus=microsoftlosangeles1 \\
        --target-edge-zone-encryption microsoftlosangeles1,WestUSDiskEncryptionSet1,0,WestUSDiskEncryptionSet2
  - name: Add a new image version and don't wait on it. Later you can invoke "az sig image-version wait" command when ready to create a vm from the gallery image version
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --virtual-machine /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/virtualMachines/MyVM \\
        --no-wait
  - name: Add a new image version but remove it from consideration as latest version in its image definition
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --virtual-machine /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/virtualMachines/MyVM \\
        --exclude-from-latest true
  - name: Add a new image version and set its end-of-life date. The image version can still be used to create a virtual machine after its end-of-life date.
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --virtual-machine /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/virtualMachines/MyVM \\
        --end-of-life-date 2024-08-02T00:00:00+00:00
  - name: Add a new image version and block the deletion for this image version if its end of life has not expired
    text: |
        az sig image-version create --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --virtual-machine /subscriptions/********-0000-0000-0000-********xxxx/resourceGroups/imageGroups/providers/Microsoft.Compute/virtualMachines/MyVM \\
        --end-of-life-date 2024-08-02T00:00:00+00:00 \\
        --block-deletion-before-end-of-life true
"""

helps['sig image-version update'] = """
type: command
short-summary: update a share image version
examples:
  - name: Change the replication regions and replica count
    text: |
        az sig image-version update --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --target-regions westcentralus=2 eastus2
  - name: Change the replication extended locations
    text: |
        az sig image-version update --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --target-edge-zones westus=microsoftlosangeles1 eastus=microsoftlosangeles2=1
  - name: Clear the replication extended locations
    text: |
        az sig image-version update --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --target-edge-zones None
  - name: Replicate to an additional region. Optional, you can set the replica count for the region and exclude this image when using the latest version of the image definition.
    text: |
        az sig image-version update --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0 \\
        --add publishingProfile.targetRegions name=westcentralus \\
        regionalReplicaCount=3 excludeFromLatest=true
  - name: Change whether an image should be included in consideration for latest version in the image definition. Setting this value to true excludes the image from consideration and setting this value to false includes the image for consideration.
    text: |
        az sig image-version update -g MyResourceGroup --gallery-name MyGallery \\
        --gallery-image-definition MyImage --gallery-image-version 1.0.0 \\
        --set publishingProfile.excludeFromLatest=true
  - name: Change the end of life date for an image version. The image can still be used to create virtual machines after the end of life date.
    text: |
        az sig image-version update -g MyResourceGroup --gallery-name MyGallery \\
        --gallery-image-definition MyImage --gallery-image-version 1.0.0 \\
        --set publishingProfile.endOfLifeDate=2024-08-02T00:00:00+00:00
  - name: Allow to remove the gallery image version from replicated regions.
    text: |
        az sig image-version update -g MyResourceGroup --gallery-name MyGallery \\
        --gallery-image-definition MyImage --gallery-image-version 1.0.0 \\
        --set safetyProfile.allowDeletionOfReplicatedLocations=true
  - name: Block the deletion for this gallery image version if its end of life has not expired.
    text: |
        az sig image-version update -g MyResourceGroup --gallery-name MyGallery \\
        --gallery-image-definition MyImage --gallery-image-version 1.0.0 \\
        --block-deletion-before-end-of-life true
"""

helps['sig image-version undelete'] = """
type: command
short-summary: Restore soft deleted Image Version
long-summary: >
    Operation will only be successful if used within a Gallery with soft-deletion on.
examples:
  - name: Restore a soft deleted Image Version
    text: |
        az sig image-version undelete --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.1.1
"""

helps['sig image-version wait'] = """
type: command
short-summary: wait for image version related operation
parameters:
  - name: --expand
    short-summary: The expand expression to apply on the operation. 'ReplicationStatus' Default value is None.
examples:
  - name: wait for an image version gets updated
    text: |
        az sig image-version wait --updated --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0
  - name: wait for image version related operation. (autogenerated)
    text: |
        az sig image-version wait --created --resource-group MyResourceGroup \\
        --gallery-name MyGallery --gallery-image-definition MyImage \\
        --gallery-image-version 1.0.0
    crafted: true
"""

helps['sig list-community'] = """
type: command
short-summary: List all community galleries shared directly to your subscription or tenant
long-summary: List all community galleries shared directly to your subscription or tenant
examples:
  - name: List community galleries shared directly to your subscription in a given location
    text: |
        az sig list-community --location myLocation
  - name: List paging community galleries shared directly to your tenant in a given location according to next marker
    text: |
        az sig list-community --location myLocation --marker nextMarker
"""

helps['sig share'] = """
type: group
short-summary: Manage gallery sharing profile
"""

helps['snapshot create'] = """
type: command
short-summary: Create a snapshot.
examples:
  - name: Create a snapshot by importing from a blob uri.
    text: az snapshot create -g MyResourceGroup -n MySnapshot --source https://vhd1234.blob.core.windows.net/vhds/osdisk1234.vhd
  - name: Create an empty snapshot.
    text: az snapshot create -g MyResourceGroup -n MySnapshot --size-gb 10
  - name: Create a snapshot by copying an existing disk in the same resource group.
    text: az snapshot create -g MyResourceGroup -n MySnapshot2 --source MyDisk
  - name: Create a snapshot from an existing disk in another resource group.
    text: az snapshot create -g MyResourceGroup -n MySnapshot2 --source "/subscriptions/00000/resourceGroups/AnotherResourceGroup/providers/Microsoft.Compute/disks/MyDisk"
  - name: Create a snapshot and associate it with a disk access resource.
    text: az snapshot create -g MyResourceGroup -n MySnapshot --size-gb 10 --network-access-policy AllowPrivate --disk-access MyDiskAccessID
"""

helps['vm'] = """
type: group
short-summary: Manage Linux or Windows virtual machines.
"""

helps['vm install-patches'] = """
type: command
short-summary: Install patches on a VM.
examples:
  - name: Install patches on a windows VM, allowing the maximum amount of time to be 4 hours, and the VM will reboot if required during the software update operation.
    text: az vm install-patches -g MyResourceGroup -n MyVm --maximum-duration PT4H --reboot-setting IfRequired --classifications-to-include-win Critical Security --exclude-kbs-requiring-reboot true
  - name: Install patches on a linux VM, allowing the maximum amount of time to be 4 hours, and the VM will reboot if required during the software update operation.
    text: az vm install-patches -g MyResourceGroup -n MyVm --maximum-duration PT4H --reboot-setting IfRequired --classifications-to-include-linux Critical
"""

helps['vm auto-shutdown'] = """
type: command
short-summary: Manage auto-shutdown for VM.
examples:
  - name: Create auto-shutdown schedule for a VM.
    text: az vm auto-shutdown -g MyResourceGroup -n MyVm --time 1730 --email "<EMAIL>" --webhook "https://example.com/"
  - name: Delete auto-shutdown schedule for a VM.
    text: az vm auto-shutdown -g MyResourceGroup -n MyVm --off
"""

helps['vm list-sizes'] = """
type: command
short-summary: List available sizes for VMs.
examples:
  - name: List the available VM sizes in the West US region.
    text: az vm list-sizes -l westus
"""

helps['vm availability-set create'] = """
type: command
short-summary: Create an Azure Availability Set.
long-summary: 'For more information, see https://learn.microsoft.com/azure/virtual-machines/availability.'
examples:
  - name: Create an availability set.
    text: az vm availability-set create -n MyAvSet -g MyResourceGroup --platform-fault-domain-count 2 --platform-update-domain-count 2
"""

helps['vm boot-diagnostics'] = """
type: group
short-summary: Troubleshoot the startup of an Azure Virtual Machine.
long-summary: Use this feature to troubleshoot boot failures for custom or platform images.
"""

helps['vm boot-diagnostics disable'] = """
type: command
short-summary: Disable the boot diagnostics on a VM.
examples:
  - name: Disable boot diagnostics on all VMs in a resource group.
    text: >
        az vm boot-diagnostics disable --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

  - name: Disable the boot diagnostics on a VM
    text: |-
        az vm boot-diagnostics disable --ids $(az vm list --resource-group MyResourceGroup --query "[].id" -o tsv) --name MyVirtualMachine --resource-group MyResourceGroup
"""

helps['vm boot-diagnostics enable'] = """
type: command
short-summary: Enable the boot diagnostics on a VM.
parameters:
  - name: --storage
    short-summary: Name or URI of a storage account (e.g. https://your_storage_account_name.blob.core.windows.net/). If it's not specified, managed storage will be used.
examples:
  - name: Enable boot diagnostics on all VMs in a resource group.
    text: >
        az vm boot-diagnostics enable --storage https://mystor.blob.core.windows.net/ --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

  - name: Enable the boot diagnostics on a VM. (autogenerated)
    text: |
        az vm boot-diagnostics enable --name MyVirtualMachine --resource-group MyResourceGroup --storage https://mystor.blob.core.windows.net/
    crafted: true
"""

helps['vm boot-diagnostics get-boot-log'] = """
type: command
short-summary: Get the boot diagnostics log from a VM.
examples:
  - name: Get diagnostics logs for all VMs in a resource group.
    text: >
        az vm boot-diagnostics get-boot-log --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

  - name: Get the boot diagnostics log from a VM. (autogenerated)
    text: |
        az vm boot-diagnostics get-boot-log --name MyVirtualMachine --resource-group MyResourceGroup
    crafted: true
"""

helps['vm boot-diagnostics get-boot-log-uris'] = """
type: command
short-summary: Get SAS URIs for a virtual machine's boot diagnostic logs.
parameters:
  - name: --expire
    short-summary: Expiration duration in minutes for the SAS URIs with a value between 1 to 1440 minutes. If not specified, SAS URIs will be generated with a default expiration duration of 120 minutes.
examples:
  - name: Get SAS URIs for a virtual machine's boot diagnostic logs.
    text: >
        az vm boot-diagnostics get-boot-log-uris -g MyResourceGroup -n MyVirtualMachine
"""

helps['vm create'] = """
type: command
short-summary: Create an Azure Virtual Machine.
long-summary: 'For an end-to-end tutorial, see https://learn.microsoft.com/azure/virtual-machines/linux/quick-create-cli.'
parameters:
  - name: --image
    type: string
    short-summary: >
        The name of the operating system image as a URN alias, URN, custom image name or ID, custom image version ID, or VHD blob URI. In addition, it also supports shared gallery image.
        Please use the image alias including the version of the distribution you want to use. For example: please use Debian11 instead of Debian.'
        This parameter is required unless using `--attach-os-disk.` Valid URN format: "Publisher:Offer:Sku:Version". For more information, see https://learn.microsoft.com/azure/virtual-machines/linux/cli-ps-findimage
    populator-commands:
      - az vm image list
      - az vm image show
      - az sig image-version show-shared
  - name: --size
    populator-commands:
      - az vm list-sizes
  - name: --ssh-key-values
    short-summary: Space-separated list of SSH public keys or public key file paths.
  - name: --computer-name
    short-summary: The host OS name of the virtual machine. Defaults to the name of the VM.
examples:
  - name: Create a default Ubuntu2204 VM with automatic SSH authentication.
    text: >
        az vm create -n MyVm -g MyResourceGroup --image Ubuntu2204
  - name: Create a default RedHat VM with automatic SSH authentication using an image URN.
    text: >
        az vm create -n MyVm -g MyResourceGroup --image RedHat:RHEL:7-RAW:7.4.2018010506
  - name: Create a default Windows Server VM with a private IP address.
    text: >
        az vm create -n MyVm -g MyResourceGroup --public-ip-address "" --image Win2012R2Datacenter
  - name: Create a VM from a custom managed image.
    text: >
        az vm create -g MyResourceGroup -n MyVm --image MyImage
  - name: Create a VM from a generalized gallery image version.
    text: >
        az vm create -g MyResourceGroup -n MyVm --image /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0
  - name: Create a VM from a specialized gallery image version.
    text: >
        az vm create -g MyResourceGroup -n MyVm --image /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0 --specialized
  - name: Create a VM from the latest version of a gallery image
    text: >
        az vm create -g MyResourceGroup -n MyVm --image /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage
  - name: Create a VM by attaching to a managed operating system disk.
    text: >
        az vm create -g MyResourceGroup -n MyVm --attach-os-disk MyOsDisk --os-type linux
  - name: Create a VM by attaching to an unmanaged operating system disk from a VHD blob uri.
    text: >
        az vm create -g MyResourceGroup -n MyVm --attach-os-disk https://vhd1234.blob.core.windows.net/vhds/osdisk1234.vhd --os-type linux --use-unmanaged-disk
  - name: 'Create an Debian11 VM using a cloud-init script for configuration. See: https://learn.microsoft.com/azure/virtual-machines/linux/using-cloud-init.'
    text: >
        az vm create -g MyResourceGroup -n MyVm --image Debian11 --custom-data MyCloudInitScript.yml
  - name: Create a Debian11 VM with SSH key authentication and a public DNS entry, located on an existing virtual network and availability set.
    text: |
        az vm create -n MyVm -g MyResourceGroup --image Debian11 --vnet-name MyVnet --subnet subnet1 \\
            --availability-set MyAvailabilitySet --public-ip-address-dns-name MyUniqueDnsName \\
            --ssh-key-values @key-file
  - name: Create a simple Ubuntu Linux VM with a public IP address, DNS entry, two data disks (10GB and 20GB), and then generate RSA ssh key pairs.
    text: |
        az vm create -n MyVm -g MyResourceGroup --public-ip-address-dns-name MyUniqueDnsName \\
            --image Ubuntu2204 --data-disk-sizes-gb 10 20 --size Standard_DS2_v2 \\
            --generate-ssh-keys
  - name: Create a Debian11 VM using Key Vault secrets.
    text: >
        az keyvault certificate create --vault-name vaultname -n cert1 \\
          -p "$(az keyvault certificate get-default-policy)"

        secrets=$(az keyvault secret list-versions --vault-name vaultname \\
          -n cert1 --query "[?attributes.enabled].id" -o tsv)

        vm_secrets=$(az vm secret format -s "$secrets")


        az vm create -g group-name -n vm-name --admin-username deploy  \\
          --image debian11 --secrets "$vm_secrets"
  - name: Create a CentOS VM with a system assigned identity. The VM will have a 'Contributor' role with access to a storage account.
    text: >
        az vm create -n MyVm -g rg1 --image CentOS85Gen2 --assign-identity [system] --scope /subscriptions/********-1bf0-4dda-aec3-cb9272f09590/MyResourceGroup/myRG/providers/Microsoft.Storage/storageAccounts/storage1 --role Contributor
  - name: Create a Debian11 VM with a user assigned identity.
    text: >
        az vm create -n MyVm -g rg1 --image Debian11 --assign-identity /subscriptions/********-1bf0-4dda-aec3-cb9272f09590/resourcegroups/myRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/myID
  - name: Create a Debian11 VM with both system and user assigned identity.
    text: >
        az vm create -n MyVm -g rg1 --image Debian11 --assign-identity [system] /subscriptions/********-1bf0-4dda-aec3-cb9272f09590/resourcegroups/myRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/myID
  - name: Create a vm with user assigned identity and add encryption identity for Azure disk encryption
    text: >
        az vm create -n MyVm -g rg1 --image Debian11 --assign-identity myID --encryption-identity /subscriptions/********-0000-0000-0000-********0000/resourcegroups/myRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/myID
  - name: Create a VM in an availability zone in the current resource group's region.
    supported-profiles: latest
    text: >
        az vm create -n MyVm -g MyResourceGroup --image CentOS85Gen2 --zone 1
  - name: Create multiple VMs. In this example, 3 VMs are created. They are MyVm0, MyVm1, MyVm2.
    text: >
        az vm create -n MyVm -g MyResourceGroup --image CentOS85Gen2 --count 3
  - name: Create a VM from shared gallery image
    text: >
        az vm create -n MyVm -g MyResourceGroup --image /SharedGalleries/{gallery_unique_name}/Images/{image}/Versions/{version}
  - name: Create a VM from community gallery image
    text: >
        az vm create -n MyVm -g MyResourceGroup --image /CommunityGalleries/{gallery_unique_name}/Images/{image}/Versions/{version}
"""

helps['vm diagnostics'] = """
type: group
short-summary: Configure the Azure Virtual Machine diagnostics extension.
"""

helps['vm diagnostics get-default-config'] = """
type: command
short-summary: Get the default configuration settings for a VM.
examples:
  - name: Get the default diagnostics for a Linux VM and override the storage account name and the VM resource ID.
    text: |
        az vm diagnostics get-default-config \\
            | sed "s#__DIAGNOSTIC_STORAGE_ACCOUNT__#MyStorageAccount#g" \\
            | sed "s#__VM_OR_VMSS_RESOURCE_ID__#MyVmResourceId#g"
  - name: Get the default diagnostics for a Windows VM.
    text: >
        az vm diagnostics get-default-config --is-windows-os
"""

helps['vm diagnostics set'] = """
type: command
short-summary: Configure the Azure VM diagnostics extension.
examples:
  - name: Set up default diagnostics on a Linux VM for Azure Portal VM metrics graphs and syslog collection.
    text: |
        # Set the following 3 parameters first.
        my_resource_group={Resource group name containing your Linux VM and the storage account}
        my_linux_vm={Your Azure Linux VM name}
        my_diagnostic_storage_account={Your Azure storage account for storing VM diagnostic data}

        my_vm_resource_id=$(az vm show -g $my_resource_group -n $my_linux_vm --query "id" -o tsv)

        default_config=$(az vm diagnostics get-default-config \\
            | sed "s#__DIAGNOSTIC_STORAGE_ACCOUNT__#$my_diagnostic_storage_account#g" \\
            | sed "s#__VM_OR_VMSS_RESOURCE_ID__#$my_vm_resource_id#g")

        storage_sastoken=$(az storage account generate-sas \\
            --account-name $my_diagnostic_storage_account --expiry 2037-12-31T23:59:00Z \\
            --permissions wlacu --resource-types co --services bt -o tsv)

        protected_settings="{'storageAccountName': '$my_diagnostic_storage_account', \\
            'storageAccountSasToken': '$storage_sastoken'}"

        az vm diagnostics set --settings "$default_config" \\
            --protected-settings "$protected_settings" \\
            --resource-group $my_resource_group --vm-name $my_linux_vm

  - name: Set up default diagnostics on a Windows VM.
    text: |
        # Set the following 3 parameters first.
        my_resource_group={Resource group name containing your Windows VM and the storage account}
        my_windows_vm={Your Azure Windows VM name}
        my_diagnostic_storage_account={Your Azure storage account for storing VM diagnostic data}

        my_vm_resource_id=$(az vm show -g $my_resource_group -n $my_windows_vm --query "id" -o tsv)

        default_config=$(az vm diagnostics get-default-config  --is-windows-os \\
            | sed "s#__DIAGNOSTIC_STORAGE_ACCOUNT__#$my_diagnostic_storage_account#g" \\
            | sed "s#__VM_OR_VMSS_RESOURCE_ID__#$my_vm_resource_id#g")

        # Please use the same options, the WAD diagnostic extension has strict
        # expectations of the sas token's format. Set the expiry as desired.
        storage_sastoken=$(az storage account generate-sas \\
            --account-name $my_diagnostic_storage_account --expiry 2037-12-31T23:59:00Z \\
            --permissions acuw --resource-types co --services bt --https-only --output tsv)

        protected_settings="{'storageAccountName': '$my_diagnostic_storage_account', \\
            'storageAccountSasToken': '$storage_sastoken'}"

        az vm diagnostics set --settings "$default_config" \\
            --protected-settings "$protected_settings" \\
            --resource-group $my_resource_group --vm-name $my_windows_vm

        # # Alternatively, if the WAD extension has issues parsing the sas token,
        # # one can use a storage account key instead.
        storage_account_key=$(az storage account keys list --account-name {my_storage_account} \\
          --query [0].value -o tsv)
        protected_settings="{'storageAccountName': '$my_diagnostic_storage_account', \\
          'storageAccountKey': '$storage_account_key'}"
"""

helps['vm disk'] = """
type: group
short-summary: Manage the managed data disks attached to a VM.
long-summary: >4

    Azure Virtual Machines use disks as a place to store an operating system, applications, and data.
    All Azure virtual machines have at least two disks: An operating system disk, and a temporary disk.
    The operating system disk is created from an image, and both the operating system disk and the image are actually virtual hard disks (VHDs)
    stored in an Azure storage account. Virtual machines also can have one or more data disks, that are also stored as VHDs.


    Azure Managed and Unmanaged Data Disks have a maximum size of 4095 GB (with the exception of larger disks in preview). Azure Unmanaged Disks also have a maximum capacity of 4095 GB.


    For more information, see:

    - Azure Disks - https://learn.microsoft.com/azure/virtual-machines/managed-disks-overview.

    - Larger Managed Disks in Public Preview - https://azure.microsoft.com/blog/introducing-the-public-preview-of-larger-managed-disks-sizes/

    - Ultra SSD Managed Disks in Public Preview - https://learn.microsoft.com/azure/virtual-machines/disks-types


"""

helps['vm disk attach'] = """
type: command
short-summary: Attach a managed persistent disk to a VM.
long-summary: This allows for the preservation of data, even if the VM is reprovisioned due to maintenance or resizing.
examples:
  - name: Attach a new default sized (1023 GB) managed data disk to a VM.
    text: az vm disk attach -g MyResourceGroup --vm-name MyVm --name disk_name --new
  - name: Attach a managed persistent disk to a VM. (autogenerated)
    text: |
        az vm disk attach --name $diskId --new --resource-group MyResourceGroup --size-gb 128 --sku Standard_LRS --vm-name MyVm
    crafted: true
  - name: Attach multiple managed disks to a VM.
    text: |
        az vm disk attach --vm-name MyVm --resource-group MyResourceGroup --sku Standard_LRS --disks diskId1 diskId2 diskId3
    crafted: true
"""

helps['vm disk detach'] = """
type: command
short-summary: Detach a managed disk from a VM.
examples:
  - name: Detach a data disk from a VM.
    text: >
        az vm disk detach -g MyResourceGroup --vm-name MyVm --name disk_name
  - name: Force detach a data disk from a VM.
    text: >
        az vm disk detach -g MyResourceGroup --vm-name MyVm --name disk_name --force-detach
"""

helps['vm encryption'] = """
type: group
short-summary: "Manage encryption of VM disks."
long-summary: |
    For more information, see:
    https://learn.microsoft.com/azure/security/fundamentals/azure-disk-encryption-vms-vmss
"""

helps['vm encryption disable'] = """
type: command
short-summary: Disable disk encryption on the OS disk and/or data disks. Decrypt mounted disks.
long-summary: |
    For Linux VMs, disabling encryption is only permitted on data volumes.
    For Windows VMs, disabling encryption is permitted on both OS and data volumes.
examples:
  - name: Disable disk encryption on the OS disk and/or data disks. (autogenerated)
    text: |
        az vm encryption disable --name MyVirtualMachine --resource-group MyResourceGroup --volume-type DATA
    crafted: true
"""

helps['vm encryption enable'] = """
type: command
short-summary: "Enable disk encryption on the OS disk and/or data disks. Encrypt mounted disks."
long-summary: |
    Note that Azure Active Directory / service principal arguments are unnecessary for vm encryption. The older version of Azure Disk Encryption required AAD arguments.
    For more information, see:
    https://learn.microsoft.com/azure/security/fundamentals/azure-disk-encryption-vms-vmss
parameters:
  - name: --aad-client-id
    short-summary: Client ID of an AAD app with permissions to write secrets to the key vault.
  - name: --aad-client-secret
    short-summary: Client secret of the AAD app with permissions to write secrets to the key vault.
  - name: --aad-client-cert-thumbprint
    short-summary: Thumbprint of the AAD app certificate with permissions to write secrets to the key vault.
examples:
  - name: encrypt a VM using a key vault in the same resource group
    text: >
        az vm encryption enable -g MyResourceGroup -n MyVm --disk-encryption-keyvault MyVault
  - name: Enable disk encryption on the OS disk and/or data disks. Encrypt mounted disks. (autogenerated)
    text: |
        az vm encryption enable --disk-encryption-keyvault MyVault --name MyVm --resource-group MyResourceGroup --volume-type DATA
  - name: Add support for using managed identity to authenticate to customer's keyvault for ADE operation
    text: >
        az vm encryption enable --disk-encryption-keyvault MyVault --name MyVm --resource-group MyResourceGroup --encryption-identity EncryptionIdentity
    crafted: true
"""

helps['vm encryption show'] = """
type: command
short-summary: Show encryption status.
examples:
  - name: Show encryption status. (autogenerated)
    text: |
        az vm encryption show --name MyVirtualMachine --resource-group MyResourceGroup
    crafted: true
"""

helps['vm extension image list'] = """
type: command
short-summary: List the information on available extensions.
examples:
  - name: List the unique publishers for extensions.
    text: az vm extension image list --query "[].publisher" -o tsv | sort -u
  - name: Find extensions with "Docker" in the name.
    text: az vm extension image list --query "[].name" -o tsv | sort -u | grep Docker
  - name: List extension names where the publisher name starts with "Microsoft.Azure.App".
    text: |
        az vm extension image list --query \\
            "[?starts_with(publisher, 'Microsoft.Azure.App')].publisher" \\
            -o tsv | sort -u | xargs -I{} az vm extension image list-names --publisher {} -l westus
"""

helps['vm extension list'] = """
type: command
short-summary: List the extensions attached to a VM.
examples:
  - name: List attached extensions to a named VM.
    text: az vm extension list -g MyResourceGroup --vm-name MyVm
"""

helps['vm extension set'] = """
type: command
short-summary: Set extensions for a VM.
long-summary: Get extension details from `az vm extension image list`.
examples:
  - name: Add a user account to a Linux VM.
    text: |
        az vm extension set -n VMAccessForLinux --publisher Microsoft.OSTCExtensions --version 1.4 \\
            --vm-name MyVm --resource-group MyResourceGroup \\
            --protected-settings '{"username":"user1", "ssh_key":"ssh_rsa ..."}'
  - name: Add a customScript extension to VM(s) specified by --ids.
    text: |
        az vm extension set -n customScript --publisher Microsoft.Azure.Extensions --ids {vm_id}
  - name: Add an extension and enable automatic upgrade by the platform if there is a newer version of the extension available.
    text: |
        az vm extension set -n extName --publisher publisher --vm-name MyVM -g MyResourceGroup \\
        --enable-auto-upgrade true
parameters:
  - name: --name -n
    populator-commands:
      - az vm extension image list
"""

helps['vm extension show'] = """
type: command
short-summary: Display information about extensions attached to a VM.
examples:
  - name: Use VM name and extension name to show the extensions attached to a VM.
    text: az vm extension show -g MyResourceGroup --vm-name MyVm -n extension_name
"""

helps['vm get-instance-view'] = """
type: command
short-summary: Get instance information about a VM.
examples:
  - name: Use a resource group and name to get instance view information of a VM.
    text: az vm get-instance-view -g MyResourceGroup -n MyVm
  - name: Get instance views for all VMs in a resource group.
    text: >
        az vm get-instance-view --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm host'] = """
type: group
short-summary: Manage Dedicated Hosts for Virtual Machines
"""

helps['vm host create'] = """
type: command
short-summary: Create a dedicated host.
examples:
  - name: Create a dedicated host. Ensure it auto replaces on failure
    text: |-
        az vm host create --host-group my-host-group --name my-host --platform-fault-domain 2 \\
            --auto-replace --resource-group my-resource-group --sku DSv3-Type1
  - name: Create a dedicated host in the 'east asia' region. Don't auto replace on failure.
    text: |-
        az vm host create --host-group my-host-group --name my-host --platform-fault-domain 0 \\
            --auto-replace false --resource-group my-resource-group --sku ESv3-Type1 --location eastasia
  - name: Create a dedicated host (autogenerated)
    text: |
        az vm host create --auto-replace true --host-group my-host-group --license-type None --location eastasia --name my-host --resource-group my-resource-group --sku DSv3-Type1
    crafted: true
"""

helps['vm host get-instance-view'] = """
type: command
short-summary: Get instance information about a dedicated host.
examples:
  - name: Get instance view information of a dedicated host.
    text: az vm host get-instance-view --host-group my-host-group --name my-host -g my-rg

  - name: Get instance views for all dedicated hosts in a host group.
    text: >
        az vm host get-instance-view --ids $(az vm host list -g my-rg --host-group my-host-group --query "[].id" -o tsv)
"""

helps['vm host group'] = """
type: group
short-summary: Manage Dedicated Host Groups
"""

helps['vm host group create'] = """
type: command
short-summary: Create a dedicated host group.
examples:
  - name: Create a dedicated host group. (autogenerated)
    text: |
        az vm host group create --name MyDedicatedHostGroup --platform-fault-domain-count 2 --resource-group MyResourceGroup
    crafted: true
"""

helps['vm host group get-instance-view'] = """
type: command
short-summary: Get instance view of a dedicated host group.
examples:
  - name: Get instance view of a dedicated host group
    text: |
        az vm host group get-instance-view --name MyDedicatedHostGroup --resource-group MyResourceGroup
"""

helps['vm host group update'] = """
type: command
short-summary: Update a dedicated host group.
"""

helps['vm host update'] = """
type: command
short-summary: Update a dedicated host.
examples:
  - name: Update the 'autoReplaceOnFailure' field of a dedicated host.
    text: |-
        az vm host update --host-group my-host-group --name my-host \\
            --resource-group my-resource-group --set autoReplaceOnFailure=True
"""

helps['vm identity'] = """
type: group
short-summary: manage service identities of a VM
"""

helps['vm identity assign'] = """
type: command
short-summary: Enable managed service identity on a VM.
long-summary: This is required to authenticate and interact with other Azure services using bearer tokens.
examples:
  - name: Enable the system assigned identity on a VM with the 'Reader' role.
    text: az vm identity assign -g MyResourceGroup -n MyVm --role Reader --scope /subscriptions/********-0000-0000-0000-********0000/resourceGroups/MyResourceGroup
  - name: Enable the system assigned identity and a user assigned identity on a VM with the 'Reader' role.
    text: az vm identity assign -g MyResourceGroup -n MyVm --role Reader --identities [system] myAssignedId --scope /subscriptions/********-0000-0000-0000-********0000/resourceGroups/MyResourceGroup
"""

helps['vm identity remove'] = """
type: command
short-summary: Remove managed service identities from a VM.
examples:
  - name: Remove the system assigned identity
    text: az vm identity remove -g MyResourceGroup -n MyVm
  - name: Remove a user assigned identity
    text: az vm identity remove -g MyResourceGroup -n MyVm --identities readerId
  - name: Remove 2 identities which are in the same resource group with the VM
    text: az vm identity remove -g MyResourceGroup -n MyVm --identities readerId writerId
  - name: Remove the system assigned identity and a user identity
    text: az vm identity remove -g MyResourceGroup -n MyVm --identities [system] readerId
"""

helps['vm identity show'] = """
type: command
short-summary: display VM's managed identity info.
examples:
  - name: display VM's managed identity info. (autogenerated)
    text: |
        az vm identity show --name MyVirtualMachine --resource-group MyResourceGroup
    crafted: true
"""

helps['vm application'] = """
type: group
short-summary: Manage applications for VM
"""

helps['vm application set'] = """
type: command
short-summary: Set applications for VM.
examples:
  - name: Set applications for vm
    text: az vm application set -g MyResourceGroup -n MyVm --app-version-ids /subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.0 \
/subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.1
  - name: Set applications of a vm with config
    text: az vm application set -g MyResourceGroup -n MyVm --app-version-ids /subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.0 \
/subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.1 \
--app-config-overrides https://mystorageaccount.blob.core.windows.net/configurations/settings.config null
"""

helps['vm application list'] = """
type: command
short-summary: List applications for VM
examples:
  - name: List applications for vm
    text: az vm application list -g MyResourceGroup -n MyVm
"""

helps['vm image'] = """
type: group
short-summary: Information on available virtual machine images.
"""

helps['vm image accept-terms'] = """
type: command
short-summary: Accept Azure Marketplace term so that the image can be used to create VMs
examples:
  - name: Accept Azure Marketplace term so that the image can be used to create VMs. (autogenerated)
    text: |
        az vm image accept-terms --urn publisher:offer:sku:version
    crafted: true
"""

helps['vm image list'] = """
type: command
short-summary: List the VM/VMSS images available in the Azure Marketplace.
parameters:
  - name: --all
    short-summary: Retrieve image list from live Azure service rather using an offline image list
  - name: --offer -f
    short-summary: Image offer name, partial name is accepted
  - name: --publisher -p
    short-summary: Image publisher name, partial name is accepted
  - name: --sku -s
    short-summary: Image sku name, partial name is accepted
examples:
  - name: List all available images.
    text: az vm image list --all
  - name: List all offline cached CentOS images.
    text: az vm image list -f CentOS
  - name: List all CentOS images.
    text: az vm image list -f CentOS --all
"""

helps['vm image list-offers'] = """
type: command
short-summary: List the VM image offers available in the Azure Marketplace.
parameters:
  - name: --publisher -p
    populator-commands:
      - az vm image list-publishers
examples:
  - name: List all offers from Microsoft in the West US region.
    text: az vm image list-offers -l westus -p MicrosoftWindowsServer
  - name: List all offers from OpenLocic in the West US region.
    text: az vm image list-offers -l westus -p OpenLogic
"""

helps['vm image list-publishers'] = """
type: command
short-summary: List the VM image publishers available in the Azure Marketplace.
examples:
  - name: List all publishers in the West US region.
    text: az vm image list-publishers -l westus
  - name: List all publishers with names starting with "Open" in westus.
    text: az vm image list-publishers -l westus --query "[?starts_with(name, 'Open')]"
"""

helps['vm image list-skus'] = """
type: command
short-summary: List the VM image SKUs available in the Azure Marketplace.
parameters:
  - name: --publisher -p
    populator-commands:
      - az vm image list-publishers
examples:
  - name: List all skus available for CentOS published by OpenLogic in the West US region.
    text: az vm image list-skus -l westus -f CentOS -p OpenLogic
"""

helps['vm image show'] = """
type: command
short-summary: Get the details for a VM image available in the Azure Marketplace.
examples:
  - name: Show information for the latest available CentOS image from OpenLogic.
    text: >
        latest=$(az vm image list -p OpenLogic -s 7.3 --all --query \\
            "[?offer=='CentOS'].version" -o tsv | sort -u | tail -n 1)
        az vm image show -l westus -f CentOS -p OpenLogic --sku 7.3 --version ${latest}
  - name: Get the details for a VM image available in the Azure Marketplace. (autogenerated)
    text: |
        az vm image show --location westus --urn publisher:offer:sku:version
    crafted: true
"""

helps['vm image terms'] = """
type: group
short-summary: Manage Azure Marketplace image terms.
"""

helps['vm image terms accept'] = """
type: command
short-summary: Accept Azure Marketplace image terms so that the image can be used to create VMs.
examples:
  - name: Accept Azure Marketplace image terms so that the image can be used to create VMs.
    text: az vm image terms accept --urn publisher:offer:sku:version
"""

helps['vm image terms cancel'] = """
type: command
short-summary: Cancel Azure Marketplace image terms.
examples:
  - name: Cancel Azure Marketplace image terms.
    text: az vm image terms cancel --urn publisher:offer:sku:version
  - name: Cancel Azure Marketplace image terms. (autogenerated)
    text: |
        az vm image terms cancel --subscription MySubscription --urn publisher:offer:sku:version
    crafted: true
"""

helps['vm image terms show'] = """
type: command
short-summary: Get the details of Azure Marketplace image terms.
examples:
  - name: Get the details of Azure Marketplace image terms.
    text: az vm image terms show --urn publisher:offer:sku:version
"""

helps['vm list'] = """
type: command
short-summary: List details of Virtual Machines.
long-summary: '`--resource-group` can pass in an empty string as a parameter, which will output all VM information under the subscription. For more information on querying information about Virtual Machines, see https://learn.microsoft.com/cli/azure/query-az-cli2'
examples:
  - name: List all VMs.
    text: az vm list
  - name: List all VMs by resource group.
    text: az vm list -g MyResourceGroup
  - name: List all VMs by resource group with details.
    text: az vm list -g MyResourceGroup -d
"""

helps['vm list-ip-addresses'] = """
type: command
short-summary: List IP addresses associated with a VM.
examples:
  - name: Get the IP addresses for a VM.
    text: az vm list-ip-addresses -g MyResourceGroup -n MyVm
  - name: Get IP addresses for all VMs in a resource group.
    text: >
        az vm list-ip-addresses --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm list-skus'] = """
type: command
short-summary: Get details for compute-related resource SKUs.
long-summary: This command incorporates subscription level restriction, offering the most accurate information.
examples:
  - name: List all SKUs in the West US region.
    text: az vm list-skus -l westus
  - name: List all available vm sizes in the East US2 region which support availability zone.
    text: az vm list-skus -l eastus2 --zone
  - name: List all available vm sizes in the East US2 region which support availability zone with name like "standard_ds1...".
    text: az vm list-skus -l eastus2 --zone --size standard_ds1
  - name: List availability set related sku information in The West US region.
    text: az vm list-skus -l westus --resource-type availabilitySets
"""

helps['vm list-usage'] = """
type: command
short-summary: List available usage resources for VMs.
examples:
  - name: Get the compute resource usage for the West US region.
    text: az vm list-usage -l westus
"""

helps['vm nic'] = """
type: group
short-summary: Manage network interfaces. See also `az network nic`.
long-summary: >
    A network interface (NIC) is the interconnection between a VM and the underlying software
    network. For more information, see https://learn.microsoft.com/azure/virtual-network/virtual-network-network-interface-overview.
"""

helps['vm nic add'] = """
type: command
short-summary: Add existing NICs to a VM.
examples:
  - name: Add two NICs to a VM.
    text: az vm nic add -g MyResourceGroup --vm-name MyVm --nics nic_name1 nic_name2
"""

helps['vm nic list'] = """
type: command
short-summary: List the NICs available on a VM.
examples:
  - name: List all of the NICs on a VM.
    text: az vm nic list -g MyResourceGroup --vm-name MyVm
"""

helps['vm nic remove'] = """
type: command
short-summary: Remove NICs from a VM.
examples:
  - name: Remove two NICs from a VM.
    text: az vm nic remove -g MyResourceGroup --vm-name MyVm --nics nic_name1 nic_name2
"""

helps['vm nic set'] = """
type: command
short-summary: Configure settings of a NIC attached to a VM.
examples:
  - name: Set a NIC on a VM to be the primary interface.
    text: az vm nic set -g MyResourceGroup --vm-name MyVm --nic nic_name1 nic_name2 --primary-nic nic_name2
  - name: Configure settings of a NIC attached to a VM. (autogenerated)
    text: |
        az vm nic set --nics nic_name1 nic_name2 --primary-nic nic_name2 --resource-group MyResourceGroup --vm-name MyVm
    crafted: true
"""

helps['vm nic show'] = """
type: command
short-summary: Display information for a NIC attached to a VM.
examples:
  - name: Show details of a NIC on a VM.
    text: az vm nic show -g MyResourceGroup --vm-name MyVm --nic nic_name1
"""

helps['vm open-port'] = """
type: command
short-summary: Opens a VM to inbound traffic on specified ports.
long-summary: >
    Adds a security rule to the network security group (NSG) that is attached to the VM's
    network interface (NIC) or subnet. The existing NSG will be used or a new one will be
    created. The rule name is 'open-port-{port}' and will overwrite an existing rule with
    this name. For multi-NIC VMs, or for more fine-grained control, use the appropriate
    network commands directly (nsg rule create, etc).
examples:
  - name: Open all ports on a VM to inbound traffic.
    text: az vm open-port -g MyResourceGroup -n MyVm --port '*'
  - name: Open a range of ports on a VM to inbound traffic with the highest priority.
    text: az vm open-port -g MyResourceGroup -n MyVm --port 80-100 --priority 100
  - name: Open ports 555, 557, 558, and 559 to inbound traffic with the highest priority.
    text: az vm open-port -g MyResourceGroup -n MyVm --port 555,557-559 --priority 100
  - name: Open all ports for all VMs in a resource group.
    text: >
        az vm open-port --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv) --port '*'

"""

helps['vm resize'] = """
type: command
short-summary: Update a VM's size.
parameters:
  - name: --size
    type: string
    short-summary: The VM size.
    populator-commands:
      - az vm list-vm-resize-options
examples:
  - name: Resize a VM.
    text: az vm resize -g MyResourceGroup -n MyVm --size Standard_DS3_v2
  - name: Resize all VMs in a resource group.
    text: >
        az vm resize --size Standard_DS3_v2 --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm restart'] = """
type: command
short-summary: Restart VMs.
examples:
  - name: Restart a VM.
    text: az vm restart -g MyResourceGroup -n MyVm
  - name: Restart all VMs in a resource group.
    text: >
        az vm restart --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm run-command'] = """
type: group
short-summary: Manage run commands on a Virtual Machine.
long-summary: 'For more information, see https://learn.microsoft.com/azure/virtual-machines/windows/run-command or https://learn.microsoft.com/azure/virtual-machines/linux/run-command.'
"""

helps['vm run-command invoke'] = """
type: command
short-summary: Execute a specific run command on a vm.
long-summary: >
    `az vm run-command show` returns helpful information on each run-command.
    Discover Run command-id's via `az vmss run-command list`
parameters:
  - name: --command-id
    type: string
    short-summary: The command id
    populator-commands:
      - az vm run-command list
examples:
  - name: Install nginx on a linux VM.
    text: az vm run-command invoke -g MyResourceGroup -n MyVm --command-id RunShellScript --scripts "sudo apt-get update && sudo apt-get install -y nginx"
  - name: Run shell command on a linux VM with parameters.
    text: az vm run-command invoke -g MyResourceGroup -n MyVm --command-id RunShellScript --scripts 'echo $1 $2' --parameters hello world
  - name: Run powershell script on a windows VM with parameters. Script supplied inline. Be wary of single-quoting in CMD.exe.
    text: |-
        az vm run-command invoke  --command-id RunPowerShellScript --name win-vm -g my-resource-group  \\
            --scripts 'param([string]$arg1,[string]$arg2)' \\
            'Write-Host This is a sample script with parameters $arg1 and $arg2' \\
            --parameters 'arg1=somefoo' 'arg2=somebar'
  - name: Run powershell script on a windows VM with parameters. Script supplied from file.
    text: |-
        # script.ps1
        #   param(
        #       [string]$arg1,
        #       [string]$arg2
        #   )
        #   Write-Host This is a sample script with parameters $arg1 and $arg2

        az vm run-command invoke  --command-id RunPowerShellScript --name win-vm -g my-resource-group \\
            --scripts @script.ps1 --parameters "arg1=somefoo" "arg2=somebar"
"""

helps['vm run-command show'] = """
type: command
short-summary: Get specific run command
long-summary: You can specify "--resource-group", "--run-command-name" and "--vm-name" to get run command in a virtual machine. Or you can specify "--command-id" and "--location" to get run command for a subscription in a location.
examples:
  - name: Get the run commands in a virtual machine.
    text: |-
           az vm run-command show --resource-group "myResourceGroup" --run-command-name \
"myRunCommand" --vm-name "myVM"
  - name: Get specific run command for a subscription in a location.
    text: |-
           az vm run-command show --command-id "RunPowerShellScript" --location "SoutheastAsia"
"""


helps['vm run-command create'] = """
type: command
short-summary: "The operation to create the run command."
parameters:
  - name: --script
    short-summary: "Specify the script content to be executed on the VM."
  - name: --script-uri
    short-summary: "Specify the script download location."
  - name: --command-id
    short-summary: "Specify a commandId of predefined built-in script."
  - name: --output-blob-uri
    short-summary: "Specify the Azure storage blob (SAS URI) where script output stream will be uploaded."
  - name: --parameters
    short-summary: "The parameters used by the script."
    long-summary: |
        Usage: --parameters arg1=XX arg2=XX
  - name: --protected-parameters
    short-summary: "The parameters used by the script."
    long-summary: |
        Usage: --protected-parameters credentials=somefoo secret=somebar
examples:
  - name: Create a run command.
    text: |-
           az vm run-command create --resource-group "myResourceGroup" --location "West US" \
--async-execution false --parameters arg1=param1 arg2=value1 --run-as-password "<runAsPassword>" \
--run-as-user "user1" --script "Write-Host Hello World!" --timeout-in-seconds 3600 \
--run-command-name "myRunCommand" --vm-name "myVM"
  - name: Create a run command with uploading script output stream to Azure storage blob (SAS URI).
    text: |-
           az vm run-command create --resource-group "myResourceGroup" --location "West US" \
--script "Write-Host Hello World!" --run-command-name "myRunCommand" --vm-name "myVM" --output-blob-uri \
"*******************************************************************************?sp=racw&st=2022-10-17T19:02:15Z&se=2022-10-18T03:02:15Z&spr=https&sv=2021-06-08&sr=b&sig=3BxtEasfdasdfasdfdYki9yvYsqc60V0%3D"
"""

helps['vm run-command update'] = """
type: command
short-summary: "The operation to update the run command."
parameters:
  - name: --script
    short-summary: "Specify the script content to be executed on the VM."
  - name: --script-uri
    short-summary: "Specify the script download location."
  - name: --command-id
    short-summary: "Specify a commandId of predefined built-in script."
  - name: --output-blob-uri
    short-summary: "Specify the Azure storage blob (SAS URI) where script output stream will be uploaded."
  - name: --parameters
    short-summary: "The parameters used by the script."
    long-summary: |
        Usage: --parameters arg1=XX arg2=XX
  - name: --protected-parameters
    short-summary: "The parameters used by the script."
    long-summary: |
        Usage: --protected-parameters credentials=somefoo secret=somebar
examples:
  - name: Update a run command.
    text: |-
           az vm run-command update --resource-group "myResourceGroup" --location "West US" \
--async-execution false --parameters arg1=param1 arg2=value1 --run-as-password "<runAsPassword>" \
--run-as-user "user1" --script "Write-Host Hello World!" --timeout-in-seconds 3600 \
--run-command-name "myRunCommand" --vm-name "myVM"
  - name: Update a run command with uploading script output stream to Azure storage blob (SAS URI).
    text: |-
           az vm run-command update --resource-group "myResourceGroup" --location "West US" \
--script "Write-Host Hello World!" --run-command-name "myRunCommand" --vm-name "myVM" --output-blob-uri \
"*******************************************************************************?sp=racw&st=2022-10-17T19:02:15Z&se=2022-10-18T03:02:15Z&spr=https&sv=2021-06-08&sr=b&sig=3BxtEasfdasdfasdfdYki9yvYsqc60V0%3D"
"""

helps['vm run-command delete'] = """
type: command
short-summary: "The operation to delete the run command."
examples:
  - name: Delete a run command.
    text: |-
           az vm run-command delete --resource-group "myResourceGroup" --run-command-name \
"myRunCommand" --vm-name "myVM"
"""

helps['vm run-command wait'] = """
type: command
short-summary: Place the CLI in a waiting state until a condition of the res virtual-machine-run-command is met.
"""

helps['vm run-command list'] = """
type: command
short-summary: List run commands from a VM or a location
long-summary: You can specify "--resource-group" and "--vm-name" to get all run commands of a virtual machine. Or you can specify "--location" to list all available run commands for a subscription in a location.
examples:
  - name: List run commands in a virtual machine.
    text: |-
           az vm run-command list --resource-group "myResourceGroup" --vm-name "myVM"
  - name: List all available run commands for a subscription in a location.
    text: |-
           az vm run-command list --location "SoutheastAsia"
"""

helps['vm secret'] = """
type: group
short-summary: Manage VM secrets.
"""

helps['vm secret add'] = """
type: command
short-summary: Add a secret to a VM.
long-summary: 'To install certificates on a virtual machine it is recommended to use the [Azure Key Vault virtual machine extension for Linux](https://learn.microsoft.com/azure/virtual-machines/extensions/key-vault-linux) or the [Azure Key Vault virtual machine extension for Windows](https://learn.microsoft.com/azure/virtual-machines/extensions/key-vault-windows) instead of `az vm secret add`.'
examples:
  - name: Add a secret to a VM. (autogenerated)
    text: |
        az vm secret add --certificate {certificate} --keyvault {keyvault} --name MyVirtualMachine --resource-group MyResourceGroup
    crafted: true
"""

helps['vm secret format'] = """
type: command
short-summary: Transform secrets into a form that can be used by VMs and VMSSes.
parameters:
  - name: --secrets -s
    long-summary: >
        The command will attempt to resolve the vault ID for each secret. If it is unable to do so,
        specify the vault ID to use for *all* secrets using: --keyvault NAME --resource-group NAME | --keyvault ID.
examples:
  - name: Create a self-signed certificate with the default policy, and add it to a virtual machine.
    text: >
        az keyvault certificate create --vault-name vaultname -n cert1 \\
          -p "$(az keyvault certificate get-default-policy)"

        secrets=$(az keyvault secret list-versions --vault-name vaultname \\
          -n cert1 --query "[?attributes.enabled].id" -o tsv)

        vm_secrets=$(az vm secret format -s "$secrets")

        az vm create -g group-name -n vm-name --admin-username deploy  \\
          --image Debian11 --secrets "$vm_secrets"
"""

helps['vm secret list'] = """
type: command
short-summary: List secrets on a VM.
examples:
  - name: List secrets on a VM. (autogenerated)
    text: |
        az vm secret list --name MyVirtualMachine --resource-group MyResourceGroup
    crafted: true
"""

helps['vm secret remove'] = """
type: command
short-summary: Remove a secret from a VM.
"""

helps['vm show'] = """
type: command
short-summary: Get the details of a VM.
examples:
  - name: Show information about a VM.
    text: az vm show -g MyResourceGroup -n MyVm -d
  - name: Get the details for all VMs in a resource group.
    text: >
        az vm show -d --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm stop'] = """
type: command
short-summary: Power off (stop) a running VM.
long-summary: The VM will continue to be billed. To avoid this, you can deallocate the VM through "az vm deallocate"
examples:
  - name: Power off (stop) a running VM.
    text: az vm stop --resource-group MyResourceGroup --name MyVm
  - name: Power off a running VM without shutting down.
    text: az vm stop --resource-group MyResourceGroup --name MyVm --skip-shutdown
  - name: Power off VMs in a resource group.
    text: >
        az vm stop --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm unmanaged-disk'] = """
type: group
short-summary: Manage the unmanaged data disks attached to a VM.
long-summary: >4

    Azure Virtual Machines use disks as a place to store an operating system, applications, and data.
    All Azure virtual machines have at least two disks: An operating system disk, and a temporary disk.
    The operating system disk is created from an image, and both the operating system disk and the image are actually virtual hard disks (VHDs)
    stored in an Azure storage account. Virtual machines also can have one or more data disks, that are also stored as VHDs.


    Azure Managed and Unmanaged Data Disks have a maximum size of 4095 GB (with the exception of larger disks in preview). Azure Unmanaged Disks also have a maximum capacity of 4095 GB.


    For more information, see:

    - Azure Disks - https://learn.microsoft.com/azure/virtual-machines/managed-disks-overview.

    - Larger Managed Disks in Public Preview - https://azure.microsoft.com/blog/introducing-the-public-preview-of-larger-managed-disks-sizes/

    - Ultra SSD Managed Disks in Public Preview - https://learn.microsoft.com/azure/virtual-machines/disks-types


"""

helps['vm unmanaged-disk attach'] = """
type: command
short-summary: Attach an unmanaged persistent disk to a VM.
long-summary: This allows for the preservation of data, even if the VM is reprovisioned due to maintenance or resizing.
examples:
  - name: Attach a new default sized (1023 GB) unmanaged data disk to a VM.
    text: az vm unmanaged-disk attach -g MyResourceGroup --vm-name MyVm --new
  - name: Attach an existing data disk to a VM as unmanaged.
    text: >
        az vm unmanaged-disk attach -g MyResourceGroup --vm-name MyVm --name MyDataDisk \\
            --vhd-uri https://mystorage.blob.core.windows.net/vhds/d1.vhd
  - name: Attach an unmanaged persistent disk to a VM. (autogenerated)
    text: |
        az vm unmanaged-disk attach --name MyDataDisk --new --resource-group MyResourceGroup --size-gb 50 --vm-name MyVm
    crafted: true
"""

helps['vm unmanaged-disk detach'] = """
type: command
short-summary: Detach an unmanaged disk from a VM.
examples:
  - name: Detach a data disk from a VM.
    text: >
        az vm unmanaged-disk detach -g MyResourceGroup --vm-name MyVm -n disk_name
"""

helps['vm unmanaged-disk list'] = """
type: command
short-summary: List unmanaged disks of a VM.
examples:
  - name: List the unmanaged disks attached to a VM.
    text: az vm unmanaged-disk list -g MyResourceGroup --vm-name MyVm
  - name: List unmanaged disks with names containing the string "data_disk".
    text: >
        az vm unmanaged-disk list -g MyResourceGroup --vm-name MyVm \\
            --query "[?contains(name, 'data_disk')]" --output table
"""

helps['vm update'] = """
type: command
short-summary: Update the properties of a VM.
long-summary: Update VM objects and properties using paths that correspond to 'az vm show'.
examples:
  - name: Add or update a tag.
    text: az vm update -n name -g group --set tags.tagName=tagValue
  - name: Remove a tag.
    text: az vm update -n name -g group --remove tags.tagName
  - name: Set the primary NIC of a VM.
    text: az vm update -n name -g group --set networkProfile.networkInterfaces[1].primary=false networkProfile.networkInterfaces[0].primary=true
  - name: Add a new non-primary NIC to a VM.
    text: az vm update -n name -g group --add networkProfile.networkInterfaces primary=false id={NIC_ID}
  - name: Remove the fourth NIC from a VM.
    text: az vm update -n name -g group --remove networkProfile.networkInterfaces 3
  - name: Add an existing VM to a dedicated host
    text: |-
        az vm deallocate -n name -g group
        az vm update -n name -g group --host my-host
        az vm start -n name -g group
  - name: Add an existing VM to a dedicated host group
    text: |-
        az vm deallocate -n name -g group
        az vm update -n name -g group --host-group my-host-group
        az vm start -n name -g group
"""

helps['vm user'] = """
type: group
short-summary: Manage user accounts for a VM.
"""

helps['vm user delete'] = """
type: command
short-summary: Delete a user account from a VM.
long-summary: >
    Also deletes the user home directory on Linux VMs.
examples:
  - name: Delete a user account.
    text: az vm user delete -u username -n MyVm -g MyResourceGroup
  - name: Delete a user on all VMs in a resource group.
    text: >
        az vm user delete -u username --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm user reset-ssh'] = """
type: command
short-summary: Reset the SSH configuration on a VM.
long-summary: >
    The extension will restart the SSH service, open the SSH port on your VM, and reset the SSH configuration to default values. The user account (name, password, and SSH keys) are not changed.
examples:
  - name: Reset the SSH configuration.
    text: az vm user reset-ssh -n MyVm -g MyResourceGroup
  - name: Reset the SSH server on all VMs in a resource group.
    text: >
        az vm user reset-ssh --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm user update'] = """
type: command
short-summary: Update a user account for VM. You can use it to update password or ssh key value for VM user.
long-summary: >
    This command uses VMAccessForLinux 1.5 for Linux operating system and VMAccessAgent 2.4 for Window operating system.
parameters:
  - name: --ssh-key-value
    short-summary: SSH public key file value or public key file path. This command appends the new public key text to the ~/.ssh/authorized_keys file for the admin user on the VM. This does not replace or remove any existing SSH keys.
examples:
  - name: Update a Windows user account. If username does not exist, a new user will be created.
    text: az vm user update -u username -p password -n MyVm -g MyResourceGroup
  - name: Update a Linux user account. ("$(< filename)" syntax is not supported on Command Prompt or PowerShell.)
    text: az vm user update -u username --ssh-key-value "$(< ~/.ssh/id_rsa.pub)" -n MyVm -g MyResourceGroup
  - name: Update a user on all VMs in a resource group. ("$(< filename)" syntax is not supported on Command Prompt or PowerShell.)
    text: >
        az vm user update -u username --ssh-key-value "$(< ~/.ssh/id_rsa.pub)" --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vm wait'] = """
type: command
short-summary: Place the CLI in a waiting state until a condition of the VM is met.
examples:
  - name: Wait until a VM is created.
    text: az vm wait -g MyResourceGroup -n MyVm --created
  - name: Wait until all VMs in a resource group are deleted.
    text: >
        az vm wait --deleted --ids $(az vm list -g MyResourceGroup --query "[].id" -o tsv)

"""

helps['vmss'] = """
type: group
short-summary: Manage groupings of virtual machines in an Azure Virtual Machine Scale Set (VMSS).
"""

helps['vmss create'] = """
type: command
short-summary: Create an Azure Virtual Machine Scale Set.
long-summary: 'For an end-to-end tutorial, see https://learn.microsoft.com/azure/virtual-machine-scale-sets/virtual-machine-scale-sets-linux-create-cli.'
parameters:
  - name: --image
    type: string
    short-summary: >
        The name of the operating system image as a URN alias, URN, custom image name or ID, or VHD blob URI. In addition, it also supports shared gallery image.
        Please use the image alias including the version of the distribution you want to use. For example: please use Debian11 instead of Debian.'
        This parameter is required unless using `--attach-os-disk.` Valid URN format: "Publisher:Offer:Sku:Version". For more information, see https://learn.microsoft.com/azure/virtual-machines/linux/cli-ps-findimage
    populator-commands:
      - az vm image list
      - az vm image show
      - az sig image-version show-shared
  - name: --ssh-key-values
    short-summary: Space-separated list of SSH public keys or public key file paths.
examples:
  - name: Create a Windows VM scale set with 5 instances, a load balancer, a public IP address, a 2GB data disk and 40GB OS disk.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --instance-count 5 --image Win2016Datacenter --data-disk-sizes-gb 2 --os-disk-size-gb 40
  - name: Create a Linux VM scale set with an auto-generated ssh key pair, a public IP address, a DNS entry, an existing load balancer, and an existing virtual network.
    text: |
        az vmss create -n MyVmss -g MyResourceGroup --public-ip-address-dns-name my-globally-dns-name \\
            --load-balancer MyLoadBalancer --vnet-name MyVnet --subnet MySubnet --image Ubuntu2204 \\
            --generate-ssh-keys
  - name: Create a Linux VM scale set from a custom image using the default existing public SSH key.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image MyImage
  - name: Create a Linux VM scale set with a load balancer and custom DNS servers. Each VM has a public-ip address and a custom domain name.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image CentOS85Gen2 \\
            --public-ip-per-vm --vm-domain-name myvmss --dns-servers ******** ********
  - name: 'Create a Linux VM scale set using a cloud-init script for configuration. See: https://learn.microsoft.com/azure/virtual-machines/linux/using-cloud-init'
    text: >
        az vmss create -g MyResourceGroup -n MyVmss --image Debian11 --custom-data MyCloudInitScript.yml
  - name: Create a VMSS from a generalized gallery image version.
    text: >
        az vmss create -g MyResourceGroup -n MyVmss --image /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0
  - name: Create a VMSS from a specialized gallery image version.
    text: >
        az vmss create -g MyResourceGroup -n MyVmss --image /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0 --specialized
  - name: Create a VMSS from the latest version of a gallery image
    text: >
        az vmss create -g MyResourceGroup -n MyVmss --image /subscriptions/********-0000-0000-0000-********0000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage
  - name: Create a Debian11 VM scaleset using Key Vault secrets.
    text: >
        az keyvault certificate create --vault-name vaultname -n cert1 \\
          -p "$(az keyvault certificate get-default-policy)"

        secrets=$(az keyvault secret list-versions --vault-name vaultname \\
          -n cert1 --query "[?attributes.enabled].id" -o tsv)

        vm_secrets=$(az vm secret format -s "$secrets")


        az vmss create -g group-name -n vm-name --admin-username deploy  \\
          --image Debian11 --secrets "$vm_secrets"
  - name: Create a VM scaleset with system assigned identity. The VM will have a 'Contributor' Role with access to a storage account.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image CentOS85Gen2 --assign-identity --scope /subscriptions/********-1bf0-4dda-aec3-cb9272f09590/MyResourceGroup/myRG/providers/Microsoft.Storage/storageAccounts/storage1 --role Contributor
  - name: Create a Debian11 VM scaleset with a user assigned identity.
    text: >
        az vmss create -n MyVmss -g rg1 --image Debian11 --assign-identity  /subscriptions/********-1bf0-4dda-aec3-cb9272f09590/resourcegroups/myRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/myID
  - name: Create a vmss with user assigned identity and add encryption identity for Azure disk encryption
    text: >
        az vmss create -n MyVm -g rg1 --image Debian11 --assign-identity myID --encryption-identity /subscriptions/********-0000-0000-0000-********0000/resourcegroups/myRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/myID --orchestration-mode Uniform --lb-sku Standard
  - name: Create a Debian11 VM scaleset with both system and user assigned identity.
    text: >
        az vmss create -n MyVmss -g rg1 --image Debian11 --assign-identity  [system] /subscriptions/********-1bf0-4dda-aec3-cb9272f09590/resourcegroups/myRG/providers/Microsoft.ManagedIdentity/userAssignedIdentities/myID
  - name: Create a single zone VM scaleset in the current resource group's region
    supported-profiles: latest
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image CentOS85Gen2 --zones 1
  - name: Create a VMSS that supports SpotRestore.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup  --location NorthEurope --instance-count 2 --image CentOS85Gen2 --priority Spot --eviction-policy Deallocate --single-placement-group --enable-spot-restore True --spot-restore-timeout PT1H
  - name: Create a VMSS from shared gallery image.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image /SharedGalleries/{gallery_unique_name}/Images/{image}/Versions/{version}
  - name: Create a VMSS from community gallery image.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image /CommunityGalleries/{gallery_unique_name}/Images/{image}/Versions/{version}
  - name: Create a Windows VMSS with patch mode 'Manual' (Currently patch mode 'AutomaticByPlatform' is not supported during VMSS creation as health extension which is required for 'AutomaticByPlatform' mode cannot be set during VMSS creation).
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image Win2019Datacenter --enable-agent --enable-auto-update false --patch-mode Manual --orchestration-mode Flexible
  - name: Create a VMSS with specifying the security posture to be used for all virtual machines in the scale set.
    text: >
        az vmss create -n MyVmss -g MyResourceGroup --image /CommunityGalleries/{gallery_unique_name}/Images/{image}/Versions/{version} --security-posture-reference-id /CommunityGalleries/{communityGalleryName}/securityPostures/{securityPostureName}/versions/{version} \\
            --security-posture-reference-exclude-extensions "c:\\tmp\\exclude_extensions.json"
"""

helps['vmss deallocate'] = """
type: command
short-summary: Deallocate VMs within a VMSS.
examples:
  - name: Deallocate VMs within a VMSS. (autogenerated)
    text: |
        az vmss deallocate --instance-ids 1 --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""


helps['vmss diagnostics'] = """
type: group
short-summary: Configure the Azure Virtual Machine Scale Set diagnostics extension.
"""

helps['vmss diagnostics get-default-config'] = """
type: command
short-summary: Show the default config file which defines data to be collected.
"""

helps['vmss diagnostics set'] = """
type: command
short-summary: Enable diagnostics on a VMSS.
examples:
  - name: Enable diagnostics on a VMSS. (autogenerated)
    text: |
        az vmss diagnostics set --protected-settings {protected-settings} --resource-group MyResourceGroup --settings '{"commandToExecute": "echo testing"}' --vmss-name MyVmss
    crafted: true
"""

helps['vmss disk'] = """
type: group
short-summary: Manage data disks of a VMSS.
"""

helps['vmss disk attach'] = """
type: command
short-summary: Attach managed data disks to a scale set or its instances.
examples:
  - name: Attach managed data disks to a scale set or its instances. (autogenerated)
    text: |
        az vmss disk attach --disk {disk} --instance-id 0 --resource-group MyResourceGroup
    crafted: true
  - name: Attach managed data disks of a given size to a scale set or its instances. (autogenerated)
    text: |
        az vmss disk attach --vmss-name MyVmss --resource-group MyResourceGroup --size-gb 50
    crafted: true
"""

helps['vmss disk detach'] = """
type: command
short-summary: Detach managed data disks from a scale set or its instances.
examples:
  - name: Detach managed data disks from a scale set or its instances. (autogenerated)
    text: |
        az vmss disk detach --instance-id 0 --lun {lun} --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss encryption'] = """
type: group
short-summary: "Manage encryption of VMSS."
long-summary: "For more information, see: ttps://learn.microsoft.com/azure/security/fundamentals/azure-disk-encryption-vms-vmss"
"""

helps['vmss encryption disable'] = """
type: command
short-summary: Disable the encryption on a VMSS with managed disks.
examples:
  - name: disable encryption a VMSS
    text: >
        az vmss encryption disable -g MyResourceGroup -n MyVm
"""

helps['vmss encryption enable'] = """
type: command
short-summary: "Encrypt a VMSS with managed disks."
long-summary: "For more information, see: For more information, see: ttps://learn.microsoft.com/azure/security/fundamentals/azure-disk-encryption-vms-vmss"
examples:
  - name: encrypt a VM scale set using a key vault in the same resource group
    text: >
        az vmss encryption enable -g MyResourceGroup -n MyVmss --disk-encryption-keyvault MyVault
  - name: Add support for using managed identity to authenticate to customer's keyvault for ADE operation
    text: >
        az vmss encryption enable --disk-encryption-keyvault MyVault --name MyVm --resource-group MyResourceGroup --encryption-identity EncryptionIdentity
  - name: Encrypt a VMSS with managed disks. (autogenerated)
    text: |
        az vmss encryption enable --disk-encryption-keyvault MyVault --name MyVmss --resource-group MyResourceGroup --volume-type DATA
    crafted: true
"""

helps['vmss encryption show'] = """
type: command
short-summary: Show encryption status.
examples:
  - name: Show encryption status. (autogenerated)
    text: |
        az vmss encryption show --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss extension'] = """
type: group
short-summary: Manage extensions on a VM scale set.
"""

helps['vmss extension delete'] = """
type: command
short-summary: Delete an extension from a VMSS.
examples:
  - name: Delete an extension from a VMSS. (autogenerated)
    text: |
        az vmss extension delete --name MyExtension --resource-group MyResourceGroup --vmss-name MyVmss
    crafted: true
"""

helps['vmss extension image'] = """
type: group
short-summary: Find the available VM extensions for a subscription and region.
"""

helps['vmss extension image list'] = """
type: command
short-summary: List the information on available extensions.
examples:
  - name: List the unique publishers for extensions.
    text: az vmss extension image list --query "[].publisher" -o tsv | sort -u
  - name: Find extensions with "Docker" in the name.
    text: az vmss extension image list --query "[].name" -o tsv | sort -u | grep Docker
  - name: List extension names where the publisher name starts with "Microsoft.Azure.App".
    text: |
        az vmss extension image list --query \\
            "[?starts_with(publisher, 'Microsoft.Azure.App')].publisher" \\
            -o tsv | sort -u | xargs -I{} az vmss extension image list-names --publisher {} -l westus
"""

helps['vmss extension list'] = """
type: command
short-summary: List extensions associated with a VMSS.
examples:
  - name: List extensions associated with a VMSS. (autogenerated)
    text: |
        az vmss extension list --resource-group MyResourceGroup --vmss-name MyVmss
    crafted: true
"""

helps['vmss extension set'] = """
type: command
short-summary: Add an extension to a VMSS or update an existing extension.
long-summary: Get extension details from `az vmss extension image list`.
parameters:
  - name: --name -n
    populator-commands:
      - az vm extension image list
examples:
  - name: >
        Set an extension which depends on two previously set extensions. That is, When a VMSS instance is created or reimaged, the customScript extension will be provisioned only after all extensions that it depends on have been provisioned. The extension need not depend on the other extensions for pre-requisite configurations.
    text: >
        az vmss extension set --vmss-name my-vmss --name customScript --resource-group my-group \\
            --version 2.0 --publisher Microsoft.Azure.Extensions \\
            --provision-after-extensions NetworkWatcherAgentLinux VMAccessForLinux  \\
            --settings '{"commandToExecute": "echo testing"}'
  - name: Add an extension and enable automatic upgrade by the platform if there is a newer version of the extension available.
    text: >
        az vmss extension set -n extName --publisher publisher --vmss-name my-vmss -g my-group \\
        --enable-auto-upgrade true
"""

helps['vmss extension show'] = """
type: command
short-summary: Show details on a VMSS extension.
examples:
  - name: Show details on a VMSS extension. (autogenerated)
    text: |
        az vmss extension show --name MyExtension --resource-group MyResourceGroup --vmss-name MyVmss
    crafted: true
"""

helps['vmss extension upgrade'] = """
type: command
short-summary: Upgrade all extensions for all VMSS instances to the latest version.
examples:
  - name: Upgrade all extensions to the latest version.
    text: az vmss extension upgrade -g MyResourceGroup -n MyVmss
"""

helps['vmss get-instance-view'] = """
type: command
short-summary: View an instance of a VMSS.
parameters:
  - name: --instance-id
    short-summary: A VM instance ID or "*" to list instance view for all VMs in a scale set.

examples:
  - name: View an instance of a VMSS. (autogenerated)
    text: |
        az vmss get-instance-view --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss identity'] = """
type: group
short-summary: manage service identities of a VM scaleset.
"""

helps['vmss identity assign'] = """
type: command
short-summary: Enable managed service identity on a VMSS.
long-summary: This is required to authenticate and interact with other Azure services using bearer tokens.
examples:
  - name: Enable system assigned identity on a VMSS with the 'Owner' role.
    text: az vmss identity assign -g MyResourceGroup -n MyVmss --role Owner --scope /subscriptions/********-0000-0000-0000-********0000/resourceGroups/MyResourceGroup
  - name: Enable system assigned identity and a user assigned identity on a VMSS with the 'Owner' role.
    text: az vmss identity assign -g MyResourceGroup -n MyVmss --role Owner --identities [system] myAssignedId --scope /subscriptions/********-0000-0000-0000-********0000/resourceGroups/MyResourceGroup
  - name: Enable managed service identity on a VMSS. (autogenerated)
    text: |
        az vmss identity assign --identities readerId writerId --name MyVmss --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss identity remove'] = """
type: command
short-summary: Remove user assigned identities from a VM scaleset.
examples:
  - name: Remove system assigned identity
    text: az vmss identity remove -g MyResourceGroup -n MyVmss
  - name: Remove 2 identities which are in the same resource group with the VM scaleset
    text: az vmss identity remove -g MyResourceGroup -n MyVmss --identities readerId writerId
  - name: Remove system assigned identity and a user identity
    text: az vmss identity remove -g MyResourceGroup -n MyVmss --identities [system] readerId
"""

helps['vmss identity show'] = """
type: command
short-summary: display VM scaleset's managed identity info.
examples:
  - name: display VM scaleset's managed identity info. (autogenerated)
    text: |
        az vmss identity show --name MyVirtualMachine --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss application'] = """
type: group
short-summary: manage applications for VM scale set.
"""

helps['vmss application set'] = """
type: command
short-summary: Set applications for VMSS.
examples:
  - name: Set applications for vmss
    text: az vmss application set -g MyResourceGroup -n MyVmss --app-version-ids /subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.0 \
/subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.1
  - name: Set applications for vmss with config
    text: az vmss application set -g MyResourceGroup -n MyVmss --app-version-ids /subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.0 \
/subscriptions/subid/resourceGroups/MyResourceGroup/providers/Microsoft.Compute/galleries/myGallery1/applications/MyApplication1/versions/1.1 \
--app-config-overrides https://mystorageaccount.blob.core.windows.net/configurations/settings.config null
"""

helps['vmss application list'] = """
type: command
short-summary: List applications for VMSS
examples:
  - name: List applications for vmss
    text: az vmss application list -g MyResourceGroup --name MyVmss
"""

helps['vmss list-instance-connection-info'] = """
type: command
short-summary: Get the IP address and port number used to connect to individual VM instances within a set.
examples:
  - name: Get the IP address and port number used to connect to individual VM instances within a set. (autogenerated)
    text: |
        az vmss list-instance-connection-info --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss list-instance-public-ips'] = """
type: command
short-summary: List public IP addresses of VM instances within a set.
examples:
  - name: List public IP addresses of VM instances within a set. (autogenerated)
    text: |
        az vmss list-instance-public-ips --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss reimage'] = """
type: command
short-summary: Reimage VMs within a VMSS.
examples:
  - name: Reimage a VM instance within a VMSS.
    text: |
        az vmss reimage --instance-ids 1 --name MyScaleSet --resource-group MyResourceGroup --subscription MySubscription
  - name: Reimage a batch of VM instances within a VMSS.
    text: |
        az vmss reimage --instance-ids 1 2 3 --name MyScaleSet --resource-group MyResourceGroup --subscription MySubscription
  - name: Reimage all the VM instances within a VMSS.
    text: |
        az vmss reimage --name MyScaleSet --resource-group MyResourceGroup --subscription MySubscription
"""

helps['vmss restart'] = """
type: command
short-summary: Restart VMs within a VMSS.
examples:
  - name: Restart VMs within a VMSS. (autogenerated)
    text: |
        az vmss restart --instance-ids 1 --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss run-command'] = """
type: group
short-summary: Manage run commands on a Virtual Machine Scale Set.
long-summary: 'For more information, see https://learn.microsoft.com/azure/virtual-machines/windows/run-command or https://learn.microsoft.com/azure/virtual-machines/linux/run-command.'
"""

helps['vmss run-command invoke'] = """
type: command
short-summary: Execute a specific run command on a Virtual Machine Scale Set instance.
long-summary: >
    `az vmss run-command show` returns helpful information on each run-command.
    Discover Run command-id's via `az vmss run-command list`
parameters:
  - name: --command-id
    type: string
    short-summary: The command id
    populator-commands:
      - az vmss run-command list
  - name: --instance-id
    short-summary: Scale set VM instance id.
examples:
  - name: Install nginx on a VMSS instance.
    text: az vmss run-command invoke -g MyResourceGroup -n MyVMSS --command-id RunShellScript \\
            --instance-id 0 --scripts "sudo apt-get update && sudo apt-get install -y nginx"
  - name: Invoke a run-command with parameters on a VMSS instance.
    text: az vmss run-command invoke -g MyResourceGroup -n MyVMSS --command-id RunShellScript \\
            --instance-id 4 --scripts 'echo $1 $2' --parameters hello world
  - name: 'Invoke command on all VMSS instances using the VMSS instance resource IDs. Note: "@-" expands to stdin.'
    text: |-
        az vmss list-instances -n MyVMSS -g my-rg --query "[].id" --output tsv | \\
        az vmss run-command invoke --scripts 'echo $1 $2' --parameters hello world  \\
            --command-id RunShellScript --ids @-
  - name: Run powershell script on a windows VMSS instance with parameters. Script supplied inline. Be wary of single-quoting in CMD.exe.
    text: |-
        az vmss run-command invoke  --command-id RunPowerShellScript --name win-vm -g my-resource-group \\
            --scripts 'param([string]$arg1,[string]$arg2)' \\
            'Write-Host This is a sample script with parameters $arg1 and $arg2' \\
            --parameters 'arg1=somefoo' 'arg2=somebar' --instance-id 2
  - name: Run powershell script on a windows VMSS instance with parameters. Script supplied from file.
    text: |-
        # script.ps1
        #   param(
        #       [string]$arg1,
        #       [string]$arg2
        #   )
        #   Write-Host This is a sample script with parameters $arg1 and $arg2

        az vmss run-command invoke  --command-id RunPowerShellScript --name win-vm -g my-resource-group \\
            --scripts @script.ps1 --parameters "arg1=somefoo" "arg2=somebar" --instance-id 5
"""

helps['vmss run-command create'] = """
    type: command
    short-summary: "The operation to Create the VMSS VM run command."
    parameters:
      - name: --script
        short-summary: "Specify the script content to be executed on the VM."
      - name: --script-uri
        short-summary: "Specify the script download location."
      - name: --command-id
        short-summary: "Specify a commandId of predefined built-in script."
      - name: --parameters
        short-summary: "The parameters used by the script."
        long-summary: |
            Usage: --parameters arg1=XX arg2=XX
      - name: --protected-parameters
        short-summary: "The parameters used by the script."
        long-summary: |
            Usage: --protected-parameters credentials=somefoo secret=somebar
    examples:
      - name: Create VMSS run command.
        text: |-
               az vmss run-command create --resource-group "myResourceGroup" --instance-id "0" --location "West US" \
--async-execution false --parameters arg1=param1 arg2=value1 --run-as-password "<runAsPassword>" \
--run-as-user "user1" --script "Write-Host Hello World!" --timeout-in-seconds 3600 \
--run-command-name "myRunCommand" --vmss-name "myVMSS"
"""

helps['vmss run-command update'] = """
    type: command
    short-summary: "The operation to update the VMSS run command."
    parameters:
      - name: --script
        short-summary: "Specify the script content to be executed on the VM."
      - name: --script-uri
        short-summary: "Specify the script download location."
      - name: --command-id
        short-summary: "Specify a commandId of predefined built-in script."
      - name: --parameters
        short-summary: "The parameters used by the script."
        long-summary: |
            Usage: --parameters arg1=XX arg2=XX
      - name: --protected-parameters
        short-summary: "The parameters used by the script."
        long-summary: |
            Usage: --protected-parameters credentials=somefoo secret=somebar
    examples:
      - name: Update VMSS run command.
        text: |-
               az vmss run-command update --resource-group "myResourceGroup" --instance-id "0" --location "West US" \
--async-execution false --parameters arg1=param1 arg2=value1 --run-as-password "<runAsPassword>" \
--run-as-user "user1" --script "Write-Host Hello World!" --timeout-in-seconds 3600 \
--run-command-name "myRunCommand" --vmss-name "myVMSS"
"""

helps['vmss run-command delete'] = """
    type: command
    short-summary: "The operation to delete the run command."
    examples:
      - name: The operation to delete the VMSS run command..
        text: |-
               az vmss run-command delete --resource-group "myResourceGroup" --instance-id "0" --run-command-name \
"myRunCommand" --vmss-name "myVMSS"
"""

helps['vmss run-command list'] = """
    type: command
    short-summary: "The operation to get all run commands of an instance in Virtual Machine Scaleset."
    examples:
      - name: List run commands in Vmss instance.
        text: |-
               az vmss run-command list --resource-group "myResourceGroup" --vmss-name "myVMSS" --instance-id "0"
"""

helps['vmss run-command show'] = """
    type: command
    short-summary: "The operation to get the VMSS run command."
    examples:
      - name: Get VMSS run commands.
        text: |-
               az vmss run-command show --instance-id "0" --resource-group \
"myResourceGroup" --run-command-name "myRunCommand" --vmss-name "myVMSS"
"""

helps['vmss scale'] = """
type: command
short-summary: Change the number of VMs within a VMSS.
parameters:
  - name: --new-capacity
    short-summary: Number of VMs in the VMSS.
examples:
  - name: Change the number of VMs within a VMSS. (autogenerated)
    text: |
        az vmss scale --name MyScaleSet --new-capacity 6 --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss show'] = """
type: command
short-summary: Get details on VMs within a VMSS.
parameters:
  - name: --instance-id
    short-summary: VM instance ID. If missing, show the VMSS.
examples:
  - name: Get details on VMs within a VMSS. (autogenerated)
    text: |
        az vmss show --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss stop'] = """
type: command
short-summary: Power off (stop) VMs within a VMSS.
long-summary: The VMs will continue to be billed. To avoid this, you can deallocate VM instances within a VMSS through "az vmss deallocate"
examples:
  - name: Power off VMs within a VMSS without shutting down.
    text: az vmss stop --name MyScaleSet --resource-group MyResourceGroup --skip-shutdown
  - name: Power off (stop) VMs within a VMSS. (autogenerated)
    text: |
        az vmss stop --instance-ids 1 --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss update'] = """
type: command
short-summary: Update a VMSS. Run 'az vmss update-instances' command to roll out the changes to VMs if you have not configured upgrade policy.
examples:
  - name: Update a VMSS' license type for Azure Hybrid Benefit.
    text: az vmss update --name MyScaleSet --resource-group MyResourceGroup --license-type windows_server
  - name: Update a VM instance's protection policies.
    text: az vmss update --name MyScaleSet --resource-group MyResourceGroup --instance-id 4 --protect-from-scale-set-actions False --protect-from-scale-in
  - name: Update a VM instance's protection policies.
    text: az vmss update --name MyScaleSet --resource-group MyResourceGroup --instance-id 4 --set protectionPolicy.protectFromScaleIn=True protectionPolicy.protectFromScaleSetActions=False
  - name: Update a VM instance's Read-Write IOPS of the managed disk.
    text: az vmss update --name MyScaleSet --resource-group MyResourceGroup --set virtualMachineProfile.storageProfile.dataDisks[0].diskIOPSReadWrite=444
  - name: Update a VM instance's bandwidth in MB per second of the managed disk.
    text: az vmss update --name MyScaleSet --resource-group MyResourceGroup --set virtualMachineProfile.storageProfile.dataDisks[0].diskMBpsReadWrite=66
  - name: Update a VM to use a custom image.
    text: az vmss update --name MyScaleSet --resource-group MyResourceGroup --set virtualMachineProfile.storageProfile.imageReference.id=imageID
  - name: Update a Linux VMSS to patch mode 'AutomaticByPlatform'.
    text: az vmss update -n MyVmss -g MyResourceGroup --set virtualMachineProfile.osProfile.linuxConfiguration.patchSettings.patchMode=AutomaticByPlatform
  - name: Update a VMSS with specifying the security posture to be used for all virtual machines in the scale set.
    text: >
        az vmss update -n MyVmss -g MyResourceGroup --security-posture-reference-id /CommunityGalleries/{communityGalleryName}/securityPostures/{securityPostureName}/versions/{version} \\
            --security-posture-reference-exclude-extensions "c:\\tmp\\exclude_extensions.json"
"""

helps['vmss update-instances'] = """
type: command
short-summary: Upgrade VMs within a VMSS.
examples:
  - name: Upgrade VMs within a VMSS. (autogenerated)
    text: |
        az vmss update-instances --instance-ids 1 --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss wait'] = """
type: command
short-summary: Place the CLI in a waiting state until a condition of a scale set is met.
examples:
  - name: Place the CLI in a waiting state until the VMSS has been updated.
    text: az vmss wait --updated --name MyScaleSet --resource-group MyResourceGroup
  - name: Place the CLI in a waiting state until the VMSS instance has been updated.
    text: az vmss wait --updated --instance-id 1 --name MyScaleSet --resource-group MyResourceGroup
  - name: Place the CLI in a waiting state until a condition of a scale set is met. (autogenerated)
    text: |
        az vmss wait --created --name MyScaleSet --resource-group MyResourceGroup
    crafted: true
"""

helps['vmss set-orchestration-service-state'] = """
type: command
short-summary: Change ServiceState property for a given service within a VMSS.
examples:
  - name: Change ServiceState property for AutomaticRepairs
    text: az vmss set-orchestration-service-state --service-name AutomaticRepairs --action Resume --name MyScaleSet --resource-group MyResourceGroup
"""

helps['vm monitor'] = """
type: group
short-summary: Manage monitor aspect for a vm.
"""

helps['vm monitor log'] = """
type: group
short-summary: Manage log analytics workspace for a vm.
"""

helps['vm monitor log show'] = """
type: command
short-summary: Execute a query against the Log Analytics workspace linked with a VM.
examples:
  - name: Get performance log for a VM linked with a workspace
    text: >
        az vm monitor log show --name myVM -g myRG -q "Perf | limit 10"
"""

helps['vm monitor metrics'] = """
type: group
short-summary: Manage metrics for a vm.
"""

helps['vm monitor metrics tail'] = """
type: command
short-summary: List the metric values for a VM.
parameters:
  - name: --aggregation
    short-summary: The list of aggregation types (space-separated) to retrieve.
    populator-commands:
      - az vm monitor metrics list-definitions -n MyVM -g MyRG --query "@[*].supportedAggregationTypes"
  - name: --interval
    short-summary: >
        The interval over which to aggregate metrics, in ##h##m format.
  - name: --filter
    short-summary: A string used to reduce the set of metric data returned. eg. "LUN eq '*'"
    long-summary: 'For a full list of filters, see the filter string reference at https://learn.microsoft.com/rest/api/monitor/metrics/list'
  - name: --metadata
    short-summary: Return the metadata values instead of metric data
  - name: --dimension
    short-summary: The list of dimensions (space-separated) the metrics are queried into.
    populator-commands:
      - az vm monitor metrics list-definitions -n MyVM -g MyRG --query "@[*].dimensions"
  - name: --namespace
    short-summary: Namespace to query metric definitions for.
  - name: --offset
    short-summary: >
        Time offset of the query range, in ##d##h format.
    long-summary: >
        Can be used with either --start-time or --end-time. If used with --start-time, then
        the end time will be calculated by adding the offset. If used with --end-time (default), then
        the start time will be calculated by subtracting the offset. If --start-time and --end-time are
        provided, then --offset will be ignored.
  - name: --metrics
    short-summary: >
        Space-separated list of metric names to retrieve.
    populator-commands:
      - az vm monitor metrics list-definitions -n MyVM -g MyRG --query "@[*].name.value"
examples:
  - name: List CPU usage of VM for past one hour
    text: >
        az vm monitor metrics tail --name myVM -g myRG --metric "Percentage CPU"
  - name: List one hour CPU usage of VM started at 2019-12-18T00:00:00Z
    text: >
        az vm monitor metrics tail --name myVM -g myRG --metric "Percentage CPU" --start-time 2019-12-18T00:00:00Z
  - name: List CPU usage of VM for past one hour with filter
    text: >
        az vm monitor metrics tail --name myVM -g myRG --metrics "Per Disk Read Bytes/sec" --filter "SlotId eq '*'"
"""

helps['vm monitor metrics list-definitions'] = """
type: command
short-summary: List the metric definitions for a VM.
examples:
  - name: List the metric definitions for a VM. (autogenerated)
    text: |
        az vm monitor metrics list-definitions --name MyIDVirtualMachine --resource-group MyResourceGroup
    crafted: true
"""

helps['capacity reservation group create'] = """
type: command
short-summary: Create capacity reservation group.
examples:
  - name: Create a capacity reservation group.
    text: az capacity reservation group create -n ReservationGroupName -g MyResourceGroup
  - name: Create a capacity reservation group with specific zones.
    text: |
        az capacity reservation group create -n ReservationGroupName -l centraluseuap \\
            -g MyResourceGroup --tags key=val --zones 1 2
"""

helps['capacity reservation group update'] = """
type: command
short-summary: Update capacity reservation group.
examples:
  - name: Update a capacity reservation group.
    text: az capacity reservation group update -n ReservationGroupName -g MyResourceGroup --tags key=val
"""

helps['capacity reservation group show'] = """
type: command
short-summary: Show capacity reservation group.
examples:
  - name: Get a capacity reservation group.
    text: az capacity reservation group show -n ReservationGroupName -g MyResourceGroup
  - name: Get a capacity reservation group containing the instance views of the capacity reservations under the capacity reservation group
    text: az capacity reservation group show -n ReservationGroupName -g MyResourceGroup --instance-view
"""

helps['restore-point'] = """
    type: group
    short-summary: Manage restore point with res
"""

helps['restore-point show'] = """
    type: command
    short-summary: "Get the restore point."
    examples:
      - name: Get a restore point
        text: |-
               az restore-point show --resource-group "myResourceGroup" --collection-name "rpcName" --name "rpName"
"""

helps['restore-point create'] = """
    type: command
    short-summary: "Create the restore point. Updating properties of an existing restore point is not allowed."
    parameters:
      - name: --exclude-disks
        short-summary: "List of disk resource ids that the customer wishes to exclude from the restore point. If no \
disks are specified, all disks will be included."
        long-summary: |
            Usage: --exclude-disks XX XX
            id: The ARM resource id in the form of /subscriptions/{SubscriptionId}/resourceGroups/{ResourceGroupName}/.\
..
            Multiple actions can be specified by using more than one --exclude-disks argument.
    examples:
      - name: Create a restore point
        text: |-
               az restore-point create --exclude-disks "/subscriptions/{subscription-id}/resourceGroups/myResour\
               ceGroup/providers/Microsoft.Compute/disks/disk123" --resource-group "myResourceGroup" \
               --collection-name "rpcName" --name "rpName"
      - name: Create a restore point with --consistency-mode CrashConsistent
        text: |-
               az vm create -n vm -g rg --image Ubuntu2204 --tag EnableCrashConsistentRestorePoint=True

               az restore-point collection create --source-id "/subscriptions/{subscription-id}/resourceGroups/myResourceGroup/providers/Microsoft.Compute/virtualMachines/myVM"\
                -g rg --collection-name "myRpc"

               az restore-point create --exclude-disks "/subscriptions/{subscription-id}/resourceGroups/myResourceGroup/providers/Microsoft.Compute/disks/disk123" \
               --resource-group "myResourceGroup" --collection-name "rpcName" --name "rpName"
"""

helps['restore-point wait'] = """
    type: command
    short-summary: Place the CLI in a waiting state until a condition of the restore-point is met.
    parameters:
      - name: --expand
        short-summary: The expand expression to apply on the operation. 'InstanceView' retrieves information
                       about the run-time state of a restore point. 'instanceView' Default value is None.
    examples:
      - name: Pause executing next line of CLI script until the restore-point is successfully created.
        text: |-
               az restore-point wait --resource-group "myResourceGroup" --collection-name "rpcName" \
--name "rpName" --created
      - name: Pause executing next line of CLI script until the restore-point is successfully deleted.
        text: |-
               az restore-point wait --resource-group "myResourceGroup" --collection-name "rpcName" \
--name "rpName" --deleted
"""

helps['restore-point collection show'] = """
    type: command
    short-summary: "Get the restore point collection."
    examples:
      - name: Get a restore point collection (but not the restore points contained in the restore point collection)
        text: |-
               az restore-point collection show --resource-group "myResourceGroup" --collection-name "myRpc"
      - name: Get a restore point collection, including the restore points contained in the restore point collection
        text: |-
               az restore-point collection show --resource-group "myResourceGroup" --collection-name "rpcName"
"""

helps['restore-point collection create'] = """
    type: command
    short-summary: "Create the restore point collection. Please refer to https://aka.ms/RestorePoints \
for more details. When updating a restore point collection, only tags may be modified."
    examples:
      - name: Create or update a restore point collection.
        text: |-
               az restore-point collection create --location "norwayeast" --source-id "/subscriptions/{subscription-id}\
/resourceGroups/myResourceGroup/providers/Microsoft.Compute/virtualMachines/myVM" --tags myTag1="tagValue1" \
--resource-group "myResourceGroup" --collection-name "myRpc"
"""

helps['restore-point collection update'] = """
    type: command
    short-summary: "Update the restore point collection."
"""

helps['restore-point collection wait'] = """
    type: command
    short-summary: Place the CLI in a waiting state until a condition of the restore-point-collection is met.
    parameters:
      - name: --expand
        short-summary: The expand expression to apply on the operation. If expand=restorePoints, server will return all
                       contained restore points in the restorePointCollection. "restorePoints" Default value is None.
    examples:
      - name: Pause executing next line of CLI script until the restore-point-collection is successfully deleted.
        text: |-
               az restore-point collection wait --resource-group "myResourceGroup" --collection-name "rpcName" --deleted
"""
