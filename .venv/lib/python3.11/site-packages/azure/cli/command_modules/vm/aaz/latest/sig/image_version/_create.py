# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class Create(AAZCommand):
    """Create a gallery image version.
    """

    _aaz_info = {
        "version": "2024-03-03",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/galleries/{}/images/{}/versions/{}", "2024-03-03"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.gallery_image_definition = AAZStrArg(
            options=["-i", "--gallery-image-definition"],
            help="The name of the gallery image definition in which the Image Version resides.",
            required=True,
        )
        _args_schema.gallery_image_version_name = AAZStrArg(
            options=["-e", "--gallery-image-version", "--gallery-image-version-name"],
            help="The name of the gallery image version to be deleted.",
            required=True,
        )
        _args_schema.gallery_name = AAZStrArg(
            options=["-r", "--gallery-name"],
            help="The name of the Shared Image Gallery in which the Image Definition resides.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "GalleryImageVersion"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="GalleryImageVersion",
            help="Resource location",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="GalleryImageVersion",
            help="Resource tags",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.publishing_profile = AAZObjectArg(
            options=["--publishing-profile"],
            arg_group="Properties",
            help="The publishing profile of a gallery image Version.",
        )
        _args_schema.restore = AAZBoolArg(
            options=["--restore"],
            arg_group="Properties",
            help="Indicates if this is a soft-delete resource restoration request.",
        )
        _args_schema.safety_profile = AAZObjectArg(
            options=["--safety-profile"],
            arg_group="Properties",
            help="This is the safety profile of the Gallery Image Version.",
        )
        _args_schema.security_profile = AAZObjectArg(
            options=["--security-profile"],
            arg_group="Properties",
            help="The security profile of a gallery image version",
        )
        _args_schema.storage_profile = AAZObjectArg(
            options=["--storage-profile"],
            arg_group="Properties",
            help="This is the storage profile of a Gallery Image Version.",
        )

        publishing_profile = cls._args_schema.publishing_profile
        publishing_profile.end_of_life_date = AAZDateTimeArg(
            options=["end-of-life-date"],
            help="The end of life date of the gallery image version. This property can be used for decommissioning purposes. This property is updatable.",
        )
        publishing_profile.exclude_from_latest = AAZBoolArg(
            options=["exclude-from-latest"],
            help="If set to true, Virtual Machines deployed from the latest version of the Image Definition won't use this Image Version.",
        )
        publishing_profile.replica_count = AAZIntArg(
            options=["replica-count"],
            help="The number of replicas of the Image Version to be created per region. This property would take effect for a region when regionalReplicaCount is not specified. This property is updatable.",
        )
        publishing_profile.replication_mode = AAZStrArg(
            options=["replication-mode"],
            help="Optional parameter which specifies the mode to be used for replication. This property is not updatable.",
            enum={"Full": "Full", "Shallow": "Shallow"},
        )
        publishing_profile.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type to be used to store the image. This property is not updatable.",
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )
        publishing_profile.target_extended_locations = AAZListArg(
            options=["target-extended-locations"],
            help="The target extended locations where the Image Version is going to be replicated to. This property is updatable.",
        )
        publishing_profile.target_regions = AAZListArg(
            options=["target-regions"],
            help="The target regions where the Image Version is going to be replicated to. This property is updatable.",
        )

        target_extended_locations = cls._args_schema.publishing_profile.target_extended_locations
        target_extended_locations.Element = AAZObjectArg()

        _element = cls._args_schema.publishing_profile.target_extended_locations.Element
        _element.encryption = AAZObjectArg(
            options=["encryption"],
        )
        cls._build_args_encryption_images_create(_element.encryption)
        _element.extended_location = AAZObjectArg(
            options=["extended-location"],
            help="The name of the extended location.",
        )
        _element.extended_location_replica_count = AAZIntArg(
            options=["extended-location-replica-count"],
            help="The number of replicas of the Image Version to be created per extended location. This property is updatable.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the region.",
        )
        _element.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type to be used to store the image. This property is not updatable.",
            enum={"Premium_LRS": "Premium_LRS", "StandardSSD_LRS": "StandardSSD_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        extended_location = cls._args_schema.publishing_profile.target_extended_locations.Element.extended_location
        extended_location.name = AAZStrArg(
            options=["name"],
        )
        extended_location.type = AAZStrArg(
            options=["type"],
            help="It is type of the extended location.",
            enum={"EdgeZone": "EdgeZone", "Unknown": "Unknown"},
        )

        target_regions = cls._args_schema.publishing_profile.target_regions
        target_regions.Element = AAZObjectArg()

        _element = cls._args_schema.publishing_profile.target_regions.Element
        _element.additional_replica_sets = AAZListArg(
            options=["additional-replica-sets"],
            help="List of storage sku with replica count to create direct drive replicas.",
        )
        _element.encryption = AAZObjectArg(
            options=["encryption"],
            help="Optional. Allows users to provide customer managed keys for encrypting the OS and data disks in the gallery artifact.",
        )
        cls._build_args_encryption_images_create(_element.encryption)
        _element.exclude_from_latest = AAZBoolArg(
            options=["exclude-from-latest"],
            help="Contains the flag setting to hide an image when users specify version='latest'",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the region.",
            required=True,
        )
        _element.regional_replica_count = AAZIntArg(
            options=["regional-replica-count"],
            help="The number of replicas of the Image Version to be created per region. This property is updatable.",
        )
        _element.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type to be used to store the image. This property is not updatable.",
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        additional_replica_sets = cls._args_schema.publishing_profile.target_regions.Element.additional_replica_sets
        additional_replica_sets.Element = AAZObjectArg()

        _element = cls._args_schema.publishing_profile.target_regions.Element.additional_replica_sets.Element
        _element.regional_replica_count = AAZIntArg(
            options=["regional-replica-count"],
            help="The number of direct drive replicas of the Image Version to be created.This Property is updatable",
        )
        _element.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type to be used to create the direct drive replicas",
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        safety_profile = cls._args_schema.safety_profile
        safety_profile.allow_deletion_of_replicated_locations = AAZBoolArg(
            options=["allow-deletion-of-replicated-locations"],
            help="Indicates whether or not removing this Gallery Image Version from replicated regions is allowed.",
        )
        safety_profile.block_deletion_before_end_of_life = AAZBoolArg(
            options=["block-deletion-before-end-of-life"],
            help="Indicates whether or not the deletion is blocked for this Gallery Image Version if its End Of Life has not expired.",
        )

        security_profile = cls._args_schema.security_profile
        security_profile.uefi_settings = AAZObjectArg(
            options=["uefi-settings"],
            help="Contains UEFI settings for the image version.",
        )

        uefi_settings = cls._args_schema.security_profile.uefi_settings
        uefi_settings.additional_signatures = AAZObjectArg(
            options=["additional-signatures"],
            help="Additional UEFI key signatures that will be added to the image in addition to the signature templates",
        )
        uefi_settings.signature_template_names = AAZListArg(
            options=["signature-template-names"],
            help="The name of the template(s) that contains default UEFI key signatures that will be added to the image.",
        )

        additional_signatures = cls._args_schema.security_profile.uefi_settings.additional_signatures
        additional_signatures.db = AAZListArg(
            options=["db"],
            help="The database of UEFI keys for this image version.",
        )
        additional_signatures.dbx = AAZListArg(
            options=["dbx"],
            help="The database of revoked UEFI keys for this image version.",
        )
        additional_signatures.kek = AAZListArg(
            options=["kek"],
            help="The Key Encryption Keys of this image version.",
        )
        additional_signatures.pk = AAZObjectArg(
            options=["pk"],
            help="The Platform Key of this image version.",
        )
        cls._build_args_uefi_key_create(additional_signatures.pk)

        db = cls._args_schema.security_profile.uefi_settings.additional_signatures.db
        db.Element = AAZObjectArg()
        cls._build_args_uefi_key_create(db.Element)

        dbx = cls._args_schema.security_profile.uefi_settings.additional_signatures.dbx
        dbx.Element = AAZObjectArg()
        cls._build_args_uefi_key_create(dbx.Element)

        kek = cls._args_schema.security_profile.uefi_settings.additional_signatures.kek
        kek.Element = AAZObjectArg()
        cls._build_args_uefi_key_create(kek.Element)

        signature_template_names = cls._args_schema.security_profile.uefi_settings.signature_template_names
        signature_template_names.Element = AAZStrArg(
            enum={"MicrosoftUefiCertificateAuthorityTemplate": "MicrosoftUefiCertificateAuthorityTemplate", "MicrosoftWindowsTemplate": "MicrosoftWindowsTemplate", "NoSignatureTemplate": "NoSignatureTemplate"},
        )

        storage_profile = cls._args_schema.storage_profile
        storage_profile.data_disk_images = AAZListArg(
            options=["data-disk-images"],
            help="A list of data disk images.",
        )
        storage_profile.os_disk_image = AAZObjectArg(
            options=["os-disk-image"],
            help="This is the OS disk image.",
        )
        storage_profile.source = AAZObjectArg(
            options=["source"],
            help="The source of the gallery artifact version.",
        )

        data_disk_images = cls._args_schema.storage_profile.data_disk_images
        data_disk_images.Element = AAZObjectArg()

        _element = cls._args_schema.storage_profile.data_disk_images.Element
        _element.host_caching = AAZStrArg(
            options=["host-caching"],
            help="The host caching of the disk. Valid values are 'None', 'ReadOnly', and 'ReadWrite'",
            enum={"None": "None", "ReadOnly": "ReadOnly", "ReadWrite": "ReadWrite"},
        )
        _element.lun = AAZIntArg(
            options=["lun"],
            help="This property specifies the logical unit number of the data disk. This value is used to identify data disks within the Virtual Machine and therefore must be unique for each data disk attached to the Virtual Machine.",
            required=True,
        )
        _element.source = AAZObjectArg(
            options=["source"],
        )
        cls._build_args_gallery_disk_image_source_create(_element.source)

        os_disk_image = cls._args_schema.storage_profile.os_disk_image
        os_disk_image.host_caching = AAZStrArg(
            options=["host-caching"],
            help="The host caching of the disk. Valid values are 'None', 'ReadOnly', and 'ReadWrite'",
            enum={"None": "None", "ReadOnly": "ReadOnly", "ReadWrite": "ReadWrite"},
        )
        os_disk_image.source = AAZObjectArg(
            options=["source"],
            help="The source for the disk image.",
        )
        cls._build_args_gallery_disk_image_source_create(os_disk_image.source)

        source = cls._args_schema.storage_profile.source
        source.community_gallery_image_id = AAZStrArg(
            options=["community-gallery-image-id"],
            help="The resource Id of the source Community Gallery Image.  Only required when using Community Gallery Image as a source.",
        )
        source.id = AAZResourceIdArg(
            options=["id"],
            help="The id of the gallery artifact version source. Can specify a disk uri, snapshot uri, user image or storage account resource.",
        )
        source.virtual_machine_id = AAZResourceIdArg(
            options=["virtual-machine-id"],
            help="The resource Id of the source virtual machine.  Only required when capturing a virtual machine to source this Gallery Image Version.",
        )
        return cls._args_schema

    _args_encryption_images_create = None

    @classmethod
    def _build_args_encryption_images_create(cls, _schema):
        if cls._args_encryption_images_create is not None:
            _schema.data_disk_images = cls._args_encryption_images_create.data_disk_images
            _schema.os_disk_image = cls._args_encryption_images_create.os_disk_image
            return

        cls._args_encryption_images_create = AAZObjectArg()

        encryption_images_create = cls._args_encryption_images_create
        encryption_images_create.data_disk_images = AAZListArg(
            options=["data-disk-images"],
            help="A list of encryption specifications for data disk images.",
        )
        encryption_images_create.os_disk_image = AAZObjectArg(
            options=["os-disk-image"],
            help="Contains encryption settings for an OS disk image.",
        )

        data_disk_images = cls._args_encryption_images_create.data_disk_images
        data_disk_images.Element = AAZObjectArg()

        _element = cls._args_encryption_images_create.data_disk_images.Element
        _element.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="A relative URI containing the resource ID of the disk encryption set.",
        )
        _element.lun = AAZIntArg(
            options=["lun"],
            help="This property specifies the logical unit number of the data disk. This value is used to identify data disks within the Virtual Machine and therefore must be unique for each data disk attached to the Virtual Machine.",
            required=True,
        )

        os_disk_image = cls._args_encryption_images_create.os_disk_image
        os_disk_image.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="A relative URI containing the resource ID of the disk encryption set.",
        )
        os_disk_image.security_profile = AAZObjectArg(
            options=["security-profile"],
            help="This property specifies the security profile of an OS disk image.",
        )

        security_profile = cls._args_encryption_images_create.os_disk_image.security_profile
        security_profile.confidential_vm_encryption_type = AAZStrArg(
            options=["confidential-vm-encryption-type"],
            help="confidential VM encryption types",
            enum={"EncryptedVMGuestStateOnlyWithPmk": "EncryptedVMGuestStateOnlyWithPmk", "EncryptedWithCmk": "EncryptedWithCmk", "EncryptedWithPmk": "EncryptedWithPmk", "NonPersistedTPM": "NonPersistedTPM"},
        )
        security_profile.secure_vm_disk_encryption_set_id = AAZStrArg(
            options=["secure-vm-disk-encryption-set-id"],
            help="secure VM disk encryption set id",
        )

        _schema.data_disk_images = cls._args_encryption_images_create.data_disk_images
        _schema.os_disk_image = cls._args_encryption_images_create.os_disk_image

    _args_gallery_disk_image_source_create = None

    @classmethod
    def _build_args_gallery_disk_image_source_create(cls, _schema):
        if cls._args_gallery_disk_image_source_create is not None:
            _schema.id = cls._args_gallery_disk_image_source_create.id
            _schema.storage_account_id = cls._args_gallery_disk_image_source_create.storage_account_id
            _schema.uri = cls._args_gallery_disk_image_source_create.uri
            return

        cls._args_gallery_disk_image_source_create = AAZObjectArg()

        gallery_disk_image_source_create = cls._args_gallery_disk_image_source_create
        gallery_disk_image_source_create.id = AAZResourceIdArg(
            options=["id"],
            help="The id of the gallery artifact version source. Can specify a disk uri, snapshot uri, user image or storage account resource.",
        )
        gallery_disk_image_source_create.storage_account_id = AAZResourceIdArg(
            options=["storage-account-id"],
            help="The Storage Account Id that contains the vhd blob being used as a source for this artifact version.",
        )
        gallery_disk_image_source_create.uri = AAZStrArg(
            options=["uri"],
            help="The uri of the gallery artifact version source. Currently used to specify vhd/blob source.",
        )

        _schema.id = cls._args_gallery_disk_image_source_create.id
        _schema.storage_account_id = cls._args_gallery_disk_image_source_create.storage_account_id
        _schema.uri = cls._args_gallery_disk_image_source_create.uri

    _args_uefi_key_create = None

    @classmethod
    def _build_args_uefi_key_create(cls, _schema):
        if cls._args_uefi_key_create is not None:
            _schema.type = cls._args_uefi_key_create.type
            _schema.value = cls._args_uefi_key_create.value
            return

        cls._args_uefi_key_create = AAZObjectArg()

        uefi_key_create = cls._args_uefi_key_create
        uefi_key_create.type = AAZStrArg(
            options=["type"],
            help="The type of key signature.",
            enum={"sha256": "sha256", "x509": "x509"},
        )
        uefi_key_create.value = AAZListArg(
            options=["value"],
            help="The value of the key signature.",
        )

        value = cls._args_uefi_key_create.value
        value.Element = AAZStrArg()

        _schema.type = cls._args_uefi_key_create.type
        _schema.value = cls._args_uefi_key_create.value

    def _execute_operations(self):
        self.pre_operations()
        yield self.GalleryImageVersionsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class GalleryImageVersionsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/images/{galleryImageName}/versions/{galleryImageVersionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryImageName", self.ctx.args.gallery_image_definition,
                    required=True,
                ),
                **self.serialize_url_param(
                    "galleryImageVersionName", self.ctx.args.gallery_image_version_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-03-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("publishingProfile", AAZObjectType, ".publishing_profile")
                properties.set_prop("restore", AAZBoolType, ".restore")
                properties.set_prop("safetyProfile", AAZObjectType, ".safety_profile")
                properties.set_prop("securityProfile", AAZObjectType, ".security_profile")
                properties.set_prop("storageProfile", AAZObjectType, ".storage_profile", typ_kwargs={"flags": {"required": True}})

            publishing_profile = _builder.get(".properties.publishingProfile")
            if publishing_profile is not None:
                publishing_profile.set_prop("endOfLifeDate", AAZStrType, ".end_of_life_date")
                publishing_profile.set_prop("excludeFromLatest", AAZBoolType, ".exclude_from_latest")
                publishing_profile.set_prop("replicaCount", AAZIntType, ".replica_count")
                publishing_profile.set_prop("replicationMode", AAZStrType, ".replication_mode")
                publishing_profile.set_prop("storageAccountType", AAZStrType, ".storage_account_type")
                publishing_profile.set_prop("targetExtendedLocations", AAZListType, ".target_extended_locations")
                publishing_profile.set_prop("targetRegions", AAZListType, ".target_regions")

            target_extended_locations = _builder.get(".properties.publishingProfile.targetExtendedLocations")
            if target_extended_locations is not None:
                target_extended_locations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.publishingProfile.targetExtendedLocations[]")
            if _elements is not None:
                _CreateHelper._build_schema_encryption_images_create(_elements.set_prop("encryption", AAZObjectType, ".encryption"))
                _elements.set_prop("extendedLocation", AAZObjectType, ".extended_location")
                _elements.set_prop("extendedLocationReplicaCount", AAZIntType, ".extended_location_replica_count")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

            extended_location = _builder.get(".properties.publishingProfile.targetExtendedLocations[].extendedLocation")
            if extended_location is not None:
                extended_location.set_prop("name", AAZStrType, ".name")
                extended_location.set_prop("type", AAZStrType, ".type")

            target_regions = _builder.get(".properties.publishingProfile.targetRegions")
            if target_regions is not None:
                target_regions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.publishingProfile.targetRegions[]")
            if _elements is not None:
                _elements.set_prop("additionalReplicaSets", AAZListType, ".additional_replica_sets")
                _CreateHelper._build_schema_encryption_images_create(_elements.set_prop("encryption", AAZObjectType, ".encryption"))
                _elements.set_prop("excludeFromLatest", AAZBoolType, ".exclude_from_latest")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("regionalReplicaCount", AAZIntType, ".regional_replica_count")
                _elements.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

            additional_replica_sets = _builder.get(".properties.publishingProfile.targetRegions[].additionalReplicaSets")
            if additional_replica_sets is not None:
                additional_replica_sets.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.publishingProfile.targetRegions[].additionalReplicaSets[]")
            if _elements is not None:
                _elements.set_prop("regionalReplicaCount", AAZIntType, ".regional_replica_count")
                _elements.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

            safety_profile = _builder.get(".properties.safetyProfile")
            if safety_profile is not None:
                safety_profile.set_prop("allowDeletionOfReplicatedLocations", AAZBoolType, ".allow_deletion_of_replicated_locations")
                safety_profile.set_prop("blockDeletionBeforeEndOfLife", AAZBoolType, ".block_deletion_before_end_of_life")

            security_profile = _builder.get(".properties.securityProfile")
            if security_profile is not None:
                security_profile.set_prop("uefiSettings", AAZObjectType, ".uefi_settings")

            uefi_settings = _builder.get(".properties.securityProfile.uefiSettings")
            if uefi_settings is not None:
                uefi_settings.set_prop("additionalSignatures", AAZObjectType, ".additional_signatures")
                uefi_settings.set_prop("signatureTemplateNames", AAZListType, ".signature_template_names")

            additional_signatures = _builder.get(".properties.securityProfile.uefiSettings.additionalSignatures")
            if additional_signatures is not None:
                additional_signatures.set_prop("db", AAZListType, ".db")
                additional_signatures.set_prop("dbx", AAZListType, ".dbx")
                additional_signatures.set_prop("kek", AAZListType, ".kek")
                _CreateHelper._build_schema_uefi_key_create(additional_signatures.set_prop("pk", AAZObjectType, ".pk"))

            db = _builder.get(".properties.securityProfile.uefiSettings.additionalSignatures.db")
            if db is not None:
                _CreateHelper._build_schema_uefi_key_create(db.set_elements(AAZObjectType, "."))

            dbx = _builder.get(".properties.securityProfile.uefiSettings.additionalSignatures.dbx")
            if dbx is not None:
                _CreateHelper._build_schema_uefi_key_create(dbx.set_elements(AAZObjectType, "."))

            kek = _builder.get(".properties.securityProfile.uefiSettings.additionalSignatures.kek")
            if kek is not None:
                _CreateHelper._build_schema_uefi_key_create(kek.set_elements(AAZObjectType, "."))

            signature_template_names = _builder.get(".properties.securityProfile.uefiSettings.signatureTemplateNames")
            if signature_template_names is not None:
                signature_template_names.set_elements(AAZStrType, ".")

            storage_profile = _builder.get(".properties.storageProfile")
            if storage_profile is not None:
                storage_profile.set_prop("dataDiskImages", AAZListType, ".data_disk_images")
                storage_profile.set_prop("osDiskImage", AAZObjectType, ".os_disk_image")
                storage_profile.set_prop("source", AAZObjectType, ".source")

            data_disk_images = _builder.get(".properties.storageProfile.dataDiskImages")
            if data_disk_images is not None:
                data_disk_images.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.storageProfile.dataDiskImages[]")
            if _elements is not None:
                _elements.set_prop("hostCaching", AAZStrType, ".host_caching")
                _elements.set_prop("lun", AAZIntType, ".lun", typ_kwargs={"flags": {"required": True}})
                _CreateHelper._build_schema_gallery_disk_image_source_create(_elements.set_prop("source", AAZObjectType, ".source"))

            os_disk_image = _builder.get(".properties.storageProfile.osDiskImage")
            if os_disk_image is not None:
                os_disk_image.set_prop("hostCaching", AAZStrType, ".host_caching")
                _CreateHelper._build_schema_gallery_disk_image_source_create(os_disk_image.set_prop("source", AAZObjectType, ".source"))

            source = _builder.get(".properties.storageProfile.source")
            if source is not None:
                source.set_prop("communityGalleryImageId", AAZStrType, ".community_gallery_image_id")
                source.set_prop("id", AAZStrType, ".id")
                source.set_prop("virtualMachineId", AAZStrType, ".virtual_machine_id")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_gallery_image_version_read(cls._schema_on_200_201)

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_encryption_images_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("dataDiskImages", AAZListType, ".data_disk_images")
        _builder.set_prop("osDiskImage", AAZObjectType, ".os_disk_image")

        data_disk_images = _builder.get(".dataDiskImages")
        if data_disk_images is not None:
            data_disk_images.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".dataDiskImages[]")
        if _elements is not None:
            _elements.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
            _elements.set_prop("lun", AAZIntType, ".lun", typ_kwargs={"flags": {"required": True}})

        os_disk_image = _builder.get(".osDiskImage")
        if os_disk_image is not None:
            os_disk_image.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
            os_disk_image.set_prop("securityProfile", AAZObjectType, ".security_profile")

        security_profile = _builder.get(".osDiskImage.securityProfile")
        if security_profile is not None:
            security_profile.set_prop("confidentialVMEncryptionType", AAZStrType, ".confidential_vm_encryption_type")
            security_profile.set_prop("secureVMDiskEncryptionSetId", AAZStrType, ".secure_vm_disk_encryption_set_id")

    @classmethod
    def _build_schema_gallery_disk_image_source_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")
        _builder.set_prop("storageAccountId", AAZStrType, ".storage_account_id")
        _builder.set_prop("uri", AAZStrType, ".uri")

    @classmethod
    def _build_schema_uefi_key_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("type", AAZStrType, ".type")
        _builder.set_prop("value", AAZListType, ".value")

        value = _builder.get(".value")
        if value is not None:
            value.set_elements(AAZStrType, ".")

    _schema_encryption_images_read = None

    @classmethod
    def _build_schema_encryption_images_read(cls, _schema):
        if cls._schema_encryption_images_read is not None:
            _schema.data_disk_images = cls._schema_encryption_images_read.data_disk_images
            _schema.os_disk_image = cls._schema_encryption_images_read.os_disk_image
            return

        cls._schema_encryption_images_read = _schema_encryption_images_read = AAZObjectType()

        encryption_images_read = _schema_encryption_images_read
        encryption_images_read.data_disk_images = AAZListType(
            serialized_name="dataDiskImages",
        )
        encryption_images_read.os_disk_image = AAZObjectType(
            serialized_name="osDiskImage",
        )

        data_disk_images = _schema_encryption_images_read.data_disk_images
        data_disk_images.Element = AAZObjectType()

        _element = _schema_encryption_images_read.data_disk_images.Element
        _element.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        _element.lun = AAZIntType(
            flags={"required": True},
        )

        os_disk_image = _schema_encryption_images_read.os_disk_image
        os_disk_image.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        os_disk_image.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )

        security_profile = _schema_encryption_images_read.os_disk_image.security_profile
        security_profile.confidential_vm_encryption_type = AAZStrType(
            serialized_name="confidentialVMEncryptionType",
        )
        security_profile.secure_vm_disk_encryption_set_id = AAZStrType(
            serialized_name="secureVMDiskEncryptionSetId",
        )

        _schema.data_disk_images = cls._schema_encryption_images_read.data_disk_images
        _schema.os_disk_image = cls._schema_encryption_images_read.os_disk_image

    _schema_gallery_disk_image_source_read = None

    @classmethod
    def _build_schema_gallery_disk_image_source_read(cls, _schema):
        if cls._schema_gallery_disk_image_source_read is not None:
            _schema.id = cls._schema_gallery_disk_image_source_read.id
            _schema.storage_account_id = cls._schema_gallery_disk_image_source_read.storage_account_id
            _schema.uri = cls._schema_gallery_disk_image_source_read.uri
            return

        cls._schema_gallery_disk_image_source_read = _schema_gallery_disk_image_source_read = AAZObjectType()

        gallery_disk_image_source_read = _schema_gallery_disk_image_source_read
        gallery_disk_image_source_read.id = AAZStrType()
        gallery_disk_image_source_read.storage_account_id = AAZStrType(
            serialized_name="storageAccountId",
        )
        gallery_disk_image_source_read.uri = AAZStrType()

        _schema.id = cls._schema_gallery_disk_image_source_read.id
        _schema.storage_account_id = cls._schema_gallery_disk_image_source_read.storage_account_id
        _schema.uri = cls._schema_gallery_disk_image_source_read.uri

    _schema_gallery_image_version_read = None

    @classmethod
    def _build_schema_gallery_image_version_read(cls, _schema):
        if cls._schema_gallery_image_version_read is not None:
            _schema.id = cls._schema_gallery_image_version_read.id
            _schema.location = cls._schema_gallery_image_version_read.location
            _schema.name = cls._schema_gallery_image_version_read.name
            _schema.properties = cls._schema_gallery_image_version_read.properties
            _schema.tags = cls._schema_gallery_image_version_read.tags
            _schema.type = cls._schema_gallery_image_version_read.type
            return

        cls._schema_gallery_image_version_read = _schema_gallery_image_version_read = AAZObjectType()

        gallery_image_version_read = _schema_gallery_image_version_read
        gallery_image_version_read.id = AAZStrType(
            flags={"read_only": True},
        )
        gallery_image_version_read.location = AAZStrType(
            flags={"required": True},
        )
        gallery_image_version_read.name = AAZStrType(
            flags={"read_only": True},
        )
        gallery_image_version_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        gallery_image_version_read.tags = AAZDictType()
        gallery_image_version_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_gallery_image_version_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.publishing_profile = AAZObjectType(
            serialized_name="publishingProfile",
        )
        properties.replication_status = AAZObjectType(
            serialized_name="replicationStatus",
            flags={"read_only": True},
        )
        properties.restore = AAZBoolType()
        properties.safety_profile = AAZObjectType(
            serialized_name="safetyProfile",
        )
        properties.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        properties.storage_profile = AAZObjectType(
            serialized_name="storageProfile",
            flags={"required": True},
        )
        properties.validations_profile = AAZObjectType(
            serialized_name="validationsProfile",
            flags={"read_only": True},
        )

        publishing_profile = _schema_gallery_image_version_read.properties.publishing_profile
        publishing_profile.end_of_life_date = AAZStrType(
            serialized_name="endOfLifeDate",
        )
        publishing_profile.exclude_from_latest = AAZBoolType(
            serialized_name="excludeFromLatest",
        )
        publishing_profile.published_date = AAZStrType(
            serialized_name="publishedDate",
            flags={"read_only": True},
        )
        publishing_profile.replica_count = AAZIntType(
            serialized_name="replicaCount",
        )
        publishing_profile.replication_mode = AAZStrType(
            serialized_name="replicationMode",
        )
        publishing_profile.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )
        publishing_profile.target_extended_locations = AAZListType(
            serialized_name="targetExtendedLocations",
        )
        publishing_profile.target_regions = AAZListType(
            serialized_name="targetRegions",
        )

        target_extended_locations = _schema_gallery_image_version_read.properties.publishing_profile.target_extended_locations
        target_extended_locations.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.publishing_profile.target_extended_locations.Element
        _element.encryption = AAZObjectType()
        cls._build_schema_encryption_images_read(_element.encryption)
        _element.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        _element.extended_location_replica_count = AAZIntType(
            serialized_name="extendedLocationReplicaCount",
        )
        _element.name = AAZStrType()
        _element.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        extended_location = _schema_gallery_image_version_read.properties.publishing_profile.target_extended_locations.Element.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        target_regions = _schema_gallery_image_version_read.properties.publishing_profile.target_regions
        target_regions.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.publishing_profile.target_regions.Element
        _element.additional_replica_sets = AAZListType(
            serialized_name="additionalReplicaSets",
        )
        _element.encryption = AAZObjectType()
        cls._build_schema_encryption_images_read(_element.encryption)
        _element.exclude_from_latest = AAZBoolType(
            serialized_name="excludeFromLatest",
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.regional_replica_count = AAZIntType(
            serialized_name="regionalReplicaCount",
        )
        _element.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        additional_replica_sets = _schema_gallery_image_version_read.properties.publishing_profile.target_regions.Element.additional_replica_sets
        additional_replica_sets.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.publishing_profile.target_regions.Element.additional_replica_sets.Element
        _element.regional_replica_count = AAZIntType(
            serialized_name="regionalReplicaCount",
        )
        _element.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        replication_status = _schema_gallery_image_version_read.properties.replication_status
        replication_status.aggregated_state = AAZStrType(
            serialized_name="aggregatedState",
            flags={"read_only": True},
        )
        replication_status.summary = AAZListType(
            flags={"read_only": True},
        )

        summary = _schema_gallery_image_version_read.properties.replication_status.summary
        summary.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.replication_status.summary.Element
        _element.details = AAZStrType(
            flags={"read_only": True},
        )
        _element.progress = AAZIntType(
            flags={"read_only": True},
        )
        _element.region = AAZStrType(
            flags={"read_only": True},
        )
        _element.state = AAZStrType(
            flags={"read_only": True},
        )

        safety_profile = _schema_gallery_image_version_read.properties.safety_profile
        safety_profile.allow_deletion_of_replicated_locations = AAZBoolType(
            serialized_name="allowDeletionOfReplicatedLocations",
        )
        safety_profile.block_deletion_before_end_of_life = AAZBoolType(
            serialized_name="blockDeletionBeforeEndOfLife",
        )
        safety_profile.policy_violations = AAZListType(
            serialized_name="policyViolations",
            flags={"read_only": True},
        )
        safety_profile.reported_for_policy_violation = AAZBoolType(
            serialized_name="reportedForPolicyViolation",
            flags={"read_only": True},
        )

        policy_violations = _schema_gallery_image_version_read.properties.safety_profile.policy_violations
        policy_violations.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.safety_profile.policy_violations.Element
        _element.category = AAZStrType()
        _element.details = AAZStrType()

        security_profile = _schema_gallery_image_version_read.properties.security_profile
        security_profile.uefi_settings = AAZObjectType(
            serialized_name="uefiSettings",
        )

        uefi_settings = _schema_gallery_image_version_read.properties.security_profile.uefi_settings
        uefi_settings.additional_signatures = AAZObjectType(
            serialized_name="additionalSignatures",
        )
        uefi_settings.signature_template_names = AAZListType(
            serialized_name="signatureTemplateNames",
        )

        additional_signatures = _schema_gallery_image_version_read.properties.security_profile.uefi_settings.additional_signatures
        additional_signatures.db = AAZListType()
        additional_signatures.dbx = AAZListType()
        additional_signatures.kek = AAZListType()
        additional_signatures.pk = AAZObjectType()
        cls._build_schema_uefi_key_read(additional_signatures.pk)

        db = _schema_gallery_image_version_read.properties.security_profile.uefi_settings.additional_signatures.db
        db.Element = AAZObjectType()
        cls._build_schema_uefi_key_read(db.Element)

        dbx = _schema_gallery_image_version_read.properties.security_profile.uefi_settings.additional_signatures.dbx
        dbx.Element = AAZObjectType()
        cls._build_schema_uefi_key_read(dbx.Element)

        kek = _schema_gallery_image_version_read.properties.security_profile.uefi_settings.additional_signatures.kek
        kek.Element = AAZObjectType()
        cls._build_schema_uefi_key_read(kek.Element)

        signature_template_names = _schema_gallery_image_version_read.properties.security_profile.uefi_settings.signature_template_names
        signature_template_names.Element = AAZStrType()

        storage_profile = _schema_gallery_image_version_read.properties.storage_profile
        storage_profile.data_disk_images = AAZListType(
            serialized_name="dataDiskImages",
        )
        storage_profile.os_disk_image = AAZObjectType(
            serialized_name="osDiskImage",
        )
        storage_profile.source = AAZObjectType()

        data_disk_images = _schema_gallery_image_version_read.properties.storage_profile.data_disk_images
        data_disk_images.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.storage_profile.data_disk_images.Element
        _element.host_caching = AAZStrType(
            serialized_name="hostCaching",
        )
        _element.lun = AAZIntType(
            flags={"required": True},
        )
        _element.size_in_gb = AAZIntType(
            serialized_name="sizeInGB",
            flags={"read_only": True},
        )
        _element.source = AAZObjectType()
        cls._build_schema_gallery_disk_image_source_read(_element.source)

        os_disk_image = _schema_gallery_image_version_read.properties.storage_profile.os_disk_image
        os_disk_image.host_caching = AAZStrType(
            serialized_name="hostCaching",
        )
        os_disk_image.size_in_gb = AAZIntType(
            serialized_name="sizeInGB",
            flags={"read_only": True},
        )
        os_disk_image.source = AAZObjectType()
        cls._build_schema_gallery_disk_image_source_read(os_disk_image.source)

        source = _schema_gallery_image_version_read.properties.storage_profile.source
        source.community_gallery_image_id = AAZStrType(
            serialized_name="communityGalleryImageId",
        )
        source.id = AAZStrType()
        source.virtual_machine_id = AAZStrType(
            serialized_name="virtualMachineId",
        )

        validations_profile = _schema_gallery_image_version_read.properties.validations_profile
        validations_profile.executed_validations = AAZListType(
            serialized_name="executedValidations",
        )
        validations_profile.platform_attributes = AAZListType(
            serialized_name="platformAttributes",
        )
        validations_profile.validation_etag = AAZStrType(
            serialized_name="validationEtag",
        )

        executed_validations = _schema_gallery_image_version_read.properties.validations_profile.executed_validations
        executed_validations.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.validations_profile.executed_validations.Element
        _element.execution_time = AAZStrType(
            serialized_name="executionTime",
        )
        _element.status = AAZStrType()
        _element.type = AAZStrType()
        _element.version = AAZStrType()

        platform_attributes = _schema_gallery_image_version_read.properties.validations_profile.platform_attributes
        platform_attributes.Element = AAZObjectType()

        _element = _schema_gallery_image_version_read.properties.validations_profile.platform_attributes.Element
        _element.name = AAZStrType(
            flags={"read_only": True},
        )
        _element.value = AAZStrType(
            flags={"read_only": True},
        )

        tags = _schema_gallery_image_version_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_gallery_image_version_read.id
        _schema.location = cls._schema_gallery_image_version_read.location
        _schema.name = cls._schema_gallery_image_version_read.name
        _schema.properties = cls._schema_gallery_image_version_read.properties
        _schema.tags = cls._schema_gallery_image_version_read.tags
        _schema.type = cls._schema_gallery_image_version_read.type

    _schema_uefi_key_read = None

    @classmethod
    def _build_schema_uefi_key_read(cls, _schema):
        if cls._schema_uefi_key_read is not None:
            _schema.type = cls._schema_uefi_key_read.type
            _schema.value = cls._schema_uefi_key_read.value
            return

        cls._schema_uefi_key_read = _schema_uefi_key_read = AAZObjectType()

        uefi_key_read = _schema_uefi_key_read
        uefi_key_read.type = AAZStrType()
        uefi_key_read.value = AAZListType()

        value = _schema_uefi_key_read.value
        value.Element = AAZStrType()

        _schema.type = cls._schema_uefi_key_read.type
        _schema.value = cls._schema_uefi_key_read.value


__all__ = ["Create"]
