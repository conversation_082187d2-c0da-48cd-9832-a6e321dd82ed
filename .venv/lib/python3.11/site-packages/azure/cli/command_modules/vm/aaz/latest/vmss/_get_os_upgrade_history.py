# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "vmss get-os-upgrade-history",
)
class GetOsUpgradeHistory(AAZCommand):
    """List the OS upgrades on a VM scale set instance.
    """

    _aaz_info = {
        "version": "2023-09-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachinescalesets/{}/osupgradehistory", "2023-09-01"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.vm_scale_set_name = AAZStrArg(
            options=["-n", "--name", "--vm-scale-set-name"],
            help="The name of the VM scale set.",
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.VirtualMachineScaleSetsGetOSUpgradeHistory(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class VirtualMachineScaleSetsGetOSUpgradeHistory(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachineScaleSets/{vmScaleSetName}/osUpgradeHistory",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmScaleSetName", self.ctx.args.vm_scale_set_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2023-09-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
            )
            _schema_on_200.value = AAZListType(
                flags={"required": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.location = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.error = AAZObjectType()
            _GetOsUpgradeHistoryHelper._build_schema_api_error_read(properties.error)
            properties.progress = AAZObjectType()
            properties.rollback_info = AAZObjectType(
                serialized_name="rollbackInfo",
            )
            properties.running_status = AAZObjectType(
                serialized_name="runningStatus",
            )
            properties.started_by = AAZStrType(
                serialized_name="startedBy",
                flags={"read_only": True},
            )
            properties.target_image_reference = AAZObjectType(
                serialized_name="targetImageReference",
            )

            progress = cls._schema_on_200.value.Element.properties.progress
            progress.failed_instance_count = AAZIntType(
                serialized_name="failedInstanceCount",
                flags={"read_only": True},
            )
            progress.in_progress_instance_count = AAZIntType(
                serialized_name="inProgressInstanceCount",
                flags={"read_only": True},
            )
            progress.pending_instance_count = AAZIntType(
                serialized_name="pendingInstanceCount",
                flags={"read_only": True},
            )
            progress.successful_instance_count = AAZIntType(
                serialized_name="successfulInstanceCount",
                flags={"read_only": True},
            )

            rollback_info = cls._schema_on_200.value.Element.properties.rollback_info
            rollback_info.failed_rolledback_instance_count = AAZIntType(
                serialized_name="failedRolledbackInstanceCount",
                flags={"read_only": True},
            )
            rollback_info.rollback_error = AAZObjectType(
                serialized_name="rollbackError",
                flags={"read_only": True},
            )
            _GetOsUpgradeHistoryHelper._build_schema_api_error_read(rollback_info.rollback_error)
            rollback_info.successfully_rolledback_instance_count = AAZIntType(
                serialized_name="successfullyRolledbackInstanceCount",
                flags={"read_only": True},
            )

            running_status = cls._schema_on_200.value.Element.properties.running_status
            running_status.code = AAZStrType(
                flags={"read_only": True},
            )
            running_status.end_time = AAZStrType(
                serialized_name="endTime",
                flags={"read_only": True},
            )
            running_status.start_time = AAZStrType(
                serialized_name="startTime",
                flags={"read_only": True},
            )

            target_image_reference = cls._schema_on_200.value.Element.properties.target_image_reference
            target_image_reference.community_gallery_image_id = AAZStrType(
                serialized_name="communityGalleryImageId",
            )
            target_image_reference.exact_version = AAZStrType(
                serialized_name="exactVersion",
                flags={"read_only": True},
            )
            target_image_reference.id = AAZStrType()
            target_image_reference.offer = AAZStrType()
            target_image_reference.publisher = AAZStrType()
            target_image_reference.shared_gallery_image_id = AAZStrType(
                serialized_name="sharedGalleryImageId",
            )
            target_image_reference.sku = AAZStrType()
            target_image_reference.version = AAZStrType()

            return cls._schema_on_200


class _GetOsUpgradeHistoryHelper:
    """Helper class for GetOsUpgradeHistory"""

    _schema_api_error_read = None

    @classmethod
    def _build_schema_api_error_read(cls, _schema):
        if cls._schema_api_error_read is not None:
            _schema.code = cls._schema_api_error_read.code
            _schema.details = cls._schema_api_error_read.details
            _schema.innererror = cls._schema_api_error_read.innererror
            _schema.message = cls._schema_api_error_read.message
            _schema.target = cls._schema_api_error_read.target
            return

        cls._schema_api_error_read = _schema_api_error_read = AAZObjectType()

        api_error_read = _schema_api_error_read
        api_error_read.code = AAZStrType()
        api_error_read.details = AAZListType()
        api_error_read.innererror = AAZObjectType()
        api_error_read.message = AAZStrType()
        api_error_read.target = AAZStrType()

        details = _schema_api_error_read.details
        details.Element = AAZObjectType()

        _element = _schema_api_error_read.details.Element
        _element.code = AAZStrType()
        _element.message = AAZStrType()
        _element.target = AAZStrType()

        innererror = _schema_api_error_read.innererror
        innererror.errordetail = AAZStrType()
        innererror.exceptiontype = AAZStrType()

        _schema.code = cls._schema_api_error_read.code
        _schema.details = cls._schema_api_error_read.details
        _schema.innererror = cls._schema_api_error_read.innererror
        _schema.message = cls._schema_api_error_read.message
        _schema.target = cls._schema_api_error_read.target


__all__ = ["GetOsUpgradeHistory"]
