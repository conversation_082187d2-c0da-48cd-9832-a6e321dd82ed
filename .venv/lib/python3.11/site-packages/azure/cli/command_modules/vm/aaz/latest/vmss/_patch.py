# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class Patch(AAZCommand):
    """Patch update a VM scale set.
    """

    _aaz_info = {
        "version": "2024-11-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachinescalesets/{}", "2024-11-01"],
        ]
    }

    A<PERSON>_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.if_match = AAZStrArg(
            options=["--if-match"],
            help="The ETag of the transformation. Omit this value to always overwrite the current resource. Specify the last-seen ETag value to prevent accidentally overwriting concurrent changes.",
        )
        _args_schema.if_none_match = AAZStrArg(
            options=["--if-none-match"],
            help="Set to '*' to allow a new record set to be created, but to prevent updating an existing record set. Other values will result in error from server as they are not supported.",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.vm_scale_set_name = AAZStrArg(
            options=["-n", "--name", "--vm-scale-set-name"],
            help="The name of the VM scale set to create or update.",
            required=True,
            id_part="name",
        )

        # define Arg Group "Identity"

        _args_schema = cls._args_schema
        _args_schema.mi_system_assigned = AAZStrArg(
            options=["--system-assigned", "--mi-system-assigned"],
            arg_group="Identity",
            help="Set the system managed identity.",
            blank="True",
        )
        _args_schema.mi_user_assigned = AAZListArg(
            options=["--user-assigned", "--mi-user-assigned"],
            arg_group="Identity",
            help="Set the user managed identities.",
            blank=[],
        )

        mi_user_assigned = cls._args_schema.mi_user_assigned
        mi_user_assigned.Element = AAZStrArg()

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.plan = AAZObjectArg(
            options=["--plan"],
            arg_group="Parameters",
            help="The purchase plan when deploying a virtual machine scale set from VM Marketplace images.",
        )
        _args_schema.sku = AAZObjectArg(
            options=["--sku"],
            arg_group="Parameters",
            help="The virtual machine scale set sku.",
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Parameters",
            help="Resource tags",
        )
        _args_schema.zones = AAZListArg(
            options=["--zones"],
            arg_group="Parameters",
            help="The virtual machine scale set zones.",
        )

        plan = cls._args_schema.plan
        plan.name = AAZStrArg(
            options=["name"],
            help="The plan ID.",
        )
        plan.product = AAZStrArg(
            options=["product"],
            help="Specifies the product of the image from the marketplace. This is the same value as Offer under the imageReference element.",
        )
        plan.promotion_code = AAZStrArg(
            options=["promotion-code"],
            help="The promotion code.",
        )
        plan.publisher = AAZStrArg(
            options=["publisher"],
            help="The publisher ID.",
        )

        sku = cls._args_schema.sku
        sku.capacity = AAZIntArg(
            options=["capacity"],
            help="Specifies the number of virtual machines in the scale set.",
        )
        sku.name = AAZStrArg(
            options=["name"],
            help="The sku name.",
        )
        sku.tier = AAZStrArg(
            options=["tier"],
            help="Specifies the tier of virtual machines in a scale set.<br /><br /> Possible Values:<br /><br /> **Standard**<br /><br /> **Basic**",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        zones = cls._args_schema.zones
        zones.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.additional_capabilities = AAZObjectArg(
            options=["--additional-capabilities"],
            arg_group="Properties",
            help="Specifies additional capabilities enabled or disabled on the Virtual Machines in the Virtual Machine Scale Set. For instance: whether the Virtual Machines have the capability to support attaching managed data disks with UltraSSD_LRS storage account type.",
        )
        _args_schema.automatic_repairs_policy = AAZObjectArg(
            options=["--automatic-repairs-policy"],
            arg_group="Properties",
            help="Policy for automatic repairs.",
        )
        _args_schema.do_not_run_extensions_on_overprovisioned_v_ms = AAZBoolArg(
            options=["--do-not-run-extensions-on-overprovisioned-v-ms"],
            arg_group="Properties",
            help="When Overprovision is enabled, extensions are launched only on the requested number of VMs which are finally kept. This property will hence ensure that the extensions do not run on the extra overprovisioned VMs.",
        )
        _args_schema.overprovision = AAZBoolArg(
            options=["--overprovision"],
            arg_group="Properties",
            help="Specifies whether the Virtual Machine Scale Set should be overprovisioned.",
        )
        _args_schema.priority_mix_policy = AAZObjectArg(
            options=["--priority-mix-policy"],
            arg_group="Properties",
            help="Specifies the desired targets for mixing Spot and Regular priority VMs within the same VMSS Flex instance.",
        )
        _args_schema.proximity_placement_group = AAZObjectArg(
            options=["--proximity-placement-group"],
            arg_group="Properties",
            help="Specifies information about the proximity placement group that the virtual machine scale set should be assigned to. <br><br>Minimum api-version: 2018-04-01.",
        )
        cls._build_args_sub_resource_update(_args_schema.proximity_placement_group)
        _args_schema.resiliency_policy = AAZObjectArg(
            options=["--resiliency-policy"],
            arg_group="Properties",
            help="Policy for Resiliency",
        )
        _args_schema.scale_in_policy = AAZObjectArg(
            options=["--scale-in-policy"],
            arg_group="Properties",
            help="Specifies the policies applied when scaling in Virtual Machines in the Virtual Machine Scale Set.",
        )
        _args_schema.single_placement_group = AAZBoolArg(
            options=["--single-placement-group"],
            arg_group="Properties",
            help="When true this limits the scale set to a single placement group, of max size 100 virtual machines. NOTE: If singlePlacementGroup is true, it may be modified to false. However, if singlePlacementGroup is false, it may not be modified to true.",
        )
        _args_schema.sku_profile = AAZObjectArg(
            options=["--sku-profile"],
            arg_group="Properties",
            help="Specifies the sku profile for the virtual machine scale set.",
        )
        _args_schema.spot_restore_policy = AAZObjectArg(
            options=["--spot-restore-policy"],
            arg_group="Properties",
            help="Specifies the Spot Restore properties for the virtual machine scale set.",
        )
        _args_schema.upgrade_policy = AAZObjectArg(
            options=["--upgrade-policy"],
            arg_group="Properties",
            help="The upgrade policy.",
        )
        _args_schema.virtual_machine_profile = AAZObjectArg(
            options=["--virtual-machine-profile"],
            arg_group="Properties",
            help="The virtual machine profile.",
        )
        _args_schema.zonal_platform_fault_domain_align_mode = AAZStrArg(
            options=["--zonal-platform-fault-domain-align-mode"],
            arg_group="Properties",
            help="Specifies the align mode between Virtual Machine Scale Set compute and storage Fault Domain count.",
            enum={"Aligned": "Aligned", "Unaligned": "Unaligned"},
        )

        additional_capabilities = cls._args_schema.additional_capabilities
        additional_capabilities.hibernation_enabled = AAZBoolArg(
            options=["hibernation-enabled"],
            help="The flag that enables or disables hibernation capability on the VM.",
        )
        additional_capabilities.ultra_ssd_enabled = AAZBoolArg(
            options=["ultra-ssd-enabled"],
            help="The flag that enables or disables a capability to have one or more managed data disks with UltraSSD_LRS storage account type on the VM or VMSS. Managed disks with storage account type UltraSSD_LRS can be added to a virtual machine or virtual machine scale set only if this property is enabled.",
        )

        automatic_repairs_policy = cls._args_schema.automatic_repairs_policy
        automatic_repairs_policy.enabled = AAZBoolArg(
            options=["enabled"],
            help="Specifies whether automatic repairs should be enabled on the virtual machine scale set. The default value is false.",
        )
        automatic_repairs_policy.grace_period = AAZStrArg(
            options=["grace-period"],
            help="The amount of time for which automatic repairs are suspended due to a state change on VM. The grace time starts after the state change has completed. This helps avoid premature or accidental repairs. The time duration should be specified in ISO 8601 format. The minimum allowed grace period is 10 minutes (PT10M), which is also the default value. The maximum allowed grace period is 90 minutes (PT90M).",
        )
        automatic_repairs_policy.repair_action = AAZStrArg(
            options=["repair-action"],
            help="Type of repair action (replace, restart, reimage) that will be used for repairing unhealthy virtual machines in the scale set. Default value is replace.",
            enum={"Reimage": "Reimage", "Replace": "Replace", "Restart": "Restart"},
        )

        priority_mix_policy = cls._args_schema.priority_mix_policy
        priority_mix_policy.base_regular_priority_count = AAZIntArg(
            options=["base-regular-priority-count"],
            help="The base number of regular priority VMs that will be created in this scale set as it scales out.",
            fmt=AAZIntArgFormat(
                minimum=0,
            ),
        )
        priority_mix_policy.regular_priority_percentage_above_base = AAZIntArg(
            options=["regular-priority-percentage-above-base"],
            help="The percentage of VM instances, after the base regular priority count has been reached, that are expected to use regular priority.",
            fmt=AAZIntArgFormat(
                maximum=100,
                minimum=0,
            ),
        )

        resiliency_policy = cls._args_schema.resiliency_policy
        resiliency_policy.automatic_zone_rebalancing_policy = AAZObjectArg(
            options=["automatic-zone-rebalancing-policy"],
            help="The configuration parameters used while performing automatic AZ balancing.",
        )
        resiliency_policy.resilient_vm_creation_policy = AAZObjectArg(
            options=["resilient-vm-creation-policy"],
            help="The configuration parameters used while performing resilient VM creation.",
        )
        resiliency_policy.resilient_vm_deletion_policy = AAZObjectArg(
            options=["resilient-vm-deletion-policy"],
            help="The configuration parameters used while performing resilient VM deletion.",
        )

        automatic_zone_rebalancing_policy = cls._args_schema.resiliency_policy.automatic_zone_rebalancing_policy
        automatic_zone_rebalancing_policy.enabled = AAZBoolArg(
            options=["enabled"],
            help="Specifies whether Automatic AZ Balancing should be enabled on the virtual machine scale set. The default value is false.",
        )
        automatic_zone_rebalancing_policy.rebalance_behavior = AAZStrArg(
            options=["rebalance-behavior"],
            help="Type of rebalance behavior that will be used for recreating virtual machines in the scale set across availability zones. Default and only supported value for now is CreateBeforeDelete.",
            enum={"CreateBeforeDelete": "CreateBeforeDelete"},
        )
        automatic_zone_rebalancing_policy.rebalance_strategy = AAZStrArg(
            options=["rebalance-strategy"],
            help="Type of rebalance strategy that will be used for rebalancing virtual machines in the scale set across availability zones. Default and only supported value for now is Recreate.",
            enum={"Recreate": "Recreate"},
        )

        resilient_vm_creation_policy = cls._args_schema.resiliency_policy.resilient_vm_creation_policy
        resilient_vm_creation_policy.enabled = AAZBoolArg(
            options=["enabled"],
            help="Specifies whether resilient VM creation should be enabled on the virtual machine scale set. The default value is false.",
        )

        resilient_vm_deletion_policy = cls._args_schema.resiliency_policy.resilient_vm_deletion_policy
        resilient_vm_deletion_policy.enabled = AAZBoolArg(
            options=["enabled"],
            help="Specifies whether resilient VM deletion should be enabled on the virtual machine scale set. The default value is false.",
        )

        scale_in_policy = cls._args_schema.scale_in_policy
        scale_in_policy.force_deletion = AAZBoolArg(
            options=["force-deletion"],
            help="This property allows you to specify if virtual machines chosen for removal have to be force deleted when a virtual machine scale set is being scaled-in.(Feature in Preview)",
        )
        scale_in_policy.prioritize_unhealthy_v_ms = AAZBoolArg(
            options=["prioritize-unhealthy-v-ms"],
            help="This property allows you to prioritize the deletion of unhealthy and inactive VMs when a virtual machine scale set is being scaled-in.(Feature in Preview)",
        )
        scale_in_policy.rules = AAZListArg(
            options=["rules"],
            help="The rules to be followed when scaling-in a virtual machine scale set. <br><br> Possible values are: <br><br> **Default** When a virtual machine scale set is scaled in, the scale set will first be balanced across zones if it is a zonal scale set. Then, it will be balanced across Fault Domains as far as possible. Within each Fault Domain, the virtual machines chosen for removal will be the newest ones that are not protected from scale-in. <br><br> **OldestVM** When a virtual machine scale set is being scaled-in, the oldest virtual machines that are not protected from scale-in will be chosen for removal. For zonal virtual machine scale sets, the scale set will first be balanced across zones. Within each zone, the oldest virtual machines that are not protected will be chosen for removal. <br><br> **NewestVM** When a virtual machine scale set is being scaled-in, the newest virtual machines that are not protected from scale-in will be chosen for removal. For zonal virtual machine scale sets, the scale set will first be balanced across zones. Within each zone, the newest virtual machines that are not protected will be chosen for removal. <br><br>",
        )

        rules = cls._args_schema.scale_in_policy.rules
        rules.Element = AAZStrArg(
            enum={"Default": "Default", "NewestVM": "NewestVM", "OldestVM": "OldestVM"},
        )

        sku_profile = cls._args_schema.sku_profile
        sku_profile.allocation_strategy = AAZStrArg(
            options=["allocation-strategy"],
            help="Specifies the allocation strategy for the virtual machine scale set based on which the VMs will be allocated.",
            enum={"CapacityOptimized": "CapacityOptimized", "LowestPrice": "LowestPrice", "Prioritized": "Prioritized"},
        )
        sku_profile.vm_sizes = AAZListArg(
            options=["vm-sizes"],
            help="Specifies the VM sizes for the virtual machine scale set.",
        )

        vm_sizes = cls._args_schema.sku_profile.vm_sizes
        vm_sizes.Element = AAZObjectArg()

        _element = cls._args_schema.sku_profile.vm_sizes.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="Specifies the name of the VM Size.",
        )
        _element.rank = AAZIntArg(
            options=["rank"],
            help="Specifies the rank (a.k.a priority) associated with the VM Size.",
            fmt=AAZIntArgFormat(
                minimum=0,
            ),
        )

        spot_restore_policy = cls._args_schema.spot_restore_policy
        spot_restore_policy.enabled = AAZBoolArg(
            options=["enabled"],
            help="Enables the Spot-Try-Restore feature where evicted VMSS SPOT instances will be tried to be restored opportunistically based on capacity availability and pricing constraints",
        )
        spot_restore_policy.restore_timeout = AAZStrArg(
            options=["restore-timeout"],
            help="Timeout value expressed as an ISO 8601 time duration after which the platform will not try to restore the VMSS SPOT instances",
        )

        upgrade_policy = cls._args_schema.upgrade_policy
        upgrade_policy.automatic_os_upgrade_policy = AAZObjectArg(
            options=["automatic-os-upgrade-policy"],
            help="Configuration parameters used for performing automatic OS Upgrade.",
        )
        upgrade_policy.mode = AAZStrArg(
            options=["mode"],
            help="Specifies the mode of an upgrade to virtual machines in the scale set.<br /><br /> Possible values are:<br /><br /> **Manual** - You  control the application of updates to virtual machines in the scale set. You do this by using the manualUpgrade action.<br /><br /> **Automatic** - All virtual machines in the scale set are  automatically updated at the same time.",
            enum={"Automatic": "Automatic", "Manual": "Manual", "Rolling": "Rolling"},
        )
        upgrade_policy.rolling_upgrade_policy = AAZObjectArg(
            options=["rolling-upgrade-policy"],
            help="The configuration parameters used while performing a rolling upgrade.",
        )

        automatic_os_upgrade_policy = cls._args_schema.upgrade_policy.automatic_os_upgrade_policy
        automatic_os_upgrade_policy.disable_automatic_rollback = AAZBoolArg(
            options=["disable-automatic-rollback"],
            help="Whether OS image rollback feature should be disabled. Default value is false.",
        )
        automatic_os_upgrade_policy.enable_automatic_os_upgrade = AAZBoolArg(
            options=["enable-automatic-os-upgrade"],
            help="Indicates whether OS upgrades should automatically be applied to scale set instances in a rolling fashion when a newer version of the OS image becomes available. Default value is false. If this is set to true for Windows based scale sets, [enableAutomaticUpdates](https://docs.microsoft.com/dotnet/api/microsoft.azure.management.compute.models.windowsconfiguration.enableautomaticupdates?view=azure-dotnet) is automatically set to false and cannot be set to true.",
        )
        automatic_os_upgrade_policy.os_rolling_upgrade_deferral = AAZBoolArg(
            options=["os-rolling-upgrade-deferral"],
            help="Indicates whether Auto OS Upgrade should undergo deferral. Deferred OS upgrades will send advanced notifications on a per-VM basis that an OS upgrade from rolling upgrades is incoming, via the IMDS tag 'Platform.PendingOSUpgrade'. The upgrade then defers until the upgrade is approved via an ApproveRollingUpgrade call.",
        )
        automatic_os_upgrade_policy.use_rolling_upgrade_policy = AAZBoolArg(
            options=["use-rolling-upgrade-policy"],
            help="Indicates whether rolling upgrade policy should be used during Auto OS Upgrade. Default value is false. Auto OS Upgrade will fallback to the default policy if no policy is defined on the VMSS.",
        )

        rolling_upgrade_policy = cls._args_schema.upgrade_policy.rolling_upgrade_policy
        rolling_upgrade_policy.enable_cross_zone_upgrade = AAZBoolArg(
            options=["enable-cross-zone-upgrade"],
            help="Allow VMSS to ignore AZ boundaries when constructing upgrade batches. Take into consideration the Update Domain and maxBatchInstancePercent to determine the batch size.",
        )
        rolling_upgrade_policy.max_batch_instance_percent = AAZIntArg(
            options=["max-batch-instance-percent"],
            help="The maximum percent of total virtual machine instances that will be upgraded simultaneously by the rolling upgrade in one batch. As this is a maximum, unhealthy instances in previous or future batches can cause the percentage of instances in a batch to decrease to ensure higher reliability. The default value for this parameter is 20%.",
            fmt=AAZIntArgFormat(
                maximum=100,
                minimum=5,
            ),
        )
        rolling_upgrade_policy.max_surge = AAZBoolArg(
            options=["max-surge"],
            help="Create new virtual machines to upgrade the scale set, rather than updating the existing virtual machines. Existing virtual machines will be deleted once the new virtual machines are created for each batch.",
        )
        rolling_upgrade_policy.max_unhealthy_instance_percent = AAZIntArg(
            options=["max-unhealthy-instance-percent"],
            help="The maximum percentage of the total virtual machine instances in the scale set that can be simultaneously unhealthy, either as a result of being upgraded, or by being found in an unhealthy state by the virtual machine health checks before the rolling upgrade aborts. This constraint will be checked prior to starting any batch. The default value for this parameter is 20%.",
            fmt=AAZIntArgFormat(
                maximum=100,
                minimum=5,
            ),
        )
        rolling_upgrade_policy.max_unhealthy_upgraded_instance_percent = AAZIntArg(
            options=["max-unhealthy-upgraded-instance-percent"],
            help="The maximum percentage of upgraded virtual machine instances that can be found to be in an unhealthy state. This check will happen after each batch is upgraded. If this percentage is ever exceeded, the rolling update aborts. The default value for this parameter is 20%.",
            fmt=AAZIntArgFormat(
                maximum=100,
                minimum=0,
            ),
        )
        rolling_upgrade_policy.pause_time_between_batches = AAZStrArg(
            options=["pause-time-between-batches"],
            help="The wait time between completing the update for all virtual machines in one batch and starting the next batch. The time duration should be specified in ISO 8601 format. The default value is 0 seconds (PT0S).",
        )
        rolling_upgrade_policy.prioritize_unhealthy_instances = AAZBoolArg(
            options=["prioritize-unhealthy-instances"],
            help="Upgrade all unhealthy instances in a scale set before any healthy instances.",
        )
        rolling_upgrade_policy.rollback_failed_instances_on_policy_breach = AAZBoolArg(
            options=["rollback-failed-instances-on-policy-breach"],
            help="Rollback failed instances to previous model if the Rolling Upgrade policy is violated.",
        )

        virtual_machine_profile = cls._args_schema.virtual_machine_profile
        virtual_machine_profile.billing_profile = AAZObjectArg(
            options=["billing-profile"],
            help="Specifies the billing related details of a Azure Spot VMSS. Minimum api-version: 2019-03-01.",
        )
        virtual_machine_profile.diagnostics_profile = AAZObjectArg(
            options=["diagnostics-profile"],
            help="The virtual machine scale set diagnostics profile.",
        )
        virtual_machine_profile.extension_profile = AAZObjectArg(
            options=["extension-profile"],
            help="The virtual machine scale set extension profile.",
        )
        virtual_machine_profile.hardware_profile = AAZObjectArg(
            options=["hardware-profile"],
            help="Specifies the hardware profile related details of a scale set. Minimum api-version: 2021-11-01.",
        )
        virtual_machine_profile.license_type = AAZStrArg(
            options=["license-type"],
            help="The license type, which is for bring your own license scenario.",
        )
        virtual_machine_profile.network_profile = AAZObjectArg(
            options=["network-profile"],
            help="The virtual machine scale set network profile.",
        )
        virtual_machine_profile.os_profile = AAZObjectArg(
            options=["os-profile"],
            help="The virtual machine scale set OS profile.",
        )
        virtual_machine_profile.scheduled_events_profile = AAZObjectArg(
            options=["scheduled-events-profile"],
            help="Specifies Scheduled Event related configurations.",
        )
        virtual_machine_profile.security_posture_reference = AAZObjectArg(
            options=["security-posture-reference"],
            help="The virtual machine scale set security posture reference.",
        )
        virtual_machine_profile.security_profile = AAZObjectArg(
            options=["security-profile"],
            help="The virtual machine scale set Security profile",
        )
        virtual_machine_profile.storage_profile = AAZObjectArg(
            options=["storage-profile"],
            help="The virtual machine scale set storage profile.",
        )
        virtual_machine_profile.user_data = AAZStrArg(
            options=["user-data"],
            help="UserData for the VM, which must be base-64 encoded. Customer should not pass any secrets in here. <br><br>Minimum api-version: 2021-03-01",
        )

        billing_profile = cls._args_schema.virtual_machine_profile.billing_profile
        billing_profile.max_price = AAZFloatArg(
            options=["max-price"],
            help="Specifies the maximum price you are willing to pay for a Azure Spot VM/VMSS. This price is in US Dollars. <br><br> This price will be compared with the current Azure Spot price for the VM size. Also, the prices are compared at the time of create/update of Azure Spot VM/VMSS and the operation will only succeed if  the maxPrice is greater than the current Azure Spot price. <br><br> The maxPrice will also be used for evicting a Azure Spot VM/VMSS if the current Azure Spot price goes beyond the maxPrice after creation of VM/VMSS. <br><br> Possible values are: <br><br> - Any decimal value greater than zero. Example: 0.01538 <br><br> -1 – indicates default price to be up-to on-demand. <br><br> You can set the maxPrice to -1 to indicate that the Azure Spot VM/VMSS should not be evicted for price reasons. Also, the default max price is -1 if it is not provided by you. <br><br>Minimum api-version: 2019-03-01.",
        )

        diagnostics_profile = cls._args_schema.virtual_machine_profile.diagnostics_profile
        diagnostics_profile.boot_diagnostics = AAZObjectArg(
            options=["boot-diagnostics"],
            help="Boot Diagnostics is a debugging feature which allows you to view Console Output and Screenshot to diagnose VM status. **NOTE**: If storageUri is being specified then ensure that the storage account is in the same region and subscription as the VM. You can easily view the output of your console log. Azure also enables you to see a screenshot of the VM from the hypervisor.",
        )

        boot_diagnostics = cls._args_schema.virtual_machine_profile.diagnostics_profile.boot_diagnostics
        boot_diagnostics.enabled = AAZBoolArg(
            options=["enabled"],
            help="Whether boot diagnostics should be enabled on the Virtual Machine.",
        )
        boot_diagnostics.storage_uri = AAZStrArg(
            options=["storage-uri"],
            help="Uri of the storage account to use for placing the console output and screenshot. If storageUri is not specified while enabling boot diagnostics, managed storage will be used.",
        )

        extension_profile = cls._args_schema.virtual_machine_profile.extension_profile
        extension_profile.extensions = AAZListArg(
            options=["extensions"],
            help="The virtual machine scale set child extension resources.",
        )
        extension_profile.extensions_time_budget = AAZStrArg(
            options=["extensions-time-budget"],
            help="Specifies the time alloted for all extensions to start. The time duration should be between 15 minutes and 120 minutes (inclusive) and should be specified in ISO 8601 format. The default value is 90 minutes (PT1H30M). Minimum api-version: 2020-06-01.",
        )

        extensions = cls._args_schema.virtual_machine_profile.extension_profile.extensions
        extensions.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.extension_profile.extensions.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the extension.",
        )
        _element.auto_upgrade_minor_version = AAZBoolArg(
            options=["auto-upgrade-minor-version"],
            help="Indicates whether the extension should use a newer minor version if one is available at deployment time. Once deployed, however, the extension will not upgrade minor versions unless redeployed, even with this property set to true.",
        )
        _element.enable_automatic_upgrade = AAZBoolArg(
            options=["enable-automatic-upgrade"],
            help="Indicates whether the extension should be automatically upgraded by the platform if there is a newer version of the extension available.",
        )
        _element.force_update_tag = AAZStrArg(
            options=["force-update-tag"],
            help="If a value is provided and is different from the previous value, the extension handler will be forced to update even if the extension configuration has not changed.",
        )
        _element.protected_settings = AAZFreeFormDictArg(
            options=["protected-settings"],
            help="The extension can contain either protectedSettings or protectedSettingsFromKeyVault or no protected settings at all.",
        )
        _element.protected_settings_from_key_vault = AAZObjectArg(
            options=["protected-settings-from-key-vault"],
            help="The extensions protected settings that are passed by reference, and consumed from key vault",
        )
        _element.provision_after_extensions = AAZListArg(
            options=["provision-after-extensions"],
            help="Collection of extension names after which this extension needs to be provisioned.",
        )
        _element.publisher = AAZStrArg(
            options=["publisher"],
            help="The name of the extension handler publisher.",
        )
        _element.settings = AAZFreeFormDictArg(
            options=["settings"],
            help="Json formatted public settings for the extension.",
        )
        _element.suppress_failures = AAZBoolArg(
            options=["suppress-failures"],
            help="Indicates whether failures stemming from the extension will be suppressed (Operational failures such as not connecting to the VM will not be suppressed regardless of this value). The default is false.",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="Specifies the type of the extension; an example is \"CustomScriptExtension\".",
        )
        _element.type_handler_version = AAZStrArg(
            options=["type-handler-version"],
            help="Specifies the version of the script handler.",
        )

        protected_settings_from_key_vault = cls._args_schema.virtual_machine_profile.extension_profile.extensions.Element.protected_settings_from_key_vault
        protected_settings_from_key_vault.secret_url = AAZStrArg(
            options=["secret-url"],
            help="The URL referencing a secret in a Key Vault.",
            required=True,
        )
        protected_settings_from_key_vault.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="The relative URL of the Key Vault containing the secret.",
            required=True,
        )
        cls._build_args_sub_resource_update(protected_settings_from_key_vault.source_vault)

        provision_after_extensions = cls._args_schema.virtual_machine_profile.extension_profile.extensions.Element.provision_after_extensions
        provision_after_extensions.Element = AAZStrArg()

        hardware_profile = cls._args_schema.virtual_machine_profile.hardware_profile
        hardware_profile.vm_size_properties = AAZObjectArg(
            options=["vm-size-properties"],
            help="Specifies the properties for customizing the size of the virtual machine. Minimum api-version: 2021-11-01. Please follow the instructions in [VM Customization](https://aka.ms/vmcustomization) for more details.",
        )

        vm_size_properties = cls._args_schema.virtual_machine_profile.hardware_profile.vm_size_properties
        vm_size_properties.v_cp_us_available = AAZIntArg(
            options=["v-cp-us-available"],
            help="Specifies the number of vCPUs available for the VM. When this property is not specified in the request body the default behavior is to set it to the value of vCPUs available for that VM size exposed in api response of [List all available virtual machine sizes in a region](https://docs.microsoft.com/en-us/rest/api/compute/resource-skus/list).",
        )
        vm_size_properties.v_cp_us_per_core = AAZIntArg(
            options=["v-cp-us-per-core"],
            help="Specifies the vCPU to physical core ratio. When this property is not specified in the request body the default behavior is set to the value of vCPUsPerCore for the VM Size exposed in api response of [List all available virtual machine sizes in a region](https://docs.microsoft.com/en-us/rest/api/compute/resource-skus/list). **Setting this property to 1 also means that hyper-threading is disabled.**",
        )

        network_profile = cls._args_schema.virtual_machine_profile.network_profile
        network_profile.health_probe = AAZObjectArg(
            options=["health-probe"],
            help="A reference to a load balancer probe used to determine the health of an instance in the virtual machine scale set. The reference will be in the form: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/loadBalancers/{loadBalancerName}/probes/{probeName}'.",
        )
        cls._build_args_api_entity_reference_update(network_profile.health_probe)
        network_profile.network_api_version = AAZStrArg(
            options=["network-api-version"],
            help="Specifies the Microsoft.Network API version used when creating networking resources in the Network Interface Configurations for Virtual Machine Scale Set with orchestration mode 'Flexible'. For support of all network properties, use '2022-11-01'.",
            enum={"2020-11-01": "2020-11-01", "2022-11-01": "2022-11-01"},
        )
        network_profile.network_interface_configurations = AAZListArg(
            options=["network-interface-configurations"],
            help="The list of network configurations.",
        )

        network_interface_configurations = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations
        network_interface_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The network configuration name.",
        )
        _element.auxiliary_mode = AAZStrArg(
            options=["auxiliary-mode"],
            help="Specifies whether the Auxiliary mode is enabled for the Network Interface resource.",
            enum={"AcceleratedConnections": "AcceleratedConnections", "Floating": "Floating", "None": "None"},
        )
        _element.auxiliary_sku = AAZStrArg(
            options=["auxiliary-sku"],
            help="Specifies whether the Auxiliary sku is enabled for the Network Interface resource.",
            enum={"A1": "A1", "A2": "A2", "A4": "A4", "A8": "A8", "None": "None"},
        )
        _element.delete_option = AAZStrArg(
            options=["delete-option"],
            help="Specify what happens to the network interface when the VM is deleted",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        _element.disable_tcp_state_tracking = AAZBoolArg(
            options=["disable-tcp-state-tracking"],
            help="Specifies whether the network interface is disabled for tcp state tracking.",
        )
        _element.dns_settings = AAZObjectArg(
            options=["dns-settings"],
            help="The dns settings to be applied on the network interfaces.",
        )
        _element.enable_accelerated_networking = AAZBoolArg(
            options=["enable-accelerated-networking"],
            help="Specifies whether the network interface is accelerated networking-enabled.",
        )
        _element.enable_fpga = AAZBoolArg(
            options=["enable-fpga"],
            help="Specifies whether the network interface is FPGA networking-enabled.",
        )
        _element.enable_ip_forwarding = AAZBoolArg(
            options=["enable-ip-forwarding"],
            help="Whether IP forwarding enabled on this NIC.",
        )
        _element.ip_configurations = AAZListArg(
            options=["ip-configurations"],
            help="The virtual machine scale set IP Configuration.",
        )
        _element.network_security_group = AAZObjectArg(
            options=["network-security-group"],
            help="The network security group.",
        )
        cls._build_args_sub_resource_update(_element.network_security_group)
        _element.primary = AAZBoolArg(
            options=["primary"],
            help="Whether this is a primary NIC on a virtual machine.",
        )

        dns_settings = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.dns_settings
        dns_settings.dns_servers = AAZListArg(
            options=["dns-servers"],
            help="List of DNS servers IP addresses",
        )

        dns_servers = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.dns_settings.dns_servers
        dns_servers.Element = AAZStrArg()

        ip_configurations = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations
        ip_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The IP configuration name.",
        )
        _element.application_gateway_backend_address_pools = AAZListArg(
            options=["application-gateway-backend-address-pools"],
            help="The application gateway backend address pools.",
        )
        _element.application_security_groups = AAZListArg(
            options=["application-security-groups"],
            help="Specifies an array of references to application security group.",
        )
        _element.load_balancer_backend_address_pools = AAZListArg(
            options=["load-balancer-backend-address-pools"],
            help="The load balancer backend address pools.",
        )
        _element.load_balancer_inbound_nat_pools = AAZListArg(
            options=["load-balancer-inbound-nat-pools"],
            help="The load balancer inbound nat pools.",
        )
        _element.primary = AAZBoolArg(
            options=["primary"],
            help="Specifies the primary IP Configuration in case the network interface has more than one IP Configuration.",
        )
        _element.private_ip_address_version = AAZStrArg(
            options=["private-ip-address-version"],
            help="Available from Api-Version 2017-03-30 onwards, it represents whether the specific ipconfiguration is IPv4 or IPv6. Default is taken as IPv4.  Possible values are: 'IPv4' and 'IPv6'.",
            enum={"IPv4": "IPv4", "IPv6": "IPv6"},
        )
        _element.public_ip_address_configuration = AAZObjectArg(
            options=["public-ip-address-configuration"],
            help="The publicIPAddressConfiguration.",
        )
        _element.subnet = AAZObjectArg(
            options=["subnet"],
            help="The subnet.",
        )
        cls._build_args_api_entity_reference_update(_element.subnet)

        application_gateway_backend_address_pools = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations.Element.application_gateway_backend_address_pools
        application_gateway_backend_address_pools.Element = AAZObjectArg()
        cls._build_args_sub_resource_update(application_gateway_backend_address_pools.Element)

        application_security_groups = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations.Element.application_security_groups
        application_security_groups.Element = AAZObjectArg()
        cls._build_args_sub_resource_update(application_security_groups.Element)

        load_balancer_backend_address_pools = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations.Element.load_balancer_backend_address_pools
        load_balancer_backend_address_pools.Element = AAZObjectArg()
        cls._build_args_sub_resource_update(load_balancer_backend_address_pools.Element)

        load_balancer_inbound_nat_pools = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations.Element.load_balancer_inbound_nat_pools
        load_balancer_inbound_nat_pools.Element = AAZObjectArg()
        cls._build_args_sub_resource_update(load_balancer_inbound_nat_pools.Element)

        public_ip_address_configuration = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations.Element.public_ip_address_configuration
        public_ip_address_configuration.name = AAZStrArg(
            options=["name"],
            help="The publicIP address configuration name.",
        )
        public_ip_address_configuration.delete_option = AAZStrArg(
            options=["delete-option"],
            help="Specify what happens to the public IP when the VM is deleted",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        public_ip_address_configuration.dns_settings = AAZObjectArg(
            options=["dns-settings"],
            help="The dns settings to be applied on the publicIP addresses .",
        )
        public_ip_address_configuration.idle_timeout_in_minutes = AAZIntArg(
            options=["idle-timeout-in-minutes"],
            help="The idle timeout of the public IP address.",
        )
        public_ip_address_configuration.public_ip_prefix = AAZObjectArg(
            options=["public-ip-prefix"],
            help="The PublicIPPrefix from which to allocate publicIP addresses.",
        )
        cls._build_args_sub_resource_update(public_ip_address_configuration.public_ip_prefix)

        dns_settings = cls._args_schema.virtual_machine_profile.network_profile.network_interface_configurations.Element.ip_configurations.Element.public_ip_address_configuration.dns_settings
        dns_settings.domain_name_label = AAZStrArg(
            options=["domain-name-label"],
            help="The Domain name label.The concatenation of the domain name label and vm index will be the domain name labels of the PublicIPAddress resources that will be created",
            required=True,
        )
        dns_settings.domain_name_label_scope = AAZStrArg(
            options=["domain-name-label-scope"],
            help="The Domain name label scope.The concatenation of the hashed domain name label that generated according to the policy from domain name label scope and vm index will be the domain name labels of the PublicIPAddress resources that will be created",
            enum={"NoReuse": "NoReuse", "ResourceGroupReuse": "ResourceGroupReuse", "SubscriptionReuse": "SubscriptionReuse", "TenantReuse": "TenantReuse"},
        )

        os_profile = cls._args_schema.virtual_machine_profile.os_profile
        os_profile.custom_data = AAZStrArg(
            options=["custom-data"],
            help="A base-64 encoded string of custom data.",
        )
        os_profile.linux_configuration = AAZObjectArg(
            options=["linux-configuration"],
            help="The Linux Configuration of the OS profile.",
        )
        os_profile.secrets = AAZListArg(
            options=["secrets"],
            help="The List of certificates for addition to the VM.",
        )
        os_profile.windows_configuration = AAZObjectArg(
            options=["windows-configuration"],
            help="The Windows Configuration of the OS profile.",
        )

        linux_configuration = cls._args_schema.virtual_machine_profile.os_profile.linux_configuration
        linux_configuration.disable_password_authentication = AAZBoolArg(
            options=["disable-password-authentication"],
            help="Specifies whether password authentication should be disabled.",
        )
        linux_configuration.enable_vm_agent_platform_updates = AAZBoolArg(
            options=["enable-vm-agent-platform-updates"],
            help="Indicates whether VMAgent Platform Updates is enabled for the Linux virtual machine. Default value is false.",
        )
        linux_configuration.patch_settings = AAZObjectArg(
            options=["patch-settings"],
            help="[Preview Feature] Specifies settings related to VM Guest Patching on Linux.",
        )
        linux_configuration.provision_vm_agent = AAZBoolArg(
            options=["provision-vm-agent"],
            help="Indicates whether virtual machine agent should be provisioned on the virtual machine. When this property is not specified in the request body, default behavior is to set it to true. This will ensure that VM Agent is installed on the VM so that extensions can be added to the VM later.",
        )
        linux_configuration.ssh = AAZObjectArg(
            options=["ssh"],
            help="Specifies the ssh key configuration for a Linux OS.",
        )

        patch_settings = cls._args_schema.virtual_machine_profile.os_profile.linux_configuration.patch_settings
        patch_settings.assessment_mode = AAZStrArg(
            options=["assessment-mode"],
            help="Specifies the mode of VM Guest Patch Assessment for the IaaS virtual machine.<br /><br /> Possible values are:<br /><br /> **ImageDefault** - You control the timing of patch assessments on a virtual machine. <br /><br /> **AutomaticByPlatform** - The platform will trigger periodic patch assessments. The property provisionVMAgent must be true.",
            enum={"AutomaticByPlatform": "AutomaticByPlatform", "ImageDefault": "ImageDefault"},
        )
        patch_settings.automatic_by_platform_settings = AAZObjectArg(
            options=["automatic-by-platform-settings"],
            help="Specifies additional settings for patch mode AutomaticByPlatform in VM Guest Patching on Linux.",
        )
        patch_settings.patch_mode = AAZStrArg(
            options=["patch-mode"],
            help="Specifies the mode of VM Guest Patching to IaaS virtual machine or virtual machines associated to virtual machine scale set with OrchestrationMode as Flexible.<br /><br /> Possible values are:<br /><br /> **ImageDefault** - The virtual machine's default patching configuration is used. <br /><br /> **AutomaticByPlatform** - The virtual machine will be automatically updated by the platform. The property provisionVMAgent must be true",
            enum={"AutomaticByPlatform": "AutomaticByPlatform", "ImageDefault": "ImageDefault"},
        )

        automatic_by_platform_settings = cls._args_schema.virtual_machine_profile.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
        automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolArg(
            options=["bypass-platform-safety-checks-on-user-schedule"],
            help="Enables customer to schedule patching without accidental upgrades",
        )
        automatic_by_platform_settings.reboot_setting = AAZStrArg(
            options=["reboot-setting"],
            help="Specifies the reboot setting for all AutomaticByPlatform patch installation operations.",
            enum={"Always": "Always", "IfRequired": "IfRequired", "Never": "Never", "Unknown": "Unknown"},
        )

        ssh = cls._args_schema.virtual_machine_profile.os_profile.linux_configuration.ssh
        ssh.public_keys = AAZListArg(
            options=["public-keys"],
            help="The list of SSH public keys used to authenticate with linux based VMs.",
        )

        public_keys = cls._args_schema.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys
        public_keys.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys.Element
        _element.key_data = AAZStrArg(
            options=["key-data"],
            help="SSH public key certificate used to authenticate with the VM through ssh. The key needs to be at least 2048-bit and in ssh-rsa format. For creating ssh keys, see [Create SSH keys on Linux and Mac for Linux VMs in Azure]https://docs.microsoft.com/azure/virtual-machines/linux/create-ssh-keys-detailed).",
        )
        _element.path = AAZStrArg(
            options=["path"],
            help="Specifies the full path on the created VM where ssh public key is stored. If the file already exists, the specified key is appended to the file. Example: /home/<USER>/.ssh/authorized_keys",
        )

        secrets = cls._args_schema.virtual_machine_profile.os_profile.secrets
        secrets.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.os_profile.secrets.Element
        _element.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="The relative URL of the Key Vault containing all of the certificates in VaultCertificates.",
        )
        cls._build_args_sub_resource_update(_element.source_vault)
        _element.vault_certificates = AAZListArg(
            options=["vault-certificates"],
            help="The list of key vault references in SourceVault which contain certificates.",
        )

        vault_certificates = cls._args_schema.virtual_machine_profile.os_profile.secrets.Element.vault_certificates
        vault_certificates.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.os_profile.secrets.Element.vault_certificates.Element
        _element.certificate_store = AAZStrArg(
            options=["certificate-store"],
            help="For Windows VMs, specifies the certificate store on the Virtual Machine to which the certificate should be added. The specified certificate store is implicitly in the LocalMachine account. For Linux VMs, the certificate file is placed under the /var/lib/waagent directory, with the file name &lt;UppercaseThumbprint&gt;.crt for the X509 certificate file and &lt;UppercaseThumbprint&gt;.prv for private key. Both of these files are .pem formatted.",
        )
        _element.certificate_url = AAZStrArg(
            options=["certificate-url"],
            help="This is the URL of a certificate that has been uploaded to Key Vault as a secret. For adding a secret to the Key Vault, see [Add a key or secret to the key vault](https://docs.microsoft.com/azure/key-vault/key-vault-get-started/#add). In this case, your certificate needs to be It is the Base64 encoding of the following JSON Object which is encoded in UTF-8: <br><br> {<br>  \"data\":\"<Base64-encoded-certificate>\",<br>  \"dataType\":\"pfx\",<br>  \"password\":\"<pfx-file-password>\"<br>} <br> To install certificates on a virtual machine it is recommended to use the [Azure Key Vault virtual machine extension for Linux](https://docs.microsoft.com/azure/virtual-machines/extensions/key-vault-linux) or the [Azure Key Vault virtual machine extension for Windows](https://docs.microsoft.com/azure/virtual-machines/extensions/key-vault-windows).",
        )

        windows_configuration = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration
        windows_configuration.additional_unattend_content = AAZListArg(
            options=["additional-unattend-content"],
            help="Specifies additional base-64 encoded XML formatted information that can be included in the Unattend.xml file, which is used by Windows Setup.",
        )
        windows_configuration.enable_automatic_updates = AAZBoolArg(
            options=["enable-automatic-updates"],
            help="Indicates whether Automatic Updates is enabled for the Windows virtual machine. Default value is true. For virtual machine scale sets, this property can be updated and updates will take effect on OS reprovisioning.",
        )
        windows_configuration.patch_settings = AAZObjectArg(
            options=["patch-settings"],
            help="[Preview Feature] Specifies settings related to VM Guest Patching on Windows.",
        )
        windows_configuration.provision_vm_agent = AAZBoolArg(
            options=["provision-vm-agent"],
            help="Indicates whether virtual machine agent should be provisioned on the virtual machine. When this property is not specified in the request body, it is set to true by default. This will ensure that VM Agent is installed on the VM so that extensions can be added to the VM later.",
        )
        windows_configuration.time_zone = AAZStrArg(
            options=["time-zone"],
            help="Specifies the time zone of the virtual machine. e.g. \"Pacific Standard Time\". Possible values can be [TimeZoneInfo.Id](https://docs.microsoft.com/dotnet/api/system.timezoneinfo.id?#System_TimeZoneInfo_Id) value from time zones returned by [TimeZoneInfo.GetSystemTimeZones](https://docs.microsoft.com/dotnet/api/system.timezoneinfo.getsystemtimezones).",
        )
        windows_configuration.win_rm = AAZObjectArg(
            options=["win-rm"],
            help="Specifies the Windows Remote Management listeners. This enables remote Windows PowerShell.",
        )

        additional_unattend_content = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content
        additional_unattend_content.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content.Element
        _element.component_name = AAZStrArg(
            options=["component-name"],
            help="The component name. Currently, the only allowable value is Microsoft-Windows-Shell-Setup.",
            enum={"Microsoft-Windows-Shell-Setup": "Microsoft-Windows-Shell-Setup"},
        )
        _element.content = AAZStrArg(
            options=["content"],
            help="Specifies the XML formatted content that is added to the unattend.xml file for the specified path and component. The XML must be less than 4KB and must include the root element for the setting or feature that is being inserted.",
        )
        _element.pass_name = AAZStrArg(
            options=["pass-name"],
            help="The pass name. Currently, the only allowable value is OobeSystem.",
            enum={"OobeSystem": "OobeSystem"},
        )
        _element.setting_name = AAZStrArg(
            options=["setting-name"],
            help="Specifies the name of the setting to which the content applies. Possible values are: FirstLogonCommands and AutoLogon.",
            enum={"AutoLogon": "AutoLogon", "FirstLogonCommands": "FirstLogonCommands"},
        )

        patch_settings = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration.patch_settings
        patch_settings.assessment_mode = AAZStrArg(
            options=["assessment-mode"],
            help="Specifies the mode of VM Guest patch assessment for the IaaS virtual machine.<br /><br /> Possible values are:<br /><br /> **ImageDefault** - You control the timing of patch assessments on a virtual machine.<br /><br /> **AutomaticByPlatform** - The platform will trigger periodic patch assessments. The property provisionVMAgent must be true. ",
            enum={"AutomaticByPlatform": "AutomaticByPlatform", "ImageDefault": "ImageDefault"},
        )
        patch_settings.automatic_by_platform_settings = AAZObjectArg(
            options=["automatic-by-platform-settings"],
            help="Specifies additional settings for patch mode AutomaticByPlatform in VM Guest Patching on Windows.",
        )
        patch_settings.enable_hotpatching = AAZBoolArg(
            options=["enable-hotpatching"],
            help="Enables customers to patch their Azure VMs without requiring a reboot. For enableHotpatching, the 'provisionVMAgent' must be set to true and 'patchMode' must be set to 'AutomaticByPlatform'.",
        )
        patch_settings.patch_mode = AAZStrArg(
            options=["patch-mode"],
            help="Specifies the mode of VM Guest Patching to IaaS virtual machine or virtual machines associated to virtual machine scale set with OrchestrationMode as Flexible.<br /><br /> Possible values are:<br /><br /> **Manual** - You  control the application of patches to a virtual machine. You do this by applying patches manually inside the VM. In this mode, automatic updates are disabled; the property WindowsConfiguration.enableAutomaticUpdates must be false<br /><br /> **AutomaticByOS** - The virtual machine will automatically be updated by the OS. The property WindowsConfiguration.enableAutomaticUpdates must be true. <br /><br /> **AutomaticByPlatform** - the virtual machine will automatically updated by the platform. The properties provisionVMAgent and WindowsConfiguration.enableAutomaticUpdates must be true ",
            enum={"AutomaticByOS": "AutomaticByOS", "AutomaticByPlatform": "AutomaticByPlatform", "Manual": "Manual"},
        )

        automatic_by_platform_settings = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
        automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolArg(
            options=["bypass-platform-safety-checks-on-user-schedule"],
            help="Enables customer to schedule patching without accidental upgrades",
        )
        automatic_by_platform_settings.reboot_setting = AAZStrArg(
            options=["reboot-setting"],
            help="Specifies the reboot setting for all AutomaticByPlatform patch installation operations.",
            enum={"Always": "Always", "IfRequired": "IfRequired", "Never": "Never", "Unknown": "Unknown"},
        )

        win_rm = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration.win_rm
        win_rm.listeners = AAZListArg(
            options=["listeners"],
            help="The list of Windows Remote Management listeners",
        )

        listeners = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners
        listeners.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners.Element
        _element.certificate_url = AAZStrArg(
            options=["certificate-url"],
            help="This is the URL of a certificate that has been uploaded to Key Vault as a secret. For adding a secret to the Key Vault, see [Add a key or secret to the key vault](https://docs.microsoft.com/azure/key-vault/key-vault-get-started/#add). In this case, your certificate needs to be the Base64 encoding of the following JSON Object which is encoded in UTF-8: <br><br> {<br>  \"data\":\"<Base64-encoded-certificate>\",<br>  \"dataType\":\"pfx\",<br>  \"password\":\"<pfx-file-password>\"<br>} <br> To install certificates on a virtual machine it is recommended to use the [Azure Key Vault virtual machine extension for Linux](https://docs.microsoft.com/azure/virtual-machines/extensions/key-vault-linux) or the [Azure Key Vault virtual machine extension for Windows](https://docs.microsoft.com/azure/virtual-machines/extensions/key-vault-windows).",
        )
        _element.protocol = AAZStrArg(
            options=["protocol"],
            help="Specifies the protocol of WinRM listener. Possible values are: **http,** **https.**",
            enum={"Http": "Http", "Https": "Https"},
        )

        scheduled_events_profile = cls._args_schema.virtual_machine_profile.scheduled_events_profile
        scheduled_events_profile.os_image_notification_profile = AAZObjectArg(
            options=["os-image-notification-profile"],
            help="Specifies OS Image Scheduled Event related configurations.",
        )
        scheduled_events_profile.terminate_notification_profile = AAZObjectArg(
            options=["terminate-notification-profile"],
            help="Specifies Terminate Scheduled Event related configurations.",
        )

        os_image_notification_profile = cls._args_schema.virtual_machine_profile.scheduled_events_profile.os_image_notification_profile
        os_image_notification_profile.enable = AAZBoolArg(
            options=["enable"],
            help="Specifies whether the OS Image Scheduled event is enabled or disabled.",
        )
        os_image_notification_profile.not_before_timeout = AAZStrArg(
            options=["not-before-timeout"],
            help="Length of time a Virtual Machine being reimaged or having its OS upgraded will have to potentially approve the OS Image Scheduled Event before the event is auto approved (timed out). The configuration is specified in ISO 8601 format, and the value must be 15 minutes (PT15M)",
        )

        terminate_notification_profile = cls._args_schema.virtual_machine_profile.scheduled_events_profile.terminate_notification_profile
        terminate_notification_profile.enable = AAZBoolArg(
            options=["enable"],
            help="Specifies whether the Terminate Scheduled event is enabled or disabled.",
        )
        terminate_notification_profile.not_before_timeout = AAZStrArg(
            options=["not-before-timeout"],
            help="Configurable length of time a Virtual Machine being deleted will have to potentially approve the Terminate Scheduled Event before the event is auto approved (timed out). The configuration must be specified in ISO 8601 format, the default value is 5 minutes (PT5M)",
        )

        security_posture_reference = cls._args_schema.virtual_machine_profile.security_posture_reference
        security_posture_reference.exclude_extensions = AAZListArg(
            options=["exclude-extensions"],
            help="The list of virtual machine extension names to exclude when applying the security posture.",
        )
        security_posture_reference.id = AAZStrArg(
            options=["id"],
            help="The security posture reference id in the form of /CommunityGalleries/{communityGalleryName}/securityPostures/{securityPostureName}/versions/{major.minor.patch}|latest",
        )
        security_posture_reference.is_overridable = AAZBoolArg(
            options=["is-overridable"],
            help="Whether the security posture can be overridden by the user.",
        )

        exclude_extensions = cls._args_schema.virtual_machine_profile.security_posture_reference.exclude_extensions
        exclude_extensions.Element = AAZStrArg()

        security_profile = cls._args_schema.virtual_machine_profile.security_profile
        security_profile.encryption_at_host = AAZBoolArg(
            options=["encryption-at-host"],
            help="This property can be used by user in the request to enable or disable the Host Encryption for the virtual machine or virtual machine scale set. This will enable the encryption for all the disks including Resource/Temp disk at host itself. The default behavior is: The Encryption at host will be disabled unless this property is set to true for the resource.",
        )
        security_profile.encryption_identity = AAZObjectArg(
            options=["encryption-identity"],
            help="Specifies the Managed Identity used by ADE to get access token for keyvault operations.",
        )
        security_profile.proxy_agent_settings = AAZObjectArg(
            options=["proxy-agent-settings"],
            help="Specifies ProxyAgent settings while creating the virtual machine. Minimum api-version: 2023-09-01.",
        )
        security_profile.security_type = AAZStrArg(
            options=["security-type"],
            help="Specifies the SecurityType of the virtual machine. It has to be set to any specified value to enable UefiSettings. The default behavior is: UefiSettings will not be enabled unless this property is set.",
            enum={"ConfidentialVM": "ConfidentialVM", "TrustedLaunch": "TrustedLaunch"},
        )
        security_profile.uefi_settings = AAZObjectArg(
            options=["uefi-settings"],
            help="Specifies the security settings like secure boot and vTPM used while creating the virtual machine. Minimum api-version: 2020-12-01.",
        )

        encryption_identity = cls._args_schema.virtual_machine_profile.security_profile.encryption_identity
        encryption_identity.user_assigned_identity_resource_id = AAZStrArg(
            options=["user-assigned-identity-resource-id"],
            help="Specifies ARM Resource ID of one of the user identities associated with the VM.",
        )

        proxy_agent_settings = cls._args_schema.virtual_machine_profile.security_profile.proxy_agent_settings
        proxy_agent_settings.enabled = AAZBoolArg(
            options=["enabled"],
            help="Specifies whether ProxyAgent feature should be enabled on the virtual machine or virtual machine scale set.",
        )
        proxy_agent_settings.imds = AAZObjectArg(
            options=["imds"],
            help="Specifies the IMDS endpoint settings while creating the virtual machine or virtual machine scale set. Minimum api-version: 2024-03-01.",
        )
        cls._build_args_host_endpoint_settings_update(proxy_agent_settings.imds)
        proxy_agent_settings.key_incarnation_id = AAZIntArg(
            options=["key-incarnation-id"],
            help="Increase the value of this property allows users to reset the key used for securing communication channel between guest and host.",
        )
        proxy_agent_settings.mode = AAZStrArg(
            options=["mode"],
            help="Specifies the mode that ProxyAgent will execute on. Warning: this property has been deprecated, please specify 'mode' under particular hostendpoint setting.",
            enum={"Audit": "Audit", "Enforce": "Enforce"},
        )
        proxy_agent_settings.wire_server = AAZObjectArg(
            options=["wire-server"],
            help="Specifies the Wire Server endpoint settings while creating the virtual machine or virtual machine scale set. Minimum api-version: 2024-03-01.",
        )
        cls._build_args_host_endpoint_settings_update(proxy_agent_settings.wire_server)

        uefi_settings = cls._args_schema.virtual_machine_profile.security_profile.uefi_settings
        uefi_settings.secure_boot_enabled = AAZBoolArg(
            options=["secure-boot-enabled"],
            help="Specifies whether secure boot should be enabled on the virtual machine. Minimum api-version: 2020-12-01.",
        )
        uefi_settings.v_tpm_enabled = AAZBoolArg(
            options=["v-tpm-enabled"],
            help="Specifies whether vTPM should be enabled on the virtual machine. Minimum api-version: 2020-12-01.",
        )

        storage_profile = cls._args_schema.virtual_machine_profile.storage_profile
        storage_profile.data_disks = AAZListArg(
            options=["data-disks"],
            help="The data disks.",
        )
        storage_profile.disk_controller_type = AAZStrArg(
            options=["disk-controller-type"],
        )
        storage_profile.image_reference = AAZObjectArg(
            options=["image-reference"],
            help="The image reference.",
        )
        storage_profile.os_disk = AAZObjectArg(
            options=["os-disk"],
            help="The OS disk.",
        )

        data_disks = cls._args_schema.virtual_machine_profile.storage_profile.data_disks
        data_disks.Element = AAZObjectArg()

        _element = cls._args_schema.virtual_machine_profile.storage_profile.data_disks.Element
        _element.caching = AAZStrArg(
            options=["caching"],
            help="Specifies the caching requirements. Possible values are: **None,** **ReadOnly,** **ReadWrite.** The default values are: **None for Standard storage. ReadOnly for Premium storage.**",
            enum={"None": "None", "ReadOnly": "ReadOnly", "ReadWrite": "ReadWrite"},
        )
        _element.create_option = AAZStrArg(
            options=["create-option"],
            help="The create option.",
            required=True,
            enum={"Attach": "Attach", "Copy": "Copy", "Empty": "Empty", "FromImage": "FromImage", "Restore": "Restore"},
        )
        _element.delete_option = AAZStrArg(
            options=["delete-option"],
            help="Specifies whether data disk should be deleted or detached upon VMSS Flex deletion (This feature is available for VMSS with Flexible OrchestrationMode only).<br><br> Possible values: <br><br> **Delete** If this value is used, the data disk is deleted when the VMSS Flex VM is deleted.<br><br> **Detach** If this value is used, the data disk is retained after VMSS Flex VM is deleted.<br><br> The default value is set to **Delete**.",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        _element.disk_iops_read_write = AAZIntArg(
            options=["disk-iops-read-write"],
            help="Specifies the Read-Write IOPS for the managed disk. Should be used only when StorageAccountType is UltraSSD_LRS. If not specified, a default value would be assigned based on diskSizeGB.",
        )
        _element.disk_m_bps_read_write = AAZIntArg(
            options=["disk-m-bps-read-write"],
            help="Specifies the bandwidth in MB per second for the managed disk. Should be used only when StorageAccountType is UltraSSD_LRS. If not specified, a default value would be assigned based on diskSizeGB.",
        )
        _element.disk_size_gb = AAZIntArg(
            options=["disk-size-gb"],
            help="Specifies the size of an empty data disk in gigabytes. This element can be used to overwrite the size of the disk in a virtual machine image. The property diskSizeGB is the number of bytes x 1024^3 for the disk and the value cannot be larger than 1023.",
        )
        _element.lun = AAZIntArg(
            options=["lun"],
            help="Specifies the logical unit number of the data disk. This value is used to identify data disks within the VM and therefore must be unique for each data disk attached to a VM.",
            required=True,
        )
        _element.managed_disk = AAZObjectArg(
            options=["managed-disk"],
            help="The managed disk parameters.",
        )
        cls._build_args_virtual_machine_scale_set_managed_disk_parameters_update(_element.managed_disk)
        _element.name = AAZStrArg(
            options=["name"],
            help="The disk name.",
        )
        _element.write_accelerator_enabled = AAZBoolArg(
            options=["write-accelerator-enabled"],
            help="Specifies whether writeAccelerator should be enabled or disabled on the disk.",
        )

        image_reference = cls._args_schema.virtual_machine_profile.storage_profile.image_reference
        image_reference.community_gallery_image_id = AAZStrArg(
            options=["community-gallery-image-id"],
            help="Specified the community gallery image unique id for vm deployment. This can be fetched from community gallery image GET call.",
        )
        image_reference.id = AAZStrArg(
            options=["id"],
            help="Resource Id",
        )
        image_reference.offer = AAZStrArg(
            options=["offer"],
            help="Specifies the offer of the platform image or marketplace image used to create the virtual machine.",
        )
        image_reference.publisher = AAZStrArg(
            options=["publisher"],
            help="The image publisher.",
        )
        image_reference.shared_gallery_image_id = AAZStrArg(
            options=["shared-gallery-image-id"],
            help="Specified the shared gallery image unique id for vm deployment. This can be fetched from shared gallery image GET call.",
        )
        image_reference.sku = AAZStrArg(
            options=["sku"],
            help="The image SKU.",
        )
        image_reference.version = AAZStrArg(
            options=["version"],
            help="Specifies the version of the platform image or marketplace image used to create the virtual machine. The allowed formats are Major.Minor.Build or 'latest'. Major, Minor, and Build are decimal numbers. Specify 'latest' to use the latest version of an image available at deploy time. Even if you use 'latest', the VM image will not automatically update after deploy time even if a new version becomes available. Please do not use field 'version' for gallery image deployment, gallery image should always use 'id' field for deployment, to use 'latest' version of gallery image, just set '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/images/{imageName}' in the 'id' field without version input.",
        )

        os_disk = cls._args_schema.virtual_machine_profile.storage_profile.os_disk
        os_disk.caching = AAZStrArg(
            options=["caching"],
            help="The caching type.",
            enum={"None": "None", "ReadOnly": "ReadOnly", "ReadWrite": "ReadWrite"},
        )
        os_disk.delete_option = AAZStrArg(
            options=["delete-option"],
            help="Specifies whether OS Disk should be deleted or detached upon VMSS Flex deletion (This feature is available for VMSS with Flexible OrchestrationMode only). <br><br> Possible values: <br><br> **Delete** If this value is used, the OS disk is deleted when VMSS Flex VM is deleted.<br><br> **Detach** If this value is used, the OS disk is retained after VMSS Flex VM is deleted. <br><br> The default value is set to **Delete**. For an Ephemeral OS Disk, the default value is set to **Delete**. User cannot change the delete option for Ephemeral OS Disk.",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        os_disk.diff_disk_settings = AAZObjectArg(
            options=["diff-disk-settings"],
            help="Specifies the ephemeral disk Settings for the operating system disk used by the virtual machine scale set.",
        )
        os_disk.disk_size_gb = AAZIntArg(
            options=["disk-size-gb"],
            help="Specifies the size of an empty data disk in gigabytes. This element can be used to overwrite the size of the disk in a virtual machine image. <br><br> diskSizeGB is the number of bytes x 1024^3 for the disk and the value cannot be larger than 1023",
        )
        os_disk.image = AAZObjectArg(
            options=["image"],
            help="The Source User Image VirtualHardDisk. This VirtualHardDisk will be copied before using it to attach to the Virtual Machine. If SourceImage is provided, the destination VirtualHardDisk should not exist.",
        )
        os_disk.managed_disk = AAZObjectArg(
            options=["managed-disk"],
            help="The managed disk parameters.",
        )
        cls._build_args_virtual_machine_scale_set_managed_disk_parameters_update(os_disk.managed_disk)
        os_disk.vhd_containers = AAZListArg(
            options=["vhd-containers"],
            help="The list of virtual hard disk container uris.",
        )
        os_disk.write_accelerator_enabled = AAZBoolArg(
            options=["write-accelerator-enabled"],
            help="Specifies whether writeAccelerator should be enabled or disabled on the disk.",
        )

        diff_disk_settings = cls._args_schema.virtual_machine_profile.storage_profile.os_disk.diff_disk_settings
        diff_disk_settings.option = AAZStrArg(
            options=["option"],
            help="Specifies the ephemeral disk settings for operating system disk.",
            enum={"Local": "Local"},
        )
        diff_disk_settings.placement = AAZStrArg(
            options=["placement"],
            help="Specifies the ephemeral disk placement for operating system disk. Possible values are: **CacheDisk,** **ResourceDisk,** **NvmeDisk.** The defaulting behavior is: **CacheDisk** if one is configured for the VM size otherwise **ResourceDisk** or **NvmeDisk** is used. Refer to the VM size documentation for Windows VM at https://docs.microsoft.com/azure/virtual-machines/windows/sizes and Linux VM at https://docs.microsoft.com/azure/virtual-machines/linux/sizes to check which VM sizes exposes a cache disk. Minimum api-version for NvmeDisk: 2024-03-01.",
            enum={"CacheDisk": "CacheDisk", "NvmeDisk": "NvmeDisk", "ResourceDisk": "ResourceDisk"},
        )

        image = cls._args_schema.virtual_machine_profile.storage_profile.os_disk.image
        image.uri = AAZStrArg(
            options=["uri"],
            help="Specifies the virtual hard disk's uri.",
        )

        vhd_containers = cls._args_schema.virtual_machine_profile.storage_profile.os_disk.vhd_containers
        vhd_containers.Element = AAZStrArg()
        return cls._args_schema

    _args_api_entity_reference_update = None

    @classmethod
    def _build_args_api_entity_reference_update(cls, _schema):
        if cls._args_api_entity_reference_update is not None:
            _schema.id = cls._args_api_entity_reference_update.id
            return

        cls._args_api_entity_reference_update = AAZObjectArg()

        api_entity_reference_update = cls._args_api_entity_reference_update
        api_entity_reference_update.id = AAZStrArg(
            options=["id"],
            help="The ARM resource id in the form of /subscriptions/{SubscriptionId}/resourceGroups/{ResourceGroupName}/...",
        )

        _schema.id = cls._args_api_entity_reference_update.id

    _args_disk_encryption_set_parameters_update = None

    @classmethod
    def _build_args_disk_encryption_set_parameters_update(cls, _schema):
        if cls._args_disk_encryption_set_parameters_update is not None:
            _schema.id = cls._args_disk_encryption_set_parameters_update.id
            return

        cls._args_disk_encryption_set_parameters_update = AAZObjectArg()

        disk_encryption_set_parameters_update = cls._args_disk_encryption_set_parameters_update
        disk_encryption_set_parameters_update.id = AAZStrArg(
            options=["id"],
            help="Resource Id",
        )

        _schema.id = cls._args_disk_encryption_set_parameters_update.id

    _args_host_endpoint_settings_update = None

    @classmethod
    def _build_args_host_endpoint_settings_update(cls, _schema):
        if cls._args_host_endpoint_settings_update is not None:
            _schema.in_vm_access_control_profile_reference_id = cls._args_host_endpoint_settings_update.in_vm_access_control_profile_reference_id
            _schema.mode = cls._args_host_endpoint_settings_update.mode
            return

        cls._args_host_endpoint_settings_update = AAZObjectArg()

        host_endpoint_settings_update = cls._args_host_endpoint_settings_update
        host_endpoint_settings_update.in_vm_access_control_profile_reference_id = AAZStrArg(
            options=["in-vm-access-control-profile-reference-id"],
            help="Specifies the InVMAccessControlProfileVersion resource id in the format of /subscriptions/{SubscriptionId}/resourceGroups/{ResourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/inVMAccessControlProfiles/{profile}/versions/{version}",
        )
        host_endpoint_settings_update.mode = AAZStrArg(
            options=["mode"],
            help="Specifies the execution mode. In Audit mode, the system acts as if it is enforcing the access control policy, including emitting access denial entries in the logs but it does not actually deny any requests to host endpoints. In Enforce mode, the system will enforce the access control and it is the recommended mode of operation.",
            enum={"Audit": "Audit", "Disabled": "Disabled", "Enforce": "Enforce"},
        )

        _schema.in_vm_access_control_profile_reference_id = cls._args_host_endpoint_settings_update.in_vm_access_control_profile_reference_id
        _schema.mode = cls._args_host_endpoint_settings_update.mode

    _args_sub_resource_update = None

    @classmethod
    def _build_args_sub_resource_update(cls, _schema):
        if cls._args_sub_resource_update is not None:
            _schema.id = cls._args_sub_resource_update.id
            return

        cls._args_sub_resource_update = AAZObjectArg()

        sub_resource_update = cls._args_sub_resource_update
        sub_resource_update.id = AAZStrArg(
            options=["id"],
            help="Resource Id",
        )

        _schema.id = cls._args_sub_resource_update.id

    _args_virtual_machine_scale_set_managed_disk_parameters_update = None

    @classmethod
    def _build_args_virtual_machine_scale_set_managed_disk_parameters_update(cls, _schema):
        if cls._args_virtual_machine_scale_set_managed_disk_parameters_update is not None:
            _schema.disk_encryption_set = cls._args_virtual_machine_scale_set_managed_disk_parameters_update.disk_encryption_set
            _schema.security_profile = cls._args_virtual_machine_scale_set_managed_disk_parameters_update.security_profile
            _schema.storage_account_type = cls._args_virtual_machine_scale_set_managed_disk_parameters_update.storage_account_type
            return

        cls._args_virtual_machine_scale_set_managed_disk_parameters_update = AAZObjectArg()

        virtual_machine_scale_set_managed_disk_parameters_update = cls._args_virtual_machine_scale_set_managed_disk_parameters_update
        virtual_machine_scale_set_managed_disk_parameters_update.disk_encryption_set = AAZObjectArg(
            options=["disk-encryption-set"],
            help="Specifies the customer managed disk encryption set resource id for the managed disk.",
        )
        cls._build_args_disk_encryption_set_parameters_update(virtual_machine_scale_set_managed_disk_parameters_update.disk_encryption_set)
        virtual_machine_scale_set_managed_disk_parameters_update.security_profile = AAZObjectArg(
            options=["security-profile"],
            help="Specifies the security profile for the managed disk.",
        )
        virtual_machine_scale_set_managed_disk_parameters_update.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type for the managed disk. NOTE: UltraSSD_LRS can only be used with data disks, it cannot be used with OS Disk.",
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Premium_ZRS": "Premium_ZRS", "StandardSSD_LRS": "StandardSSD_LRS", "StandardSSD_ZRS": "StandardSSD_ZRS", "Standard_LRS": "Standard_LRS", "UltraSSD_LRS": "UltraSSD_LRS"},
        )

        security_profile = cls._args_virtual_machine_scale_set_managed_disk_parameters_update.security_profile
        security_profile.disk_encryption_set = AAZObjectArg(
            options=["disk-encryption-set"],
            help="Specifies the customer managed disk encryption set resource id for the managed disk that is used for Customer Managed Key encrypted ConfidentialVM OS Disk and VMGuest blob.",
        )
        cls._build_args_disk_encryption_set_parameters_update(security_profile.disk_encryption_set)
        security_profile.security_encryption_type = AAZStrArg(
            options=["security-encryption-type"],
            help="Specifies the EncryptionType of the managed disk. It is set to DiskWithVMGuestState for encryption of the managed disk along with VMGuestState blob, VMGuestStateOnly for encryption of just the VMGuestState blob, and NonPersistedTPM for not persisting firmware state in the VMGuestState blob.. **Note:** It can be set for only Confidential VMs.",
            enum={"DiskWithVMGuestState": "DiskWithVMGuestState", "NonPersistedTPM": "NonPersistedTPM", "VMGuestStateOnly": "VMGuestStateOnly"},
        )

        _schema.disk_encryption_set = cls._args_virtual_machine_scale_set_managed_disk_parameters_update.disk_encryption_set
        _schema.security_profile = cls._args_virtual_machine_scale_set_managed_disk_parameters_update.security_profile
        _schema.storage_account_type = cls._args_virtual_machine_scale_set_managed_disk_parameters_update.storage_account_type

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualMachineScaleSetsUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualMachineScaleSetsUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachineScaleSets/{vmScaleSetName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PATCH"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmScaleSetName", self.ctx.args.vm_scale_set_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-11-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "If-Match", self.ctx.args.if_match,
                ),
                **self.serialize_header_param(
                    "If-None-Match", self.ctx.args.if_none_match,
                ),
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("identity", AAZIdentityObjectType)
            _builder.set_prop("plan", AAZObjectType, ".plan")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("sku", AAZObjectType, ".sku")
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zones")

            identity = _builder.get(".identity")
            if identity is not None:
                identity.set_prop("userAssigned", AAZListType, ".mi_user_assigned", typ_kwargs={"flags": {"action": "create"}})
                identity.set_prop("systemAssigned", AAZStrType, ".mi_system_assigned", typ_kwargs={"flags": {"action": "create"}})

            user_assigned = _builder.get(".identity.userAssigned")
            if user_assigned is not None:
                user_assigned.set_elements(AAZStrType, ".")

            plan = _builder.get(".plan")
            if plan is not None:
                plan.set_prop("name", AAZStrType, ".name")
                plan.set_prop("product", AAZStrType, ".product")
                plan.set_prop("promotionCode", AAZStrType, ".promotion_code")
                plan.set_prop("publisher", AAZStrType, ".publisher")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("additionalCapabilities", AAZObjectType, ".additional_capabilities")
                properties.set_prop("automaticRepairsPolicy", AAZObjectType, ".automatic_repairs_policy")
                properties.set_prop("doNotRunExtensionsOnOverprovisionedVMs", AAZBoolType, ".do_not_run_extensions_on_overprovisioned_v_ms")
                properties.set_prop("overprovision", AAZBoolType, ".overprovision")
                properties.set_prop("priorityMixPolicy", AAZObjectType, ".priority_mix_policy")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("proximityPlacementGroup", AAZObjectType, ".proximity_placement_group"))
                properties.set_prop("resiliencyPolicy", AAZObjectType, ".resiliency_policy")
                properties.set_prop("scaleInPolicy", AAZObjectType, ".scale_in_policy")
                properties.set_prop("singlePlacementGroup", AAZBoolType, ".single_placement_group")
                properties.set_prop("skuProfile", AAZObjectType, ".sku_profile")
                properties.set_prop("spotRestorePolicy", AAZObjectType, ".spot_restore_policy")
                properties.set_prop("upgradePolicy", AAZObjectType, ".upgrade_policy")
                properties.set_prop("virtualMachineProfile", AAZObjectType, ".virtual_machine_profile")
                properties.set_prop("zonalPlatformFaultDomainAlignMode", AAZStrType, ".zonal_platform_fault_domain_align_mode")

            additional_capabilities = _builder.get(".properties.additionalCapabilities")
            if additional_capabilities is not None:
                additional_capabilities.set_prop("hibernationEnabled", AAZBoolType, ".hibernation_enabled")
                additional_capabilities.set_prop("ultraSSDEnabled", AAZBoolType, ".ultra_ssd_enabled")

            automatic_repairs_policy = _builder.get(".properties.automaticRepairsPolicy")
            if automatic_repairs_policy is not None:
                automatic_repairs_policy.set_prop("enabled", AAZBoolType, ".enabled")
                automatic_repairs_policy.set_prop("gracePeriod", AAZStrType, ".grace_period")
                automatic_repairs_policy.set_prop("repairAction", AAZStrType, ".repair_action")

            priority_mix_policy = _builder.get(".properties.priorityMixPolicy")
            if priority_mix_policy is not None:
                priority_mix_policy.set_prop("baseRegularPriorityCount", AAZIntType, ".base_regular_priority_count")
                priority_mix_policy.set_prop("regularPriorityPercentageAboveBase", AAZIntType, ".regular_priority_percentage_above_base")

            resiliency_policy = _builder.get(".properties.resiliencyPolicy")
            if resiliency_policy is not None:
                resiliency_policy.set_prop("automaticZoneRebalancingPolicy", AAZObjectType, ".automatic_zone_rebalancing_policy")
                resiliency_policy.set_prop("resilientVMCreationPolicy", AAZObjectType, ".resilient_vm_creation_policy")
                resiliency_policy.set_prop("resilientVMDeletionPolicy", AAZObjectType, ".resilient_vm_deletion_policy")

            automatic_zone_rebalancing_policy = _builder.get(".properties.resiliencyPolicy.automaticZoneRebalancingPolicy")
            if automatic_zone_rebalancing_policy is not None:
                automatic_zone_rebalancing_policy.set_prop("enabled", AAZBoolType, ".enabled")
                automatic_zone_rebalancing_policy.set_prop("rebalanceBehavior", AAZStrType, ".rebalance_behavior")
                automatic_zone_rebalancing_policy.set_prop("rebalanceStrategy", AAZStrType, ".rebalance_strategy")

            resilient_vm_creation_policy = _builder.get(".properties.resiliencyPolicy.resilientVMCreationPolicy")
            if resilient_vm_creation_policy is not None:
                resilient_vm_creation_policy.set_prop("enabled", AAZBoolType, ".enabled")

            resilient_vm_deletion_policy = _builder.get(".properties.resiliencyPolicy.resilientVMDeletionPolicy")
            if resilient_vm_deletion_policy is not None:
                resilient_vm_deletion_policy.set_prop("enabled", AAZBoolType, ".enabled")

            scale_in_policy = _builder.get(".properties.scaleInPolicy")
            if scale_in_policy is not None:
                scale_in_policy.set_prop("forceDeletion", AAZBoolType, ".force_deletion")
                scale_in_policy.set_prop("prioritizeUnhealthyVMs", AAZBoolType, ".prioritize_unhealthy_v_ms")
                scale_in_policy.set_prop("rules", AAZListType, ".rules")

            rules = _builder.get(".properties.scaleInPolicy.rules")
            if rules is not None:
                rules.set_elements(AAZStrType, ".")

            sku_profile = _builder.get(".properties.skuProfile")
            if sku_profile is not None:
                sku_profile.set_prop("allocationStrategy", AAZStrType, ".allocation_strategy")
                sku_profile.set_prop("vmSizes", AAZListType, ".vm_sizes")

            vm_sizes = _builder.get(".properties.skuProfile.vmSizes")
            if vm_sizes is not None:
                vm_sizes.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.skuProfile.vmSizes[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("rank", AAZIntType, ".rank")

            spot_restore_policy = _builder.get(".properties.spotRestorePolicy")
            if spot_restore_policy is not None:
                spot_restore_policy.set_prop("enabled", AAZBoolType, ".enabled")
                spot_restore_policy.set_prop("restoreTimeout", AAZStrType, ".restore_timeout")

            upgrade_policy = _builder.get(".properties.upgradePolicy")
            if upgrade_policy is not None:
                upgrade_policy.set_prop("automaticOSUpgradePolicy", AAZObjectType, ".automatic_os_upgrade_policy")
                upgrade_policy.set_prop("mode", AAZStrType, ".mode")
                upgrade_policy.set_prop("rollingUpgradePolicy", AAZObjectType, ".rolling_upgrade_policy")

            automatic_os_upgrade_policy = _builder.get(".properties.upgradePolicy.automaticOSUpgradePolicy")
            if automatic_os_upgrade_policy is not None:
                automatic_os_upgrade_policy.set_prop("disableAutomaticRollback", AAZBoolType, ".disable_automatic_rollback")
                automatic_os_upgrade_policy.set_prop("enableAutomaticOSUpgrade", AAZBoolType, ".enable_automatic_os_upgrade")
                automatic_os_upgrade_policy.set_prop("osRollingUpgradeDeferral", AAZBoolType, ".os_rolling_upgrade_deferral")
                automatic_os_upgrade_policy.set_prop("useRollingUpgradePolicy", AAZBoolType, ".use_rolling_upgrade_policy")

            rolling_upgrade_policy = _builder.get(".properties.upgradePolicy.rollingUpgradePolicy")
            if rolling_upgrade_policy is not None:
                rolling_upgrade_policy.set_prop("enableCrossZoneUpgrade", AAZBoolType, ".enable_cross_zone_upgrade")
                rolling_upgrade_policy.set_prop("maxBatchInstancePercent", AAZIntType, ".max_batch_instance_percent")
                rolling_upgrade_policy.set_prop("maxSurge", AAZBoolType, ".max_surge")
                rolling_upgrade_policy.set_prop("maxUnhealthyInstancePercent", AAZIntType, ".max_unhealthy_instance_percent")
                rolling_upgrade_policy.set_prop("maxUnhealthyUpgradedInstancePercent", AAZIntType, ".max_unhealthy_upgraded_instance_percent")
                rolling_upgrade_policy.set_prop("pauseTimeBetweenBatches", AAZStrType, ".pause_time_between_batches")
                rolling_upgrade_policy.set_prop("prioritizeUnhealthyInstances", AAZBoolType, ".prioritize_unhealthy_instances")
                rolling_upgrade_policy.set_prop("rollbackFailedInstancesOnPolicyBreach", AAZBoolType, ".rollback_failed_instances_on_policy_breach")

            virtual_machine_profile = _builder.get(".properties.virtualMachineProfile")
            if virtual_machine_profile is not None:
                virtual_machine_profile.set_prop("billingProfile", AAZObjectType, ".billing_profile")
                virtual_machine_profile.set_prop("diagnosticsProfile", AAZObjectType, ".diagnostics_profile")
                virtual_machine_profile.set_prop("extensionProfile", AAZObjectType, ".extension_profile")
                virtual_machine_profile.set_prop("hardwareProfile", AAZObjectType, ".hardware_profile")
                virtual_machine_profile.set_prop("licenseType", AAZStrType, ".license_type")
                virtual_machine_profile.set_prop("networkProfile", AAZObjectType, ".network_profile")
                virtual_machine_profile.set_prop("osProfile", AAZObjectType, ".os_profile")
                virtual_machine_profile.set_prop("scheduledEventsProfile", AAZObjectType, ".scheduled_events_profile")
                virtual_machine_profile.set_prop("securityPostureReference", AAZObjectType, ".security_posture_reference")
                virtual_machine_profile.set_prop("securityProfile", AAZObjectType, ".security_profile")
                virtual_machine_profile.set_prop("storageProfile", AAZObjectType, ".storage_profile")
                virtual_machine_profile.set_prop("userData", AAZStrType, ".user_data")

            billing_profile = _builder.get(".properties.virtualMachineProfile.billingProfile")
            if billing_profile is not None:
                billing_profile.set_prop("maxPrice", AAZFloatType, ".max_price")

            diagnostics_profile = _builder.get(".properties.virtualMachineProfile.diagnosticsProfile")
            if diagnostics_profile is not None:
                diagnostics_profile.set_prop("bootDiagnostics", AAZObjectType, ".boot_diagnostics")

            boot_diagnostics = _builder.get(".properties.virtualMachineProfile.diagnosticsProfile.bootDiagnostics")
            if boot_diagnostics is not None:
                boot_diagnostics.set_prop("enabled", AAZBoolType, ".enabled")
                boot_diagnostics.set_prop("storageUri", AAZStrType, ".storage_uri")

            extension_profile = _builder.get(".properties.virtualMachineProfile.extensionProfile")
            if extension_profile is not None:
                extension_profile.set_prop("extensions", AAZListType, ".extensions")
                extension_profile.set_prop("extensionsTimeBudget", AAZStrType, ".extensions_time_budget")

            extensions = _builder.get(".properties.virtualMachineProfile.extensionProfile.extensions")
            if extensions is not None:
                extensions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.extensionProfile.extensions[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.virtualMachineProfile.extensionProfile.extensions[].properties")
            if properties is not None:
                properties.set_prop("autoUpgradeMinorVersion", AAZBoolType, ".auto_upgrade_minor_version")
                properties.set_prop("enableAutomaticUpgrade", AAZBoolType, ".enable_automatic_upgrade")
                properties.set_prop("forceUpdateTag", AAZStrType, ".force_update_tag")
                properties.set_prop("protectedSettings", AAZFreeFormDictType, ".protected_settings")
                properties.set_prop("protectedSettingsFromKeyVault", AAZObjectType, ".protected_settings_from_key_vault")
                properties.set_prop("provisionAfterExtensions", AAZListType, ".provision_after_extensions")
                properties.set_prop("publisher", AAZStrType, ".publisher")
                properties.set_prop("settings", AAZFreeFormDictType, ".settings")
                properties.set_prop("suppressFailures", AAZBoolType, ".suppress_failures")
                properties.set_prop("type", AAZStrType, ".type")
                properties.set_prop("typeHandlerVersion", AAZStrType, ".type_handler_version")

            protected_settings = _builder.get(".properties.virtualMachineProfile.extensionProfile.extensions[].properties.protectedSettings")
            if protected_settings is not None:
                protected_settings.set_anytype_elements(".")

            protected_settings_from_key_vault = _builder.get(".properties.virtualMachineProfile.extensionProfile.extensions[].properties.protectedSettingsFromKeyVault")
            if protected_settings_from_key_vault is not None:
                protected_settings_from_key_vault.set_prop("secretUrl", AAZStrType, ".secret_url", typ_kwargs={"flags": {"required": True}})
                _PatchHelper._build_schema_sub_resource_update(protected_settings_from_key_vault.set_prop("sourceVault", AAZObjectType, ".source_vault", typ_kwargs={"flags": {"required": True}}))

            provision_after_extensions = _builder.get(".properties.virtualMachineProfile.extensionProfile.extensions[].properties.provisionAfterExtensions")
            if provision_after_extensions is not None:
                provision_after_extensions.set_elements(AAZStrType, ".")

            settings = _builder.get(".properties.virtualMachineProfile.extensionProfile.extensions[].properties.settings")
            if settings is not None:
                settings.set_anytype_elements(".")

            hardware_profile = _builder.get(".properties.virtualMachineProfile.hardwareProfile")
            if hardware_profile is not None:
                hardware_profile.set_prop("vmSizeProperties", AAZObjectType, ".vm_size_properties")

            vm_size_properties = _builder.get(".properties.virtualMachineProfile.hardwareProfile.vmSizeProperties")
            if vm_size_properties is not None:
                vm_size_properties.set_prop("vCPUsAvailable", AAZIntType, ".v_cp_us_available")
                vm_size_properties.set_prop("vCPUsPerCore", AAZIntType, ".v_cp_us_per_core")

            network_profile = _builder.get(".properties.virtualMachineProfile.networkProfile")
            if network_profile is not None:
                _PatchHelper._build_schema_api_entity_reference_update(network_profile.set_prop("healthProbe", AAZObjectType, ".health_probe"))
                network_profile.set_prop("networkApiVersion", AAZStrType, ".network_api_version")
                network_profile.set_prop("networkInterfaceConfigurations", AAZListType, ".network_interface_configurations")

            network_interface_configurations = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations")
            if network_interface_configurations is not None:
                network_interface_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties")
            if properties is not None:
                properties.set_prop("auxiliaryMode", AAZStrType, ".auxiliary_mode")
                properties.set_prop("auxiliarySku", AAZStrType, ".auxiliary_sku")
                properties.set_prop("deleteOption", AAZStrType, ".delete_option")
                properties.set_prop("disableTcpStateTracking", AAZBoolType, ".disable_tcp_state_tracking")
                properties.set_prop("dnsSettings", AAZObjectType, ".dns_settings")
                properties.set_prop("enableAcceleratedNetworking", AAZBoolType, ".enable_accelerated_networking")
                properties.set_prop("enableFpga", AAZBoolType, ".enable_fpga")
                properties.set_prop("enableIPForwarding", AAZBoolType, ".enable_ip_forwarding")
                properties.set_prop("ipConfigurations", AAZListType, ".ip_configurations")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("networkSecurityGroup", AAZObjectType, ".network_security_group"))
                properties.set_prop("primary", AAZBoolType, ".primary")

            dns_settings = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.dnsSettings")
            if dns_settings is not None:
                dns_settings.set_prop("dnsServers", AAZListType, ".dns_servers")

            dns_servers = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.dnsSettings.dnsServers")
            if dns_servers is not None:
                dns_servers.set_elements(AAZStrType, ".")

            ip_configurations = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations")
            if ip_configurations is not None:
                ip_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties")
            if properties is not None:
                properties.set_prop("applicationGatewayBackendAddressPools", AAZListType, ".application_gateway_backend_address_pools")
                properties.set_prop("applicationSecurityGroups", AAZListType, ".application_security_groups")
                properties.set_prop("loadBalancerBackendAddressPools", AAZListType, ".load_balancer_backend_address_pools")
                properties.set_prop("loadBalancerInboundNatPools", AAZListType, ".load_balancer_inbound_nat_pools")
                properties.set_prop("primary", AAZBoolType, ".primary")
                properties.set_prop("privateIPAddressVersion", AAZStrType, ".private_ip_address_version")
                properties.set_prop("publicIPAddressConfiguration", AAZObjectType, ".public_ip_address_configuration")
                _PatchHelper._build_schema_api_entity_reference_update(properties.set_prop("subnet", AAZObjectType, ".subnet"))

            application_gateway_backend_address_pools = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.applicationGatewayBackendAddressPools")
            if application_gateway_backend_address_pools is not None:
                _PatchHelper._build_schema_sub_resource_update(application_gateway_backend_address_pools.set_elements(AAZObjectType, "."))

            application_security_groups = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.applicationSecurityGroups")
            if application_security_groups is not None:
                _PatchHelper._build_schema_sub_resource_update(application_security_groups.set_elements(AAZObjectType, "."))

            load_balancer_backend_address_pools = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.loadBalancerBackendAddressPools")
            if load_balancer_backend_address_pools is not None:
                _PatchHelper._build_schema_sub_resource_update(load_balancer_backend_address_pools.set_elements(AAZObjectType, "."))

            load_balancer_inbound_nat_pools = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.loadBalancerInboundNatPools")
            if load_balancer_inbound_nat_pools is not None:
                _PatchHelper._build_schema_sub_resource_update(load_balancer_inbound_nat_pools.set_elements(AAZObjectType, "."))

            public_ip_address_configuration = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration")
            if public_ip_address_configuration is not None:
                public_ip_address_configuration.set_prop("name", AAZStrType, ".name")
                public_ip_address_configuration.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration.properties")
            if properties is not None:
                properties.set_prop("deleteOption", AAZStrType, ".delete_option")
                properties.set_prop("dnsSettings", AAZObjectType, ".dns_settings")
                properties.set_prop("idleTimeoutInMinutes", AAZIntType, ".idle_timeout_in_minutes")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("publicIPPrefix", AAZObjectType, ".public_ip_prefix"))

            dns_settings = _builder.get(".properties.virtualMachineProfile.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration.properties.dnsSettings")
            if dns_settings is not None:
                dns_settings.set_prop("domainNameLabel", AAZStrType, ".domain_name_label", typ_kwargs={"flags": {"required": True}})
                dns_settings.set_prop("domainNameLabelScope", AAZStrType, ".domain_name_label_scope")

            os_profile = _builder.get(".properties.virtualMachineProfile.osProfile")
            if os_profile is not None:
                os_profile.set_prop("customData", AAZStrType, ".custom_data")
                os_profile.set_prop("linuxConfiguration", AAZObjectType, ".linux_configuration")
                os_profile.set_prop("secrets", AAZListType, ".secrets")
                os_profile.set_prop("windowsConfiguration", AAZObjectType, ".windows_configuration")

            linux_configuration = _builder.get(".properties.virtualMachineProfile.osProfile.linuxConfiguration")
            if linux_configuration is not None:
                linux_configuration.set_prop("disablePasswordAuthentication", AAZBoolType, ".disable_password_authentication")
                linux_configuration.set_prop("enableVMAgentPlatformUpdates", AAZBoolType, ".enable_vm_agent_platform_updates")
                linux_configuration.set_prop("patchSettings", AAZObjectType, ".patch_settings")
                linux_configuration.set_prop("provisionVMAgent", AAZBoolType, ".provision_vm_agent")
                linux_configuration.set_prop("ssh", AAZObjectType, ".ssh")

            patch_settings = _builder.get(".properties.virtualMachineProfile.osProfile.linuxConfiguration.patchSettings")
            if patch_settings is not None:
                patch_settings.set_prop("assessmentMode", AAZStrType, ".assessment_mode")
                patch_settings.set_prop("automaticByPlatformSettings", AAZObjectType, ".automatic_by_platform_settings")
                patch_settings.set_prop("patchMode", AAZStrType, ".patch_mode")

            automatic_by_platform_settings = _builder.get(".properties.virtualMachineProfile.osProfile.linuxConfiguration.patchSettings.automaticByPlatformSettings")
            if automatic_by_platform_settings is not None:
                automatic_by_platform_settings.set_prop("bypassPlatformSafetyChecksOnUserSchedule", AAZBoolType, ".bypass_platform_safety_checks_on_user_schedule")
                automatic_by_platform_settings.set_prop("rebootSetting", AAZStrType, ".reboot_setting")

            ssh = _builder.get(".properties.virtualMachineProfile.osProfile.linuxConfiguration.ssh")
            if ssh is not None:
                ssh.set_prop("publicKeys", AAZListType, ".public_keys")

            public_keys = _builder.get(".properties.virtualMachineProfile.osProfile.linuxConfiguration.ssh.publicKeys")
            if public_keys is not None:
                public_keys.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.osProfile.linuxConfiguration.ssh.publicKeys[]")
            if _elements is not None:
                _elements.set_prop("keyData", AAZStrType, ".key_data")
                _elements.set_prop("path", AAZStrType, ".path")

            secrets = _builder.get(".properties.virtualMachineProfile.osProfile.secrets")
            if secrets is not None:
                secrets.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.osProfile.secrets[]")
            if _elements is not None:
                _PatchHelper._build_schema_sub_resource_update(_elements.set_prop("sourceVault", AAZObjectType, ".source_vault"))
                _elements.set_prop("vaultCertificates", AAZListType, ".vault_certificates")

            vault_certificates = _builder.get(".properties.virtualMachineProfile.osProfile.secrets[].vaultCertificates")
            if vault_certificates is not None:
                vault_certificates.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.osProfile.secrets[].vaultCertificates[]")
            if _elements is not None:
                _elements.set_prop("certificateStore", AAZStrType, ".certificate_store")
                _elements.set_prop("certificateUrl", AAZStrType, ".certificate_url")

            windows_configuration = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration")
            if windows_configuration is not None:
                windows_configuration.set_prop("additionalUnattendContent", AAZListType, ".additional_unattend_content")
                windows_configuration.set_prop("enableAutomaticUpdates", AAZBoolType, ".enable_automatic_updates")
                windows_configuration.set_prop("patchSettings", AAZObjectType, ".patch_settings")
                windows_configuration.set_prop("provisionVMAgent", AAZBoolType, ".provision_vm_agent")
                windows_configuration.set_prop("timeZone", AAZStrType, ".time_zone")
                windows_configuration.set_prop("winRM", AAZObjectType, ".win_rm")

            additional_unattend_content = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration.additionalUnattendContent")
            if additional_unattend_content is not None:
                additional_unattend_content.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration.additionalUnattendContent[]")
            if _elements is not None:
                _elements.set_prop("componentName", AAZStrType, ".component_name")
                _elements.set_prop("content", AAZStrType, ".content")
                _elements.set_prop("passName", AAZStrType, ".pass_name")
                _elements.set_prop("settingName", AAZStrType, ".setting_name")

            patch_settings = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration.patchSettings")
            if patch_settings is not None:
                patch_settings.set_prop("assessmentMode", AAZStrType, ".assessment_mode")
                patch_settings.set_prop("automaticByPlatformSettings", AAZObjectType, ".automatic_by_platform_settings")
                patch_settings.set_prop("enableHotpatching", AAZBoolType, ".enable_hotpatching")
                patch_settings.set_prop("patchMode", AAZStrType, ".patch_mode")

            automatic_by_platform_settings = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration.patchSettings.automaticByPlatformSettings")
            if automatic_by_platform_settings is not None:
                automatic_by_platform_settings.set_prop("bypassPlatformSafetyChecksOnUserSchedule", AAZBoolType, ".bypass_platform_safety_checks_on_user_schedule")
                automatic_by_platform_settings.set_prop("rebootSetting", AAZStrType, ".reboot_setting")

            win_rm = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration.winRM")
            if win_rm is not None:
                win_rm.set_prop("listeners", AAZListType, ".listeners")

            listeners = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration.winRM.listeners")
            if listeners is not None:
                listeners.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.osProfile.windowsConfiguration.winRM.listeners[]")
            if _elements is not None:
                _elements.set_prop("certificateUrl", AAZStrType, ".certificate_url")
                _elements.set_prop("protocol", AAZStrType, ".protocol")

            scheduled_events_profile = _builder.get(".properties.virtualMachineProfile.scheduledEventsProfile")
            if scheduled_events_profile is not None:
                scheduled_events_profile.set_prop("osImageNotificationProfile", AAZObjectType, ".os_image_notification_profile")
                scheduled_events_profile.set_prop("terminateNotificationProfile", AAZObjectType, ".terminate_notification_profile")

            os_image_notification_profile = _builder.get(".properties.virtualMachineProfile.scheduledEventsProfile.osImageNotificationProfile")
            if os_image_notification_profile is not None:
                os_image_notification_profile.set_prop("enable", AAZBoolType, ".enable")
                os_image_notification_profile.set_prop("notBeforeTimeout", AAZStrType, ".not_before_timeout")

            terminate_notification_profile = _builder.get(".properties.virtualMachineProfile.scheduledEventsProfile.terminateNotificationProfile")
            if terminate_notification_profile is not None:
                terminate_notification_profile.set_prop("enable", AAZBoolType, ".enable")
                terminate_notification_profile.set_prop("notBeforeTimeout", AAZStrType, ".not_before_timeout")

            security_posture_reference = _builder.get(".properties.virtualMachineProfile.securityPostureReference")
            if security_posture_reference is not None:
                security_posture_reference.set_prop("excludeExtensions", AAZListType, ".exclude_extensions")
                security_posture_reference.set_prop("id", AAZStrType, ".id")
                security_posture_reference.set_prop("isOverridable", AAZBoolType, ".is_overridable")

            exclude_extensions = _builder.get(".properties.virtualMachineProfile.securityPostureReference.excludeExtensions")
            if exclude_extensions is not None:
                exclude_extensions.set_elements(AAZStrType, ".")

            security_profile = _builder.get(".properties.virtualMachineProfile.securityProfile")
            if security_profile is not None:
                security_profile.set_prop("encryptionAtHost", AAZBoolType, ".encryption_at_host")
                security_profile.set_prop("encryptionIdentity", AAZObjectType, ".encryption_identity")
                security_profile.set_prop("proxyAgentSettings", AAZObjectType, ".proxy_agent_settings")
                security_profile.set_prop("securityType", AAZStrType, ".security_type")
                security_profile.set_prop("uefiSettings", AAZObjectType, ".uefi_settings")

            encryption_identity = _builder.get(".properties.virtualMachineProfile.securityProfile.encryptionIdentity")
            if encryption_identity is not None:
                encryption_identity.set_prop("userAssignedIdentityResourceId", AAZStrType, ".user_assigned_identity_resource_id")

            proxy_agent_settings = _builder.get(".properties.virtualMachineProfile.securityProfile.proxyAgentSettings")
            if proxy_agent_settings is not None:
                proxy_agent_settings.set_prop("enabled", AAZBoolType, ".enabled")
                _PatchHelper._build_schema_host_endpoint_settings_update(proxy_agent_settings.set_prop("imds", AAZObjectType, ".imds"))
                proxy_agent_settings.set_prop("keyIncarnationId", AAZIntType, ".key_incarnation_id")
                proxy_agent_settings.set_prop("mode", AAZStrType, ".mode")
                _PatchHelper._build_schema_host_endpoint_settings_update(proxy_agent_settings.set_prop("wireServer", AAZObjectType, ".wire_server"))

            uefi_settings = _builder.get(".properties.virtualMachineProfile.securityProfile.uefiSettings")
            if uefi_settings is not None:
                uefi_settings.set_prop("secureBootEnabled", AAZBoolType, ".secure_boot_enabled")
                uefi_settings.set_prop("vTpmEnabled", AAZBoolType, ".v_tpm_enabled")

            storage_profile = _builder.get(".properties.virtualMachineProfile.storageProfile")
            if storage_profile is not None:
                storage_profile.set_prop("dataDisks", AAZListType, ".data_disks")
                storage_profile.set_prop("diskControllerType", AAZStrType, ".disk_controller_type")
                storage_profile.set_prop("imageReference", AAZObjectType, ".image_reference")
                storage_profile.set_prop("osDisk", AAZObjectType, ".os_disk")

            data_disks = _builder.get(".properties.virtualMachineProfile.storageProfile.dataDisks")
            if data_disks is not None:
                data_disks.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.virtualMachineProfile.storageProfile.dataDisks[]")
            if _elements is not None:
                _elements.set_prop("caching", AAZStrType, ".caching")
                _elements.set_prop("createOption", AAZStrType, ".create_option", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("deleteOption", AAZStrType, ".delete_option")
                _elements.set_prop("diskIOPSReadWrite", AAZIntType, ".disk_iops_read_write")
                _elements.set_prop("diskMBpsReadWrite", AAZIntType, ".disk_m_bps_read_write")
                _elements.set_prop("diskSizeGB", AAZIntType, ".disk_size_gb")
                _elements.set_prop("lun", AAZIntType, ".lun", typ_kwargs={"flags": {"required": True}})
                _PatchHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_update(_elements.set_prop("managedDisk", AAZObjectType, ".managed_disk"))
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("writeAcceleratorEnabled", AAZBoolType, ".write_accelerator_enabled")

            image_reference = _builder.get(".properties.virtualMachineProfile.storageProfile.imageReference")
            if image_reference is not None:
                image_reference.set_prop("communityGalleryImageId", AAZStrType, ".community_gallery_image_id")
                image_reference.set_prop("id", AAZStrType, ".id")
                image_reference.set_prop("offer", AAZStrType, ".offer")
                image_reference.set_prop("publisher", AAZStrType, ".publisher")
                image_reference.set_prop("sharedGalleryImageId", AAZStrType, ".shared_gallery_image_id")
                image_reference.set_prop("sku", AAZStrType, ".sku")
                image_reference.set_prop("version", AAZStrType, ".version")

            os_disk = _builder.get(".properties.virtualMachineProfile.storageProfile.osDisk")
            if os_disk is not None:
                os_disk.set_prop("caching", AAZStrType, ".caching")
                os_disk.set_prop("deleteOption", AAZStrType, ".delete_option")
                os_disk.set_prop("diffDiskSettings", AAZObjectType, ".diff_disk_settings")
                os_disk.set_prop("diskSizeGB", AAZIntType, ".disk_size_gb")
                os_disk.set_prop("image", AAZObjectType, ".image")
                _PatchHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_update(os_disk.set_prop("managedDisk", AAZObjectType, ".managed_disk"))
                os_disk.set_prop("vhdContainers", AAZListType, ".vhd_containers")
                os_disk.set_prop("writeAcceleratorEnabled", AAZBoolType, ".write_accelerator_enabled")

            diff_disk_settings = _builder.get(".properties.virtualMachineProfile.storageProfile.osDisk.diffDiskSettings")
            if diff_disk_settings is not None:
                diff_disk_settings.set_prop("option", AAZStrType, ".option")
                diff_disk_settings.set_prop("placement", AAZStrType, ".placement")

            image = _builder.get(".properties.virtualMachineProfile.storageProfile.osDisk.image")
            if image is not None:
                image.set_prop("uri", AAZStrType, ".uri")

            vhd_containers = _builder.get(".properties.virtualMachineProfile.storageProfile.osDisk.vhdContainers")
            if vhd_containers is not None:
                vhd_containers.set_elements(AAZStrType, ".")

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("capacity", AAZIntType, ".capacity")
                sku.set_prop("name", AAZStrType, ".name")
                sku.set_prop("tier", AAZStrType, ".tier")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.extended_location = AAZObjectType(
                serialized_name="extendedLocation",
            )
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.identity = AAZIdentityObjectType()
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.plan = AAZObjectType()
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.sku = AAZObjectType()
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.zones = AAZListType()

            extended_location = cls._schema_on_200.extended_location
            extended_location.name = AAZStrType()
            extended_location.type = AAZStrType()

            identity = cls._schema_on_200.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            plan = cls._schema_on_200.plan
            plan.name = AAZStrType()
            plan.product = AAZStrType()
            plan.promotion_code = AAZStrType(
                serialized_name="promotionCode",
            )
            plan.publisher = AAZStrType()

            properties = cls._schema_on_200.properties
            properties.additional_capabilities = AAZObjectType(
                serialized_name="additionalCapabilities",
            )
            properties.automatic_repairs_policy = AAZObjectType(
                serialized_name="automaticRepairsPolicy",
            )
            properties.constrained_maximum_capacity = AAZBoolType(
                serialized_name="constrainedMaximumCapacity",
            )
            properties.do_not_run_extensions_on_overprovisioned_v_ms = AAZBoolType(
                serialized_name="doNotRunExtensionsOnOverprovisionedVMs",
            )
            properties.host_group = AAZObjectType(
                serialized_name="hostGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.host_group)
            properties.orchestration_mode = AAZStrType(
                serialized_name="orchestrationMode",
            )
            properties.overprovision = AAZBoolType()
            properties.platform_fault_domain_count = AAZIntType(
                serialized_name="platformFaultDomainCount",
            )
            properties.priority_mix_policy = AAZObjectType(
                serialized_name="priorityMixPolicy",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.proximity_placement_group = AAZObjectType(
                serialized_name="proximityPlacementGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.proximity_placement_group)
            properties.resiliency_policy = AAZObjectType(
                serialized_name="resiliencyPolicy",
            )
            properties.scale_in_policy = AAZObjectType(
                serialized_name="scaleInPolicy",
            )
            properties.scheduled_events_policy = AAZObjectType(
                serialized_name="scheduledEventsPolicy",
            )
            properties.single_placement_group = AAZBoolType(
                serialized_name="singlePlacementGroup",
            )
            properties.sku_profile = AAZObjectType(
                serialized_name="skuProfile",
            )
            properties.spot_restore_policy = AAZObjectType(
                serialized_name="spotRestorePolicy",
            )
            properties.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            properties.unique_id = AAZStrType(
                serialized_name="uniqueId",
                flags={"read_only": True},
            )
            properties.upgrade_policy = AAZObjectType(
                serialized_name="upgradePolicy",
            )
            properties.virtual_machine_profile = AAZObjectType(
                serialized_name="virtualMachineProfile",
            )
            properties.zonal_platform_fault_domain_align_mode = AAZStrType(
                serialized_name="zonalPlatformFaultDomainAlignMode",
            )
            properties.zone_balance = AAZBoolType(
                serialized_name="zoneBalance",
            )

            additional_capabilities = cls._schema_on_200.properties.additional_capabilities
            additional_capabilities.hibernation_enabled = AAZBoolType(
                serialized_name="hibernationEnabled",
            )
            additional_capabilities.ultra_ssd_enabled = AAZBoolType(
                serialized_name="ultraSSDEnabled",
            )

            automatic_repairs_policy = cls._schema_on_200.properties.automatic_repairs_policy
            automatic_repairs_policy.enabled = AAZBoolType()
            automatic_repairs_policy.grace_period = AAZStrType(
                serialized_name="gracePeriod",
            )
            automatic_repairs_policy.repair_action = AAZStrType(
                serialized_name="repairAction",
            )

            priority_mix_policy = cls._schema_on_200.properties.priority_mix_policy
            priority_mix_policy.base_regular_priority_count = AAZIntType(
                serialized_name="baseRegularPriorityCount",
            )
            priority_mix_policy.regular_priority_percentage_above_base = AAZIntType(
                serialized_name="regularPriorityPercentageAboveBase",
            )

            resiliency_policy = cls._schema_on_200.properties.resiliency_policy
            resiliency_policy.automatic_zone_rebalancing_policy = AAZObjectType(
                serialized_name="automaticZoneRebalancingPolicy",
            )
            resiliency_policy.resilient_vm_creation_policy = AAZObjectType(
                serialized_name="resilientVMCreationPolicy",
            )
            resiliency_policy.resilient_vm_deletion_policy = AAZObjectType(
                serialized_name="resilientVMDeletionPolicy",
            )

            automatic_zone_rebalancing_policy = cls._schema_on_200.properties.resiliency_policy.automatic_zone_rebalancing_policy
            automatic_zone_rebalancing_policy.enabled = AAZBoolType()
            automatic_zone_rebalancing_policy.rebalance_behavior = AAZStrType(
                serialized_name="rebalanceBehavior",
            )
            automatic_zone_rebalancing_policy.rebalance_strategy = AAZStrType(
                serialized_name="rebalanceStrategy",
            )

            resilient_vm_creation_policy = cls._schema_on_200.properties.resiliency_policy.resilient_vm_creation_policy
            resilient_vm_creation_policy.enabled = AAZBoolType()

            resilient_vm_deletion_policy = cls._schema_on_200.properties.resiliency_policy.resilient_vm_deletion_policy
            resilient_vm_deletion_policy.enabled = AAZBoolType()

            scale_in_policy = cls._schema_on_200.properties.scale_in_policy
            scale_in_policy.force_deletion = AAZBoolType(
                serialized_name="forceDeletion",
            )
            scale_in_policy.prioritize_unhealthy_v_ms = AAZBoolType(
                serialized_name="prioritizeUnhealthyVMs",
            )
            scale_in_policy.rules = AAZListType()

            rules = cls._schema_on_200.properties.scale_in_policy.rules
            rules.Element = AAZStrType()

            scheduled_events_policy = cls._schema_on_200.properties.scheduled_events_policy
            scheduled_events_policy.scheduled_events_additional_publishing_targets = AAZObjectType(
                serialized_name="scheduledEventsAdditionalPublishingTargets",
            )
            scheduled_events_policy.user_initiated_reboot = AAZObjectType(
                serialized_name="userInitiatedReboot",
            )
            scheduled_events_policy.user_initiated_redeploy = AAZObjectType(
                serialized_name="userInitiatedRedeploy",
            )

            scheduled_events_additional_publishing_targets = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets
            scheduled_events_additional_publishing_targets.event_grid_and_resource_graph = AAZObjectType(
                serialized_name="eventGridAndResourceGraph",
            )

            event_grid_and_resource_graph = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets.event_grid_and_resource_graph
            event_grid_and_resource_graph.enable = AAZBoolType()

            user_initiated_reboot = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_reboot
            user_initiated_reboot.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            user_initiated_redeploy = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_redeploy
            user_initiated_redeploy.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            sku_profile = cls._schema_on_200.properties.sku_profile
            sku_profile.allocation_strategy = AAZStrType(
                serialized_name="allocationStrategy",
            )
            sku_profile.vm_sizes = AAZListType(
                serialized_name="vmSizes",
            )

            vm_sizes = cls._schema_on_200.properties.sku_profile.vm_sizes
            vm_sizes.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.sku_profile.vm_sizes.Element
            _element.name = AAZStrType()
            _element.rank = AAZIntType()

            spot_restore_policy = cls._schema_on_200.properties.spot_restore_policy
            spot_restore_policy.enabled = AAZBoolType()
            spot_restore_policy.restore_timeout = AAZStrType(
                serialized_name="restoreTimeout",
            )

            upgrade_policy = cls._schema_on_200.properties.upgrade_policy
            upgrade_policy.automatic_os_upgrade_policy = AAZObjectType(
                serialized_name="automaticOSUpgradePolicy",
            )
            upgrade_policy.mode = AAZStrType()
            upgrade_policy.rolling_upgrade_policy = AAZObjectType(
                serialized_name="rollingUpgradePolicy",
            )

            automatic_os_upgrade_policy = cls._schema_on_200.properties.upgrade_policy.automatic_os_upgrade_policy
            automatic_os_upgrade_policy.disable_automatic_rollback = AAZBoolType(
                serialized_name="disableAutomaticRollback",
            )
            automatic_os_upgrade_policy.enable_automatic_os_upgrade = AAZBoolType(
                serialized_name="enableAutomaticOSUpgrade",
            )
            automatic_os_upgrade_policy.os_rolling_upgrade_deferral = AAZBoolType(
                serialized_name="osRollingUpgradeDeferral",
            )
            automatic_os_upgrade_policy.use_rolling_upgrade_policy = AAZBoolType(
                serialized_name="useRollingUpgradePolicy",
            )

            rolling_upgrade_policy = cls._schema_on_200.properties.upgrade_policy.rolling_upgrade_policy
            rolling_upgrade_policy.enable_cross_zone_upgrade = AAZBoolType(
                serialized_name="enableCrossZoneUpgrade",
            )
            rolling_upgrade_policy.max_batch_instance_percent = AAZIntType(
                serialized_name="maxBatchInstancePercent",
            )
            rolling_upgrade_policy.max_surge = AAZBoolType(
                serialized_name="maxSurge",
            )
            rolling_upgrade_policy.max_unhealthy_instance_percent = AAZIntType(
                serialized_name="maxUnhealthyInstancePercent",
            )
            rolling_upgrade_policy.max_unhealthy_upgraded_instance_percent = AAZIntType(
                serialized_name="maxUnhealthyUpgradedInstancePercent",
            )
            rolling_upgrade_policy.pause_time_between_batches = AAZStrType(
                serialized_name="pauseTimeBetweenBatches",
            )
            rolling_upgrade_policy.prioritize_unhealthy_instances = AAZBoolType(
                serialized_name="prioritizeUnhealthyInstances",
            )
            rolling_upgrade_policy.rollback_failed_instances_on_policy_breach = AAZBoolType(
                serialized_name="rollbackFailedInstancesOnPolicyBreach",
            )

            virtual_machine_profile = cls._schema_on_200.properties.virtual_machine_profile
            virtual_machine_profile.application_profile = AAZObjectType(
                serialized_name="applicationProfile",
            )
            virtual_machine_profile.billing_profile = AAZObjectType(
                serialized_name="billingProfile",
            )
            virtual_machine_profile.capacity_reservation = AAZObjectType(
                serialized_name="capacityReservation",
            )
            virtual_machine_profile.diagnostics_profile = AAZObjectType(
                serialized_name="diagnosticsProfile",
            )
            virtual_machine_profile.eviction_policy = AAZStrType(
                serialized_name="evictionPolicy",
            )
            virtual_machine_profile.extension_profile = AAZObjectType(
                serialized_name="extensionProfile",
            )
            virtual_machine_profile.hardware_profile = AAZObjectType(
                serialized_name="hardwareProfile",
            )
            virtual_machine_profile.license_type = AAZStrType(
                serialized_name="licenseType",
            )
            virtual_machine_profile.network_profile = AAZObjectType(
                serialized_name="networkProfile",
            )
            virtual_machine_profile.os_profile = AAZObjectType(
                serialized_name="osProfile",
            )
            virtual_machine_profile.priority = AAZStrType()
            virtual_machine_profile.scheduled_events_profile = AAZObjectType(
                serialized_name="scheduledEventsProfile",
            )
            virtual_machine_profile.security_posture_reference = AAZObjectType(
                serialized_name="securityPostureReference",
            )
            virtual_machine_profile.security_profile = AAZObjectType(
                serialized_name="securityProfile",
            )
            virtual_machine_profile.service_artifact_reference = AAZObjectType(
                serialized_name="serviceArtifactReference",
            )
            virtual_machine_profile.storage_profile = AAZObjectType(
                serialized_name="storageProfile",
            )
            virtual_machine_profile.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            virtual_machine_profile.user_data = AAZStrType(
                serialized_name="userData",
            )

            application_profile = cls._schema_on_200.properties.virtual_machine_profile.application_profile
            application_profile.gallery_applications = AAZListType(
                serialized_name="galleryApplications",
            )

            gallery_applications = cls._schema_on_200.properties.virtual_machine_profile.application_profile.gallery_applications
            gallery_applications.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.application_profile.gallery_applications.Element
            _element.configuration_reference = AAZStrType(
                serialized_name="configurationReference",
            )
            _element.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            _element.order = AAZIntType()
            _element.package_reference_id = AAZStrType(
                serialized_name="packageReferenceId",
                flags={"required": True},
            )
            _element.tags = AAZStrType()
            _element.treat_failure_as_deployment_failure = AAZBoolType(
                serialized_name="treatFailureAsDeploymentFailure",
            )

            billing_profile = cls._schema_on_200.properties.virtual_machine_profile.billing_profile
            billing_profile.max_price = AAZFloatType(
                serialized_name="maxPrice",
            )

            capacity_reservation = cls._schema_on_200.properties.virtual_machine_profile.capacity_reservation
            capacity_reservation.capacity_reservation_group = AAZObjectType(
                serialized_name="capacityReservationGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(capacity_reservation.capacity_reservation_group)

            diagnostics_profile = cls._schema_on_200.properties.virtual_machine_profile.diagnostics_profile
            diagnostics_profile.boot_diagnostics = AAZObjectType(
                serialized_name="bootDiagnostics",
            )

            boot_diagnostics = cls._schema_on_200.properties.virtual_machine_profile.diagnostics_profile.boot_diagnostics
            boot_diagnostics.enabled = AAZBoolType()
            boot_diagnostics.storage_uri = AAZStrType(
                serialized_name="storageUri",
            )

            extension_profile = cls._schema_on_200.properties.virtual_machine_profile.extension_profile
            extension_profile.extensions = AAZListType()
            extension_profile.extensions_time_budget = AAZStrType(
                serialized_name="extensionsTimeBudget",
            )

            extensions = cls._schema_on_200.properties.virtual_machine_profile.extension_profile.extensions
            extensions.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.extension_profile.extensions.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties.virtual_machine_profile.extension_profile.extensions.Element.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.protected_settings = AAZFreeFormDictType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZFreeFormDictType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            protected_settings_from_key_vault = cls._schema_on_200.properties.virtual_machine_profile.extension_profile.extensions.Element.properties.protected_settings_from_key_vault
            protected_settings_from_key_vault.secret_url = AAZStrType(
                serialized_name="secretUrl",
                flags={"required": True},
            )
            protected_settings_from_key_vault.source_vault = AAZObjectType(
                serialized_name="sourceVault",
                flags={"required": True},
            )
            _PatchHelper._build_schema_sub_resource_read(protected_settings_from_key_vault.source_vault)

            provision_after_extensions = cls._schema_on_200.properties.virtual_machine_profile.extension_profile.extensions.Element.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            hardware_profile = cls._schema_on_200.properties.virtual_machine_profile.hardware_profile
            hardware_profile.vm_size_properties = AAZObjectType(
                serialized_name="vmSizeProperties",
            )

            vm_size_properties = cls._schema_on_200.properties.virtual_machine_profile.hardware_profile.vm_size_properties
            vm_size_properties.v_cp_us_available = AAZIntType(
                serialized_name="vCPUsAvailable",
            )
            vm_size_properties.v_cp_us_per_core = AAZIntType(
                serialized_name="vCPUsPerCore",
            )

            network_profile = cls._schema_on_200.properties.virtual_machine_profile.network_profile
            network_profile.health_probe = AAZObjectType(
                serialized_name="healthProbe",
            )
            _PatchHelper._build_schema_api_entity_reference_read(network_profile.health_probe)
            network_profile.network_api_version = AAZStrType(
                serialized_name="networkApiVersion",
            )
            network_profile.network_interface_configurations = AAZListType(
                serialized_name="networkInterfaceConfigurations",
            )

            network_interface_configurations = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations
            network_interface_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties
            properties.auxiliary_mode = AAZStrType(
                serialized_name="auxiliaryMode",
            )
            properties.auxiliary_sku = AAZStrType(
                serialized_name="auxiliarySku",
            )
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.disable_tcp_state_tracking = AAZBoolType(
                serialized_name="disableTcpStateTracking",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.enable_accelerated_networking = AAZBoolType(
                serialized_name="enableAcceleratedNetworking",
            )
            properties.enable_fpga = AAZBoolType(
                serialized_name="enableFpga",
            )
            properties.enable_ip_forwarding = AAZBoolType(
                serialized_name="enableIPForwarding",
            )
            properties.ip_configurations = AAZListType(
                serialized_name="ipConfigurations",
                flags={"required": True},
            )
            properties.network_security_group = AAZObjectType(
                serialized_name="networkSecurityGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.network_security_group)
            properties.primary = AAZBoolType()

            dns_settings = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings
            dns_settings.dns_servers = AAZListType(
                serialized_name="dnsServers",
            )

            dns_servers = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings.dns_servers
            dns_servers.Element = AAZStrType()

            ip_configurations = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations
            ip_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties
            properties.application_gateway_backend_address_pools = AAZListType(
                serialized_name="applicationGatewayBackendAddressPools",
            )
            properties.application_security_groups = AAZListType(
                serialized_name="applicationSecurityGroups",
            )
            properties.load_balancer_backend_address_pools = AAZListType(
                serialized_name="loadBalancerBackendAddressPools",
            )
            properties.load_balancer_inbound_nat_pools = AAZListType(
                serialized_name="loadBalancerInboundNatPools",
            )
            properties.primary = AAZBoolType()
            properties.private_ip_address_version = AAZStrType(
                serialized_name="privateIPAddressVersion",
            )
            properties.public_ip_address_configuration = AAZObjectType(
                serialized_name="publicIPAddressConfiguration",
            )
            properties.subnet = AAZObjectType()
            _PatchHelper._build_schema_api_entity_reference_read(properties.subnet)

            application_gateway_backend_address_pools = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_gateway_backend_address_pools
            application_gateway_backend_address_pools.Element = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(application_gateway_backend_address_pools.Element)

            application_security_groups = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_security_groups
            application_security_groups.Element = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(application_security_groups.Element)

            load_balancer_backend_address_pools = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_backend_address_pools
            load_balancer_backend_address_pools.Element = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(load_balancer_backend_address_pools.Element)

            load_balancer_inbound_nat_pools = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_inbound_nat_pools
            load_balancer_inbound_nat_pools.Element = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(load_balancer_inbound_nat_pools.Element)

            public_ip_address_configuration = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration
            public_ip_address_configuration.name = AAZStrType(
                flags={"required": True},
            )
            public_ip_address_configuration.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            public_ip_address_configuration.sku = AAZObjectType()

            properties = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.idle_timeout_in_minutes = AAZIntType(
                serialized_name="idleTimeoutInMinutes",
            )
            properties.ip_tags = AAZListType(
                serialized_name="ipTags",
            )
            properties.public_ip_address_version = AAZStrType(
                serialized_name="publicIPAddressVersion",
            )
            properties.public_ip_prefix = AAZObjectType(
                serialized_name="publicIPPrefix",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.public_ip_prefix)

            dns_settings = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.dns_settings
            dns_settings.domain_name_label = AAZStrType(
                serialized_name="domainNameLabel",
                flags={"required": True},
            )
            dns_settings.domain_name_label_scope = AAZStrType(
                serialized_name="domainNameLabelScope",
            )

            ip_tags = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags
            ip_tags.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags.Element
            _element.ip_tag_type = AAZStrType(
                serialized_name="ipTagType",
            )
            _element.tag = AAZStrType()

            sku = cls._schema_on_200.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            os_profile = cls._schema_on_200.properties.virtual_machine_profile.os_profile
            os_profile.admin_password = AAZStrType(
                serialized_name="adminPassword",
                flags={"secret": True},
            )
            os_profile.admin_username = AAZStrType(
                serialized_name="adminUsername",
            )
            os_profile.allow_extension_operations = AAZBoolType(
                serialized_name="allowExtensionOperations",
            )
            os_profile.computer_name_prefix = AAZStrType(
                serialized_name="computerNamePrefix",
            )
            os_profile.custom_data = AAZStrType(
                serialized_name="customData",
            )
            os_profile.linux_configuration = AAZObjectType(
                serialized_name="linuxConfiguration",
            )
            os_profile.require_guest_provision_signal = AAZBoolType(
                serialized_name="requireGuestProvisionSignal",
            )
            os_profile.secrets = AAZListType()
            os_profile.windows_configuration = AAZObjectType(
                serialized_name="windowsConfiguration",
            )

            linux_configuration = cls._schema_on_200.properties.virtual_machine_profile.os_profile.linux_configuration
            linux_configuration.disable_password_authentication = AAZBoolType(
                serialized_name="disablePasswordAuthentication",
            )
            linux_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
            )
            linux_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            linux_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            linux_configuration.ssh = AAZObjectType()

            patch_settings = cls._schema_on_200.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            ssh = cls._schema_on_200.properties.virtual_machine_profile.os_profile.linux_configuration.ssh
            ssh.public_keys = AAZListType(
                serialized_name="publicKeys",
            )

            public_keys = cls._schema_on_200.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys
            public_keys.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys.Element
            _element.key_data = AAZStrType(
                serialized_name="keyData",
            )
            _element.path = AAZStrType()

            secrets = cls._schema_on_200.properties.virtual_machine_profile.os_profile.secrets
            secrets.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.os_profile.secrets.Element
            _element.source_vault = AAZObjectType(
                serialized_name="sourceVault",
            )
            _PatchHelper._build_schema_sub_resource_read(_element.source_vault)
            _element.vault_certificates = AAZListType(
                serialized_name="vaultCertificates",
            )

            vault_certificates = cls._schema_on_200.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates
            vault_certificates.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates.Element
            _element.certificate_store = AAZStrType(
                serialized_name="certificateStore",
            )
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )

            windows_configuration = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration
            windows_configuration.additional_unattend_content = AAZListType(
                serialized_name="additionalUnattendContent",
            )
            windows_configuration.enable_automatic_updates = AAZBoolType(
                serialized_name="enableAutomaticUpdates",
            )
            windows_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
                flags={"read_only": True},
            )
            windows_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            windows_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            windows_configuration.time_zone = AAZStrType(
                serialized_name="timeZone",
            )
            windows_configuration.win_rm = AAZObjectType(
                serialized_name="winRM",
            )

            additional_unattend_content = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content
            additional_unattend_content.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content.Element
            _element.component_name = AAZStrType(
                serialized_name="componentName",
            )
            _element.content = AAZStrType()
            _element.pass_name = AAZStrType(
                serialized_name="passName",
            )
            _element.setting_name = AAZStrType(
                serialized_name="settingName",
            )

            patch_settings = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.enable_hotpatching = AAZBoolType(
                serialized_name="enableHotpatching",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            win_rm = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm
            win_rm.listeners = AAZListType()

            listeners = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners
            listeners.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners.Element
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )
            _element.protocol = AAZStrType()

            scheduled_events_profile = cls._schema_on_200.properties.virtual_machine_profile.scheduled_events_profile
            scheduled_events_profile.os_image_notification_profile = AAZObjectType(
                serialized_name="osImageNotificationProfile",
            )
            scheduled_events_profile.terminate_notification_profile = AAZObjectType(
                serialized_name="terminateNotificationProfile",
            )

            os_image_notification_profile = cls._schema_on_200.properties.virtual_machine_profile.scheduled_events_profile.os_image_notification_profile
            os_image_notification_profile.enable = AAZBoolType()
            os_image_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            terminate_notification_profile = cls._schema_on_200.properties.virtual_machine_profile.scheduled_events_profile.terminate_notification_profile
            terminate_notification_profile.enable = AAZBoolType()
            terminate_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            security_posture_reference = cls._schema_on_200.properties.virtual_machine_profile.security_posture_reference
            security_posture_reference.exclude_extensions = AAZListType(
                serialized_name="excludeExtensions",
            )
            security_posture_reference.id = AAZStrType(
                flags={"required": True},
            )
            security_posture_reference.is_overridable = AAZBoolType(
                serialized_name="isOverridable",
            )

            exclude_extensions = cls._schema_on_200.properties.virtual_machine_profile.security_posture_reference.exclude_extensions
            exclude_extensions.Element = AAZStrType()

            security_profile = cls._schema_on_200.properties.virtual_machine_profile.security_profile
            security_profile.encryption_at_host = AAZBoolType(
                serialized_name="encryptionAtHost",
            )
            security_profile.encryption_identity = AAZObjectType(
                serialized_name="encryptionIdentity",
            )
            security_profile.proxy_agent_settings = AAZObjectType(
                serialized_name="proxyAgentSettings",
            )
            security_profile.security_type = AAZStrType(
                serialized_name="securityType",
            )
            security_profile.uefi_settings = AAZObjectType(
                serialized_name="uefiSettings",
            )

            encryption_identity = cls._schema_on_200.properties.virtual_machine_profile.security_profile.encryption_identity
            encryption_identity.user_assigned_identity_resource_id = AAZStrType(
                serialized_name="userAssignedIdentityResourceId",
            )

            proxy_agent_settings = cls._schema_on_200.properties.virtual_machine_profile.security_profile.proxy_agent_settings
            proxy_agent_settings.enabled = AAZBoolType()
            proxy_agent_settings.imds = AAZObjectType()
            _PatchHelper._build_schema_host_endpoint_settings_read(proxy_agent_settings.imds)
            proxy_agent_settings.key_incarnation_id = AAZIntType(
                serialized_name="keyIncarnationId",
            )
            proxy_agent_settings.mode = AAZStrType()
            proxy_agent_settings.wire_server = AAZObjectType(
                serialized_name="wireServer",
            )
            _PatchHelper._build_schema_host_endpoint_settings_read(proxy_agent_settings.wire_server)

            uefi_settings = cls._schema_on_200.properties.virtual_machine_profile.security_profile.uefi_settings
            uefi_settings.secure_boot_enabled = AAZBoolType(
                serialized_name="secureBootEnabled",
            )
            uefi_settings.v_tpm_enabled = AAZBoolType(
                serialized_name="vTpmEnabled",
            )

            service_artifact_reference = cls._schema_on_200.properties.virtual_machine_profile.service_artifact_reference
            service_artifact_reference.id = AAZStrType()

            storage_profile = cls._schema_on_200.properties.virtual_machine_profile.storage_profile
            storage_profile.data_disks = AAZListType(
                serialized_name="dataDisks",
            )
            storage_profile.disk_controller_type = AAZStrType(
                serialized_name="diskControllerType",
            )
            storage_profile.image_reference = AAZObjectType(
                serialized_name="imageReference",
            )
            storage_profile.os_disk = AAZObjectType(
                serialized_name="osDisk",
            )

            data_disks = cls._schema_on_200.properties.virtual_machine_profile.storage_profile.data_disks
            data_disks.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.virtual_machine_profile.storage_profile.data_disks.Element
            _element.caching = AAZStrType()
            _element.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            _element.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            _element.disk_iops_read_write = AAZIntType(
                serialized_name="diskIOPSReadWrite",
            )
            _element.disk_m_bps_read_write = AAZIntType(
                serialized_name="diskMBpsReadWrite",
            )
            _element.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            _element.lun = AAZIntType(
                flags={"required": True},
            )
            _element.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _PatchHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(_element.managed_disk)
            _element.name = AAZStrType()
            _element.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            image_reference = cls._schema_on_200.properties.virtual_machine_profile.storage_profile.image_reference
            image_reference.community_gallery_image_id = AAZStrType(
                serialized_name="communityGalleryImageId",
            )
            image_reference.exact_version = AAZStrType(
                serialized_name="exactVersion",
                flags={"read_only": True},
            )
            image_reference.id = AAZStrType()
            image_reference.offer = AAZStrType()
            image_reference.publisher = AAZStrType()
            image_reference.shared_gallery_image_id = AAZStrType(
                serialized_name="sharedGalleryImageId",
            )
            image_reference.sku = AAZStrType()
            image_reference.version = AAZStrType()

            os_disk = cls._schema_on_200.properties.virtual_machine_profile.storage_profile.os_disk
            os_disk.caching = AAZStrType()
            os_disk.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            os_disk.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            os_disk.diff_disk_settings = AAZObjectType(
                serialized_name="diffDiskSettings",
            )
            os_disk.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            os_disk.image = AAZObjectType()
            os_disk.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _PatchHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(os_disk.managed_disk)
            os_disk.name = AAZStrType()
            os_disk.os_type = AAZStrType(
                serialized_name="osType",
            )
            os_disk.vhd_containers = AAZListType(
                serialized_name="vhdContainers",
            )
            os_disk.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            diff_disk_settings = cls._schema_on_200.properties.virtual_machine_profile.storage_profile.os_disk.diff_disk_settings
            diff_disk_settings.option = AAZStrType()
            diff_disk_settings.placement = AAZStrType()

            image = cls._schema_on_200.properties.virtual_machine_profile.storage_profile.os_disk.image
            image.uri = AAZStrType()

            vhd_containers = cls._schema_on_200.properties.virtual_machine_profile.storage_profile.os_disk.vhd_containers
            vhd_containers.Element = AAZStrType()

            sku = cls._schema_on_200.sku
            sku.capacity = AAZIntType()
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200


class _PatchHelper:
    """Helper class for Patch"""

    @classmethod
    def _build_schema_api_entity_reference_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    @classmethod
    def _build_schema_disk_encryption_set_parameters_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    @classmethod
    def _build_schema_host_endpoint_settings_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("inVMAccessControlProfileReferenceId", AAZStrType, ".in_vm_access_control_profile_reference_id")
        _builder.set_prop("mode", AAZStrType, ".mode")

    @classmethod
    def _build_schema_sub_resource_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    @classmethod
    def _build_schema_virtual_machine_scale_set_managed_disk_parameters_update(cls, _builder):
        if _builder is None:
            return
        cls._build_schema_disk_encryption_set_parameters_update(_builder.set_prop("diskEncryptionSet", AAZObjectType, ".disk_encryption_set"))
        _builder.set_prop("securityProfile", AAZObjectType, ".security_profile")
        _builder.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

        security_profile = _builder.get(".securityProfile")
        if security_profile is not None:
            cls._build_schema_disk_encryption_set_parameters_update(security_profile.set_prop("diskEncryptionSet", AAZObjectType, ".disk_encryption_set"))
            security_profile.set_prop("securityEncryptionType", AAZStrType, ".security_encryption_type")

    _schema_api_entity_reference_read = None

    @classmethod
    def _build_schema_api_entity_reference_read(cls, _schema):
        if cls._schema_api_entity_reference_read is not None:
            _schema.id = cls._schema_api_entity_reference_read.id
            return

        cls._schema_api_entity_reference_read = _schema_api_entity_reference_read = AAZObjectType()

        api_entity_reference_read = _schema_api_entity_reference_read
        api_entity_reference_read.id = AAZStrType()

        _schema.id = cls._schema_api_entity_reference_read.id

    _schema_disk_encryption_set_parameters_read = None

    @classmethod
    def _build_schema_disk_encryption_set_parameters_read(cls, _schema):
        if cls._schema_disk_encryption_set_parameters_read is not None:
            _schema.id = cls._schema_disk_encryption_set_parameters_read.id
            return

        cls._schema_disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read = AAZObjectType()

        disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read
        disk_encryption_set_parameters_read.id = AAZStrType()

        _schema.id = cls._schema_disk_encryption_set_parameters_read.id

    _schema_host_endpoint_settings_read = None

    @classmethod
    def _build_schema_host_endpoint_settings_read(cls, _schema):
        if cls._schema_host_endpoint_settings_read is not None:
            _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
            _schema.mode = cls._schema_host_endpoint_settings_read.mode
            return

        cls._schema_host_endpoint_settings_read = _schema_host_endpoint_settings_read = AAZObjectType()

        host_endpoint_settings_read = _schema_host_endpoint_settings_read
        host_endpoint_settings_read.in_vm_access_control_profile_reference_id = AAZStrType(
            serialized_name="inVMAccessControlProfileReferenceId",
        )
        host_endpoint_settings_read.mode = AAZStrType()

        _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
        _schema.mode = cls._schema_host_endpoint_settings_read.mode

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_virtual_machine_scale_set_managed_disk_parameters_read = None

    @classmethod
    def _build_schema_virtual_machine_scale_set_managed_disk_parameters_read(cls, _schema):
        if cls._schema_virtual_machine_scale_set_managed_disk_parameters_read is not None:
            _schema.disk_encryption_set = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set
            _schema.security_profile = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
            _schema.storage_account_type = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type
            return

        cls._schema_virtual_machine_scale_set_managed_disk_parameters_read = _schema_virtual_machine_scale_set_managed_disk_parameters_read = AAZObjectType()

        virtual_machine_scale_set_managed_disk_parameters_read = _schema_virtual_machine_scale_set_managed_disk_parameters_read
        virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set)
        virtual_machine_scale_set_managed_disk_parameters_read.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        security_profile = _schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
        security_profile.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(security_profile.disk_encryption_set)
        security_profile.security_encryption_type = AAZStrType(
            serialized_name="securityEncryptionType",
        )

        _schema.disk_encryption_set = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set
        _schema.security_profile = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
        _schema.storage_account_type = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type


__all__ = ["Patch"]
