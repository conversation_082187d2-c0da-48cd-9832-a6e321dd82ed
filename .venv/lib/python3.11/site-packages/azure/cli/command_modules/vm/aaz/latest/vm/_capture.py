# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "vm capture",
)
class Capture(AAZCommand):
    """Capture information for a stopped VM.

    For an end-to-end tutorial, see https://learn.microsoft.com/azure/virtual-machines/linux/capture-image

    :example: Deallocate, generalize, and capture a stopped virtual machine.
        az vm deallocate -g MyResourceGroup -n MyVm
        az vm generalize -g MyResourceGroup -n MyVm
        az vm capture -g MyResourceGroup -n MyVm --vhd-name-prefix MyPrefix

    :example: Deallocate, generalize, and capture multiple stopped virtual machines.
        az vm deallocate --ids MyVmIds
        az vm generalize --ids MyVmIds
        az vm capture --ids MyVmIds --vhd-name-prefix MyPrefix
    """

    _aaz_info = {
        "version": "2024-11-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachines/{}/capture", "2024-11-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The name of the virtual machine.",
            required=True,
            id_part="name",
        )

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.storage_container = AAZStrArg(
            options=["--storage-container"],
            arg_group="Parameters",
            help="The storage account container name in which to save the disks.",
            required=True,
            default="vhds",
        )
        _args_schema.overwrite = AAZBoolArg(
            options=["--overwrite"],
            arg_group="Parameters",
            help="Overwrite the existing disk file.",
            required=True,
            default=True,
        )
        _args_schema.vhd_name_prefix = AAZStrArg(
            options=["--vhd-name-prefix"],
            arg_group="Parameters",
            help="The VHD name prefix specify for the VM disks.",
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualMachinesCapture(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualMachinesCapture(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachines/{vmName}/capture",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmName", self.ctx.args.name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-11-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("destinationContainerName", AAZStrType, ".storage_container", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("overwriteVhds", AAZBoolType, ".overwrite", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("vhdPrefix", AAZStrType, ".vhd_name_prefix", typ_kwargs={"flags": {"required": True}})

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200["$schema"] = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.content_version = AAZStrType(
                serialized_name="contentVersion",
                flags={"read_only": True},
            )
            _schema_on_200.id = AAZStrType()
            _schema_on_200.parameters = AAZDictType(
                flags={"read_only": True},
            )
            _schema_on_200.resources = AAZListType(
                flags={"read_only": True},
            )

            parameters = cls._schema_on_200.parameters
            parameters.Element = AAZAnyType()

            resources = cls._schema_on_200.resources
            resources.Element = AAZDictType()

            _element = cls._schema_on_200.resources.Element
            _element.Element = AAZAnyType()

            return cls._schema_on_200


class _CaptureHelper:
    """Helper class for Capture"""


__all__ = ["Capture"]
