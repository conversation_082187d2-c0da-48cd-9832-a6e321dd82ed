# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
#
# Generation mode: Incremental
# --------------------------------------------------------------------------

from azure.cli.core import AzCommandsLoader
from azure.cli.core.profiles import ResourceType

import azure.cli.command_modules.vm._help  # pylint: disable=unused-import


class ComputeCommandsLoader(AzCommandsLoader):

    def __init__(self, cli_ctx=None):
        from azure.cli.core.commands import CliCommandType
        compute_custom = CliCommandType(
            operations_tmpl='azure.cli.command_modules.vm.custom#{}',
            operation_group='virtual_machines'
        )
        super().__init__(cli_ctx=cli_ctx,
                         resource_type=ResourceType.MGMT_COMPUTE,
                         operation_group='virtual_machines',
                         custom_command_type=compute_custom)

    def load_command_table(self, args):
        from azure.cli.command_modules.vm.commands import load_command_table
        from azure.cli.core.aaz import load_aaz_command_table
        try:
            from . import aaz
        except ImportError:
            aaz = None
        if aaz:
            load_aaz_command_table(
                loader=self,
                aaz_pkg_name=aaz.__name__,
                args=args
            )
        load_command_table(self, args)
        try:
            # When generated commands are required uncomment the following two lines.
            from .generated.commands import load_command_table as load_command_table_generated
            load_command_table_generated(self, args)
            from .manual.commands import load_command_table as load_command_table_manual
            load_command_table_manual(self, args)
        except ImportError:
            pass
        return self.command_table

    def load_arguments(self, command):
        from azure.cli.command_modules.vm._params import load_arguments
        load_arguments(self, command)
        try:
            from .generated._params import load_arguments as load_arguments_generated
            load_arguments_generated(self, command)
            from .manual._params import load_arguments as load_arguments_manual
            load_arguments_manual(self, command)
        except ImportError:
            pass


COMMAND_LOADER_CLS = ComputeCommandsLoader
