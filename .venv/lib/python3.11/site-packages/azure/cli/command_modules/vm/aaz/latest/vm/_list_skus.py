# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class ListSkus(AAZCommand):
    """Get details for compute-related resource SKUs.

    This command incorporates subscription level restriction, offering the most accurate information.
    """

    _aaz_info = {
        "version": "2019-04-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.compute/skus", "2019-04-01"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.filter = AAZStrArg(
            options=["--filter"],
            help="The filter to apply on the operation. Only **location** filter is supported currently.",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.ResourceSkusList(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class ResourceSkusList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.Compute/skus",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$filter", self.ctx.args.filter,
                ),
                **self.serialize_query_param(
                    "api-version", "2019-04-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
            )
            _schema_on_200.value = AAZListType(
                flags={"required": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.api_versions = AAZListType(
                serialized_name="apiVersions",
                flags={"read_only": True},
            )
            _element.capabilities = AAZListType(
                flags={"read_only": True},
            )
            _element.capacity = AAZObjectType(
                flags={"read_only": True},
            )
            _element.costs = AAZListType(
                flags={"read_only": True},
            )
            _element.family = AAZStrType(
                flags={"read_only": True},
            )
            _element.kind = AAZStrType(
                flags={"read_only": True},
            )
            _element.location_info = AAZListType(
                serialized_name="locationInfo",
                flags={"read_only": True},
            )
            _element.locations = AAZListType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.resource_type = AAZStrType(
                serialized_name="resourceType",
                flags={"read_only": True},
            )
            _element.restrictions = AAZListType(
                flags={"read_only": True},
            )
            _element.size = AAZStrType(
                flags={"read_only": True},
            )
            _element.tier = AAZStrType(
                flags={"read_only": True},
            )

            api_versions = cls._schema_on_200.value.Element.api_versions
            api_versions.Element = AAZStrType()

            capabilities = cls._schema_on_200.value.Element.capabilities
            capabilities.Element = AAZObjectType()
            _ListSkusHelper._build_schema_resource_sku_capabilities_read(capabilities.Element)

            capacity = cls._schema_on_200.value.Element.capacity
            capacity.default = AAZIntType(
                flags={"read_only": True},
            )
            capacity.maximum = AAZIntType(
                flags={"read_only": True},
            )
            capacity.minimum = AAZIntType(
                flags={"read_only": True},
            )
            capacity.scale_type = AAZStrType(
                serialized_name="scaleType",
                flags={"read_only": True},
            )

            costs = cls._schema_on_200.value.Element.costs
            costs.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.costs.Element
            _element.extended_unit = AAZStrType(
                serialized_name="extendedUnit",
                flags={"read_only": True},
            )
            _element.meter_id = AAZStrType(
                serialized_name="meterID",
                flags={"read_only": True},
            )
            _element.quantity = AAZIntType(
                flags={"read_only": True},
            )

            location_info = cls._schema_on_200.value.Element.location_info
            location_info.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.location_info.Element
            _element.location = AAZStrType(
                flags={"read_only": True},
            )
            _element.zone_details = AAZListType(
                serialized_name="zoneDetails",
                flags={"read_only": True},
            )
            _element.zones = AAZListType(
                flags={"read_only": True},
            )

            zone_details = cls._schema_on_200.value.Element.location_info.Element.zone_details
            zone_details.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.location_info.Element.zone_details.Element
            _element.capabilities = AAZListType(
                flags={"read_only": True},
            )
            _element.name = AAZListType(
                flags={"read_only": True},
            )

            capabilities = cls._schema_on_200.value.Element.location_info.Element.zone_details.Element.capabilities
            capabilities.Element = AAZObjectType()
            _ListSkusHelper._build_schema_resource_sku_capabilities_read(capabilities.Element)

            name = cls._schema_on_200.value.Element.location_info.Element.zone_details.Element.name
            name.Element = AAZStrType()

            zones = cls._schema_on_200.value.Element.location_info.Element.zones
            zones.Element = AAZStrType()

            locations = cls._schema_on_200.value.Element.locations
            locations.Element = AAZStrType()

            restrictions = cls._schema_on_200.value.Element.restrictions
            restrictions.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.restrictions.Element
            _element.reason_code = AAZStrType(
                serialized_name="reasonCode",
                flags={"read_only": True},
            )
            _element.restriction_info = AAZObjectType(
                serialized_name="restrictionInfo",
                flags={"read_only": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )
            _element.values = AAZListType(
                flags={"read_only": True},
            )

            restriction_info = cls._schema_on_200.value.Element.restrictions.Element.restriction_info
            restriction_info.locations = AAZListType(
                flags={"read_only": True},
            )
            restriction_info.zones = AAZListType(
                flags={"read_only": True},
            )

            locations = cls._schema_on_200.value.Element.restrictions.Element.restriction_info.locations
            locations.Element = AAZStrType()

            zones = cls._schema_on_200.value.Element.restrictions.Element.restriction_info.zones
            zones.Element = AAZStrType()

            values = cls._schema_on_200.value.Element.restrictions.Element.values
            values.Element = AAZStrType()

            return cls._schema_on_200


class _ListSkusHelper:
    """Helper class for ListSkus"""

    _schema_resource_sku_capabilities_read = None

    @classmethod
    def _build_schema_resource_sku_capabilities_read(cls, _schema):
        if cls._schema_resource_sku_capabilities_read is not None:
            _schema.name = cls._schema_resource_sku_capabilities_read.name
            _schema.value = cls._schema_resource_sku_capabilities_read.value
            return

        cls._schema_resource_sku_capabilities_read = _schema_resource_sku_capabilities_read = AAZObjectType()

        resource_sku_capabilities_read = _schema_resource_sku_capabilities_read
        resource_sku_capabilities_read.name = AAZStrType(
            flags={"read_only": True},
        )
        resource_sku_capabilities_read.value = AAZStrType(
            flags={"read_only": True},
        )

        _schema.name = cls._schema_resource_sku_capabilities_read.name
        _schema.value = cls._schema_resource_sku_capabilities_read.value


__all__ = ["ListSkus"]
