# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "disk create",
)
class Create(AAZCommand):
    """Create a managed disk.

    :example: Create a managed disk by importing from a blob uri.
        az disk create -g MyResourceGroup -n MyDisk --source https://vhd1234.blob.core.windows.net/vhds/osdisk1234.vhd

    :example: Create an empty managed disk.
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10

    :example: Create an empty managed disk with bursting enabled.
        az disk create -g MyResourceGroup -n MyDisk --size-gb 1024 --location centraluseuap --enable-bursting

    :example: Create a managed disk by copying an existing disk or snapshot.
        az disk create -g MyResourceGroup -n MyDisk2 --source MyDisk

    :example: Create a disk in an availability zone in the region of "East US 2".
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --location eastus2 --zone 1

    :example: Create a disk from image.
        az disk create -g MyResourceGroup -n MyDisk --image-reference Canonical:0001-com-ubuntu-server-jammy:22_04-lts-gen2:latest

    :example: Create a disk from the OS Disk of a compute gallery image version.
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0

    :example: Create a disk from the OS Disk of the latest version in a compute gallery image.
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage

    :example: Create a disk from the OS Disk of a shared gallery image version.
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /SharedGalleries/sharedGalleryUniqueName/Images/imageName/Versions/1.0.0

    :example: Create a disk from the OS Disk of a community gallery image version.
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /CommunityGalleries/communityGalleryPublicGalleryName/Images/imageName/Versions/1.0.0

    :example: Create a disk from the Data Disk of a gallery image.
        az disk create -g MyResourceGroup -n MyDisk --gallery-image-reference /subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/myRG/providers/Microsoft.Compute/galleries/myGallery/images/myImage/versions/1.0.0 --gallery-image-reference-lun 0

    :example: Create a disk with total number of IOPS and total throughput (MBps) limitation.
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --sku UltraSSD_LRS --disk-iops-read-only 200 --disk-mbps-read-only 30

    :example: Create a disk and specify maximum number of VMs that can attach to the disk at the same time.
        az disk create -g MyResourceGroup -n MyDisk --size-gb 256 --max-shares 2 -l centraluseuap

    :example: Create a disk and associate it with a disk access resource.
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --network-access-policy AllowPrivate --disk-access MyDiskAccessID

    :example: Create a disk from the blob URI for VM guest state VHD.
        az disk create -g MyResourceGroup -n MyDisk --size-gb 10 --security-data-uri GuestStateDiskVhdUri --security-type TrustedLaunch --hyper-v-generation V2

    :example: Create a standard disk for uploading blobs.
        az disk create -g MyResourceGroup -n MyDisk --upload-size-bytes ******** --upload-type Upload

    :example: Create an OS disk for uploading along with VM guest state.
        az disk create -g MyResourceGroup -n MyDisk --upload-size-bytes ******** --upload-type UploadWithSecurityData --security-type TrustedLaunch --hyper-v-generation V2
    """

    _aaz_info = {
        "version": "2025-01-02",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/disks/{}", "2025-01-02"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.disk_name = AAZStrArg(
            options=["-n", "--name", "--disk-name"],
            help="The name of the managed disk that is being created. The name can't be changed after the disk is created. Supported characters for the name are a-z, A-Z, 0-9, _ and -. The maximum name length is 80 characters.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "Disk"

        _args_schema = cls._args_schema
        _args_schema.extended_location = AAZObjectArg(
            options=["--extended-location"],
            arg_group="Disk",
            help="The extended location where the disk will be created. Extended location cannot be changed.",
        )
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Disk",
            help="Resource location",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.sku = AAZObjectArg(
            options=["--sku"],
            arg_group="Disk",
            help="The disks sku name. Can be Standard_LRS, Premium_LRS, StandardSSD_LRS, UltraSSD_LRS, Premium_ZRS, StandardSSD_ZRS, or PremiumV2_LRS.",
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Disk",
            help="Resource tags",
        )
        _args_schema.zones = AAZListArg(
            options=["--zones"],
            arg_group="Disk",
            help="The Logical zone list for Disk.",
        )

        extended_location = cls._args_schema.extended_location
        extended_location.name = AAZStrArg(
            options=["name"],
            help="The name of the extended location.",
        )
        extended_location.type = AAZStrArg(
            options=["type"],
            help="The type of the extended location.",
            enum={"EdgeZone": "EdgeZone"},
        )

        sku = cls._args_schema.sku
        sku.name = AAZStrArg(
            options=["name"],
            help="The sku name.",
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Premium_ZRS": "Premium_ZRS", "StandardSSD_LRS": "StandardSSD_LRS", "StandardSSD_ZRS": "StandardSSD_ZRS", "Standard_LRS": "Standard_LRS", "UltraSSD_LRS": "UltraSSD_LRS"},
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        zones = cls._args_schema.zones
        zones.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.availability_policy = AAZObjectArg(
            options=["--availability-policy"],
            arg_group="Properties",
            help="Determines how platform treats disk failures",
        )
        _args_schema.bursting_enabled = AAZBoolArg(
            options=["--bursting-enabled"],
            arg_group="Properties",
            help="Set to true to enable bursting beyond the provisioned performance target of the disk. Bursting is disabled by default. Does not apply to Ultra disks.",
        )
        _args_schema.creation_data = AAZObjectArg(
            options=["--creation-data"],
            arg_group="Properties",
            help="Disk source information. CreationData information cannot be changed after the disk has been created.",
        )
        _args_schema.data_access_auth_mode = AAZStrArg(
            options=["--data-access-auth-mode"],
            arg_group="Properties",
            help="Additional authentication requirements when exporting or uploading to a disk or snapshot.",
            enum={"AzureActiveDirectory": "AzureActiveDirectory", "None": "None"},
        )
        _args_schema.disk_access_id = AAZStrArg(
            options=["--disk-access-id"],
            arg_group="Properties",
            help="ARM id of the DiskAccess resource for using private endpoints on disks.",
        )
        _args_schema.disk_iops_read_only = AAZIntArg(
            options=["--disk-iops-read-only"],
            arg_group="Properties",
            help="The total number of IOPS that will be allowed across all VMs mounting the shared disk as ReadOnly. One operation can transfer between 4k and 256k bytes.",
        )
        _args_schema.disk_iops_read_write = AAZIntArg(
            options=["--disk-iops-read-write"],
            arg_group="Properties",
            help="The number of IOPS allowed for this disk; only settable for UltraSSD disks. One operation can transfer between 4k and 256k bytes.",
        )
        _args_schema.disk_m_bps_read_only = AAZIntArg(
            options=["--disk-m-bps-read-only"],
            arg_group="Properties",
            help="The total throughput (MBps) that will be allowed across all VMs mounting the shared disk as ReadOnly. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of 10.",
        )
        _args_schema.disk_m_bps_read_write = AAZIntArg(
            options=["--disk-m-bps-read-write"],
            arg_group="Properties",
            help="The bandwidth allowed for this disk; only settable for UltraSSD disks. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of 10.",
        )
        _args_schema.disk_size_gb = AAZIntArg(
            options=["--disk-size-gb"],
            arg_group="Properties",
            help="If creationData.createOption is Empty, this field is mandatory and it indicates the size of the disk to create. If this field is present for updates or creation with other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a running VM, and can only increase the disk's size.",
        )
        _args_schema.encryption = AAZObjectArg(
            options=["--encryption"],
            arg_group="Properties",
            help="Encryption property can be used to encrypt data at rest with customer managed keys or platform managed keys.",
        )
        _args_schema.hyper_v_generation = AAZStrArg(
            options=["--hyper-v-generation"],
            arg_group="Properties",
            help="The hypervisor generation of the Virtual Machine. Applicable to OS disks only.",
            enum={"V1": "V1", "V2": "V2"},
        )
        _args_schema.max_shares = AAZIntArg(
            options=["--max-shares"],
            arg_group="Properties",
            help="The maximum number of VMs that can attach to the disk at the same time. Value greater than one indicates a disk that can be mounted on multiple VMs at the same time.",
        )
        _args_schema.network_access_policy = AAZStrArg(
            options=["--network-access-policy"],
            arg_group="Properties",
            help="Policy for accessing the disk via network.",
            enum={"AllowAll": "AllowAll", "AllowPrivate": "AllowPrivate", "DenyAll": "DenyAll"},
        )
        _args_schema.optimized_for_frequent_attach = AAZBoolArg(
            options=["--optimized-for-frequent-attach"],
            arg_group="Properties",
            help="Setting this property to true improves reliability and performance of data disks that are frequently (more than 5 times a day) by detached from one virtual machine and attached to another. This property should not be set for disks that are not detached and attached frequently as it causes the disks to not align with the fault domain of the virtual machine.",
        )
        _args_schema.os_type = AAZStrArg(
            options=["--os-type"],
            arg_group="Properties",
            help="The Operating System type.",
            enum={"Linux": "Linux", "Windows": "Windows"},
        )
        _args_schema.public_network_access = AAZStrArg(
            options=["--public-network-access"],
            arg_group="Properties",
            help="Policy for controlling export on the disk.",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _args_schema.security_profile = AAZObjectArg(
            options=["--security-profile"],
            arg_group="Properties",
            help="Contains the security related information for the resource.",
        )
        _args_schema.supported_capabilities = AAZObjectArg(
            options=["--supported-capabilities"],
            arg_group="Properties",
            help="List of supported capabilities for the image from which the OS disk was created.",
        )
        _args_schema.supports_hibernation = AAZBoolArg(
            options=["--supports-hibernation"],
            arg_group="Properties",
            help="Indicates the OS on a disk supports hibernation.",
        )
        _args_schema.tier = AAZStrArg(
            options=["--tier"],
            arg_group="Properties",
            help="Performance tier of the disk (e.g, P4, S10) as described here: https://azure.microsoft.com/en-us/pricing/details/managed-disks/. Does not apply to Ultra disks.",
        )

        availability_policy = cls._args_schema.availability_policy
        availability_policy.action_on_disk_delay = AAZStrArg(
            options=["action-on-disk-delay"],
            help="Determines on how to handle disks with slow I/O.",
            enum={"AutomaticReattach": "AutomaticReattach", "None": "None"},
        )

        creation_data = cls._args_schema.creation_data
        creation_data.create_option = AAZStrArg(
            options=["create-option"],
            help="This enumerates the possible sources of a disk's creation.",
            required=True,
            enum={"Attach": "Attach", "Copy": "Copy", "CopyFromSanSnapshot": "CopyFromSanSnapshot", "CopyStart": "CopyStart", "Empty": "Empty", "FromImage": "FromImage", "Import": "Import", "ImportSecure": "ImportSecure", "Restore": "Restore", "Upload": "Upload", "UploadPreparedSecure": "UploadPreparedSecure"},
        )
        creation_data.elastic_san_resource_id = AAZStrArg(
            options=["elastic-san-resource-id"],
            help="Required if createOption is CopyFromSanSnapshot. This is the ARM id of the source elastic san volume snapshot.",
        )
        creation_data.gallery_image_reference = AAZObjectArg(
            options=["gallery-image-reference"],
            help="Required if creating from a Gallery Image. The id/sharedGalleryImageId/communityGalleryImageId of the ImageDiskReference will be the ARM id of the shared galley image version from which to create a disk.",
        )
        cls._build_args_image_disk_reference_create(creation_data.gallery_image_reference)
        creation_data.image_reference = AAZObjectArg(
            options=["image-reference"],
            help="Disk source information for PIR or user images.",
        )
        cls._build_args_image_disk_reference_create(creation_data.image_reference)
        creation_data.instant_access_duration_minutes = AAZIntArg(
            options=["instant-access-duration-minutes"],
            help="For snapshots created from Premium SSD v2 or Ultra disk, this property determines the time in minutes the snapshot is retained for instant access to enable faster restore.",
            fmt=AAZIntArgFormat(
                minimum=1,
            ),
        )
        creation_data.logical_sector_size = AAZIntArg(
            options=["logical-sector-size"],
            help="Logical sector size in bytes for Ultra disks. Supported values are 512 ad 4096. 4096 is the default.",
        )
        creation_data.performance_plus = AAZBoolArg(
            options=["performance-plus"],
            help="Set this flag to true to get a boost on the performance target of the disk deployed, see here on the respective performance target. This flag can only be set on disk creation time and cannot be disabled after enabled.",
        )
        creation_data.provisioned_bandwidth_copy_speed = AAZStrArg(
            options=["provisioned-bandwidth-copy-speed"],
            help="If this field is set on a snapshot and createOption is CopyStart, the snapshot will be copied at a quicker speed.",
            enum={"Enhanced": "Enhanced", "None": "None"},
        )
        creation_data.security_data_uri = AAZStrArg(
            options=["security-data-uri"],
            help="If createOption is ImportSecure, this is the URI of a blob to be imported into VM guest state.",
        )
        creation_data.security_metadata_uri = AAZStrArg(
            options=["security-metadata-uri"],
            help="If createOption is ImportSecure, this is the URI of a blob to be imported into VM metadata for Confidential VM.",
        )
        creation_data.source_resource_id = AAZStrArg(
            options=["source-resource-id"],
            help="If createOption is Copy, this is the ARM id of the source snapshot or disk.",
        )
        creation_data.source_uri = AAZStrArg(
            options=["source-uri"],
            help="If createOption is Import, this is the URI of a blob to be imported into a managed disk.",
        )
        creation_data.storage_account_id = AAZStrArg(
            options=["storage-account-id"],
            help="Required if createOption is Import. The Azure Resource Manager identifier of the storage account containing the blob to import as a disk.",
        )
        creation_data.upload_size_bytes = AAZIntArg(
            options=["upload-size-bytes"],
            help="If createOption is Upload, this is the size of the contents of the upload including the VHD footer. This value should be between ******** (20 MiB + 512 bytes for the VHD footer) and ************** bytes (32 TiB + 512 bytes for the VHD footer).",
        )

        encryption = cls._args_schema.encryption
        encryption.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="ResourceId of the disk encryption set to use for enabling encryption at rest.",
        )
        encryption.type = AAZStrArg(
            options=["type"],
            help="The type of key used to encrypt the data of the disk.",
            enum={"EncryptionAtRestWithCustomerKey": "EncryptionAtRestWithCustomerKey", "EncryptionAtRestWithPlatformAndCustomerKeys": "EncryptionAtRestWithPlatformAndCustomerKeys", "EncryptionAtRestWithPlatformKey": "EncryptionAtRestWithPlatformKey"},
        )

        security_profile = cls._args_schema.security_profile
        security_profile.secure_vm_disk_encryption_set_id = AAZStrArg(
            options=["secure-vm-disk-encryption-set-id"],
            help="ResourceId of the disk encryption set associated to Confidential VM supported disk encrypted with customer managed key",
        )
        security_profile.security_type = AAZStrArg(
            options=["security-type"],
            help="Specifies the SecurityType of the VM. Applicable for OS disks only.",
            enum={"ConfidentialVM_DiskEncryptedWithCustomerKey": "ConfidentialVM_DiskEncryptedWithCustomerKey", "ConfidentialVM_DiskEncryptedWithPlatformKey": "ConfidentialVM_DiskEncryptedWithPlatformKey", "ConfidentialVM_NonPersistedTPM": "ConfidentialVM_NonPersistedTPM", "ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey": "ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey", "TrustedLaunch": "TrustedLaunch"},
        )

        supported_capabilities = cls._args_schema.supported_capabilities
        supported_capabilities.accelerated_network = AAZBoolArg(
            options=["accelerated-network"],
            help="True if the image from which the OS disk is created supports accelerated networking.",
        )
        supported_capabilities.architecture = AAZStrArg(
            options=["architecture"],
            help="CPU architecture supported by an OS disk.",
            enum={"Arm64": "Arm64", "x64": "x64"},
        )
        supported_capabilities.disk_controller_types = AAZStrArg(
            options=["disk-controller-types"],
            help="The disk controllers that an OS disk supports. If set it can be SCSI or SCSI, NVME or NVME, SCSI.",
        )
        supported_capabilities.supported_security_option = AAZStrArg(
            options=["supported-security-option"],
            help="Refers to the security capability of the disk supported to create a Trusted launch or Confidential VM",
            enum={"TrustedLaunchAndConfidentialVMSupported": "TrustedLaunchAndConfidentialVMSupported", "TrustedLaunchSupported": "TrustedLaunchSupported"},
        )
        return cls._args_schema

    _args_image_disk_reference_create = None

    @classmethod
    def _build_args_image_disk_reference_create(cls, _schema):
        if cls._args_image_disk_reference_create is not None:
            _schema.community_gallery_image_id = cls._args_image_disk_reference_create.community_gallery_image_id
            _schema.id = cls._args_image_disk_reference_create.id
            _schema.lun = cls._args_image_disk_reference_create.lun
            _schema.shared_gallery_image_id = cls._args_image_disk_reference_create.shared_gallery_image_id
            return

        cls._args_image_disk_reference_create = AAZObjectArg()

        image_disk_reference_create = cls._args_image_disk_reference_create
        image_disk_reference_create.community_gallery_image_id = AAZStrArg(
            options=["community-gallery-image-id"],
            help="A relative uri containing a community Azure Compute Gallery image reference.",
        )
        image_disk_reference_create.id = AAZStrArg(
            options=["id"],
            help="A relative uri containing either a Platform Image Repository, user image, or Azure Compute Gallery image reference.",
        )
        image_disk_reference_create.lun = AAZIntArg(
            options=["lun"],
            help="If the disk is created from an image's data disk, this is an index that indicates which of the data disks in the image to use. For OS disks, this field is null.",
        )
        image_disk_reference_create.shared_gallery_image_id = AAZStrArg(
            options=["shared-gallery-image-id"],
            help="A relative uri containing a direct shared Azure Compute Gallery image reference.",
        )

        _schema.community_gallery_image_id = cls._args_image_disk_reference_create.community_gallery_image_id
        _schema.id = cls._args_image_disk_reference_create.id
        _schema.lun = cls._args_image_disk_reference_create.lun
        _schema.shared_gallery_image_id = cls._args_image_disk_reference_create.shared_gallery_image_id

    _args_source_vault_create = None

    @classmethod
    def _build_args_source_vault_create(cls, _schema):
        if cls._args_source_vault_create is not None:
            _schema.id = cls._args_source_vault_create.id
            return

        cls._args_source_vault_create = AAZObjectArg()

        source_vault_create = cls._args_source_vault_create
        source_vault_create.id = AAZStrArg(
            options=["id"],
            help="Resource Id",
        )

        _schema.id = cls._args_source_vault_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.DisksCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class DisksCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/disks/{diskName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "diskName", self.ctx.args.disk_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-02",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("extendedLocation", AAZObjectType, ".extended_location")
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("sku", AAZObjectType, ".sku")
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zones")

            extended_location = _builder.get(".extendedLocation")
            if extended_location is not None:
                extended_location.set_prop("name", AAZStrType, ".name")
                extended_location.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("availabilityPolicy", AAZObjectType, ".availability_policy")
                properties.set_prop("burstingEnabled", AAZBoolType, ".bursting_enabled")
                properties.set_prop("creationData", AAZObjectType, ".creation_data", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("dataAccessAuthMode", AAZStrType, ".data_access_auth_mode")
                properties.set_prop("diskAccessId", AAZStrType, ".disk_access_id")
                properties.set_prop("diskIOPSReadOnly", AAZIntType, ".disk_iops_read_only")
                properties.set_prop("diskIOPSReadWrite", AAZIntType, ".disk_iops_read_write")
                properties.set_prop("diskMBpsReadOnly", AAZIntType, ".disk_m_bps_read_only")
                properties.set_prop("diskMBpsReadWrite", AAZIntType, ".disk_m_bps_read_write")
                properties.set_prop("diskSizeGB", AAZIntType, ".disk_size_gb")
                properties.set_prop("encryption", AAZObjectType, ".encryption")
                properties.set_prop("hyperVGeneration", AAZStrType, ".hyper_v_generation")
                properties.set_prop("maxShares", AAZIntType, ".max_shares")
                properties.set_prop("networkAccessPolicy", AAZStrType, ".network_access_policy")
                properties.set_prop("optimizedForFrequentAttach", AAZBoolType, ".optimized_for_frequent_attach")
                properties.set_prop("osType", AAZStrType, ".os_type")
                properties.set_prop("publicNetworkAccess", AAZStrType, ".public_network_access")
                properties.set_prop("securityProfile", AAZObjectType, ".security_profile")
                properties.set_prop("supportedCapabilities", AAZObjectType, ".supported_capabilities")
                properties.set_prop("supportsHibernation", AAZBoolType, ".supports_hibernation")
                properties.set_prop("tier", AAZStrType, ".tier")

            availability_policy = _builder.get(".properties.availabilityPolicy")
            if availability_policy is not None:
                availability_policy.set_prop("actionOnDiskDelay", AAZStrType, ".action_on_disk_delay")

            creation_data = _builder.get(".properties.creationData")
            if creation_data is not None:
                creation_data.set_prop("createOption", AAZStrType, ".create_option", typ_kwargs={"flags": {"required": True}})
                creation_data.set_prop("elasticSanResourceId", AAZStrType, ".elastic_san_resource_id")
                _CreateHelper._build_schema_image_disk_reference_create(creation_data.set_prop("galleryImageReference", AAZObjectType, ".gallery_image_reference"))
                _CreateHelper._build_schema_image_disk_reference_create(creation_data.set_prop("imageReference", AAZObjectType, ".image_reference"))
                creation_data.set_prop("instantAccessDurationMinutes", AAZIntType, ".instant_access_duration_minutes")
                creation_data.set_prop("logicalSectorSize", AAZIntType, ".logical_sector_size")
                creation_data.set_prop("performancePlus", AAZBoolType, ".performance_plus")
                creation_data.set_prop("provisionedBandwidthCopySpeed", AAZStrType, ".provisioned_bandwidth_copy_speed")
                creation_data.set_prop("securityDataUri", AAZStrType, ".security_data_uri")
                creation_data.set_prop("securityMetadataUri", AAZStrType, ".security_metadata_uri")
                creation_data.set_prop("sourceResourceId", AAZStrType, ".source_resource_id")
                creation_data.set_prop("sourceUri", AAZStrType, ".source_uri")
                creation_data.set_prop("storageAccountId", AAZStrType, ".storage_account_id")
                creation_data.set_prop("uploadSizeBytes", AAZIntType, ".upload_size_bytes")

            encryption = _builder.get(".properties.encryption")
            if encryption is not None:
                encryption.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
                encryption.set_prop("type", AAZStrType, ".type")

            security_profile = _builder.get(".properties.securityProfile")
            if security_profile is not None:
                security_profile.set_prop("secureVMDiskEncryptionSetId", AAZStrType, ".secure_vm_disk_encryption_set_id")
                security_profile.set_prop("securityType", AAZStrType, ".security_type")

            supported_capabilities = _builder.get(".properties.supportedCapabilities")
            if supported_capabilities is not None:
                supported_capabilities.set_prop("acceleratedNetwork", AAZBoolType, ".accelerated_network")
                supported_capabilities.set_prop("architecture", AAZStrType, ".architecture")
                supported_capabilities.set_prop("diskControllerTypes", AAZStrType, ".disk_controller_types")
                supported_capabilities.set_prop("supportedSecurityOption", AAZStrType, ".supported_security_option")

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("name", AAZStrType, ".name")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _CreateHelper._build_schema_disk_read(cls._schema_on_200)

            return cls._schema_on_200


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_image_disk_reference_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("communityGalleryImageId", AAZStrType, ".community_gallery_image_id")
        _builder.set_prop("id", AAZStrType, ".id")
        _builder.set_prop("lun", AAZIntType, ".lun")
        _builder.set_prop("sharedGalleryImageId", AAZStrType, ".shared_gallery_image_id")

    @classmethod
    def _build_schema_source_vault_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_disk_read = None

    @classmethod
    def _build_schema_disk_read(cls, _schema):
        if cls._schema_disk_read is not None:
            _schema.extended_location = cls._schema_disk_read.extended_location
            _schema.id = cls._schema_disk_read.id
            _schema.location = cls._schema_disk_read.location
            _schema.managed_by = cls._schema_disk_read.managed_by
            _schema.managed_by_extended = cls._schema_disk_read.managed_by_extended
            _schema.name = cls._schema_disk_read.name
            _schema.properties = cls._schema_disk_read.properties
            _schema.sku = cls._schema_disk_read.sku
            _schema.system_data = cls._schema_disk_read.system_data
            _schema.tags = cls._schema_disk_read.tags
            _schema.type = cls._schema_disk_read.type
            _schema.zones = cls._schema_disk_read.zones
            return

        cls._schema_disk_read = _schema_disk_read = AAZObjectType()

        disk_read = _schema_disk_read
        disk_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        disk_read.id = AAZStrType(
            flags={"read_only": True},
        )
        disk_read.location = AAZStrType(
            flags={"required": True},
        )
        disk_read.managed_by = AAZStrType(
            serialized_name="managedBy",
            flags={"read_only": True},
        )
        disk_read.managed_by_extended = AAZListType(
            serialized_name="managedByExtended",
            flags={"read_only": True},
        )
        disk_read.name = AAZStrType(
            flags={"read_only": True},
        )
        disk_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        disk_read.sku = AAZObjectType()
        disk_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        disk_read.tags = AAZDictType()
        disk_read.type = AAZStrType(
            flags={"read_only": True},
        )
        disk_read.zones = AAZListType()

        extended_location = _schema_disk_read.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        managed_by_extended = _schema_disk_read.managed_by_extended
        managed_by_extended.Element = AAZStrType()

        properties = _schema_disk_read.properties
        properties.last_ownership_update_time = AAZStrType(
            serialized_name="LastOwnershipUpdateTime",
            flags={"read_only": True},
        )
        properties.availability_policy = AAZObjectType(
            serialized_name="availabilityPolicy",
        )
        properties.bursting_enabled = AAZBoolType(
            serialized_name="burstingEnabled",
        )
        properties.bursting_enabled_time = AAZStrType(
            serialized_name="burstingEnabledTime",
            flags={"read_only": True},
        )
        properties.completion_percent = AAZFloatType(
            serialized_name="completionPercent",
        )
        properties.creation_data = AAZObjectType(
            serialized_name="creationData",
            flags={"required": True},
        )
        properties.data_access_auth_mode = AAZStrType(
            serialized_name="dataAccessAuthMode",
        )
        properties.disk_access_id = AAZStrType(
            serialized_name="diskAccessId",
        )
        properties.disk_iops_read_only = AAZIntType(
            serialized_name="diskIOPSReadOnly",
        )
        properties.disk_iops_read_write = AAZIntType(
            serialized_name="diskIOPSReadWrite",
        )
        properties.disk_m_bps_read_only = AAZIntType(
            serialized_name="diskMBpsReadOnly",
        )
        properties.disk_m_bps_read_write = AAZIntType(
            serialized_name="diskMBpsReadWrite",
        )
        properties.disk_size_bytes = AAZIntType(
            serialized_name="diskSizeBytes",
            flags={"read_only": True},
        )
        properties.disk_size_gb = AAZIntType(
            serialized_name="diskSizeGB",
        )
        properties.disk_state = AAZStrType(
            serialized_name="diskState",
            flags={"read_only": True},
        )
        properties.encryption = AAZObjectType()
        properties.encryption_settings_collection = AAZObjectType(
            serialized_name="encryptionSettingsCollection",
        )
        properties.hyper_v_generation = AAZStrType(
            serialized_name="hyperVGeneration",
        )
        properties.max_shares = AAZIntType(
            serialized_name="maxShares",
        )
        properties.network_access_policy = AAZStrType(
            serialized_name="networkAccessPolicy",
        )
        properties.optimized_for_frequent_attach = AAZBoolType(
            serialized_name="optimizedForFrequentAttach",
        )
        properties.os_type = AAZStrType(
            serialized_name="osType",
        )
        properties.property_updates_in_progress = AAZObjectType(
            serialized_name="propertyUpdatesInProgress",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_network_access = AAZStrType(
            serialized_name="publicNetworkAccess",
        )
        properties.purchase_plan = AAZObjectType(
            serialized_name="purchasePlan",
        )
        properties.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        properties.share_info = AAZListType(
            serialized_name="shareInfo",
            flags={"read_only": True},
        )
        properties.supported_capabilities = AAZObjectType(
            serialized_name="supportedCapabilities",
        )
        properties.supports_hibernation = AAZBoolType(
            serialized_name="supportsHibernation",
        )
        properties.tier = AAZStrType()
        properties.time_created = AAZStrType(
            serialized_name="timeCreated",
            flags={"read_only": True},
        )
        properties.unique_id = AAZStrType(
            serialized_name="uniqueId",
            flags={"read_only": True},
        )

        availability_policy = _schema_disk_read.properties.availability_policy
        availability_policy.action_on_disk_delay = AAZStrType(
            serialized_name="actionOnDiskDelay",
        )

        creation_data = _schema_disk_read.properties.creation_data
        creation_data.create_option = AAZStrType(
            serialized_name="createOption",
            flags={"required": True},
        )
        creation_data.elastic_san_resource_id = AAZStrType(
            serialized_name="elasticSanResourceId",
        )
        creation_data.gallery_image_reference = AAZObjectType(
            serialized_name="galleryImageReference",
        )
        cls._build_schema_image_disk_reference_read(creation_data.gallery_image_reference)
        creation_data.image_reference = AAZObjectType(
            serialized_name="imageReference",
        )
        cls._build_schema_image_disk_reference_read(creation_data.image_reference)
        creation_data.instant_access_duration_minutes = AAZIntType(
            serialized_name="instantAccessDurationMinutes",
        )
        creation_data.logical_sector_size = AAZIntType(
            serialized_name="logicalSectorSize",
        )
        creation_data.performance_plus = AAZBoolType(
            serialized_name="performancePlus",
        )
        creation_data.provisioned_bandwidth_copy_speed = AAZStrType(
            serialized_name="provisionedBandwidthCopySpeed",
        )
        creation_data.security_data_uri = AAZStrType(
            serialized_name="securityDataUri",
        )
        creation_data.security_metadata_uri = AAZStrType(
            serialized_name="securityMetadataUri",
        )
        creation_data.source_resource_id = AAZStrType(
            serialized_name="sourceResourceId",
        )
        creation_data.source_unique_id = AAZStrType(
            serialized_name="sourceUniqueId",
            flags={"read_only": True},
        )
        creation_data.source_uri = AAZStrType(
            serialized_name="sourceUri",
        )
        creation_data.storage_account_id = AAZStrType(
            serialized_name="storageAccountId",
        )
        creation_data.upload_size_bytes = AAZIntType(
            serialized_name="uploadSizeBytes",
        )

        encryption = _schema_disk_read.properties.encryption
        encryption.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        encryption.type = AAZStrType()

        encryption_settings_collection = _schema_disk_read.properties.encryption_settings_collection
        encryption_settings_collection.enabled = AAZBoolType(
            flags={"required": True},
        )
        encryption_settings_collection.encryption_settings = AAZListType(
            serialized_name="encryptionSettings",
        )
        encryption_settings_collection.encryption_settings_version = AAZStrType(
            serialized_name="encryptionSettingsVersion",
        )

        encryption_settings = _schema_disk_read.properties.encryption_settings_collection.encryption_settings
        encryption_settings.Element = AAZObjectType()

        _element = _schema_disk_read.properties.encryption_settings_collection.encryption_settings.Element
        _element.disk_encryption_key = AAZObjectType(
            serialized_name="diskEncryptionKey",
        )
        _element.key_encryption_key = AAZObjectType(
            serialized_name="keyEncryptionKey",
        )

        disk_encryption_key = _schema_disk_read.properties.encryption_settings_collection.encryption_settings.Element.disk_encryption_key
        disk_encryption_key.secret_url = AAZStrType(
            serialized_name="secretUrl",
            flags={"required": True},
        )
        disk_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_source_vault_read(disk_encryption_key.source_vault)

        key_encryption_key = _schema_disk_read.properties.encryption_settings_collection.encryption_settings.Element.key_encryption_key
        key_encryption_key.key_url = AAZStrType(
            serialized_name="keyUrl",
            flags={"required": True},
        )
        key_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_source_vault_read(key_encryption_key.source_vault)

        property_updates_in_progress = _schema_disk_read.properties.property_updates_in_progress
        property_updates_in_progress.target_tier = AAZStrType(
            serialized_name="targetTier",
        )

        purchase_plan = _schema_disk_read.properties.purchase_plan
        purchase_plan.name = AAZStrType(
            flags={"required": True},
        )
        purchase_plan.product = AAZStrType(
            flags={"required": True},
        )
        purchase_plan.promotion_code = AAZStrType(
            serialized_name="promotionCode",
        )
        purchase_plan.publisher = AAZStrType(
            flags={"required": True},
        )

        security_profile = _schema_disk_read.properties.security_profile
        security_profile.secure_vm_disk_encryption_set_id = AAZStrType(
            serialized_name="secureVMDiskEncryptionSetId",
        )
        security_profile.security_type = AAZStrType(
            serialized_name="securityType",
        )

        share_info = _schema_disk_read.properties.share_info
        share_info.Element = AAZObjectType()

        _element = _schema_disk_read.properties.share_info.Element
        _element.vm_uri = AAZStrType(
            serialized_name="vmUri",
            flags={"read_only": True},
        )

        supported_capabilities = _schema_disk_read.properties.supported_capabilities
        supported_capabilities.accelerated_network = AAZBoolType(
            serialized_name="acceleratedNetwork",
        )
        supported_capabilities.architecture = AAZStrType()
        supported_capabilities.disk_controller_types = AAZStrType(
            serialized_name="diskControllerTypes",
        )
        supported_capabilities.supported_security_option = AAZStrType(
            serialized_name="supportedSecurityOption",
        )

        sku = _schema_disk_read.sku
        sku.name = AAZStrType()
        sku.tier = AAZStrType(
            flags={"read_only": True},
        )

        system_data = _schema_disk_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_disk_read.tags
        tags.Element = AAZStrType()

        zones = _schema_disk_read.zones
        zones.Element = AAZStrType()

        _schema.extended_location = cls._schema_disk_read.extended_location
        _schema.id = cls._schema_disk_read.id
        _schema.location = cls._schema_disk_read.location
        _schema.managed_by = cls._schema_disk_read.managed_by
        _schema.managed_by_extended = cls._schema_disk_read.managed_by_extended
        _schema.name = cls._schema_disk_read.name
        _schema.properties = cls._schema_disk_read.properties
        _schema.sku = cls._schema_disk_read.sku
        _schema.system_data = cls._schema_disk_read.system_data
        _schema.tags = cls._schema_disk_read.tags
        _schema.type = cls._schema_disk_read.type
        _schema.zones = cls._schema_disk_read.zones

    _schema_image_disk_reference_read = None

    @classmethod
    def _build_schema_image_disk_reference_read(cls, _schema):
        if cls._schema_image_disk_reference_read is not None:
            _schema.community_gallery_image_id = cls._schema_image_disk_reference_read.community_gallery_image_id
            _schema.id = cls._schema_image_disk_reference_read.id
            _schema.lun = cls._schema_image_disk_reference_read.lun
            _schema.shared_gallery_image_id = cls._schema_image_disk_reference_read.shared_gallery_image_id
            return

        cls._schema_image_disk_reference_read = _schema_image_disk_reference_read = AAZObjectType()

        image_disk_reference_read = _schema_image_disk_reference_read
        image_disk_reference_read.community_gallery_image_id = AAZStrType(
            serialized_name="communityGalleryImageId",
        )
        image_disk_reference_read.id = AAZStrType()
        image_disk_reference_read.lun = AAZIntType()
        image_disk_reference_read.shared_gallery_image_id = AAZStrType(
            serialized_name="sharedGalleryImageId",
        )

        _schema.community_gallery_image_id = cls._schema_image_disk_reference_read.community_gallery_image_id
        _schema.id = cls._schema_image_disk_reference_read.id
        _schema.lun = cls._schema_image_disk_reference_read.lun
        _schema.shared_gallery_image_id = cls._schema_image_disk_reference_read.shared_gallery_image_id

    _schema_source_vault_read = None

    @classmethod
    def _build_schema_source_vault_read(cls, _schema):
        if cls._schema_source_vault_read is not None:
            _schema.id = cls._schema_source_vault_read.id
            return

        cls._schema_source_vault_read = _schema_source_vault_read = AAZObjectType()

        source_vault_read = _schema_source_vault_read
        source_vault_read.id = AAZStrType()

        _schema.id = cls._schema_source_vault_read.id


__all__ = ["Create"]
