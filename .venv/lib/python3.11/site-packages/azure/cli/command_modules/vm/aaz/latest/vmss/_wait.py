# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "vmss wait",
)
class Wait(AAZWaitCommand):
    """Place the CLI in a waiting state until a condition is met.
    """

    _aaz_info = {
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachinescalesets/{}", "2024-11-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.vm_scale_set_name = AAZStrArg(
            options=["-n", "--name", "--vm-scale-set-name"],
            help="The name of the VM scale set.",
            required=True,
            id_part="name",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.VirtualMachineScaleSetsGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=False)
        return result

    class VirtualMachineScaleSetsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachineScaleSets/{vmScaleSetName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmScaleSetName", self.ctx.args.vm_scale_set_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-11-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _WaitHelper._build_schema_virtual_machine_scale_set_read(cls._schema_on_200)

            return cls._schema_on_200


class _WaitHelper:
    """Helper class for Wait"""

    _schema_api_entity_reference_read = None

    @classmethod
    def _build_schema_api_entity_reference_read(cls, _schema):
        if cls._schema_api_entity_reference_read is not None:
            _schema.id = cls._schema_api_entity_reference_read.id
            return

        cls._schema_api_entity_reference_read = _schema_api_entity_reference_read = AAZObjectType()

        api_entity_reference_read = _schema_api_entity_reference_read
        api_entity_reference_read.id = AAZStrType()

        _schema.id = cls._schema_api_entity_reference_read.id

    _schema_disk_encryption_set_parameters_read = None

    @classmethod
    def _build_schema_disk_encryption_set_parameters_read(cls, _schema):
        if cls._schema_disk_encryption_set_parameters_read is not None:
            _schema.id = cls._schema_disk_encryption_set_parameters_read.id
            return

        cls._schema_disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read = AAZObjectType()

        disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read
        disk_encryption_set_parameters_read.id = AAZStrType()

        _schema.id = cls._schema_disk_encryption_set_parameters_read.id

    _schema_host_endpoint_settings_read = None

    @classmethod
    def _build_schema_host_endpoint_settings_read(cls, _schema):
        if cls._schema_host_endpoint_settings_read is not None:
            _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
            _schema.mode = cls._schema_host_endpoint_settings_read.mode
            return

        cls._schema_host_endpoint_settings_read = _schema_host_endpoint_settings_read = AAZObjectType()

        host_endpoint_settings_read = _schema_host_endpoint_settings_read
        host_endpoint_settings_read.in_vm_access_control_profile_reference_id = AAZStrType(
            serialized_name="inVMAccessControlProfileReferenceId",
        )
        host_endpoint_settings_read.mode = AAZStrType()

        _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
        _schema.mode = cls._schema_host_endpoint_settings_read.mode

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_virtual_machine_scale_set_managed_disk_parameters_read = None

    @classmethod
    def _build_schema_virtual_machine_scale_set_managed_disk_parameters_read(cls, _schema):
        if cls._schema_virtual_machine_scale_set_managed_disk_parameters_read is not None:
            _schema.disk_encryption_set = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set
            _schema.security_profile = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
            _schema.storage_account_type = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type
            return

        cls._schema_virtual_machine_scale_set_managed_disk_parameters_read = _schema_virtual_machine_scale_set_managed_disk_parameters_read = AAZObjectType()

        virtual_machine_scale_set_managed_disk_parameters_read = _schema_virtual_machine_scale_set_managed_disk_parameters_read
        virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set)
        virtual_machine_scale_set_managed_disk_parameters_read.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        security_profile = _schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
        security_profile.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(security_profile.disk_encryption_set)
        security_profile.security_encryption_type = AAZStrType(
            serialized_name="securityEncryptionType",
        )

        _schema.disk_encryption_set = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set
        _schema.security_profile = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
        _schema.storage_account_type = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type

    _schema_virtual_machine_scale_set_read = None

    @classmethod
    def _build_schema_virtual_machine_scale_set_read(cls, _schema):
        if cls._schema_virtual_machine_scale_set_read is not None:
            _schema.etag = cls._schema_virtual_machine_scale_set_read.etag
            _schema.extended_location = cls._schema_virtual_machine_scale_set_read.extended_location
            _schema.id = cls._schema_virtual_machine_scale_set_read.id
            _schema.identity = cls._schema_virtual_machine_scale_set_read.identity
            _schema.location = cls._schema_virtual_machine_scale_set_read.location
            _schema.name = cls._schema_virtual_machine_scale_set_read.name
            _schema.plan = cls._schema_virtual_machine_scale_set_read.plan
            _schema.properties = cls._schema_virtual_machine_scale_set_read.properties
            _schema.sku = cls._schema_virtual_machine_scale_set_read.sku
            _schema.tags = cls._schema_virtual_machine_scale_set_read.tags
            _schema.type = cls._schema_virtual_machine_scale_set_read.type
            _schema.zones = cls._schema_virtual_machine_scale_set_read.zones
            return

        cls._schema_virtual_machine_scale_set_read = _schema_virtual_machine_scale_set_read = AAZObjectType()

        virtual_machine_scale_set_read = _schema_virtual_machine_scale_set_read
        virtual_machine_scale_set_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        virtual_machine_scale_set_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        virtual_machine_scale_set_read.id = AAZStrType(
            flags={"read_only": True},
        )
        virtual_machine_scale_set_read.identity = AAZIdentityObjectType()
        virtual_machine_scale_set_read.location = AAZStrType(
            flags={"required": True},
        )
        virtual_machine_scale_set_read.name = AAZStrType(
            flags={"read_only": True},
        )
        virtual_machine_scale_set_read.plan = AAZObjectType()
        virtual_machine_scale_set_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        virtual_machine_scale_set_read.sku = AAZObjectType()
        virtual_machine_scale_set_read.tags = AAZDictType()
        virtual_machine_scale_set_read.type = AAZStrType(
            flags={"read_only": True},
        )
        virtual_machine_scale_set_read.zones = AAZListType()

        extended_location = _schema_virtual_machine_scale_set_read.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        identity = _schema_virtual_machine_scale_set_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType()
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_virtual_machine_scale_set_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        plan = _schema_virtual_machine_scale_set_read.plan
        plan.name = AAZStrType()
        plan.product = AAZStrType()
        plan.promotion_code = AAZStrType(
            serialized_name="promotionCode",
        )
        plan.publisher = AAZStrType()

        properties = _schema_virtual_machine_scale_set_read.properties
        properties.additional_capabilities = AAZObjectType(
            serialized_name="additionalCapabilities",
        )
        properties.automatic_repairs_policy = AAZObjectType(
            serialized_name="automaticRepairsPolicy",
        )
        properties.constrained_maximum_capacity = AAZBoolType(
            serialized_name="constrainedMaximumCapacity",
        )
        properties.do_not_run_extensions_on_overprovisioned_v_ms = AAZBoolType(
            serialized_name="doNotRunExtensionsOnOverprovisionedVMs",
        )
        properties.host_group = AAZObjectType(
            serialized_name="hostGroup",
        )
        cls._build_schema_sub_resource_read(properties.host_group)
        properties.orchestration_mode = AAZStrType(
            serialized_name="orchestrationMode",
        )
        properties.overprovision = AAZBoolType()
        properties.platform_fault_domain_count = AAZIntType(
            serialized_name="platformFaultDomainCount",
        )
        properties.priority_mix_policy = AAZObjectType(
            serialized_name="priorityMixPolicy",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.proximity_placement_group = AAZObjectType(
            serialized_name="proximityPlacementGroup",
        )
        cls._build_schema_sub_resource_read(properties.proximity_placement_group)
        properties.resiliency_policy = AAZObjectType(
            serialized_name="resiliencyPolicy",
        )
        properties.scale_in_policy = AAZObjectType(
            serialized_name="scaleInPolicy",
        )
        properties.scheduled_events_policy = AAZObjectType(
            serialized_name="scheduledEventsPolicy",
        )
        properties.single_placement_group = AAZBoolType(
            serialized_name="singlePlacementGroup",
        )
        properties.sku_profile = AAZObjectType(
            serialized_name="skuProfile",
        )
        properties.spot_restore_policy = AAZObjectType(
            serialized_name="spotRestorePolicy",
        )
        properties.time_created = AAZStrType(
            serialized_name="timeCreated",
            flags={"read_only": True},
        )
        properties.unique_id = AAZStrType(
            serialized_name="uniqueId",
            flags={"read_only": True},
        )
        properties.upgrade_policy = AAZObjectType(
            serialized_name="upgradePolicy",
        )
        properties.virtual_machine_profile = AAZObjectType(
            serialized_name="virtualMachineProfile",
        )
        properties.zonal_platform_fault_domain_align_mode = AAZStrType(
            serialized_name="zonalPlatformFaultDomainAlignMode",
        )
        properties.zone_balance = AAZBoolType(
            serialized_name="zoneBalance",
        )

        additional_capabilities = _schema_virtual_machine_scale_set_read.properties.additional_capabilities
        additional_capabilities.hibernation_enabled = AAZBoolType(
            serialized_name="hibernationEnabled",
        )
        additional_capabilities.ultra_ssd_enabled = AAZBoolType(
            serialized_name="ultraSSDEnabled",
        )

        automatic_repairs_policy = _schema_virtual_machine_scale_set_read.properties.automatic_repairs_policy
        automatic_repairs_policy.enabled = AAZBoolType()
        automatic_repairs_policy.grace_period = AAZStrType(
            serialized_name="gracePeriod",
        )
        automatic_repairs_policy.repair_action = AAZStrType(
            serialized_name="repairAction",
        )

        priority_mix_policy = _schema_virtual_machine_scale_set_read.properties.priority_mix_policy
        priority_mix_policy.base_regular_priority_count = AAZIntType(
            serialized_name="baseRegularPriorityCount",
        )
        priority_mix_policy.regular_priority_percentage_above_base = AAZIntType(
            serialized_name="regularPriorityPercentageAboveBase",
        )

        resiliency_policy = _schema_virtual_machine_scale_set_read.properties.resiliency_policy
        resiliency_policy.automatic_zone_rebalancing_policy = AAZObjectType(
            serialized_name="automaticZoneRebalancingPolicy",
        )
        resiliency_policy.resilient_vm_creation_policy = AAZObjectType(
            serialized_name="resilientVMCreationPolicy",
        )
        resiliency_policy.resilient_vm_deletion_policy = AAZObjectType(
            serialized_name="resilientVMDeletionPolicy",
        )

        automatic_zone_rebalancing_policy = _schema_virtual_machine_scale_set_read.properties.resiliency_policy.automatic_zone_rebalancing_policy
        automatic_zone_rebalancing_policy.enabled = AAZBoolType()
        automatic_zone_rebalancing_policy.rebalance_behavior = AAZStrType(
            serialized_name="rebalanceBehavior",
        )
        automatic_zone_rebalancing_policy.rebalance_strategy = AAZStrType(
            serialized_name="rebalanceStrategy",
        )

        resilient_vm_creation_policy = _schema_virtual_machine_scale_set_read.properties.resiliency_policy.resilient_vm_creation_policy
        resilient_vm_creation_policy.enabled = AAZBoolType()

        resilient_vm_deletion_policy = _schema_virtual_machine_scale_set_read.properties.resiliency_policy.resilient_vm_deletion_policy
        resilient_vm_deletion_policy.enabled = AAZBoolType()

        scale_in_policy = _schema_virtual_machine_scale_set_read.properties.scale_in_policy
        scale_in_policy.force_deletion = AAZBoolType(
            serialized_name="forceDeletion",
        )
        scale_in_policy.prioritize_unhealthy_v_ms = AAZBoolType(
            serialized_name="prioritizeUnhealthyVMs",
        )
        scale_in_policy.rules = AAZListType()

        rules = _schema_virtual_machine_scale_set_read.properties.scale_in_policy.rules
        rules.Element = AAZStrType()

        scheduled_events_policy = _schema_virtual_machine_scale_set_read.properties.scheduled_events_policy
        scheduled_events_policy.scheduled_events_additional_publishing_targets = AAZObjectType(
            serialized_name="scheduledEventsAdditionalPublishingTargets",
        )
        scheduled_events_policy.user_initiated_reboot = AAZObjectType(
            serialized_name="userInitiatedReboot",
        )
        scheduled_events_policy.user_initiated_redeploy = AAZObjectType(
            serialized_name="userInitiatedRedeploy",
        )

        scheduled_events_additional_publishing_targets = _schema_virtual_machine_scale_set_read.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets
        scheduled_events_additional_publishing_targets.event_grid_and_resource_graph = AAZObjectType(
            serialized_name="eventGridAndResourceGraph",
        )

        event_grid_and_resource_graph = _schema_virtual_machine_scale_set_read.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets.event_grid_and_resource_graph
        event_grid_and_resource_graph.enable = AAZBoolType()

        user_initiated_reboot = _schema_virtual_machine_scale_set_read.properties.scheduled_events_policy.user_initiated_reboot
        user_initiated_reboot.automatically_approve = AAZBoolType(
            serialized_name="automaticallyApprove",
        )

        user_initiated_redeploy = _schema_virtual_machine_scale_set_read.properties.scheduled_events_policy.user_initiated_redeploy
        user_initiated_redeploy.automatically_approve = AAZBoolType(
            serialized_name="automaticallyApprove",
        )

        sku_profile = _schema_virtual_machine_scale_set_read.properties.sku_profile
        sku_profile.allocation_strategy = AAZStrType(
            serialized_name="allocationStrategy",
        )
        sku_profile.vm_sizes = AAZListType(
            serialized_name="vmSizes",
        )

        vm_sizes = _schema_virtual_machine_scale_set_read.properties.sku_profile.vm_sizes
        vm_sizes.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.sku_profile.vm_sizes.Element
        _element.name = AAZStrType()
        _element.rank = AAZIntType()

        spot_restore_policy = _schema_virtual_machine_scale_set_read.properties.spot_restore_policy
        spot_restore_policy.enabled = AAZBoolType()
        spot_restore_policy.restore_timeout = AAZStrType(
            serialized_name="restoreTimeout",
        )

        upgrade_policy = _schema_virtual_machine_scale_set_read.properties.upgrade_policy
        upgrade_policy.automatic_os_upgrade_policy = AAZObjectType(
            serialized_name="automaticOSUpgradePolicy",
        )
        upgrade_policy.mode = AAZStrType()
        upgrade_policy.rolling_upgrade_policy = AAZObjectType(
            serialized_name="rollingUpgradePolicy",
        )

        automatic_os_upgrade_policy = _schema_virtual_machine_scale_set_read.properties.upgrade_policy.automatic_os_upgrade_policy
        automatic_os_upgrade_policy.disable_automatic_rollback = AAZBoolType(
            serialized_name="disableAutomaticRollback",
        )
        automatic_os_upgrade_policy.enable_automatic_os_upgrade = AAZBoolType(
            serialized_name="enableAutomaticOSUpgrade",
        )
        automatic_os_upgrade_policy.os_rolling_upgrade_deferral = AAZBoolType(
            serialized_name="osRollingUpgradeDeferral",
        )
        automatic_os_upgrade_policy.use_rolling_upgrade_policy = AAZBoolType(
            serialized_name="useRollingUpgradePolicy",
        )

        rolling_upgrade_policy = _schema_virtual_machine_scale_set_read.properties.upgrade_policy.rolling_upgrade_policy
        rolling_upgrade_policy.enable_cross_zone_upgrade = AAZBoolType(
            serialized_name="enableCrossZoneUpgrade",
        )
        rolling_upgrade_policy.max_batch_instance_percent = AAZIntType(
            serialized_name="maxBatchInstancePercent",
        )
        rolling_upgrade_policy.max_surge = AAZBoolType(
            serialized_name="maxSurge",
        )
        rolling_upgrade_policy.max_unhealthy_instance_percent = AAZIntType(
            serialized_name="maxUnhealthyInstancePercent",
        )
        rolling_upgrade_policy.max_unhealthy_upgraded_instance_percent = AAZIntType(
            serialized_name="maxUnhealthyUpgradedInstancePercent",
        )
        rolling_upgrade_policy.pause_time_between_batches = AAZStrType(
            serialized_name="pauseTimeBetweenBatches",
        )
        rolling_upgrade_policy.prioritize_unhealthy_instances = AAZBoolType(
            serialized_name="prioritizeUnhealthyInstances",
        )
        rolling_upgrade_policy.rollback_failed_instances_on_policy_breach = AAZBoolType(
            serialized_name="rollbackFailedInstancesOnPolicyBreach",
        )

        virtual_machine_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile
        virtual_machine_profile.application_profile = AAZObjectType(
            serialized_name="applicationProfile",
        )
        virtual_machine_profile.billing_profile = AAZObjectType(
            serialized_name="billingProfile",
        )
        virtual_machine_profile.capacity_reservation = AAZObjectType(
            serialized_name="capacityReservation",
        )
        virtual_machine_profile.diagnostics_profile = AAZObjectType(
            serialized_name="diagnosticsProfile",
        )
        virtual_machine_profile.eviction_policy = AAZStrType(
            serialized_name="evictionPolicy",
        )
        virtual_machine_profile.extension_profile = AAZObjectType(
            serialized_name="extensionProfile",
        )
        virtual_machine_profile.hardware_profile = AAZObjectType(
            serialized_name="hardwareProfile",
        )
        virtual_machine_profile.license_type = AAZStrType(
            serialized_name="licenseType",
        )
        virtual_machine_profile.network_profile = AAZObjectType(
            serialized_name="networkProfile",
        )
        virtual_machine_profile.os_profile = AAZObjectType(
            serialized_name="osProfile",
        )
        virtual_machine_profile.priority = AAZStrType()
        virtual_machine_profile.scheduled_events_profile = AAZObjectType(
            serialized_name="scheduledEventsProfile",
        )
        virtual_machine_profile.security_posture_reference = AAZObjectType(
            serialized_name="securityPostureReference",
        )
        virtual_machine_profile.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        virtual_machine_profile.service_artifact_reference = AAZObjectType(
            serialized_name="serviceArtifactReference",
        )
        virtual_machine_profile.storage_profile = AAZObjectType(
            serialized_name="storageProfile",
        )
        virtual_machine_profile.time_created = AAZStrType(
            serialized_name="timeCreated",
            flags={"read_only": True},
        )
        virtual_machine_profile.user_data = AAZStrType(
            serialized_name="userData",
        )

        application_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.application_profile
        application_profile.gallery_applications = AAZListType(
            serialized_name="galleryApplications",
        )

        gallery_applications = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.application_profile.gallery_applications
        gallery_applications.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.application_profile.gallery_applications.Element
        _element.configuration_reference = AAZStrType(
            serialized_name="configurationReference",
        )
        _element.enable_automatic_upgrade = AAZBoolType(
            serialized_name="enableAutomaticUpgrade",
        )
        _element.order = AAZIntType()
        _element.package_reference_id = AAZStrType(
            serialized_name="packageReferenceId",
            flags={"required": True},
        )
        _element.tags = AAZStrType()
        _element.treat_failure_as_deployment_failure = AAZBoolType(
            serialized_name="treatFailureAsDeploymentFailure",
        )

        billing_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.billing_profile
        billing_profile.max_price = AAZFloatType(
            serialized_name="maxPrice",
        )

        capacity_reservation = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.capacity_reservation
        capacity_reservation.capacity_reservation_group = AAZObjectType(
            serialized_name="capacityReservationGroup",
        )
        cls._build_schema_sub_resource_read(capacity_reservation.capacity_reservation_group)

        diagnostics_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.diagnostics_profile
        diagnostics_profile.boot_diagnostics = AAZObjectType(
            serialized_name="bootDiagnostics",
        )

        boot_diagnostics = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.diagnostics_profile.boot_diagnostics
        boot_diagnostics.enabled = AAZBoolType()
        boot_diagnostics.storage_uri = AAZStrType(
            serialized_name="storageUri",
        )

        extension_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.extension_profile
        extension_profile.extensions = AAZListType()
        extension_profile.extensions_time_budget = AAZStrType(
            serialized_name="extensionsTimeBudget",
        )

        extensions = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.extension_profile.extensions
        extensions.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.extension_profile.extensions.Element
        _element.id = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        _element.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.extension_profile.extensions.Element.properties
        properties.auto_upgrade_minor_version = AAZBoolType(
            serialized_name="autoUpgradeMinorVersion",
        )
        properties.enable_automatic_upgrade = AAZBoolType(
            serialized_name="enableAutomaticUpgrade",
        )
        properties.force_update_tag = AAZStrType(
            serialized_name="forceUpdateTag",
        )
        properties.protected_settings = AAZFreeFormDictType(
            serialized_name="protectedSettings",
        )
        properties.protected_settings_from_key_vault = AAZObjectType(
            serialized_name="protectedSettingsFromKeyVault",
        )
        properties.provision_after_extensions = AAZListType(
            serialized_name="provisionAfterExtensions",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.publisher = AAZStrType()
        properties.settings = AAZFreeFormDictType()
        properties.suppress_failures = AAZBoolType(
            serialized_name="suppressFailures",
        )
        properties.type = AAZStrType()
        properties.type_handler_version = AAZStrType(
            serialized_name="typeHandlerVersion",
        )

        protected_settings_from_key_vault = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.extension_profile.extensions.Element.properties.protected_settings_from_key_vault
        protected_settings_from_key_vault.secret_url = AAZStrType(
            serialized_name="secretUrl",
            flags={"required": True},
        )
        protected_settings_from_key_vault.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_sub_resource_read(protected_settings_from_key_vault.source_vault)

        provision_after_extensions = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.extension_profile.extensions.Element.properties.provision_after_extensions
        provision_after_extensions.Element = AAZStrType()

        hardware_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.hardware_profile
        hardware_profile.vm_size_properties = AAZObjectType(
            serialized_name="vmSizeProperties",
        )

        vm_size_properties = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.hardware_profile.vm_size_properties
        vm_size_properties.v_cp_us_available = AAZIntType(
            serialized_name="vCPUsAvailable",
        )
        vm_size_properties.v_cp_us_per_core = AAZIntType(
            serialized_name="vCPUsPerCore",
        )

        network_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile
        network_profile.health_probe = AAZObjectType(
            serialized_name="healthProbe",
        )
        cls._build_schema_api_entity_reference_read(network_profile.health_probe)
        network_profile.network_api_version = AAZStrType(
            serialized_name="networkApiVersion",
        )
        network_profile.network_interface_configurations = AAZListType(
            serialized_name="networkInterfaceConfigurations",
        )

        network_interface_configurations = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations
        network_interface_configurations.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties
        properties.auxiliary_mode = AAZStrType(
            serialized_name="auxiliaryMode",
        )
        properties.auxiliary_sku = AAZStrType(
            serialized_name="auxiliarySku",
        )
        properties.delete_option = AAZStrType(
            serialized_name="deleteOption",
        )
        properties.disable_tcp_state_tracking = AAZBoolType(
            serialized_name="disableTcpStateTracking",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.enable_accelerated_networking = AAZBoolType(
            serialized_name="enableAcceleratedNetworking",
        )
        properties.enable_fpga = AAZBoolType(
            serialized_name="enableFpga",
        )
        properties.enable_ip_forwarding = AAZBoolType(
            serialized_name="enableIPForwarding",
        )
        properties.ip_configurations = AAZListType(
            serialized_name="ipConfigurations",
            flags={"required": True},
        )
        properties.network_security_group = AAZObjectType(
            serialized_name="networkSecurityGroup",
        )
        cls._build_schema_sub_resource_read(properties.network_security_group)
        properties.primary = AAZBoolType()

        dns_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings
        dns_settings.dns_servers = AAZListType(
            serialized_name="dnsServers",
        )

        dns_servers = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings.dns_servers
        dns_servers.Element = AAZStrType()

        ip_configurations = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations
        ip_configurations.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties
        properties.application_gateway_backend_address_pools = AAZListType(
            serialized_name="applicationGatewayBackendAddressPools",
        )
        properties.application_security_groups = AAZListType(
            serialized_name="applicationSecurityGroups",
        )
        properties.load_balancer_backend_address_pools = AAZListType(
            serialized_name="loadBalancerBackendAddressPools",
        )
        properties.load_balancer_inbound_nat_pools = AAZListType(
            serialized_name="loadBalancerInboundNatPools",
        )
        properties.primary = AAZBoolType()
        properties.private_ip_address_version = AAZStrType(
            serialized_name="privateIPAddressVersion",
        )
        properties.public_ip_address_configuration = AAZObjectType(
            serialized_name="publicIPAddressConfiguration",
        )
        properties.subnet = AAZObjectType()
        cls._build_schema_api_entity_reference_read(properties.subnet)

        application_gateway_backend_address_pools = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_gateway_backend_address_pools
        application_gateway_backend_address_pools.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(application_gateway_backend_address_pools.Element)

        application_security_groups = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_security_groups
        application_security_groups.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(application_security_groups.Element)

        load_balancer_backend_address_pools = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_backend_address_pools
        load_balancer_backend_address_pools.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancer_backend_address_pools.Element)

        load_balancer_inbound_nat_pools = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_inbound_nat_pools
        load_balancer_inbound_nat_pools.Element = AAZObjectType()
        cls._build_schema_sub_resource_read(load_balancer_inbound_nat_pools.Element)

        public_ip_address_configuration = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration
        public_ip_address_configuration.name = AAZStrType(
            flags={"required": True},
        )
        public_ip_address_configuration.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        public_ip_address_configuration.sku = AAZObjectType()

        properties = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties
        properties.delete_option = AAZStrType(
            serialized_name="deleteOption",
        )
        properties.dns_settings = AAZObjectType(
            serialized_name="dnsSettings",
        )
        properties.idle_timeout_in_minutes = AAZIntType(
            serialized_name="idleTimeoutInMinutes",
        )
        properties.ip_tags = AAZListType(
            serialized_name="ipTags",
        )
        properties.public_ip_address_version = AAZStrType(
            serialized_name="publicIPAddressVersion",
        )
        properties.public_ip_prefix = AAZObjectType(
            serialized_name="publicIPPrefix",
        )
        cls._build_schema_sub_resource_read(properties.public_ip_prefix)

        dns_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.dns_settings
        dns_settings.domain_name_label = AAZStrType(
            serialized_name="domainNameLabel",
            flags={"required": True},
        )
        dns_settings.domain_name_label_scope = AAZStrType(
            serialized_name="domainNameLabelScope",
        )

        ip_tags = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags
        ip_tags.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags.Element
        _element.ip_tag_type = AAZStrType(
            serialized_name="ipTagType",
        )
        _element.tag = AAZStrType()

        sku = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.sku
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        os_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile
        os_profile.admin_password = AAZStrType(
            serialized_name="adminPassword",
            flags={"secret": True},
        )
        os_profile.admin_username = AAZStrType(
            serialized_name="adminUsername",
        )
        os_profile.allow_extension_operations = AAZBoolType(
            serialized_name="allowExtensionOperations",
        )
        os_profile.computer_name_prefix = AAZStrType(
            serialized_name="computerNamePrefix",
        )
        os_profile.custom_data = AAZStrType(
            serialized_name="customData",
        )
        os_profile.linux_configuration = AAZObjectType(
            serialized_name="linuxConfiguration",
        )
        os_profile.require_guest_provision_signal = AAZBoolType(
            serialized_name="requireGuestProvisionSignal",
        )
        os_profile.secrets = AAZListType()
        os_profile.windows_configuration = AAZObjectType(
            serialized_name="windowsConfiguration",
        )

        linux_configuration = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.linux_configuration
        linux_configuration.disable_password_authentication = AAZBoolType(
            serialized_name="disablePasswordAuthentication",
        )
        linux_configuration.enable_vm_agent_platform_updates = AAZBoolType(
            serialized_name="enableVMAgentPlatformUpdates",
        )
        linux_configuration.patch_settings = AAZObjectType(
            serialized_name="patchSettings",
        )
        linux_configuration.provision_vm_agent = AAZBoolType(
            serialized_name="provisionVMAgent",
        )
        linux_configuration.ssh = AAZObjectType()

        patch_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings
        patch_settings.assessment_mode = AAZStrType(
            serialized_name="assessmentMode",
        )
        patch_settings.automatic_by_platform_settings = AAZObjectType(
            serialized_name="automaticByPlatformSettings",
        )
        patch_settings.patch_mode = AAZStrType(
            serialized_name="patchMode",
        )

        automatic_by_platform_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
        automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
            serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
        )
        automatic_by_platform_settings.reboot_setting = AAZStrType(
            serialized_name="rebootSetting",
        )

        ssh = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.linux_configuration.ssh
        ssh.public_keys = AAZListType(
            serialized_name="publicKeys",
        )

        public_keys = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys
        public_keys.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys.Element
        _element.key_data = AAZStrType(
            serialized_name="keyData",
        )
        _element.path = AAZStrType()

        secrets = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.secrets
        secrets.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.secrets.Element
        _element.source_vault = AAZObjectType(
            serialized_name="sourceVault",
        )
        cls._build_schema_sub_resource_read(_element.source_vault)
        _element.vault_certificates = AAZListType(
            serialized_name="vaultCertificates",
        )

        vault_certificates = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates
        vault_certificates.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates.Element
        _element.certificate_store = AAZStrType(
            serialized_name="certificateStore",
        )
        _element.certificate_url = AAZStrType(
            serialized_name="certificateUrl",
        )

        windows_configuration = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration
        windows_configuration.additional_unattend_content = AAZListType(
            serialized_name="additionalUnattendContent",
        )
        windows_configuration.enable_automatic_updates = AAZBoolType(
            serialized_name="enableAutomaticUpdates",
        )
        windows_configuration.enable_vm_agent_platform_updates = AAZBoolType(
            serialized_name="enableVMAgentPlatformUpdates",
            flags={"read_only": True},
        )
        windows_configuration.patch_settings = AAZObjectType(
            serialized_name="patchSettings",
        )
        windows_configuration.provision_vm_agent = AAZBoolType(
            serialized_name="provisionVMAgent",
        )
        windows_configuration.time_zone = AAZStrType(
            serialized_name="timeZone",
        )
        windows_configuration.win_rm = AAZObjectType(
            serialized_name="winRM",
        )

        additional_unattend_content = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content
        additional_unattend_content.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content.Element
        _element.component_name = AAZStrType(
            serialized_name="componentName",
        )
        _element.content = AAZStrType()
        _element.pass_name = AAZStrType(
            serialized_name="passName",
        )
        _element.setting_name = AAZStrType(
            serialized_name="settingName",
        )

        patch_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings
        patch_settings.assessment_mode = AAZStrType(
            serialized_name="assessmentMode",
        )
        patch_settings.automatic_by_platform_settings = AAZObjectType(
            serialized_name="automaticByPlatformSettings",
        )
        patch_settings.enable_hotpatching = AAZBoolType(
            serialized_name="enableHotpatching",
        )
        patch_settings.patch_mode = AAZStrType(
            serialized_name="patchMode",
        )

        automatic_by_platform_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
        automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
            serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
        )
        automatic_by_platform_settings.reboot_setting = AAZStrType(
            serialized_name="rebootSetting",
        )

        win_rm = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm
        win_rm.listeners = AAZListType()

        listeners = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners
        listeners.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners.Element
        _element.certificate_url = AAZStrType(
            serialized_name="certificateUrl",
        )
        _element.protocol = AAZStrType()

        scheduled_events_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.scheduled_events_profile
        scheduled_events_profile.os_image_notification_profile = AAZObjectType(
            serialized_name="osImageNotificationProfile",
        )
        scheduled_events_profile.terminate_notification_profile = AAZObjectType(
            serialized_name="terminateNotificationProfile",
        )

        os_image_notification_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.scheduled_events_profile.os_image_notification_profile
        os_image_notification_profile.enable = AAZBoolType()
        os_image_notification_profile.not_before_timeout = AAZStrType(
            serialized_name="notBeforeTimeout",
        )

        terminate_notification_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.scheduled_events_profile.terminate_notification_profile
        terminate_notification_profile.enable = AAZBoolType()
        terminate_notification_profile.not_before_timeout = AAZStrType(
            serialized_name="notBeforeTimeout",
        )

        security_posture_reference = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.security_posture_reference
        security_posture_reference.exclude_extensions = AAZListType(
            serialized_name="excludeExtensions",
        )
        security_posture_reference.id = AAZStrType(
            flags={"required": True},
        )
        security_posture_reference.is_overridable = AAZBoolType(
            serialized_name="isOverridable",
        )

        exclude_extensions = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.security_posture_reference.exclude_extensions
        exclude_extensions.Element = AAZStrType()

        security_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.security_profile
        security_profile.encryption_at_host = AAZBoolType(
            serialized_name="encryptionAtHost",
        )
        security_profile.encryption_identity = AAZObjectType(
            serialized_name="encryptionIdentity",
        )
        security_profile.proxy_agent_settings = AAZObjectType(
            serialized_name="proxyAgentSettings",
        )
        security_profile.security_type = AAZStrType(
            serialized_name="securityType",
        )
        security_profile.uefi_settings = AAZObjectType(
            serialized_name="uefiSettings",
        )

        encryption_identity = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.security_profile.encryption_identity
        encryption_identity.user_assigned_identity_resource_id = AAZStrType(
            serialized_name="userAssignedIdentityResourceId",
        )

        proxy_agent_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.security_profile.proxy_agent_settings
        proxy_agent_settings.enabled = AAZBoolType()
        proxy_agent_settings.imds = AAZObjectType()
        cls._build_schema_host_endpoint_settings_read(proxy_agent_settings.imds)
        proxy_agent_settings.key_incarnation_id = AAZIntType(
            serialized_name="keyIncarnationId",
        )
        proxy_agent_settings.mode = AAZStrType()
        proxy_agent_settings.wire_server = AAZObjectType(
            serialized_name="wireServer",
        )
        cls._build_schema_host_endpoint_settings_read(proxy_agent_settings.wire_server)

        uefi_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.security_profile.uefi_settings
        uefi_settings.secure_boot_enabled = AAZBoolType(
            serialized_name="secureBootEnabled",
        )
        uefi_settings.v_tpm_enabled = AAZBoolType(
            serialized_name="vTpmEnabled",
        )

        service_artifact_reference = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.service_artifact_reference
        service_artifact_reference.id = AAZStrType()

        storage_profile = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile
        storage_profile.data_disks = AAZListType(
            serialized_name="dataDisks",
        )
        storage_profile.disk_controller_type = AAZStrType(
            serialized_name="diskControllerType",
        )
        storage_profile.image_reference = AAZObjectType(
            serialized_name="imageReference",
        )
        storage_profile.os_disk = AAZObjectType(
            serialized_name="osDisk",
        )

        data_disks = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile.data_disks
        data_disks.Element = AAZObjectType()

        _element = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile.data_disks.Element
        _element.caching = AAZStrType()
        _element.create_option = AAZStrType(
            serialized_name="createOption",
            flags={"required": True},
        )
        _element.delete_option = AAZStrType(
            serialized_name="deleteOption",
        )
        _element.disk_iops_read_write = AAZIntType(
            serialized_name="diskIOPSReadWrite",
        )
        _element.disk_m_bps_read_write = AAZIntType(
            serialized_name="diskMBpsReadWrite",
        )
        _element.disk_size_gb = AAZIntType(
            serialized_name="diskSizeGB",
        )
        _element.lun = AAZIntType(
            flags={"required": True},
        )
        _element.managed_disk = AAZObjectType(
            serialized_name="managedDisk",
        )
        cls._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(_element.managed_disk)
        _element.name = AAZStrType()
        _element.write_accelerator_enabled = AAZBoolType(
            serialized_name="writeAcceleratorEnabled",
        )

        image_reference = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile.image_reference
        image_reference.community_gallery_image_id = AAZStrType(
            serialized_name="communityGalleryImageId",
        )
        image_reference.exact_version = AAZStrType(
            serialized_name="exactVersion",
            flags={"read_only": True},
        )
        image_reference.id = AAZStrType()
        image_reference.offer = AAZStrType()
        image_reference.publisher = AAZStrType()
        image_reference.shared_gallery_image_id = AAZStrType(
            serialized_name="sharedGalleryImageId",
        )
        image_reference.sku = AAZStrType()
        image_reference.version = AAZStrType()

        os_disk = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile.os_disk
        os_disk.caching = AAZStrType()
        os_disk.create_option = AAZStrType(
            serialized_name="createOption",
            flags={"required": True},
        )
        os_disk.delete_option = AAZStrType(
            serialized_name="deleteOption",
        )
        os_disk.diff_disk_settings = AAZObjectType(
            serialized_name="diffDiskSettings",
        )
        os_disk.disk_size_gb = AAZIntType(
            serialized_name="diskSizeGB",
        )
        os_disk.image = AAZObjectType()
        os_disk.managed_disk = AAZObjectType(
            serialized_name="managedDisk",
        )
        cls._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(os_disk.managed_disk)
        os_disk.name = AAZStrType()
        os_disk.os_type = AAZStrType(
            serialized_name="osType",
        )
        os_disk.vhd_containers = AAZListType(
            serialized_name="vhdContainers",
        )
        os_disk.write_accelerator_enabled = AAZBoolType(
            serialized_name="writeAcceleratorEnabled",
        )

        diff_disk_settings = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile.os_disk.diff_disk_settings
        diff_disk_settings.option = AAZStrType()
        diff_disk_settings.placement = AAZStrType()

        image = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile.os_disk.image
        image.uri = AAZStrType()

        vhd_containers = _schema_virtual_machine_scale_set_read.properties.virtual_machine_profile.storage_profile.os_disk.vhd_containers
        vhd_containers.Element = AAZStrType()

        sku = _schema_virtual_machine_scale_set_read.sku
        sku.capacity = AAZIntType()
        sku.name = AAZStrType()
        sku.tier = AAZStrType()

        tags = _schema_virtual_machine_scale_set_read.tags
        tags.Element = AAZStrType()

        zones = _schema_virtual_machine_scale_set_read.zones
        zones.Element = AAZStrType()

        _schema.etag = cls._schema_virtual_machine_scale_set_read.etag
        _schema.extended_location = cls._schema_virtual_machine_scale_set_read.extended_location
        _schema.id = cls._schema_virtual_machine_scale_set_read.id
        _schema.identity = cls._schema_virtual_machine_scale_set_read.identity
        _schema.location = cls._schema_virtual_machine_scale_set_read.location
        _schema.name = cls._schema_virtual_machine_scale_set_read.name
        _schema.plan = cls._schema_virtual_machine_scale_set_read.plan
        _schema.properties = cls._schema_virtual_machine_scale_set_read.properties
        _schema.sku = cls._schema_virtual_machine_scale_set_read.sku
        _schema.tags = cls._schema_virtual_machine_scale_set_read.tags
        _schema.type = cls._schema_virtual_machine_scale_set_read.type
        _schema.zones = cls._schema_virtual_machine_scale_set_read.zones


__all__ = ["Wait"]
