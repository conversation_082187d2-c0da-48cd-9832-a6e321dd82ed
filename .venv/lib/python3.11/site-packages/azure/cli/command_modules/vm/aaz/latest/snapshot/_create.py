# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "snapshot create",
)
class Create(AAZCommand):
    """Create a snapshot.
    """

    _aaz_info = {
        "version": "2025-01-02",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/snapshots/{}", "2025-01-02"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.snapshot_name = AAZStrArg(
            options=["-n", "--name", "--snapshot-name"],
            help="The name of the snapshot that is being created. The name can't be changed after the snapshot is created. Supported characters for the name are a-z, A-Z, 0-9, _ and -. The max name length is 80 characters.",
            required=True,
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.completion_percent = AAZFloatArg(
            options=["--completion-percent"],
            arg_group="Properties",
            help="Percentage complete for the background copy when a resource is created via the CopyStart operation.",
        )
        _args_schema.copy_completion_error = AAZObjectArg(
            options=["--copy-completion-error"],
            arg_group="Properties",
            help="Indicates the error details if the background copy of a resource created via the CopyStart operation fails.",
        )
        _args_schema.creation_data = AAZObjectArg(
            options=["--creation-data"],
            arg_group="Properties",
            help="Disk source information. CreationData information cannot be changed after the disk has been created.",
        )
        _args_schema.data_access_auth_mode = AAZStrArg(
            options=["--data-access-auth-mode"],
            arg_group="Properties",
            help="Additional authentication requirements when exporting or uploading to a disk or snapshot.",
            enum={"AzureActiveDirectory": "AzureActiveDirectory", "None": "None"},
        )
        _args_schema.disk_access_id = AAZStrArg(
            options=["--disk-access", "--disk-access-id"],
            arg_group="Properties",
            help="ARM id of the DiskAccess resource for using private endpoints on disks.",
        )
        _args_schema.disk_size_gb = AAZIntArg(
            options=["--disk-size-gb"],
            arg_group="Properties",
            help="If creationData.createOption is Empty, this field is mandatory and it indicates the size of the disk to create. If this field is present for updates or creation with other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a running VM, and can only increase the disk's size.",
        )
        _args_schema.encryption = AAZObjectArg(
            options=["--encryption"],
            arg_group="Properties",
            help="Encryption property can be used to encrypt data at rest with customer managed keys or platform managed keys.",
        )
        _args_schema.encryption_settings_collection = AAZObjectArg(
            options=["--encryption-settings-collection"],
            arg_group="Properties",
            help="Encryption settings collection used be Azure Disk Encryption, can contain multiple encryption settings per disk or snapshot.",
        )
        _args_schema.hyper_v_generation = AAZStrArg(
            options=["--hyper-v-generation"],
            arg_group="Properties",
            help="The hypervisor generation of the Virtual Machine. Applicable to OS disks only.",
            enum={"V1": "V1", "V2": "V2"},
        )
        _args_schema.incremental = AAZBoolArg(
            options=["--incremental"],
            arg_group="Properties",
            help="Whether a snapshot is incremental. Incremental snapshots on the same disk occupy less space than full snapshots and can be diffed.",
        )
        _args_schema.network_access_policy = AAZStrArg(
            options=["--network-access-policy"],
            arg_group="Properties",
            help="Policy for accessing the disk via network.",
            enum={"AllowAll": "AllowAll", "AllowPrivate": "AllowPrivate", "DenyAll": "DenyAll"},
        )
        _args_schema.os_type = AAZStrArg(
            options=["--os-type"],
            arg_group="Properties",
            help="The Operating System type.",
            enum={"Linux": "Linux", "Windows": "Windows"},
        )
        _args_schema.public_network_access = AAZStrArg(
            options=["--public-network-access"],
            arg_group="Properties",
            help="Policy for controlling export on the disk.",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _args_schema.purchase_plan = AAZObjectArg(
            options=["--purchase-plan"],
            arg_group="Properties",
            help="Purchase plan information for the image from which the source disk for the snapshot was originally created.",
        )
        _args_schema.security_profile = AAZObjectArg(
            options=["--security-profile"],
            arg_group="Properties",
            help="Contains the security related information for the resource.",
        )
        _args_schema.supported_capabilities = AAZObjectArg(
            options=["--supported-capabilities"],
            arg_group="Properties",
            help="List of supported capabilities for the image from which the source disk from the snapshot was originally created.",
        )
        _args_schema.supports_hibernation = AAZBoolArg(
            options=["--supports-hibernation"],
            arg_group="Properties",
            help="Indicates the OS on a snapshot supports hibernation.",
        )

        copy_completion_error = cls._args_schema.copy_completion_error
        copy_completion_error.error_code = AAZStrArg(
            options=["error-code"],
            help="Indicates the error code if the background copy of a resource created via the CopyStart operation fails.",
            required=True,
            enum={"CopySourceNotFound": "CopySourceNotFound"},
        )
        copy_completion_error.error_message = AAZStrArg(
            options=["error-message"],
            help="Indicates the error message if the background copy of a resource created via the CopyStart operation fails.",
            required=True,
        )

        creation_data = cls._args_schema.creation_data
        creation_data.create_option = AAZStrArg(
            options=["create-option"],
            help="This enumerates the possible sources of a disk's creation.",
            required=True,
            enum={"Attach": "Attach", "Copy": "Copy", "CopyFromSanSnapshot": "CopyFromSanSnapshot", "CopyStart": "CopyStart", "Empty": "Empty", "FromImage": "FromImage", "Import": "Import", "ImportSecure": "ImportSecure", "Restore": "Restore", "Upload": "Upload", "UploadPreparedSecure": "UploadPreparedSecure"},
        )
        creation_data.elastic_san_resource_id = AAZStrArg(
            options=["elastic-san-resource-id"],
            help="Required if createOption is CopyFromSanSnapshot. This is the ARM id of the source elastic san volume snapshot.",
        )
        creation_data.gallery_image_reference = AAZObjectArg(
            options=["gallery-image-reference"],
            help="Required if creating from a Gallery Image. The id/sharedGalleryImageId/communityGalleryImageId of the ImageDiskReference will be the ARM id of the shared galley image version from which to create a disk.",
        )
        cls._build_args_image_disk_reference_create(creation_data.gallery_image_reference)
        creation_data.image_reference = AAZObjectArg(
            options=["image-reference"],
            help="Disk source information for PIR or user images.",
        )
        cls._build_args_image_disk_reference_create(creation_data.image_reference)
        creation_data.instant_access_duration_minutes = AAZIntArg(
            options=["instant-access-duration-minutes"],
            help="For snapshots created from Premium SSD v2 or Ultra disk, this property determines the time in minutes the snapshot is retained for instant access to enable faster restore.",
            fmt=AAZIntArgFormat(
                minimum=1,
            ),
        )
        creation_data.logical_sector_size = AAZIntArg(
            options=["logical-sector-size"],
            help="Logical sector size in bytes for Ultra disks. Supported values are 512 ad 4096. 4096 is the default.",
        )
        creation_data.performance_plus = AAZBoolArg(
            options=["performance-plus"],
            help="Set this flag to true to get a boost on the performance target of the disk deployed, see here on the respective performance target. This flag can only be set on disk creation time and cannot be disabled after enabled.",
        )
        creation_data.provisioned_bandwidth_copy_speed = AAZStrArg(
            options=["provisioned-bandwidth-copy-speed"],
            help="If this field is set on a snapshot and createOption is CopyStart, the snapshot will be copied at a quicker speed.",
            enum={"Enhanced": "Enhanced", "None": "None"},
        )
        creation_data.security_data_uri = AAZStrArg(
            options=["security-data-uri"],
            help="If createOption is ImportSecure, this is the URI of a blob to be imported into VM guest state.",
        )
        creation_data.security_metadata_uri = AAZStrArg(
            options=["security-metadata-uri"],
            help="If createOption is ImportSecure, this is the URI of a blob to be imported into VM metadata for Confidential VM.",
        )
        creation_data.source_resource_id = AAZStrArg(
            options=["source-resource-id"],
            help="If createOption is Copy, this is the ARM id of the source snapshot or disk.",
        )
        creation_data.source_uri = AAZStrArg(
            options=["source-uri"],
            help="If createOption is Import, this is the URI of a blob to be imported into a managed disk.",
        )
        creation_data.storage_account_id = AAZStrArg(
            options=["storage-account-id"],
            help="Required if createOption is Import. The Azure Resource Manager identifier of the storage account containing the blob to import as a disk.",
        )
        creation_data.upload_size_bytes = AAZIntArg(
            options=["upload-size-bytes"],
            help="If createOption is Upload, this is the size of the contents of the upload including the VHD footer. This value should be between ******** (20 MiB + 512 bytes for the VHD footer) and ************** bytes (32 TiB + 512 bytes for the VHD footer).",
        )

        encryption = cls._args_schema.encryption
        encryption.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="ResourceId of the disk encryption set to use for enabling encryption at rest.",
        )
        encryption.type = AAZStrArg(
            options=["type"],
            help="The type of key used to encrypt the data of the disk.",
            enum={"EncryptionAtRestWithCustomerKey": "EncryptionAtRestWithCustomerKey", "EncryptionAtRestWithPlatformAndCustomerKeys": "EncryptionAtRestWithPlatformAndCustomerKeys", "EncryptionAtRestWithPlatformKey": "EncryptionAtRestWithPlatformKey"},
        )

        encryption_settings_collection = cls._args_schema.encryption_settings_collection
        encryption_settings_collection.enabled = AAZBoolArg(
            options=["enabled"],
            help="Set this flag to true and provide DiskEncryptionKey and optional KeyEncryptionKey to enable encryption. Set this flag to false and remove DiskEncryptionKey and KeyEncryptionKey to disable encryption. If EncryptionSettings is null in the request object, the existing settings remain unchanged.",
            required=True,
        )
        encryption_settings_collection.encryption_settings = AAZListArg(
            options=["encryption-settings"],
            help="A collection of encryption settings, one for each disk volume.",
        )
        encryption_settings_collection.encryption_settings_version = AAZStrArg(
            options=["encryption-settings-version"],
            help="Describes what type of encryption is used for the disks. Once this field is set, it cannot be overwritten. '1.0' corresponds to Azure Disk Encryption with AAD app.'1.1' corresponds to Azure Disk Encryption.",
        )

        encryption_settings = cls._args_schema.encryption_settings_collection.encryption_settings
        encryption_settings.Element = AAZObjectArg()

        _element = cls._args_schema.encryption_settings_collection.encryption_settings.Element
        _element.disk_encryption_key = AAZObjectArg(
            options=["disk-encryption-key"],
            help="Key Vault Secret Url and vault id of the disk encryption key",
        )
        _element.key_encryption_key = AAZObjectArg(
            options=["key-encryption-key"],
            help="Key Vault Key Url and vault id of the key encryption key. KeyEncryptionKey is optional and when provided is used to unwrap the disk encryption key.",
        )

        disk_encryption_key = cls._args_schema.encryption_settings_collection.encryption_settings.Element.disk_encryption_key
        disk_encryption_key.secret_url = AAZStrArg(
            options=["secret-url"],
            help="Url pointing to a key or secret in KeyVault",
            required=True,
        )
        disk_encryption_key.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="Resource id of the KeyVault containing the key or secret",
            required=True,
        )
        cls._build_args_source_vault_create(disk_encryption_key.source_vault)

        key_encryption_key = cls._args_schema.encryption_settings_collection.encryption_settings.Element.key_encryption_key
        key_encryption_key.key_url = AAZStrArg(
            options=["key-url"],
            help="Url pointing to a key or secret in KeyVault",
            required=True,
        )
        key_encryption_key.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="Resource id of the KeyVault containing the key or secret",
            required=True,
        )
        cls._build_args_source_vault_create(key_encryption_key.source_vault)

        purchase_plan = cls._args_schema.purchase_plan
        purchase_plan.name = AAZStrArg(
            options=["name"],
            help="The plan ID.",
            required=True,
        )
        purchase_plan.product = AAZStrArg(
            options=["product"],
            help="Specifies the product of the image from the marketplace. This is the same value as Offer under the imageReference element.",
            required=True,
        )
        purchase_plan.promotion_code = AAZStrArg(
            options=["promotion-code"],
            help="The Offer Promotion Code.",
        )
        purchase_plan.publisher = AAZStrArg(
            options=["publisher"],
            help="The publisher ID.",
            required=True,
        )

        security_profile = cls._args_schema.security_profile
        security_profile.secure_vm_disk_encryption_set_id = AAZStrArg(
            options=["secure-vm-disk-encryption-set-id"],
            help="ResourceId of the disk encryption set associated to Confidential VM supported disk encrypted with customer managed key",
        )
        security_profile.security_type = AAZStrArg(
            options=["security-type"],
            help="Specifies the SecurityType of the VM. Applicable for OS disks only.",
            enum={"ConfidentialVM_DiskEncryptedWithCustomerKey": "ConfidentialVM_DiskEncryptedWithCustomerKey", "ConfidentialVM_DiskEncryptedWithPlatformKey": "ConfidentialVM_DiskEncryptedWithPlatformKey", "ConfidentialVM_NonPersistedTPM": "ConfidentialVM_NonPersistedTPM", "ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey": "ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey", "TrustedLaunch": "TrustedLaunch"},
        )

        supported_capabilities = cls._args_schema.supported_capabilities
        supported_capabilities.accelerated_network = AAZBoolArg(
            options=["accelerated-network"],
            help="True if the image from which the OS disk is created supports accelerated networking.",
        )
        supported_capabilities.architecture = AAZStrArg(
            options=["architecture"],
            help="CPU architecture supported by an OS disk.",
            enum={"Arm64": "Arm64", "x64": "x64"},
        )
        supported_capabilities.disk_controller_types = AAZStrArg(
            options=["disk-controller-types"],
            help="The disk controllers that an OS disk supports. If set it can be SCSI or SCSI, NVME or NVME, SCSI.",
        )
        supported_capabilities.supported_security_option = AAZStrArg(
            options=["supported-security-option"],
            help="Refers to the security capability of the disk supported to create a Trusted launch or Confidential VM",
            enum={"TrustedLaunchAndConfidentialVMSupported": "TrustedLaunchAndConfidentialVMSupported", "TrustedLaunchSupported": "TrustedLaunchSupported"},
        )

        # define Arg Group "Snapshot"

        _args_schema = cls._args_schema
        _args_schema.extended_location = AAZObjectArg(
            options=["--extended-location"],
            arg_group="Snapshot",
            help="The extended location where the snapshot will be created. Extended location cannot be changed.",
        )
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Snapshot",
            help="Resource location",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.sku = AAZObjectArg(
            options=["--sku"],
            arg_group="Snapshot",
            help="The snapshots sku name. Can be Standard_LRS, Premium_LRS, or Standard_ZRS. This is an optional parameter for incremental snapshot and the default behavior is the SKU will be set to the same sku as the previous snapshot",
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Snapshot",
            help="Resource tags",
        )

        extended_location = cls._args_schema.extended_location
        extended_location.name = AAZStrArg(
            options=["name"],
            help="The name of the extended location.",
        )
        extended_location.type = AAZStrArg(
            options=["type"],
            help="The type of the extended location.",
            enum={"EdgeZone": "EdgeZone"},
        )

        sku = cls._args_schema.sku
        sku.name = AAZStrArg(
            options=["name"],
            help="The sku name.",
            enum={"Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()
        return cls._args_schema

    _args_image_disk_reference_create = None

    @classmethod
    def _build_args_image_disk_reference_create(cls, _schema):
        if cls._args_image_disk_reference_create is not None:
            _schema.community_gallery_image_id = cls._args_image_disk_reference_create.community_gallery_image_id
            _schema.id = cls._args_image_disk_reference_create.id
            _schema.lun = cls._args_image_disk_reference_create.lun
            _schema.shared_gallery_image_id = cls._args_image_disk_reference_create.shared_gallery_image_id
            return

        cls._args_image_disk_reference_create = AAZObjectArg()

        image_disk_reference_create = cls._args_image_disk_reference_create
        image_disk_reference_create.community_gallery_image_id = AAZStrArg(
            options=["community-gallery-image-id"],
            help="A relative uri containing a community Azure Compute Gallery image reference.",
        )
        image_disk_reference_create.id = AAZStrArg(
            options=["id"],
            help="A relative uri containing either a Platform Image Repository, user image, or Azure Compute Gallery image reference.",
        )
        image_disk_reference_create.lun = AAZIntArg(
            options=["lun"],
            help="If the disk is created from an image's data disk, this is an index that indicates which of the data disks in the image to use. For OS disks, this field is null.",
        )
        image_disk_reference_create.shared_gallery_image_id = AAZStrArg(
            options=["shared-gallery-image-id"],
            help="A relative uri containing a direct shared Azure Compute Gallery image reference.",
        )

        _schema.community_gallery_image_id = cls._args_image_disk_reference_create.community_gallery_image_id
        _schema.id = cls._args_image_disk_reference_create.id
        _schema.lun = cls._args_image_disk_reference_create.lun
        _schema.shared_gallery_image_id = cls._args_image_disk_reference_create.shared_gallery_image_id

    _args_source_vault_create = None

    @classmethod
    def _build_args_source_vault_create(cls, _schema):
        if cls._args_source_vault_create is not None:
            _schema.id = cls._args_source_vault_create.id
            return

        cls._args_source_vault_create = AAZObjectArg()

        source_vault_create = cls._args_source_vault_create
        source_vault_create.id = AAZStrArg(
            options=["id"],
            help="Resource Id",
        )

        _schema.id = cls._args_source_vault_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.SnapshotsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class SnapshotsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/snapshots/{snapshotName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "snapshotName", self.ctx.args.snapshot_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-02",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("extendedLocation", AAZObjectType, ".extended_location")
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("sku", AAZObjectType, ".sku")
            _builder.set_prop("tags", AAZDictType, ".tags")

            extended_location = _builder.get(".extendedLocation")
            if extended_location is not None:
                extended_location.set_prop("name", AAZStrType, ".name")
                extended_location.set_prop("type", AAZStrType, ".type")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("completionPercent", AAZFloatType, ".completion_percent")
                properties.set_prop("copyCompletionError", AAZObjectType, ".copy_completion_error")
                properties.set_prop("creationData", AAZObjectType, ".creation_data", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("dataAccessAuthMode", AAZStrType, ".data_access_auth_mode")
                properties.set_prop("diskAccessId", AAZStrType, ".disk_access_id")
                properties.set_prop("diskSizeGB", AAZIntType, ".disk_size_gb")
                properties.set_prop("encryption", AAZObjectType, ".encryption")
                properties.set_prop("encryptionSettingsCollection", AAZObjectType, ".encryption_settings_collection")
                properties.set_prop("hyperVGeneration", AAZStrType, ".hyper_v_generation")
                properties.set_prop("incremental", AAZBoolType, ".incremental")
                properties.set_prop("networkAccessPolicy", AAZStrType, ".network_access_policy")
                properties.set_prop("osType", AAZStrType, ".os_type")
                properties.set_prop("publicNetworkAccess", AAZStrType, ".public_network_access")
                properties.set_prop("purchasePlan", AAZObjectType, ".purchase_plan")
                properties.set_prop("securityProfile", AAZObjectType, ".security_profile")
                properties.set_prop("supportedCapabilities", AAZObjectType, ".supported_capabilities")
                properties.set_prop("supportsHibernation", AAZBoolType, ".supports_hibernation")

            copy_completion_error = _builder.get(".properties.copyCompletionError")
            if copy_completion_error is not None:
                copy_completion_error.set_prop("errorCode", AAZStrType, ".error_code", typ_kwargs={"flags": {"required": True}})
                copy_completion_error.set_prop("errorMessage", AAZStrType, ".error_message", typ_kwargs={"flags": {"required": True}})

            creation_data = _builder.get(".properties.creationData")
            if creation_data is not None:
                creation_data.set_prop("createOption", AAZStrType, ".create_option", typ_kwargs={"flags": {"required": True}})
                creation_data.set_prop("elasticSanResourceId", AAZStrType, ".elastic_san_resource_id")
                _CreateHelper._build_schema_image_disk_reference_create(creation_data.set_prop("galleryImageReference", AAZObjectType, ".gallery_image_reference"))
                _CreateHelper._build_schema_image_disk_reference_create(creation_data.set_prop("imageReference", AAZObjectType, ".image_reference"))
                creation_data.set_prop("instantAccessDurationMinutes", AAZIntType, ".instant_access_duration_minutes")
                creation_data.set_prop("logicalSectorSize", AAZIntType, ".logical_sector_size")
                creation_data.set_prop("performancePlus", AAZBoolType, ".performance_plus")
                creation_data.set_prop("provisionedBandwidthCopySpeed", AAZStrType, ".provisioned_bandwidth_copy_speed")
                creation_data.set_prop("securityDataUri", AAZStrType, ".security_data_uri")
                creation_data.set_prop("securityMetadataUri", AAZStrType, ".security_metadata_uri")
                creation_data.set_prop("sourceResourceId", AAZStrType, ".source_resource_id")
                creation_data.set_prop("sourceUri", AAZStrType, ".source_uri")
                creation_data.set_prop("storageAccountId", AAZStrType, ".storage_account_id")
                creation_data.set_prop("uploadSizeBytes", AAZIntType, ".upload_size_bytes")

            encryption = _builder.get(".properties.encryption")
            if encryption is not None:
                encryption.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
                encryption.set_prop("type", AAZStrType, ".type")

            encryption_settings_collection = _builder.get(".properties.encryptionSettingsCollection")
            if encryption_settings_collection is not None:
                encryption_settings_collection.set_prop("enabled", AAZBoolType, ".enabled", typ_kwargs={"flags": {"required": True}})
                encryption_settings_collection.set_prop("encryptionSettings", AAZListType, ".encryption_settings")
                encryption_settings_collection.set_prop("encryptionSettingsVersion", AAZStrType, ".encryption_settings_version")

            encryption_settings = _builder.get(".properties.encryptionSettingsCollection.encryptionSettings")
            if encryption_settings is not None:
                encryption_settings.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.encryptionSettingsCollection.encryptionSettings[]")
            if _elements is not None:
                _elements.set_prop("diskEncryptionKey", AAZObjectType, ".disk_encryption_key")
                _elements.set_prop("keyEncryptionKey", AAZObjectType, ".key_encryption_key")

            disk_encryption_key = _builder.get(".properties.encryptionSettingsCollection.encryptionSettings[].diskEncryptionKey")
            if disk_encryption_key is not None:
                disk_encryption_key.set_prop("secretUrl", AAZStrType, ".secret_url", typ_kwargs={"flags": {"required": True}})
                _CreateHelper._build_schema_source_vault_create(disk_encryption_key.set_prop("sourceVault", AAZObjectType, ".source_vault", typ_kwargs={"flags": {"required": True}}))

            key_encryption_key = _builder.get(".properties.encryptionSettingsCollection.encryptionSettings[].keyEncryptionKey")
            if key_encryption_key is not None:
                key_encryption_key.set_prop("keyUrl", AAZStrType, ".key_url", typ_kwargs={"flags": {"required": True}})
                _CreateHelper._build_schema_source_vault_create(key_encryption_key.set_prop("sourceVault", AAZObjectType, ".source_vault", typ_kwargs={"flags": {"required": True}}))

            purchase_plan = _builder.get(".properties.purchasePlan")
            if purchase_plan is not None:
                purchase_plan.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                purchase_plan.set_prop("product", AAZStrType, ".product", typ_kwargs={"flags": {"required": True}})
                purchase_plan.set_prop("promotionCode", AAZStrType, ".promotion_code")
                purchase_plan.set_prop("publisher", AAZStrType, ".publisher", typ_kwargs={"flags": {"required": True}})

            security_profile = _builder.get(".properties.securityProfile")
            if security_profile is not None:
                security_profile.set_prop("secureVMDiskEncryptionSetId", AAZStrType, ".secure_vm_disk_encryption_set_id")
                security_profile.set_prop("securityType", AAZStrType, ".security_type")

            supported_capabilities = _builder.get(".properties.supportedCapabilities")
            if supported_capabilities is not None:
                supported_capabilities.set_prop("acceleratedNetwork", AAZBoolType, ".accelerated_network")
                supported_capabilities.set_prop("architecture", AAZStrType, ".architecture")
                supported_capabilities.set_prop("diskControllerTypes", AAZStrType, ".disk_controller_types")
                supported_capabilities.set_prop("supportedSecurityOption", AAZStrType, ".supported_security_option")

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("name", AAZStrType, ".name")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _CreateHelper._build_schema_snapshot_read(cls._schema_on_200)

            return cls._schema_on_200


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_image_disk_reference_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("communityGalleryImageId", AAZStrType, ".community_gallery_image_id")
        _builder.set_prop("id", AAZStrType, ".id")
        _builder.set_prop("lun", AAZIntType, ".lun")
        _builder.set_prop("sharedGalleryImageId", AAZStrType, ".shared_gallery_image_id")

    @classmethod
    def _build_schema_source_vault_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_image_disk_reference_read = None

    @classmethod
    def _build_schema_image_disk_reference_read(cls, _schema):
        if cls._schema_image_disk_reference_read is not None:
            _schema.community_gallery_image_id = cls._schema_image_disk_reference_read.community_gallery_image_id
            _schema.id = cls._schema_image_disk_reference_read.id
            _schema.lun = cls._schema_image_disk_reference_read.lun
            _schema.shared_gallery_image_id = cls._schema_image_disk_reference_read.shared_gallery_image_id
            return

        cls._schema_image_disk_reference_read = _schema_image_disk_reference_read = AAZObjectType()

        image_disk_reference_read = _schema_image_disk_reference_read
        image_disk_reference_read.community_gallery_image_id = AAZStrType(
            serialized_name="communityGalleryImageId",
        )
        image_disk_reference_read.id = AAZStrType()
        image_disk_reference_read.lun = AAZIntType()
        image_disk_reference_read.shared_gallery_image_id = AAZStrType(
            serialized_name="sharedGalleryImageId",
        )

        _schema.community_gallery_image_id = cls._schema_image_disk_reference_read.community_gallery_image_id
        _schema.id = cls._schema_image_disk_reference_read.id
        _schema.lun = cls._schema_image_disk_reference_read.lun
        _schema.shared_gallery_image_id = cls._schema_image_disk_reference_read.shared_gallery_image_id

    _schema_snapshot_read = None

    @classmethod
    def _build_schema_snapshot_read(cls, _schema):
        if cls._schema_snapshot_read is not None:
            _schema.extended_location = cls._schema_snapshot_read.extended_location
            _schema.id = cls._schema_snapshot_read.id
            _schema.location = cls._schema_snapshot_read.location
            _schema.managed_by = cls._schema_snapshot_read.managed_by
            _schema.name = cls._schema_snapshot_read.name
            _schema.properties = cls._schema_snapshot_read.properties
            _schema.sku = cls._schema_snapshot_read.sku
            _schema.system_data = cls._schema_snapshot_read.system_data
            _schema.tags = cls._schema_snapshot_read.tags
            _schema.type = cls._schema_snapshot_read.type
            return

        cls._schema_snapshot_read = _schema_snapshot_read = AAZObjectType()

        snapshot_read = _schema_snapshot_read
        snapshot_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        snapshot_read.id = AAZStrType(
            flags={"read_only": True},
        )
        snapshot_read.location = AAZStrType(
            flags={"required": True},
        )
        snapshot_read.managed_by = AAZStrType(
            serialized_name="managedBy",
            flags={"read_only": True},
        )
        snapshot_read.name = AAZStrType(
            flags={"read_only": True},
        )
        snapshot_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        snapshot_read.sku = AAZObjectType()
        snapshot_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        snapshot_read.tags = AAZDictType()
        snapshot_read.type = AAZStrType(
            flags={"read_only": True},
        )

        extended_location = _schema_snapshot_read.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        properties = _schema_snapshot_read.properties
        properties.completion_percent = AAZFloatType(
            serialized_name="completionPercent",
        )
        properties.copy_completion_error = AAZObjectType(
            serialized_name="copyCompletionError",
        )
        properties.creation_data = AAZObjectType(
            serialized_name="creationData",
            flags={"required": True},
        )
        properties.data_access_auth_mode = AAZStrType(
            serialized_name="dataAccessAuthMode",
        )
        properties.disk_access_id = AAZStrType(
            serialized_name="diskAccessId",
        )
        properties.disk_size_bytes = AAZIntType(
            serialized_name="diskSizeBytes",
            flags={"read_only": True},
        )
        properties.disk_size_gb = AAZIntType(
            serialized_name="diskSizeGB",
        )
        properties.disk_state = AAZStrType(
            serialized_name="diskState",
            flags={"read_only": True},
        )
        properties.encryption = AAZObjectType()
        properties.encryption_settings_collection = AAZObjectType(
            serialized_name="encryptionSettingsCollection",
        )
        properties.hyper_v_generation = AAZStrType(
            serialized_name="hyperVGeneration",
        )
        properties.incremental = AAZBoolType()
        properties.incremental_snapshot_family_id = AAZStrType(
            serialized_name="incrementalSnapshotFamilyId",
            flags={"read_only": True},
        )
        properties.network_access_policy = AAZStrType(
            serialized_name="networkAccessPolicy",
        )
        properties.os_type = AAZStrType(
            serialized_name="osType",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_network_access = AAZStrType(
            serialized_name="publicNetworkAccess",
        )
        properties.purchase_plan = AAZObjectType(
            serialized_name="purchasePlan",
        )
        properties.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        properties.snapshot_access_state = AAZStrType(
            serialized_name="snapshotAccessState",
            flags={"read_only": True},
        )
        properties.supported_capabilities = AAZObjectType(
            serialized_name="supportedCapabilities",
        )
        properties.supports_hibernation = AAZBoolType(
            serialized_name="supportsHibernation",
        )
        properties.time_created = AAZStrType(
            serialized_name="timeCreated",
            flags={"read_only": True},
        )
        properties.unique_id = AAZStrType(
            serialized_name="uniqueId",
            flags={"read_only": True},
        )

        copy_completion_error = _schema_snapshot_read.properties.copy_completion_error
        copy_completion_error.error_code = AAZStrType(
            serialized_name="errorCode",
            flags={"required": True},
        )
        copy_completion_error.error_message = AAZStrType(
            serialized_name="errorMessage",
            flags={"required": True},
        )

        creation_data = _schema_snapshot_read.properties.creation_data
        creation_data.create_option = AAZStrType(
            serialized_name="createOption",
            flags={"required": True},
        )
        creation_data.elastic_san_resource_id = AAZStrType(
            serialized_name="elasticSanResourceId",
        )
        creation_data.gallery_image_reference = AAZObjectType(
            serialized_name="galleryImageReference",
        )
        cls._build_schema_image_disk_reference_read(creation_data.gallery_image_reference)
        creation_data.image_reference = AAZObjectType(
            serialized_name="imageReference",
        )
        cls._build_schema_image_disk_reference_read(creation_data.image_reference)
        creation_data.instant_access_duration_minutes = AAZIntType(
            serialized_name="instantAccessDurationMinutes",
        )
        creation_data.logical_sector_size = AAZIntType(
            serialized_name="logicalSectorSize",
        )
        creation_data.performance_plus = AAZBoolType(
            serialized_name="performancePlus",
        )
        creation_data.provisioned_bandwidth_copy_speed = AAZStrType(
            serialized_name="provisionedBandwidthCopySpeed",
        )
        creation_data.security_data_uri = AAZStrType(
            serialized_name="securityDataUri",
        )
        creation_data.security_metadata_uri = AAZStrType(
            serialized_name="securityMetadataUri",
        )
        creation_data.source_resource_id = AAZStrType(
            serialized_name="sourceResourceId",
        )
        creation_data.source_unique_id = AAZStrType(
            serialized_name="sourceUniqueId",
            flags={"read_only": True},
        )
        creation_data.source_uri = AAZStrType(
            serialized_name="sourceUri",
        )
        creation_data.storage_account_id = AAZStrType(
            serialized_name="storageAccountId",
        )
        creation_data.upload_size_bytes = AAZIntType(
            serialized_name="uploadSizeBytes",
        )

        encryption = _schema_snapshot_read.properties.encryption
        encryption.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        encryption.type = AAZStrType()

        encryption_settings_collection = _schema_snapshot_read.properties.encryption_settings_collection
        encryption_settings_collection.enabled = AAZBoolType(
            flags={"required": True},
        )
        encryption_settings_collection.encryption_settings = AAZListType(
            serialized_name="encryptionSettings",
        )
        encryption_settings_collection.encryption_settings_version = AAZStrType(
            serialized_name="encryptionSettingsVersion",
        )

        encryption_settings = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings
        encryption_settings.Element = AAZObjectType()

        _element = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings.Element
        _element.disk_encryption_key = AAZObjectType(
            serialized_name="diskEncryptionKey",
        )
        _element.key_encryption_key = AAZObjectType(
            serialized_name="keyEncryptionKey",
        )

        disk_encryption_key = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings.Element.disk_encryption_key
        disk_encryption_key.secret_url = AAZStrType(
            serialized_name="secretUrl",
            flags={"required": True},
        )
        disk_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_source_vault_read(disk_encryption_key.source_vault)

        key_encryption_key = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings.Element.key_encryption_key
        key_encryption_key.key_url = AAZStrType(
            serialized_name="keyUrl",
            flags={"required": True},
        )
        key_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_source_vault_read(key_encryption_key.source_vault)

        purchase_plan = _schema_snapshot_read.properties.purchase_plan
        purchase_plan.name = AAZStrType(
            flags={"required": True},
        )
        purchase_plan.product = AAZStrType(
            flags={"required": True},
        )
        purchase_plan.promotion_code = AAZStrType(
            serialized_name="promotionCode",
        )
        purchase_plan.publisher = AAZStrType(
            flags={"required": True},
        )

        security_profile = _schema_snapshot_read.properties.security_profile
        security_profile.secure_vm_disk_encryption_set_id = AAZStrType(
            serialized_name="secureVMDiskEncryptionSetId",
        )
        security_profile.security_type = AAZStrType(
            serialized_name="securityType",
        )

        supported_capabilities = _schema_snapshot_read.properties.supported_capabilities
        supported_capabilities.accelerated_network = AAZBoolType(
            serialized_name="acceleratedNetwork",
        )
        supported_capabilities.architecture = AAZStrType()
        supported_capabilities.disk_controller_types = AAZStrType(
            serialized_name="diskControllerTypes",
        )
        supported_capabilities.supported_security_option = AAZStrType(
            serialized_name="supportedSecurityOption",
        )

        sku = _schema_snapshot_read.sku
        sku.name = AAZStrType()
        sku.tier = AAZStrType(
            flags={"read_only": True},
        )

        system_data = _schema_snapshot_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_snapshot_read.tags
        tags.Element = AAZStrType()

        _schema.extended_location = cls._schema_snapshot_read.extended_location
        _schema.id = cls._schema_snapshot_read.id
        _schema.location = cls._schema_snapshot_read.location
        _schema.managed_by = cls._schema_snapshot_read.managed_by
        _schema.name = cls._schema_snapshot_read.name
        _schema.properties = cls._schema_snapshot_read.properties
        _schema.sku = cls._schema_snapshot_read.sku
        _schema.system_data = cls._schema_snapshot_read.system_data
        _schema.tags = cls._schema_snapshot_read.tags
        _schema.type = cls._schema_snapshot_read.type

    _schema_source_vault_read = None

    @classmethod
    def _build_schema_source_vault_read(cls, _schema):
        if cls._schema_source_vault_read is not None:
            _schema.id = cls._schema_source_vault_read.id
            return

        cls._schema_source_vault_read = _schema_source_vault_read = AAZObjectType()

        source_vault_read = _schema_source_vault_read
        source_vault_read.id = AAZStrType()

        _schema.id = cls._schema_source_vault_read.id


__all__ = ["Create"]
