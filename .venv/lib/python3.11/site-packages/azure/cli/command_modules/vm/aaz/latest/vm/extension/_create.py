# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class Create(AAZCommand):
    """Create operation to create or update the extension.
    """

    _aaz_info = {
        "version": "2024-11-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachines/{}/extensions/{}", "2024-11-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.vm_extension_name = AAZStrArg(
            options=["-n", "--name", "--vm-extension-name"],
            help="The name of the virtual machine extension.",
            required=True,
        )
        _args_schema.vm_name = AAZStrArg(
            options=["--vm-name"],
            help="The name of the Virtual Machine. You can configure the default using `az configure --defaults vm=<name>`.",
            required=True,
        )

        # define Arg Group "ExtensionParameters"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="ExtensionParameters",
            help="Resource location",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="ExtensionParameters",
            help="Resource tags",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.auto_upgrade_minor_version = AAZBoolArg(
            options=["--auto-upgrade-minor-version"],
            arg_group="Properties",
            help="Indicates whether the extension should use a newer minor version if one is available at deployment time. Once deployed, however, the extension will not upgrade minor versions unless redeployed, even with this property set to true.",
        )
        _args_schema.enable_automatic_upgrade = AAZBoolArg(
            options=["--enable-automatic-upgrade"],
            arg_group="Properties",
            help="Indicates whether the extension should be automatically upgraded by the platform if there is a newer version of the extension available.",
        )
        _args_schema.force_update_tag = AAZStrArg(
            options=["--force-update-tag"],
            arg_group="Properties",
            help="How the extension handler should be forced to update even if the extension configuration has not changed.",
        )
        _args_schema.instance_view = AAZObjectArg(
            options=["--instance-view"],
            arg_group="Properties",
            help="The virtual machine extension instance view.",
        )
        _args_schema.protected_settings = AAZFreeFormDictArg(
            options=["--protected-settings"],
            arg_group="Properties",
            help="The extension can contain either protectedSettings or protectedSettingsFromKeyVault or no protected settings at all.",
        )
        _args_schema.protected_settings_from_key_vault = AAZObjectArg(
            options=["--protected-settings-from-key-vault"],
            arg_group="Properties",
            help="The extensions protected settings that are passed by reference, and consumed from key vault",
        )
        _args_schema.provision_after_extensions = AAZListArg(
            options=["--provision-after-extensions"],
            arg_group="Properties",
            help="Collection of extension names after which this extension needs to be provisioned.",
        )
        _args_schema.publisher = AAZStrArg(
            options=["--publisher"],
            arg_group="Properties",
            help="The name of the extension handler publisher.",
        )
        _args_schema.settings = AAZFreeFormDictArg(
            options=["--settings"],
            arg_group="Properties",
            help="Json formatted public settings for the extension.",
        )
        _args_schema.suppress_failures = AAZBoolArg(
            options=["--suppress-failures"],
            arg_group="Properties",
            help="Indicates whether failures stemming from the extension will be suppressed (Operational failures such as not connecting to the VM will not be suppressed regardless of this value). The default is false.",
        )
        _args_schema.type = AAZStrArg(
            options=["--type"],
            arg_group="Properties",
            help="Specifies the type of the extension; an example is \"CustomScriptExtension\".",
        )
        _args_schema.type_handler_version = AAZStrArg(
            options=["--type-handler-version"],
            arg_group="Properties",
            help="Specifies the version of the script handler.",
        )

        instance_view = cls._args_schema.instance_view
        instance_view.name = AAZStrArg(
            options=["name"],
            help="The virtual machine extension name.",
        )
        instance_view.statuses = AAZListArg(
            options=["statuses"],
            help="The resource status information.",
        )
        instance_view.substatuses = AAZListArg(
            options=["substatuses"],
            help="The resource status information.",
        )
        instance_view.type = AAZStrArg(
            options=["type"],
            help="Specifies the type of the extension; an example is \"CustomScriptExtension\".",
        )
        instance_view.type_handler_version = AAZStrArg(
            options=["type-handler-version"],
            help="Specifies the version of the script handler.",
        )

        statuses = cls._args_schema.instance_view.statuses
        statuses.Element = AAZObjectArg()
        cls._build_args_instance_view_status_create(statuses.Element)

        substatuses = cls._args_schema.instance_view.substatuses
        substatuses.Element = AAZObjectArg()
        cls._build_args_instance_view_status_create(substatuses.Element)

        protected_settings_from_key_vault = cls._args_schema.protected_settings_from_key_vault
        protected_settings_from_key_vault.secret_url = AAZStrArg(
            options=["secret-url"],
            help="The URL referencing a secret in a Key Vault.",
            required=True,
        )
        protected_settings_from_key_vault.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="The relative URL of the Key Vault containing the secret.",
            required=True,
        )

        source_vault = cls._args_schema.protected_settings_from_key_vault.source_vault
        source_vault.id = AAZStrArg(
            options=["id"],
            help="Resource Id",
        )

        provision_after_extensions = cls._args_schema.provision_after_extensions
        provision_after_extensions.Element = AAZStrArg()
        return cls._args_schema

    _args_instance_view_status_create = None

    @classmethod
    def _build_args_instance_view_status_create(cls, _schema):
        if cls._args_instance_view_status_create is not None:
            _schema.code = cls._args_instance_view_status_create.code
            _schema.display_status = cls._args_instance_view_status_create.display_status
            _schema.level = cls._args_instance_view_status_create.level
            _schema.message = cls._args_instance_view_status_create.message
            _schema.time = cls._args_instance_view_status_create.time
            return

        cls._args_instance_view_status_create = AAZObjectArg()

        instance_view_status_create = cls._args_instance_view_status_create
        instance_view_status_create.code = AAZStrArg(
            options=["code"],
            help="The status code.",
        )
        instance_view_status_create.display_status = AAZStrArg(
            options=["display-status"],
            help="The short localizable label for the status.",
        )
        instance_view_status_create.level = AAZStrArg(
            options=["level"],
            help="The level code.",
            enum={"Error": "Error", "Info": "Info", "Warning": "Warning"},
        )
        instance_view_status_create.message = AAZStrArg(
            options=["message"],
            help="The detailed status message, including for alerts and error messages.",
        )
        instance_view_status_create.time = AAZDateTimeArg(
            options=["time"],
            help="The time of the status.",
        )

        _schema.code = cls._args_instance_view_status_create.code
        _schema.display_status = cls._args_instance_view_status_create.display_status
        _schema.level = cls._args_instance_view_status_create.level
        _schema.message = cls._args_instance_view_status_create.message
        _schema.time = cls._args_instance_view_status_create.time

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualMachineExtensionsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualMachineExtensionsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachines/{vmName}/extensions/{vmExtensionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmExtensionName", self.ctx.args.vm_extension_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmName", self.ctx.args.vm_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-11-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("location", AAZStrType, ".location")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("autoUpgradeMinorVersion", AAZBoolType, ".auto_upgrade_minor_version")
                properties.set_prop("enableAutomaticUpgrade", AAZBoolType, ".enable_automatic_upgrade")
                properties.set_prop("forceUpdateTag", AAZStrType, ".force_update_tag")
                properties.set_prop("instanceView", AAZObjectType, ".instance_view")
                properties.set_prop("protectedSettings", AAZFreeFormDictType, ".protected_settings")
                properties.set_prop("protectedSettingsFromKeyVault", AAZObjectType, ".protected_settings_from_key_vault")
                properties.set_prop("provisionAfterExtensions", AAZListType, ".provision_after_extensions")
                properties.set_prop("publisher", AAZStrType, ".publisher")
                properties.set_prop("settings", AAZFreeFormDictType, ".settings")
                properties.set_prop("suppressFailures", AAZBoolType, ".suppress_failures")
                properties.set_prop("type", AAZStrType, ".type")
                properties.set_prop("typeHandlerVersion", AAZStrType, ".type_handler_version")

            instance_view = _builder.get(".properties.instanceView")
            if instance_view is not None:
                instance_view.set_prop("name", AAZStrType, ".name")
                instance_view.set_prop("statuses", AAZListType, ".statuses")
                instance_view.set_prop("substatuses", AAZListType, ".substatuses")
                instance_view.set_prop("type", AAZStrType, ".type")
                instance_view.set_prop("typeHandlerVersion", AAZStrType, ".type_handler_version")

            statuses = _builder.get(".properties.instanceView.statuses")
            if statuses is not None:
                _CreateHelper._build_schema_instance_view_status_create(statuses.set_elements(AAZObjectType, "."))

            substatuses = _builder.get(".properties.instanceView.substatuses")
            if substatuses is not None:
                _CreateHelper._build_schema_instance_view_status_create(substatuses.set_elements(AAZObjectType, "."))

            protected_settings = _builder.get(".properties.protectedSettings")
            if protected_settings is not None:
                protected_settings.set_anytype_elements(".")

            protected_settings_from_key_vault = _builder.get(".properties.protectedSettingsFromKeyVault")
            if protected_settings_from_key_vault is not None:
                protected_settings_from_key_vault.set_prop("secretUrl", AAZStrType, ".secret_url", typ_kwargs={"flags": {"required": True}})
                protected_settings_from_key_vault.set_prop("sourceVault", AAZObjectType, ".source_vault", typ_kwargs={"flags": {"required": True}})

            source_vault = _builder.get(".properties.protectedSettingsFromKeyVault.sourceVault")
            if source_vault is not None:
                source_vault.set_prop("id", AAZStrType, ".id")

            provision_after_extensions = _builder.get(".properties.provisionAfterExtensions")
            if provision_after_extensions is not None:
                provision_after_extensions.set_elements(AAZStrType, ".")

            settings = _builder.get(".properties.settings")
            if settings is not None:
                settings.set_anytype_elements(".")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.location = AAZStrType()
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200_201.tags = AAZDictType()
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200_201.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
            )
            properties.protected_settings = AAZFreeFormDictType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZFreeFormDictType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            instance_view = cls._schema_on_200_201.properties.instance_view
            instance_view.name = AAZStrType()
            instance_view.statuses = AAZListType()
            instance_view.substatuses = AAZListType()
            instance_view.type = AAZStrType()
            instance_view.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            statuses = cls._schema_on_200_201.properties.instance_view.statuses
            statuses.Element = AAZObjectType()
            _CreateHelper._build_schema_instance_view_status_read(statuses.Element)

            substatuses = cls._schema_on_200_201.properties.instance_view.substatuses
            substatuses.Element = AAZObjectType()
            _CreateHelper._build_schema_instance_view_status_read(substatuses.Element)

            protected_settings_from_key_vault = cls._schema_on_200_201.properties.protected_settings_from_key_vault
            protected_settings_from_key_vault.secret_url = AAZStrType(
                serialized_name="secretUrl",
                flags={"required": True},
            )
            protected_settings_from_key_vault.source_vault = AAZObjectType(
                serialized_name="sourceVault",
                flags={"required": True},
            )

            source_vault = cls._schema_on_200_201.properties.protected_settings_from_key_vault.source_vault
            source_vault.id = AAZStrType()

            provision_after_extensions = cls._schema_on_200_201.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            tags = cls._schema_on_200_201.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_instance_view_status_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("code", AAZStrType, ".code")
        _builder.set_prop("displayStatus", AAZStrType, ".display_status")
        _builder.set_prop("level", AAZStrType, ".level")
        _builder.set_prop("message", AAZStrType, ".message")
        _builder.set_prop("time", AAZStrType, ".time")

    _schema_instance_view_status_read = None

    @classmethod
    def _build_schema_instance_view_status_read(cls, _schema):
        if cls._schema_instance_view_status_read is not None:
            _schema.code = cls._schema_instance_view_status_read.code
            _schema.display_status = cls._schema_instance_view_status_read.display_status
            _schema.level = cls._schema_instance_view_status_read.level
            _schema.message = cls._schema_instance_view_status_read.message
            _schema.time = cls._schema_instance_view_status_read.time
            return

        cls._schema_instance_view_status_read = _schema_instance_view_status_read = AAZObjectType()

        instance_view_status_read = _schema_instance_view_status_read
        instance_view_status_read.code = AAZStrType()
        instance_view_status_read.display_status = AAZStrType(
            serialized_name="displayStatus",
        )
        instance_view_status_read.level = AAZStrType()
        instance_view_status_read.message = AAZStrType()
        instance_view_status_read.time = AAZStrType()

        _schema.code = cls._schema_instance_view_status_read.code
        _schema.display_status = cls._schema_instance_view_status_read.display_status
        _schema.level = cls._schema_instance_view_status_read.level
        _schema.message = cls._schema_instance_view_status_read.message
        _schema.time = cls._schema_instance_view_status_read.time


__all__ = ["Create"]
