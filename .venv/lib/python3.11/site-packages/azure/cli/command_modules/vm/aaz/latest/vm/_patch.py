# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class Patch(AAZCommand):
    """Update operation to update a virtual machine with PATCH request.
    """

    _aaz_info = {
        "version": "2024-11-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachines/{}", "2024-11-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.if_match = AAZStrArg(
            options=["--if-match"],
            help="",
        )
        _args_schema.if_none_match = AAZStrArg(
            options=["--if-none-match"],
            help="",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.vm_name = AAZStrArg(
            options=["-n", "--name", "--vm-name"],
            help="",
            required=True,
            id_part="name",
        )

        # define Arg Group "Identity"

        _args_schema = cls._args_schema
        _args_schema.mi_system_assigned = AAZStrArg(
            options=["--system-assigned", "--mi-system-assigned"],
            arg_group="Identity",
            help="",
            blank="True",
        )
        _args_schema.mi_user_assigned = AAZListArg(
            options=["--user-assigned", "--mi-user-assigned"],
            arg_group="Identity",
            help="",
            blank=[],
        )

        mi_user_assigned = cls._args_schema.mi_user_assigned
        mi_user_assigned.Element = AAZStrArg()

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.plan = AAZObjectArg(
            options=["--plan"],
            arg_group="Parameters",
            help="",
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Parameters",
            help="",
        )
        _args_schema.zones = AAZListArg(
            options=["--zones"],
            arg_group="Parameters",
            help="",
        )

        plan = cls._args_schema.plan
        plan.name = AAZStrArg(
            options=["name"],
            help="",
        )
        plan.product = AAZStrArg(
            options=["product"],
            help="",
        )
        plan.promotion_code = AAZStrArg(
            options=["promotion-code"],
            help="",
        )
        plan.publisher = AAZStrArg(
            options=["publisher"],
            help="",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        zones = cls._args_schema.zones
        zones.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.additional_capabilities = AAZObjectArg(
            options=["--additional-capabilities"],
            arg_group="Properties",
            help="",
        )
        _args_schema.application_profile = AAZObjectArg(
            options=["--application-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.availability_set = AAZObjectArg(
            options=["--availability-set"],
            arg_group="Properties",
            help="",
        )
        cls._build_args_sub_resource_update(_args_schema.availability_set)
        _args_schema.billing_profile = AAZObjectArg(
            options=["--billing-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.capacity_reservation = AAZObjectArg(
            options=["--capacity-reservation"],
            arg_group="Properties",
            help="",
        )
        _args_schema.diagnostics_profile = AAZObjectArg(
            options=["--diagnostics-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.eviction_policy = AAZStrArg(
            options=["--eviction-policy"],
            arg_group="Properties",
            help="",
            enum={"Deallocate": "Deallocate", "Delete": "Delete"},
        )
        _args_schema.extensions_time_budget = AAZStrArg(
            options=["--extensions-time-budget"],
            arg_group="Properties",
            help="",
        )
        _args_schema.hardware_profile = AAZObjectArg(
            options=["--hardware-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.host = AAZObjectArg(
            options=["--host"],
            arg_group="Properties",
            help="",
        )
        cls._build_args_sub_resource_update(_args_schema.host)
        _args_schema.host_group = AAZObjectArg(
            options=["--host-group"],
            arg_group="Properties",
            help="",
        )
        cls._build_args_sub_resource_update(_args_schema.host_group)
        _args_schema.license_type = AAZStrArg(
            options=["--license-type"],
            arg_group="Properties",
            help="",
        )
        _args_schema.network_profile = AAZObjectArg(
            options=["--network-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.os_profile = AAZObjectArg(
            options=["--os-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.platform_fault_domain = AAZIntArg(
            options=["--platform-fault-domain"],
            arg_group="Properties",
            help="",
        )
        _args_schema.priority = AAZStrArg(
            options=["--priority"],
            arg_group="Properties",
            help="",
            enum={"Low": "Low", "Regular": "Regular", "Spot": "Spot"},
        )
        _args_schema.proximity_placement_group = AAZObjectArg(
            options=["--proximity-placement-group"],
            arg_group="Properties",
            help="",
        )
        cls._build_args_sub_resource_update(_args_schema.proximity_placement_group)
        _args_schema.scheduled_events_policy = AAZObjectArg(
            options=["--scheduled-events-policy"],
            arg_group="Properties",
            help="",
        )
        _args_schema.scheduled_events_profile = AAZObjectArg(
            options=["--scheduled-events-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.security_profile = AAZObjectArg(
            options=["--security-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.storage_profile = AAZObjectArg(
            options=["--storage-profile"],
            arg_group="Properties",
            help="",
        )
        _args_schema.user_data = AAZStrArg(
            options=["--user-data"],
            arg_group="Properties",
            help="",
        )
        _args_schema.virtual_machine_scale_set = AAZObjectArg(
            options=["--virtual-machine-scale-set"],
            arg_group="Properties",
            help="",
        )
        cls._build_args_sub_resource_update(_args_schema.virtual_machine_scale_set)

        additional_capabilities = cls._args_schema.additional_capabilities
        additional_capabilities.hibernation_enabled = AAZBoolArg(
            options=["hibernation-enabled"],
            help="",
        )
        additional_capabilities.ultra_ssd_enabled = AAZBoolArg(
            options=["ultra-ssd-enabled"],
            help="",
        )

        application_profile = cls._args_schema.application_profile
        application_profile.gallery_applications = AAZListArg(
            options=["gallery-applications"],
            help="",
        )

        gallery_applications = cls._args_schema.application_profile.gallery_applications
        gallery_applications.Element = AAZObjectArg()

        _element = cls._args_schema.application_profile.gallery_applications.Element
        _element.configuration_reference = AAZStrArg(
            options=["configuration-reference"],
            help="",
        )
        _element.enable_automatic_upgrade = AAZBoolArg(
            options=["enable-automatic-upgrade"],
            help="",
        )
        _element.order = AAZIntArg(
            options=["order"],
            help="",
        )
        _element.package_reference_id = AAZStrArg(
            options=["package-reference-id"],
            help="",
            required=True,
        )
        _element.tags = AAZStrArg(
            options=["tags"],
            help="",
        )
        _element.treat_failure_as_deployment_failure = AAZBoolArg(
            options=["treat-failure-as-deployment-failure"],
            help="",
        )

        billing_profile = cls._args_schema.billing_profile
        billing_profile.max_price = AAZFloatArg(
            options=["max-price"],
            help="",
        )

        capacity_reservation = cls._args_schema.capacity_reservation
        capacity_reservation.capacity_reservation_group = AAZObjectArg(
            options=["capacity-reservation-group"],
            help="",
        )
        cls._build_args_sub_resource_update(capacity_reservation.capacity_reservation_group)

        diagnostics_profile = cls._args_schema.diagnostics_profile
        diagnostics_profile.boot_diagnostics = AAZObjectArg(
            options=["boot-diagnostics"],
            help="",
        )

        boot_diagnostics = cls._args_schema.diagnostics_profile.boot_diagnostics
        boot_diagnostics.enabled = AAZBoolArg(
            options=["enabled"],
            help="",
        )
        boot_diagnostics.storage_uri = AAZStrArg(
            options=["storage-uri"],
            help="",
        )

        hardware_profile = cls._args_schema.hardware_profile
        hardware_profile.vm_size = AAZStrArg(
            options=["vm-size"],
            help="",
            enum={"Basic_A0": "Basic_A0", "Basic_A1": "Basic_A1", "Basic_A2": "Basic_A2", "Basic_A3": "Basic_A3", "Basic_A4": "Basic_A4", "Standard_A0": "Standard_A0", "Standard_A1": "Standard_A1", "Standard_A10": "Standard_A10", "Standard_A11": "Standard_A11", "Standard_A1_v2": "Standard_A1_v2", "Standard_A2": "Standard_A2", "Standard_A2_v2": "Standard_A2_v2", "Standard_A2m_v2": "Standard_A2m_v2", "Standard_A3": "Standard_A3", "Standard_A4": "Standard_A4", "Standard_A4_v2": "Standard_A4_v2", "Standard_A4m_v2": "Standard_A4m_v2", "Standard_A5": "Standard_A5", "Standard_A6": "Standard_A6", "Standard_A7": "Standard_A7", "Standard_A8": "Standard_A8", "Standard_A8_v2": "Standard_A8_v2", "Standard_A8m_v2": "Standard_A8m_v2", "Standard_A9": "Standard_A9", "Standard_B1ms": "Standard_B1ms", "Standard_B1s": "Standard_B1s", "Standard_B2ms": "Standard_B2ms", "Standard_B2s": "Standard_B2s", "Standard_B4ms": "Standard_B4ms", "Standard_B8ms": "Standard_B8ms", "Standard_D1": "Standard_D1", "Standard_D11": "Standard_D11", "Standard_D11_v2": "Standard_D11_v2", "Standard_D12": "Standard_D12", "Standard_D12_v2": "Standard_D12_v2", "Standard_D13": "Standard_D13", "Standard_D13_v2": "Standard_D13_v2", "Standard_D14": "Standard_D14", "Standard_D14_v2": "Standard_D14_v2", "Standard_D15_v2": "Standard_D15_v2", "Standard_D16_v3": "Standard_D16_v3", "Standard_D16s_v3": "Standard_D16s_v3", "Standard_D1_v2": "Standard_D1_v2", "Standard_D2": "Standard_D2", "Standard_D2_v2": "Standard_D2_v2", "Standard_D2_v3": "Standard_D2_v3", "Standard_D2s_v3": "Standard_D2s_v3", "Standard_D3": "Standard_D3", "Standard_D32_v3": "Standard_D32_v3", "Standard_D32s_v3": "Standard_D32s_v3", "Standard_D3_v2": "Standard_D3_v2", "Standard_D4": "Standard_D4", "Standard_D4_v2": "Standard_D4_v2", "Standard_D4_v3": "Standard_D4_v3", "Standard_D4s_v3": "Standard_D4s_v3", "Standard_D5_v2": "Standard_D5_v2", "Standard_D64_v3": "Standard_D64_v3", "Standard_D64s_v3": "Standard_D64s_v3", "Standard_D8_v3": "Standard_D8_v3", "Standard_D8s_v3": "Standard_D8s_v3", "Standard_DS1": "Standard_DS1", "Standard_DS11": "Standard_DS11", "Standard_DS11_v2": "Standard_DS11_v2", "Standard_DS12": "Standard_DS12", "Standard_DS12_v2": "Standard_DS12_v2", "Standard_DS13": "Standard_DS13", "Standard_DS13-2_v2": "Standard_DS13-2_v2", "Standard_DS13-4_v2": "Standard_DS13-4_v2", "Standard_DS13_v2": "Standard_DS13_v2", "Standard_DS14": "Standard_DS14", "Standard_DS14-4_v2": "Standard_DS14-4_v2", "Standard_DS14-8_v2": "Standard_DS14-8_v2", "Standard_DS14_v2": "Standard_DS14_v2", "Standard_DS15_v2": "Standard_DS15_v2", "Standard_DS1_v2": "Standard_DS1_v2", "Standard_DS2": "Standard_DS2", "Standard_DS2_v2": "Standard_DS2_v2", "Standard_DS3": "Standard_DS3", "Standard_DS3_v2": "Standard_DS3_v2", "Standard_DS4": "Standard_DS4", "Standard_DS4_v2": "Standard_DS4_v2", "Standard_DS5_v2": "Standard_DS5_v2", "Standard_E16_v3": "Standard_E16_v3", "Standard_E16s_v3": "Standard_E16s_v3", "Standard_E2_v3": "Standard_E2_v3", "Standard_E2s_v3": "Standard_E2s_v3", "Standard_E32-16_v3": "Standard_E32-16_v3", "Standard_E32-8s_v3": "Standard_E32-8s_v3", "Standard_E32_v3": "Standard_E32_v3", "Standard_E32s_v3": "Standard_E32s_v3", "Standard_E4_v3": "Standard_E4_v3", "Standard_E4s_v3": "Standard_E4s_v3", "Standard_E64-16s_v3": "Standard_E64-16s_v3", "Standard_E64-32s_v3": "Standard_E64-32s_v3", "Standard_E64_v3": "Standard_E64_v3", "Standard_E64s_v3": "Standard_E64s_v3", "Standard_E8_v3": "Standard_E8_v3", "Standard_E8s_v3": "Standard_E8s_v3", "Standard_F1": "Standard_F1", "Standard_F16": "Standard_F16", "Standard_F16s": "Standard_F16s", "Standard_F16s_v2": "Standard_F16s_v2", "Standard_F1s": "Standard_F1s", "Standard_F2": "Standard_F2", "Standard_F2s": "Standard_F2s", "Standard_F2s_v2": "Standard_F2s_v2", "Standard_F32s_v2": "Standard_F32s_v2", "Standard_F4": "Standard_F4", "Standard_F4s": "Standard_F4s", "Standard_F4s_v2": "Standard_F4s_v2", "Standard_F64s_v2": "Standard_F64s_v2", "Standard_F72s_v2": "Standard_F72s_v2", "Standard_F8": "Standard_F8", "Standard_F8s": "Standard_F8s", "Standard_F8s_v2": "Standard_F8s_v2", "Standard_G1": "Standard_G1", "Standard_G2": "Standard_G2", "Standard_G3": "Standard_G3", "Standard_G4": "Standard_G4", "Standard_G5": "Standard_G5", "Standard_GS1": "Standard_GS1", "Standard_GS2": "Standard_GS2", "Standard_GS3": "Standard_GS3", "Standard_GS4": "Standard_GS4", "Standard_GS4-4": "Standard_GS4-4", "Standard_GS4-8": "Standard_GS4-8", "Standard_GS5": "Standard_GS5", "Standard_GS5-16": "Standard_GS5-16", "Standard_GS5-8": "Standard_GS5-8", "Standard_H16": "Standard_H16", "Standard_H16m": "Standard_H16m", "Standard_H16mr": "Standard_H16mr", "Standard_H16r": "Standard_H16r", "Standard_H8": "Standard_H8", "Standard_H8m": "Standard_H8m", "Standard_L16s": "Standard_L16s", "Standard_L32s": "Standard_L32s", "Standard_L4s": "Standard_L4s", "Standard_L8s": "Standard_L8s", "Standard_M128-32ms": "Standard_M128-32ms", "Standard_M128-64ms": "Standard_M128-64ms", "Standard_M128ms": "Standard_M128ms", "Standard_M128s": "Standard_M128s", "Standard_M64-16ms": "Standard_M64-16ms", "Standard_M64-32ms": "Standard_M64-32ms", "Standard_M64ms": "Standard_M64ms", "Standard_M64s": "Standard_M64s", "Standard_NC12": "Standard_NC12", "Standard_NC12s_v2": "Standard_NC12s_v2", "Standard_NC12s_v3": "Standard_NC12s_v3", "Standard_NC24": "Standard_NC24", "Standard_NC24r": "Standard_NC24r", "Standard_NC24rs_v2": "Standard_NC24rs_v2", "Standard_NC24rs_v3": "Standard_NC24rs_v3", "Standard_NC24s_v2": "Standard_NC24s_v2", "Standard_NC24s_v3": "Standard_NC24s_v3", "Standard_NC6": "Standard_NC6", "Standard_NC6s_v2": "Standard_NC6s_v2", "Standard_NC6s_v3": "Standard_NC6s_v3", "Standard_ND12s": "Standard_ND12s", "Standard_ND24rs": "Standard_ND24rs", "Standard_ND24s": "Standard_ND24s", "Standard_ND6s": "Standard_ND6s", "Standard_NV12": "Standard_NV12", "Standard_NV24": "Standard_NV24", "Standard_NV6": "Standard_NV6"},
        )
        hardware_profile.vm_size_properties = AAZObjectArg(
            options=["vm-size-properties"],
            help="",
        )

        vm_size_properties = cls._args_schema.hardware_profile.vm_size_properties
        vm_size_properties.v_cp_us_available = AAZIntArg(
            options=["v-cp-us-available"],
            help="",
        )
        vm_size_properties.v_cp_us_per_core = AAZIntArg(
            options=["v-cp-us-per-core"],
            help="",
        )

        network_profile = cls._args_schema.network_profile
        network_profile.network_api_version = AAZStrArg(
            options=["network-api-version"],
            help="",
            enum={"2020-11-01": "2020-11-01", "2022-11-01": "2022-11-01"},
        )
        network_profile.network_interface_configurations = AAZListArg(
            options=["network-interface-configurations"],
            help="",
        )
        network_profile.network_interfaces = AAZListArg(
            options=["network-interfaces"],
            help="",
        )

        network_interface_configurations = cls._args_schema.network_profile.network_interface_configurations
        network_interface_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.network_profile.network_interface_configurations.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="",
            required=True,
        )
        _element.auxiliary_mode = AAZStrArg(
            options=["auxiliary-mode"],
            help="",
            enum={"AcceleratedConnections": "AcceleratedConnections", "Floating": "Floating", "None": "None"},
        )
        _element.auxiliary_sku = AAZStrArg(
            options=["auxiliary-sku"],
            help="",
            enum={"A1": "A1", "A2": "A2", "A4": "A4", "A8": "A8", "None": "None"},
        )
        _element.delete_option = AAZStrArg(
            options=["delete-option"],
            help="",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        _element.disable_tcp_state_tracking = AAZBoolArg(
            options=["disable-tcp-state-tracking"],
            help="",
        )
        _element.dns_settings = AAZObjectArg(
            options=["dns-settings"],
            help="",
        )
        _element.dscp_configuration = AAZObjectArg(
            options=["dscp-configuration"],
        )
        cls._build_args_sub_resource_update(_element.dscp_configuration)
        _element.enable_accelerated_networking = AAZBoolArg(
            options=["enable-accelerated-networking"],
            help="",
        )
        _element.enable_fpga = AAZBoolArg(
            options=["enable-fpga"],
            help="",
        )
        _element.enable_ip_forwarding = AAZBoolArg(
            options=["enable-ip-forwarding"],
            help="",
        )
        _element.ip_configurations = AAZListArg(
            options=["ip-configurations"],
            help="",
        )
        _element.network_security_group = AAZObjectArg(
            options=["network-security-group"],
            help="",
        )
        cls._build_args_sub_resource_update(_element.network_security_group)
        _element.primary = AAZBoolArg(
            options=["primary"],
            help="",
        )

        dns_settings = cls._args_schema.network_profile.network_interface_configurations.Element.dns_settings
        dns_settings.dns_servers = AAZListArg(
            options=["dns-servers"],
            help="",
        )

        dns_servers = cls._args_schema.network_profile.network_interface_configurations.Element.dns_settings.dns_servers
        dns_servers.Element = AAZStrArg()

        ip_configurations = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations
        ip_configurations.Element = AAZObjectArg()

        _element = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="",
            required=True,
        )
        _element.application_gateway_backend_address_pools = AAZListArg(
            options=["application-gateway-backend-address-pools"],
            help="",
        )
        _element.application_security_groups = AAZListArg(
            options=["application-security-groups"],
            help="",
        )
        _element.load_balancer_backend_address_pools = AAZListArg(
            options=["load-balancer-backend-address-pools"],
            help="",
        )
        _element.primary = AAZBoolArg(
            options=["primary"],
            help="",
        )
        _element.private_ip_address_version = AAZStrArg(
            options=["private-ip-address-version"],
            help="",
            enum={"IPv4": "IPv4", "IPv6": "IPv6"},
        )
        _element.public_ip_address_configuration = AAZObjectArg(
            options=["public-ip-address-configuration"],
            help="",
        )
        _element.subnet = AAZObjectArg(
            options=["subnet"],
            help="",
        )
        cls._build_args_sub_resource_update(_element.subnet)

        application_gateway_backend_address_pools = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.application_gateway_backend_address_pools
        application_gateway_backend_address_pools.Element = AAZObjectArg()
        cls._build_args_sub_resource_update(application_gateway_backend_address_pools.Element)

        application_security_groups = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.application_security_groups
        application_security_groups.Element = AAZObjectArg()
        cls._build_args_sub_resource_update(application_security_groups.Element)

        load_balancer_backend_address_pools = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.load_balancer_backend_address_pools
        load_balancer_backend_address_pools.Element = AAZObjectArg()
        cls._build_args_sub_resource_update(load_balancer_backend_address_pools.Element)

        public_ip_address_configuration = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.public_ip_address_configuration
        public_ip_address_configuration.name = AAZStrArg(
            options=["name"],
            help="",
            required=True,
        )
        public_ip_address_configuration.delete_option = AAZStrArg(
            options=["delete-option"],
            help="",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        public_ip_address_configuration.dns_settings = AAZObjectArg(
            options=["dns-settings"],
            help="",
        )
        public_ip_address_configuration.idle_timeout_in_minutes = AAZIntArg(
            options=["idle-timeout-in-minutes"],
            help="",
        )
        public_ip_address_configuration.ip_tags = AAZListArg(
            options=["ip-tags"],
            help="",
        )
        public_ip_address_configuration.public_ip_address_version = AAZStrArg(
            options=["public-ip-address-version"],
            help="",
            enum={"IPv4": "IPv4", "IPv6": "IPv6"},
        )
        public_ip_address_configuration.public_ip_allocation_method = AAZStrArg(
            options=["public-ip-allocation-method"],
            help="",
            enum={"Dynamic": "Dynamic", "Static": "Static"},
        )
        public_ip_address_configuration.public_ip_prefix = AAZObjectArg(
            options=["public-ip-prefix"],
            help="",
        )
        cls._build_args_sub_resource_update(public_ip_address_configuration.public_ip_prefix)
        public_ip_address_configuration.sku = AAZObjectArg(
            options=["sku"],
            help="",
        )

        dns_settings = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.public_ip_address_configuration.dns_settings
        dns_settings.domain_name_label = AAZStrArg(
            options=["domain-name-label"],
            help="",
            required=True,
        )
        dns_settings.domain_name_label_scope = AAZStrArg(
            options=["domain-name-label-scope"],
            help="",
            enum={"NoReuse": "NoReuse", "ResourceGroupReuse": "ResourceGroupReuse", "SubscriptionReuse": "SubscriptionReuse", "TenantReuse": "TenantReuse"},
        )

        ip_tags = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.public_ip_address_configuration.ip_tags
        ip_tags.Element = AAZObjectArg()

        _element = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.public_ip_address_configuration.ip_tags.Element
        _element.ip_tag_type = AAZStrArg(
            options=["ip-tag-type"],
            help="",
        )
        _element.tag = AAZStrArg(
            options=["tag"],
            help="",
        )

        sku = cls._args_schema.network_profile.network_interface_configurations.Element.ip_configurations.Element.public_ip_address_configuration.sku
        sku.name = AAZStrArg(
            options=["name"],
            help="",
            enum={"Basic": "Basic", "Standard": "Standard"},
        )
        sku.tier = AAZStrArg(
            options=["tier"],
            help="",
            enum={"Global": "Global", "Regional": "Regional"},
        )

        network_interfaces = cls._args_schema.network_profile.network_interfaces
        network_interfaces.Element = AAZObjectArg()

        _element = cls._args_schema.network_profile.network_interfaces.Element
        _element.id = AAZStrArg(
            options=["id"],
            help="",
        )
        _element.delete_option = AAZStrArg(
            options=["delete-option"],
            help="",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        _element.primary = AAZBoolArg(
            options=["primary"],
            help="",
        )

        os_profile = cls._args_schema.os_profile
        os_profile.admin_password = AAZStrArg(
            options=["admin-password"],
            help="",
        )
        os_profile.admin_username = AAZStrArg(
            options=["admin-username"],
            help="",
        )
        os_profile.allow_extension_operations = AAZBoolArg(
            options=["allow-extension-operations"],
            help="",
        )
        os_profile.computer_name = AAZStrArg(
            options=["computer-name"],
            help="",
        )
        os_profile.custom_data = AAZStrArg(
            options=["custom-data"],
            help="",
        )
        os_profile.linux_configuration = AAZObjectArg(
            options=["linux-configuration"],
            help="",
        )
        os_profile.require_guest_provision_signal = AAZBoolArg(
            options=["require-guest-provision-signal"],
            help="",
        )
        os_profile.secrets = AAZListArg(
            options=["secrets"],
            help="",
        )
        os_profile.windows_configuration = AAZObjectArg(
            options=["windows-configuration"],
            help="",
        )

        linux_configuration = cls._args_schema.os_profile.linux_configuration
        linux_configuration.disable_password_authentication = AAZBoolArg(
            options=["disable-password-authentication"],
            help="",
        )
        linux_configuration.enable_vm_agent_platform_updates = AAZBoolArg(
            options=["enable-vm-agent-platform-updates"],
            help="",
        )
        linux_configuration.patch_settings = AAZObjectArg(
            options=["patch-settings"],
            help="",
        )
        linux_configuration.provision_vm_agent = AAZBoolArg(
            options=["provision-vm-agent"],
            help="",
        )
        linux_configuration.ssh = AAZObjectArg(
            options=["ssh"],
            help="",
        )

        patch_settings = cls._args_schema.os_profile.linux_configuration.patch_settings
        patch_settings.assessment_mode = AAZStrArg(
            options=["assessment-mode"],
            help="",
            enum={"AutomaticByPlatform": "AutomaticByPlatform", "ImageDefault": "ImageDefault"},
        )
        patch_settings.automatic_by_platform_settings = AAZObjectArg(
            options=["automatic-by-platform-settings"],
            help="",
        )
        patch_settings.patch_mode = AAZStrArg(
            options=["patch-mode"],
            help="",
            enum={"AutomaticByPlatform": "AutomaticByPlatform", "ImageDefault": "ImageDefault"},
        )

        automatic_by_platform_settings = cls._args_schema.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
        automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolArg(
            options=["bypass-platform-safety-checks-on-user-schedule"],
            help="",
        )
        automatic_by_platform_settings.reboot_setting = AAZStrArg(
            options=["reboot-setting"],
            help="",
            enum={"Always": "Always", "IfRequired": "IfRequired", "Never": "Never", "Unknown": "Unknown"},
        )

        ssh = cls._args_schema.os_profile.linux_configuration.ssh
        ssh.public_keys = AAZListArg(
            options=["public-keys"],
            help="",
        )

        public_keys = cls._args_schema.os_profile.linux_configuration.ssh.public_keys
        public_keys.Element = AAZObjectArg()

        _element = cls._args_schema.os_profile.linux_configuration.ssh.public_keys.Element
        _element.key_data = AAZStrArg(
            options=["key-data"],
            help="",
        )
        _element.path = AAZStrArg(
            options=["path"],
            help="",
        )

        secrets = cls._args_schema.os_profile.secrets
        secrets.Element = AAZObjectArg()

        _element = cls._args_schema.os_profile.secrets.Element
        _element.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="",
        )
        cls._build_args_sub_resource_update(_element.source_vault)
        _element.vault_certificates = AAZListArg(
            options=["vault-certificates"],
            help="",
        )

        vault_certificates = cls._args_schema.os_profile.secrets.Element.vault_certificates
        vault_certificates.Element = AAZObjectArg()

        _element = cls._args_schema.os_profile.secrets.Element.vault_certificates.Element
        _element.certificate_store = AAZStrArg(
            options=["certificate-store"],
            help="",
        )
        _element.certificate_url = AAZStrArg(
            options=["certificate-url"],
            help="",
        )

        windows_configuration = cls._args_schema.os_profile.windows_configuration
        windows_configuration.additional_unattend_content = AAZListArg(
            options=["additional-unattend-content"],
            help="",
        )
        windows_configuration.enable_automatic_updates = AAZBoolArg(
            options=["enable-automatic-updates"],
            help="",
        )
        windows_configuration.patch_settings = AAZObjectArg(
            options=["patch-settings"],
            help="",
        )
        windows_configuration.provision_vm_agent = AAZBoolArg(
            options=["provision-vm-agent"],
            help="",
        )
        windows_configuration.time_zone = AAZStrArg(
            options=["time-zone"],
            help="",
        )
        windows_configuration.win_rm = AAZObjectArg(
            options=["win-rm"],
            help="",
        )

        additional_unattend_content = cls._args_schema.os_profile.windows_configuration.additional_unattend_content
        additional_unattend_content.Element = AAZObjectArg()

        _element = cls._args_schema.os_profile.windows_configuration.additional_unattend_content.Element
        _element.component_name = AAZStrArg(
            options=["component-name"],
            help="",
            enum={"Microsoft-Windows-Shell-Setup": "Microsoft-Windows-Shell-Setup"},
        )
        _element.content = AAZStrArg(
            options=["content"],
            help="",
        )
        _element.pass_name = AAZStrArg(
            options=["pass-name"],
            help="",
            enum={"OobeSystem": "OobeSystem"},
        )
        _element.setting_name = AAZStrArg(
            options=["setting-name"],
            help="",
            enum={"AutoLogon": "AutoLogon", "FirstLogonCommands": "FirstLogonCommands"},
        )

        patch_settings = cls._args_schema.os_profile.windows_configuration.patch_settings
        patch_settings.assessment_mode = AAZStrArg(
            options=["assessment-mode"],
            help="",
            enum={"AutomaticByPlatform": "AutomaticByPlatform", "ImageDefault": "ImageDefault"},
        )
        patch_settings.automatic_by_platform_settings = AAZObjectArg(
            options=["automatic-by-platform-settings"],
            help="",
        )
        patch_settings.enable_hotpatching = AAZBoolArg(
            options=["enable-hotpatching"],
            help="",
        )
        patch_settings.patch_mode = AAZStrArg(
            options=["patch-mode"],
            help="",
            enum={"AutomaticByOS": "AutomaticByOS", "AutomaticByPlatform": "AutomaticByPlatform", "Manual": "Manual"},
        )

        automatic_by_platform_settings = cls._args_schema.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
        automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolArg(
            options=["bypass-platform-safety-checks-on-user-schedule"],
            help="",
        )
        automatic_by_platform_settings.reboot_setting = AAZStrArg(
            options=["reboot-setting"],
            help="",
            enum={"Always": "Always", "IfRequired": "IfRequired", "Never": "Never", "Unknown": "Unknown"},
        )

        win_rm = cls._args_schema.os_profile.windows_configuration.win_rm
        win_rm.listeners = AAZListArg(
            options=["listeners"],
            help="",
        )

        listeners = cls._args_schema.os_profile.windows_configuration.win_rm.listeners
        listeners.Element = AAZObjectArg()

        _element = cls._args_schema.os_profile.windows_configuration.win_rm.listeners.Element
        _element.certificate_url = AAZStrArg(
            options=["certificate-url"],
            help="",
        )
        _element.protocol = AAZStrArg(
            options=["protocol"],
            help="",
            enum={"Http": "Http", "Https": "Https"},
        )

        scheduled_events_policy = cls._args_schema.scheduled_events_policy
        scheduled_events_policy.scheduled_events_additional_publishing_targets = AAZObjectArg(
            options=["scheduled-events-additional-publishing-targets"],
            help="",
        )
        scheduled_events_policy.user_initiated_reboot = AAZObjectArg(
            options=["user-initiated-reboot"],
            help="",
        )
        scheduled_events_policy.user_initiated_redeploy = AAZObjectArg(
            options=["user-initiated-redeploy"],
            help="",
        )

        scheduled_events_additional_publishing_targets = cls._args_schema.scheduled_events_policy.scheduled_events_additional_publishing_targets
        scheduled_events_additional_publishing_targets.event_grid_and_resource_graph = AAZObjectArg(
            options=["event-grid-and-resource-graph"],
            help="",
        )

        event_grid_and_resource_graph = cls._args_schema.scheduled_events_policy.scheduled_events_additional_publishing_targets.event_grid_and_resource_graph
        event_grid_and_resource_graph.enable = AAZBoolArg(
            options=["enable"],
            help="",
        )

        user_initiated_reboot = cls._args_schema.scheduled_events_policy.user_initiated_reboot
        user_initiated_reboot.automatically_approve = AAZBoolArg(
            options=["automatically-approve"],
            help="",
        )

        user_initiated_redeploy = cls._args_schema.scheduled_events_policy.user_initiated_redeploy
        user_initiated_redeploy.automatically_approve = AAZBoolArg(
            options=["automatically-approve"],
            help="",
        )

        scheduled_events_profile = cls._args_schema.scheduled_events_profile
        scheduled_events_profile.os_image_notification_profile = AAZObjectArg(
            options=["os-image-notification-profile"],
            help="",
        )
        scheduled_events_profile.terminate_notification_profile = AAZObjectArg(
            options=["terminate-notification-profile"],
            help="",
        )

        os_image_notification_profile = cls._args_schema.scheduled_events_profile.os_image_notification_profile
        os_image_notification_profile.enable = AAZBoolArg(
            options=["enable"],
            help="",
        )
        os_image_notification_profile.not_before_timeout = AAZStrArg(
            options=["not-before-timeout"],
            help="",
        )

        terminate_notification_profile = cls._args_schema.scheduled_events_profile.terminate_notification_profile
        terminate_notification_profile.enable = AAZBoolArg(
            options=["enable"],
            help="",
        )
        terminate_notification_profile.not_before_timeout = AAZStrArg(
            options=["not-before-timeout"],
            help="",
        )

        security_profile = cls._args_schema.security_profile
        security_profile.encryption_at_host = AAZBoolArg(
            options=["encryption-at-host"],
            help="",
        )
        security_profile.encryption_identity = AAZObjectArg(
            options=["encryption-identity"],
            help="",
        )
        security_profile.proxy_agent_settings = AAZObjectArg(
            options=["proxy-agent-settings"],
            help="",
        )
        security_profile.security_type = AAZStrArg(
            options=["security-type"],
            help="",
            enum={"ConfidentialVM": "ConfidentialVM", "TrustedLaunch": "TrustedLaunch"},
        )
        security_profile.uefi_settings = AAZObjectArg(
            options=["uefi-settings"],
            help="",
        )

        encryption_identity = cls._args_schema.security_profile.encryption_identity
        encryption_identity.user_assigned_identity_resource_id = AAZStrArg(
            options=["user-assigned-identity-resource-id"],
            help="",
        )

        proxy_agent_settings = cls._args_schema.security_profile.proxy_agent_settings
        proxy_agent_settings.enabled = AAZBoolArg(
            options=["enabled"],
            help="",
        )
        proxy_agent_settings.imds = AAZObjectArg(
            options=["imds"],
            help="",
        )
        cls._build_args_host_endpoint_settings_update(proxy_agent_settings.imds)
        proxy_agent_settings.key_incarnation_id = AAZIntArg(
            options=["key-incarnation-id"],
            help="",
        )
        proxy_agent_settings.mode = AAZStrArg(
            options=["mode"],
            help="",
            enum={"Audit": "Audit", "Enforce": "Enforce"},
        )
        proxy_agent_settings.wire_server = AAZObjectArg(
            options=["wire-server"],
            help="",
        )
        cls._build_args_host_endpoint_settings_update(proxy_agent_settings.wire_server)

        uefi_settings = cls._args_schema.security_profile.uefi_settings
        uefi_settings.secure_boot_enabled = AAZBoolArg(
            options=["secure-boot-enabled"],
            help="",
        )
        uefi_settings.v_tpm_enabled = AAZBoolArg(
            options=["v-tpm-enabled"],
            help="",
        )

        storage_profile = cls._args_schema.storage_profile
        storage_profile.align_regional_disks_to_vm_zone = AAZBoolArg(
            options=["align-regional-disks-to-vm-zone"],
            help="",
        )
        storage_profile.data_disks = AAZListArg(
            options=["data-disks"],
            help="",
        )
        storage_profile.disk_controller_type = AAZStrArg(
            options=["disk-controller-type"],
            help="",
            enum={"NVMe": "NVMe", "SCSI": "SCSI"},
        )
        storage_profile.image_reference = AAZObjectArg(
            options=["image-reference"],
            help="",
        )
        storage_profile.os_disk = AAZObjectArg(
            options=["os-disk"],
            help="",
        )

        data_disks = cls._args_schema.storage_profile.data_disks
        data_disks.Element = AAZObjectArg()

        _element = cls._args_schema.storage_profile.data_disks.Element
        _element.caching = AAZStrArg(
            options=["caching"],
            help="",
            enum={"None": "None", "ReadOnly": "ReadOnly", "ReadWrite": "ReadWrite"},
        )
        _element.create_option = AAZStrArg(
            options=["create-option"],
            help="",
            required=True,
            enum={"Attach": "Attach", "Copy": "Copy", "Empty": "Empty", "FromImage": "FromImage", "Restore": "Restore"},
        )
        _element.delete_option = AAZStrArg(
            options=["delete-option"],
            help="",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        _element.detach_option = AAZStrArg(
            options=["detach-option"],
            help="",
            enum={"ForceDetach": "ForceDetach"},
        )
        _element.disk_size_gb = AAZIntArg(
            options=["disk-size-gb"],
            help="",
        )
        _element.image = AAZObjectArg(
            options=["image"],
            help="",
        )
        cls._build_args_virtual_hard_disk_update(_element.image)
        _element.lun = AAZIntArg(
            options=["lun"],
            help="",
            required=True,
        )
        _element.managed_disk = AAZObjectArg(
            options=["managed-disk"],
            help="",
        )
        cls._build_args_managed_disk_parameters_update(_element.managed_disk)
        _element.name = AAZStrArg(
            options=["name"],
            help="",
        )
        _element.source_resource = AAZObjectArg(
            options=["source-resource"],
            help="",
        )
        _element.to_be_detached = AAZBoolArg(
            options=["to-be-detached"],
            help="",
        )
        _element.vhd = AAZObjectArg(
            options=["vhd"],
            help="",
        )
        cls._build_args_virtual_hard_disk_update(_element.vhd)
        _element.write_accelerator_enabled = AAZBoolArg(
            options=["write-accelerator-enabled"],
            help="",
        )

        source_resource = cls._args_schema.storage_profile.data_disks.Element.source_resource
        source_resource.id = AAZStrArg(
            options=["id"],
            help="",
        )

        image_reference = cls._args_schema.storage_profile.image_reference
        image_reference.community_gallery_image_id = AAZStrArg(
            options=["community-gallery-image-id"],
            help="",
        )
        image_reference.id = AAZStrArg(
            options=["id"],
            help="",
        )
        image_reference.offer = AAZStrArg(
            options=["offer"],
            help="",
        )
        image_reference.publisher = AAZStrArg(
            options=["publisher"],
            help="",
        )
        image_reference.shared_gallery_image_id = AAZStrArg(
            options=["shared-gallery-image-id"],
            help="",
        )
        image_reference.sku = AAZStrArg(
            options=["sku"],
            help="",
        )
        image_reference.version = AAZStrArg(
            options=["version"],
            help="",
        )

        os_disk = cls._args_schema.storage_profile.os_disk
        os_disk.caching = AAZStrArg(
            options=["caching"],
            help="",
            enum={"None": "None", "ReadOnly": "ReadOnly", "ReadWrite": "ReadWrite"},
        )
        os_disk.create_option = AAZStrArg(
            options=["create-option"],
            help="",
            required=True,
            enum={"Attach": "Attach", "Copy": "Copy", "Empty": "Empty", "FromImage": "FromImage", "Restore": "Restore"},
        )
        os_disk.delete_option = AAZStrArg(
            options=["delete-option"],
            help="",
            enum={"Delete": "Delete", "Detach": "Detach"},
        )
        os_disk.diff_disk_settings = AAZObjectArg(
            options=["diff-disk-settings"],
            help="",
        )
        os_disk.disk_size_gb = AAZIntArg(
            options=["disk-size-gb"],
            help="",
        )
        os_disk.encryption_settings = AAZObjectArg(
            options=["encryption-settings"],
            help="",
        )
        os_disk.image = AAZObjectArg(
            options=["image"],
            help="",
        )
        cls._build_args_virtual_hard_disk_update(os_disk.image)
        os_disk.managed_disk = AAZObjectArg(
            options=["managed-disk"],
            help="",
        )
        cls._build_args_managed_disk_parameters_update(os_disk.managed_disk)
        os_disk.name = AAZStrArg(
            options=["name"],
            help="",
        )
        os_disk.os_type = AAZStrArg(
            options=["os-type"],
            help="",
            enum={"Linux": "Linux", "Windows": "Windows"},
        )
        os_disk.vhd = AAZObjectArg(
            options=["vhd"],
            help="",
        )
        cls._build_args_virtual_hard_disk_update(os_disk.vhd)
        os_disk.write_accelerator_enabled = AAZBoolArg(
            options=["write-accelerator-enabled"],
            help="",
        )

        diff_disk_settings = cls._args_schema.storage_profile.os_disk.diff_disk_settings
        diff_disk_settings.option = AAZStrArg(
            options=["option"],
            help="",
            enum={"Local": "Local"},
        )
        diff_disk_settings.placement = AAZStrArg(
            options=["placement"],
            help="",
            enum={"CacheDisk": "CacheDisk", "NvmeDisk": "NvmeDisk", "ResourceDisk": "ResourceDisk"},
        )

        encryption_settings = cls._args_schema.storage_profile.os_disk.encryption_settings
        encryption_settings.disk_encryption_key = AAZObjectArg(
            options=["disk-encryption-key"],
            help="",
        )
        encryption_settings.enabled = AAZBoolArg(
            options=["enabled"],
            help="",
        )
        encryption_settings.key_encryption_key = AAZObjectArg(
            options=["key-encryption-key"],
            help="",
        )

        disk_encryption_key = cls._args_schema.storage_profile.os_disk.encryption_settings.disk_encryption_key
        disk_encryption_key.secret_url = AAZStrArg(
            options=["secret-url"],
            help="",
            required=True,
        )
        disk_encryption_key.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="",
            required=True,
        )
        cls._build_args_sub_resource_update(disk_encryption_key.source_vault)

        key_encryption_key = cls._args_schema.storage_profile.os_disk.encryption_settings.key_encryption_key
        key_encryption_key.key_url = AAZStrArg(
            options=["key-url"],
            help="",
            required=True,
        )
        key_encryption_key.source_vault = AAZObjectArg(
            options=["source-vault"],
            help="",
            required=True,
        )
        cls._build_args_sub_resource_update(key_encryption_key.source_vault)
        return cls._args_schema

    _args_disk_encryption_set_parameters_update = None

    @classmethod
    def _build_args_disk_encryption_set_parameters_update(cls, _schema):
        if cls._args_disk_encryption_set_parameters_update is not None:
            _schema.id = cls._args_disk_encryption_set_parameters_update.id
            return

        cls._args_disk_encryption_set_parameters_update = AAZObjectArg()

        disk_encryption_set_parameters_update = cls._args_disk_encryption_set_parameters_update
        disk_encryption_set_parameters_update.id = AAZStrArg(
            options=["id"],
            help="",
        )

        _schema.id = cls._args_disk_encryption_set_parameters_update.id

    _args_host_endpoint_settings_update = None

    @classmethod
    def _build_args_host_endpoint_settings_update(cls, _schema):
        if cls._args_host_endpoint_settings_update is not None:
            _schema.in_vm_access_control_profile_reference_id = cls._args_host_endpoint_settings_update.in_vm_access_control_profile_reference_id
            _schema.mode = cls._args_host_endpoint_settings_update.mode
            return

        cls._args_host_endpoint_settings_update = AAZObjectArg()

        host_endpoint_settings_update = cls._args_host_endpoint_settings_update
        host_endpoint_settings_update.in_vm_access_control_profile_reference_id = AAZStrArg(
            options=["in-vm-access-control-profile-reference-id"],
            help="",
        )
        host_endpoint_settings_update.mode = AAZStrArg(
            options=["mode"],
            help="",
            enum={"Audit": "Audit", "Disabled": "Disabled", "Enforce": "Enforce"},
        )

        _schema.in_vm_access_control_profile_reference_id = cls._args_host_endpoint_settings_update.in_vm_access_control_profile_reference_id
        _schema.mode = cls._args_host_endpoint_settings_update.mode

    _args_managed_disk_parameters_update = None

    @classmethod
    def _build_args_managed_disk_parameters_update(cls, _schema):
        if cls._args_managed_disk_parameters_update is not None:
            _schema.disk_encryption_set = cls._args_managed_disk_parameters_update.disk_encryption_set
            _schema.id = cls._args_managed_disk_parameters_update.id
            _schema.security_profile = cls._args_managed_disk_parameters_update.security_profile
            _schema.storage_account_type = cls._args_managed_disk_parameters_update.storage_account_type
            return

        cls._args_managed_disk_parameters_update = AAZObjectArg()

        managed_disk_parameters_update = cls._args_managed_disk_parameters_update
        managed_disk_parameters_update.disk_encryption_set = AAZObjectArg(
            options=["disk-encryption-set"],
            help="",
        )
        cls._build_args_disk_encryption_set_parameters_update(managed_disk_parameters_update.disk_encryption_set)
        managed_disk_parameters_update.id = AAZStrArg(
            options=["id"],
            help="",
        )
        managed_disk_parameters_update.security_profile = AAZObjectArg(
            options=["security-profile"],
            help="",
        )
        managed_disk_parameters_update.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="",
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Premium_ZRS": "Premium_ZRS", "StandardSSD_LRS": "StandardSSD_LRS", "StandardSSD_ZRS": "StandardSSD_ZRS", "Standard_LRS": "Standard_LRS", "UltraSSD_LRS": "UltraSSD_LRS"},
        )

        security_profile = cls._args_managed_disk_parameters_update.security_profile
        security_profile.disk_encryption_set = AAZObjectArg(
            options=["disk-encryption-set"],
            help="",
        )
        cls._build_args_disk_encryption_set_parameters_update(security_profile.disk_encryption_set)
        security_profile.security_encryption_type = AAZStrArg(
            options=["security-encryption-type"],
            help="",
            enum={"DiskWithVMGuestState": "DiskWithVMGuestState", "NonPersistedTPM": "NonPersistedTPM", "VMGuestStateOnly": "VMGuestStateOnly"},
        )

        _schema.disk_encryption_set = cls._args_managed_disk_parameters_update.disk_encryption_set
        _schema.id = cls._args_managed_disk_parameters_update.id
        _schema.security_profile = cls._args_managed_disk_parameters_update.security_profile
        _schema.storage_account_type = cls._args_managed_disk_parameters_update.storage_account_type

    _args_sub_resource_update = None

    @classmethod
    def _build_args_sub_resource_update(cls, _schema):
        if cls._args_sub_resource_update is not None:
            _schema.id = cls._args_sub_resource_update.id
            return

        cls._args_sub_resource_update = AAZObjectArg()

        sub_resource_update = cls._args_sub_resource_update
        sub_resource_update.id = AAZStrArg(
            options=["id"],
            help="",
        )

        _schema.id = cls._args_sub_resource_update.id

    _args_virtual_hard_disk_update = None

    @classmethod
    def _build_args_virtual_hard_disk_update(cls, _schema):
        if cls._args_virtual_hard_disk_update is not None:
            _schema.uri = cls._args_virtual_hard_disk_update.uri
            return

        cls._args_virtual_hard_disk_update = AAZObjectArg()

        virtual_hard_disk_update = cls._args_virtual_hard_disk_update
        virtual_hard_disk_update.uri = AAZStrArg(
            options=["uri"],
            help="",
        )

        _schema.uri = cls._args_virtual_hard_disk_update.uri

    def _execute_operations(self):
        self.pre_operations()
        yield self.VirtualMachinesUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualMachinesUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachines/{vmName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PATCH"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmName", self.ctx.args.vm_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-11-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "If-Match", self.ctx.args.if_match,
                ),
                **self.serialize_header_param(
                    "If-None-Match", self.ctx.args.if_none_match,
                ),
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("identity", AAZIdentityObjectType)
            _builder.set_prop("plan", AAZObjectType, ".plan")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zones")

            identity = _builder.get(".identity")
            if identity is not None:
                identity.set_prop("userAssigned", AAZListType, ".mi_user_assigned", typ_kwargs={"flags": {"action": "create"}})
                identity.set_prop("systemAssigned", AAZStrType, ".mi_system_assigned", typ_kwargs={"flags": {"action": "create"}})

            user_assigned = _builder.get(".identity.userAssigned")
            if user_assigned is not None:
                user_assigned.set_elements(AAZStrType, ".")

            plan = _builder.get(".plan")
            if plan is not None:
                plan.set_prop("name", AAZStrType, ".name")
                plan.set_prop("product", AAZStrType, ".product")
                plan.set_prop("promotionCode", AAZStrType, ".promotion_code")
                plan.set_prop("publisher", AAZStrType, ".publisher")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("additionalCapabilities", AAZObjectType, ".additional_capabilities")
                properties.set_prop("applicationProfile", AAZObjectType, ".application_profile")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("availabilitySet", AAZObjectType, ".availability_set"))
                properties.set_prop("billingProfile", AAZObjectType, ".billing_profile")
                properties.set_prop("capacityReservation", AAZObjectType, ".capacity_reservation")
                properties.set_prop("diagnosticsProfile", AAZObjectType, ".diagnostics_profile")
                properties.set_prop("evictionPolicy", AAZStrType, ".eviction_policy")
                properties.set_prop("extensionsTimeBudget", AAZStrType, ".extensions_time_budget")
                properties.set_prop("hardwareProfile", AAZObjectType, ".hardware_profile")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("host", AAZObjectType, ".host"))
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("hostGroup", AAZObjectType, ".host_group"))
                properties.set_prop("licenseType", AAZStrType, ".license_type")
                properties.set_prop("networkProfile", AAZObjectType, ".network_profile")
                properties.set_prop("osProfile", AAZObjectType, ".os_profile")
                properties.set_prop("platformFaultDomain", AAZIntType, ".platform_fault_domain")
                properties.set_prop("priority", AAZStrType, ".priority")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("proximityPlacementGroup", AAZObjectType, ".proximity_placement_group"))
                properties.set_prop("scheduledEventsPolicy", AAZObjectType, ".scheduled_events_policy")
                properties.set_prop("scheduledEventsProfile", AAZObjectType, ".scheduled_events_profile")
                properties.set_prop("securityProfile", AAZObjectType, ".security_profile")
                properties.set_prop("storageProfile", AAZObjectType, ".storage_profile")
                properties.set_prop("userData", AAZStrType, ".user_data")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("virtualMachineScaleSet", AAZObjectType, ".virtual_machine_scale_set"))

            additional_capabilities = _builder.get(".properties.additionalCapabilities")
            if additional_capabilities is not None:
                additional_capabilities.set_prop("hibernationEnabled", AAZBoolType, ".hibernation_enabled")
                additional_capabilities.set_prop("ultraSSDEnabled", AAZBoolType, ".ultra_ssd_enabled")

            application_profile = _builder.get(".properties.applicationProfile")
            if application_profile is not None:
                application_profile.set_prop("galleryApplications", AAZListType, ".gallery_applications")

            gallery_applications = _builder.get(".properties.applicationProfile.galleryApplications")
            if gallery_applications is not None:
                gallery_applications.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.applicationProfile.galleryApplications[]")
            if _elements is not None:
                _elements.set_prop("configurationReference", AAZStrType, ".configuration_reference")
                _elements.set_prop("enableAutomaticUpgrade", AAZBoolType, ".enable_automatic_upgrade")
                _elements.set_prop("order", AAZIntType, ".order")
                _elements.set_prop("packageReferenceId", AAZStrType, ".package_reference_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("tags", AAZStrType, ".tags")
                _elements.set_prop("treatFailureAsDeploymentFailure", AAZBoolType, ".treat_failure_as_deployment_failure")

            billing_profile = _builder.get(".properties.billingProfile")
            if billing_profile is not None:
                billing_profile.set_prop("maxPrice", AAZFloatType, ".max_price")

            capacity_reservation = _builder.get(".properties.capacityReservation")
            if capacity_reservation is not None:
                _PatchHelper._build_schema_sub_resource_update(capacity_reservation.set_prop("capacityReservationGroup", AAZObjectType, ".capacity_reservation_group"))

            diagnostics_profile = _builder.get(".properties.diagnosticsProfile")
            if diagnostics_profile is not None:
                diagnostics_profile.set_prop("bootDiagnostics", AAZObjectType, ".boot_diagnostics")

            boot_diagnostics = _builder.get(".properties.diagnosticsProfile.bootDiagnostics")
            if boot_diagnostics is not None:
                boot_diagnostics.set_prop("enabled", AAZBoolType, ".enabled")
                boot_diagnostics.set_prop("storageUri", AAZStrType, ".storage_uri")

            hardware_profile = _builder.get(".properties.hardwareProfile")
            if hardware_profile is not None:
                hardware_profile.set_prop("vmSize", AAZStrType, ".vm_size")
                hardware_profile.set_prop("vmSizeProperties", AAZObjectType, ".vm_size_properties")

            vm_size_properties = _builder.get(".properties.hardwareProfile.vmSizeProperties")
            if vm_size_properties is not None:
                vm_size_properties.set_prop("vCPUsAvailable", AAZIntType, ".v_cp_us_available")
                vm_size_properties.set_prop("vCPUsPerCore", AAZIntType, ".v_cp_us_per_core")

            network_profile = _builder.get(".properties.networkProfile")
            if network_profile is not None:
                network_profile.set_prop("networkApiVersion", AAZStrType, ".network_api_version")
                network_profile.set_prop("networkInterfaceConfigurations", AAZListType, ".network_interface_configurations")
                network_profile.set_prop("networkInterfaces", AAZListType, ".network_interfaces")

            network_interface_configurations = _builder.get(".properties.networkProfile.networkInterfaceConfigurations")
            if network_interface_configurations is not None:
                network_interface_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties")
            if properties is not None:
                properties.set_prop("auxiliaryMode", AAZStrType, ".auxiliary_mode")
                properties.set_prop("auxiliarySku", AAZStrType, ".auxiliary_sku")
                properties.set_prop("deleteOption", AAZStrType, ".delete_option")
                properties.set_prop("disableTcpStateTracking", AAZBoolType, ".disable_tcp_state_tracking")
                properties.set_prop("dnsSettings", AAZObjectType, ".dns_settings")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("dscpConfiguration", AAZObjectType, ".dscp_configuration"))
                properties.set_prop("enableAcceleratedNetworking", AAZBoolType, ".enable_accelerated_networking")
                properties.set_prop("enableFpga", AAZBoolType, ".enable_fpga")
                properties.set_prop("enableIPForwarding", AAZBoolType, ".enable_ip_forwarding")
                properties.set_prop("ipConfigurations", AAZListType, ".ip_configurations", typ_kwargs={"flags": {"required": True}})
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("networkSecurityGroup", AAZObjectType, ".network_security_group"))
                properties.set_prop("primary", AAZBoolType, ".primary")

            dns_settings = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.dnsSettings")
            if dns_settings is not None:
                dns_settings.set_prop("dnsServers", AAZListType, ".dns_servers")

            dns_servers = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.dnsSettings.dnsServers")
            if dns_servers is not None:
                dns_servers.set_elements(AAZStrType, ".")

            ip_configurations = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations")
            if ip_configurations is not None:
                ip_configurations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties")
            if properties is not None:
                properties.set_prop("applicationGatewayBackendAddressPools", AAZListType, ".application_gateway_backend_address_pools")
                properties.set_prop("applicationSecurityGroups", AAZListType, ".application_security_groups")
                properties.set_prop("loadBalancerBackendAddressPools", AAZListType, ".load_balancer_backend_address_pools")
                properties.set_prop("primary", AAZBoolType, ".primary")
                properties.set_prop("privateIPAddressVersion", AAZStrType, ".private_ip_address_version")
                properties.set_prop("publicIPAddressConfiguration", AAZObjectType, ".public_ip_address_configuration")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("subnet", AAZObjectType, ".subnet"))

            application_gateway_backend_address_pools = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.applicationGatewayBackendAddressPools")
            if application_gateway_backend_address_pools is not None:
                _PatchHelper._build_schema_sub_resource_update(application_gateway_backend_address_pools.set_elements(AAZObjectType, "."))

            application_security_groups = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.applicationSecurityGroups")
            if application_security_groups is not None:
                _PatchHelper._build_schema_sub_resource_update(application_security_groups.set_elements(AAZObjectType, "."))

            load_balancer_backend_address_pools = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.loadBalancerBackendAddressPools")
            if load_balancer_backend_address_pools is not None:
                _PatchHelper._build_schema_sub_resource_update(load_balancer_backend_address_pools.set_elements(AAZObjectType, "."))

            public_ip_address_configuration = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration")
            if public_ip_address_configuration is not None:
                public_ip_address_configuration.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                public_ip_address_configuration.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
                public_ip_address_configuration.set_prop("sku", AAZObjectType, ".sku")

            properties = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration.properties")
            if properties is not None:
                properties.set_prop("deleteOption", AAZStrType, ".delete_option")
                properties.set_prop("dnsSettings", AAZObjectType, ".dns_settings")
                properties.set_prop("idleTimeoutInMinutes", AAZIntType, ".idle_timeout_in_minutes")
                properties.set_prop("ipTags", AAZListType, ".ip_tags")
                properties.set_prop("publicIPAddressVersion", AAZStrType, ".public_ip_address_version")
                properties.set_prop("publicIPAllocationMethod", AAZStrType, ".public_ip_allocation_method")
                _PatchHelper._build_schema_sub_resource_update(properties.set_prop("publicIPPrefix", AAZObjectType, ".public_ip_prefix"))

            dns_settings = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration.properties.dnsSettings")
            if dns_settings is not None:
                dns_settings.set_prop("domainNameLabel", AAZStrType, ".domain_name_label", typ_kwargs={"flags": {"required": True}})
                dns_settings.set_prop("domainNameLabelScope", AAZStrType, ".domain_name_label_scope")

            ip_tags = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration.properties.ipTags")
            if ip_tags is not None:
                ip_tags.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration.properties.ipTags[]")
            if _elements is not None:
                _elements.set_prop("ipTagType", AAZStrType, ".ip_tag_type")
                _elements.set_prop("tag", AAZStrType, ".tag")

            sku = _builder.get(".properties.networkProfile.networkInterfaceConfigurations[].properties.ipConfigurations[].properties.publicIPAddressConfiguration.sku")
            if sku is not None:
                sku.set_prop("name", AAZStrType, ".name")
                sku.set_prop("tier", AAZStrType, ".tier")

            network_interfaces = _builder.get(".properties.networkProfile.networkInterfaces")
            if network_interfaces is not None:
                network_interfaces.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.networkProfile.networkInterfaces[]")
            if _elements is not None:
                _elements.set_prop("id", AAZStrType, ".id")
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.networkProfile.networkInterfaces[].properties")
            if properties is not None:
                properties.set_prop("deleteOption", AAZStrType, ".delete_option")
                properties.set_prop("primary", AAZBoolType, ".primary")

            os_profile = _builder.get(".properties.osProfile")
            if os_profile is not None:
                os_profile.set_prop("adminPassword", AAZStrType, ".admin_password", typ_kwargs={"flags": {"secret": True}})
                os_profile.set_prop("adminUsername", AAZStrType, ".admin_username")
                os_profile.set_prop("allowExtensionOperations", AAZBoolType, ".allow_extension_operations")
                os_profile.set_prop("computerName", AAZStrType, ".computer_name")
                os_profile.set_prop("customData", AAZStrType, ".custom_data")
                os_profile.set_prop("linuxConfiguration", AAZObjectType, ".linux_configuration")
                os_profile.set_prop("requireGuestProvisionSignal", AAZBoolType, ".require_guest_provision_signal")
                os_profile.set_prop("secrets", AAZListType, ".secrets")
                os_profile.set_prop("windowsConfiguration", AAZObjectType, ".windows_configuration")

            linux_configuration = _builder.get(".properties.osProfile.linuxConfiguration")
            if linux_configuration is not None:
                linux_configuration.set_prop("disablePasswordAuthentication", AAZBoolType, ".disable_password_authentication")
                linux_configuration.set_prop("enableVMAgentPlatformUpdates", AAZBoolType, ".enable_vm_agent_platform_updates")
                linux_configuration.set_prop("patchSettings", AAZObjectType, ".patch_settings")
                linux_configuration.set_prop("provisionVMAgent", AAZBoolType, ".provision_vm_agent")
                linux_configuration.set_prop("ssh", AAZObjectType, ".ssh")

            patch_settings = _builder.get(".properties.osProfile.linuxConfiguration.patchSettings")
            if patch_settings is not None:
                patch_settings.set_prop("assessmentMode", AAZStrType, ".assessment_mode")
                patch_settings.set_prop("automaticByPlatformSettings", AAZObjectType, ".automatic_by_platform_settings")
                patch_settings.set_prop("patchMode", AAZStrType, ".patch_mode")

            automatic_by_platform_settings = _builder.get(".properties.osProfile.linuxConfiguration.patchSettings.automaticByPlatformSettings")
            if automatic_by_platform_settings is not None:
                automatic_by_platform_settings.set_prop("bypassPlatformSafetyChecksOnUserSchedule", AAZBoolType, ".bypass_platform_safety_checks_on_user_schedule")
                automatic_by_platform_settings.set_prop("rebootSetting", AAZStrType, ".reboot_setting")

            ssh = _builder.get(".properties.osProfile.linuxConfiguration.ssh")
            if ssh is not None:
                ssh.set_prop("publicKeys", AAZListType, ".public_keys")

            public_keys = _builder.get(".properties.osProfile.linuxConfiguration.ssh.publicKeys")
            if public_keys is not None:
                public_keys.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.osProfile.linuxConfiguration.ssh.publicKeys[]")
            if _elements is not None:
                _elements.set_prop("keyData", AAZStrType, ".key_data")
                _elements.set_prop("path", AAZStrType, ".path")

            secrets = _builder.get(".properties.osProfile.secrets")
            if secrets is not None:
                secrets.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.osProfile.secrets[]")
            if _elements is not None:
                _PatchHelper._build_schema_sub_resource_update(_elements.set_prop("sourceVault", AAZObjectType, ".source_vault"))
                _elements.set_prop("vaultCertificates", AAZListType, ".vault_certificates")

            vault_certificates = _builder.get(".properties.osProfile.secrets[].vaultCertificates")
            if vault_certificates is not None:
                vault_certificates.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.osProfile.secrets[].vaultCertificates[]")
            if _elements is not None:
                _elements.set_prop("certificateStore", AAZStrType, ".certificate_store")
                _elements.set_prop("certificateUrl", AAZStrType, ".certificate_url")

            windows_configuration = _builder.get(".properties.osProfile.windowsConfiguration")
            if windows_configuration is not None:
                windows_configuration.set_prop("additionalUnattendContent", AAZListType, ".additional_unattend_content")
                windows_configuration.set_prop("enableAutomaticUpdates", AAZBoolType, ".enable_automatic_updates")
                windows_configuration.set_prop("patchSettings", AAZObjectType, ".patch_settings")
                windows_configuration.set_prop("provisionVMAgent", AAZBoolType, ".provision_vm_agent")
                windows_configuration.set_prop("timeZone", AAZStrType, ".time_zone")
                windows_configuration.set_prop("winRM", AAZObjectType, ".win_rm")

            additional_unattend_content = _builder.get(".properties.osProfile.windowsConfiguration.additionalUnattendContent")
            if additional_unattend_content is not None:
                additional_unattend_content.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.osProfile.windowsConfiguration.additionalUnattendContent[]")
            if _elements is not None:
                _elements.set_prop("componentName", AAZStrType, ".component_name")
                _elements.set_prop("content", AAZStrType, ".content")
                _elements.set_prop("passName", AAZStrType, ".pass_name")
                _elements.set_prop("settingName", AAZStrType, ".setting_name")

            patch_settings = _builder.get(".properties.osProfile.windowsConfiguration.patchSettings")
            if patch_settings is not None:
                patch_settings.set_prop("assessmentMode", AAZStrType, ".assessment_mode")
                patch_settings.set_prop("automaticByPlatformSettings", AAZObjectType, ".automatic_by_platform_settings")
                patch_settings.set_prop("enableHotpatching", AAZBoolType, ".enable_hotpatching")
                patch_settings.set_prop("patchMode", AAZStrType, ".patch_mode")

            automatic_by_platform_settings = _builder.get(".properties.osProfile.windowsConfiguration.patchSettings.automaticByPlatformSettings")
            if automatic_by_platform_settings is not None:
                automatic_by_platform_settings.set_prop("bypassPlatformSafetyChecksOnUserSchedule", AAZBoolType, ".bypass_platform_safety_checks_on_user_schedule")
                automatic_by_platform_settings.set_prop("rebootSetting", AAZStrType, ".reboot_setting")

            win_rm = _builder.get(".properties.osProfile.windowsConfiguration.winRM")
            if win_rm is not None:
                win_rm.set_prop("listeners", AAZListType, ".listeners")

            listeners = _builder.get(".properties.osProfile.windowsConfiguration.winRM.listeners")
            if listeners is not None:
                listeners.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.osProfile.windowsConfiguration.winRM.listeners[]")
            if _elements is not None:
                _elements.set_prop("certificateUrl", AAZStrType, ".certificate_url")
                _elements.set_prop("protocol", AAZStrType, ".protocol")

            scheduled_events_policy = _builder.get(".properties.scheduledEventsPolicy")
            if scheduled_events_policy is not None:
                scheduled_events_policy.set_prop("scheduledEventsAdditionalPublishingTargets", AAZObjectType, ".scheduled_events_additional_publishing_targets")
                scheduled_events_policy.set_prop("userInitiatedReboot", AAZObjectType, ".user_initiated_reboot")
                scheduled_events_policy.set_prop("userInitiatedRedeploy", AAZObjectType, ".user_initiated_redeploy")

            scheduled_events_additional_publishing_targets = _builder.get(".properties.scheduledEventsPolicy.scheduledEventsAdditionalPublishingTargets")
            if scheduled_events_additional_publishing_targets is not None:
                scheduled_events_additional_publishing_targets.set_prop("eventGridAndResourceGraph", AAZObjectType, ".event_grid_and_resource_graph")

            event_grid_and_resource_graph = _builder.get(".properties.scheduledEventsPolicy.scheduledEventsAdditionalPublishingTargets.eventGridAndResourceGraph")
            if event_grid_and_resource_graph is not None:
                event_grid_and_resource_graph.set_prop("enable", AAZBoolType, ".enable")

            user_initiated_reboot = _builder.get(".properties.scheduledEventsPolicy.userInitiatedReboot")
            if user_initiated_reboot is not None:
                user_initiated_reboot.set_prop("automaticallyApprove", AAZBoolType, ".automatically_approve")

            user_initiated_redeploy = _builder.get(".properties.scheduledEventsPolicy.userInitiatedRedeploy")
            if user_initiated_redeploy is not None:
                user_initiated_redeploy.set_prop("automaticallyApprove", AAZBoolType, ".automatically_approve")

            scheduled_events_profile = _builder.get(".properties.scheduledEventsProfile")
            if scheduled_events_profile is not None:
                scheduled_events_profile.set_prop("osImageNotificationProfile", AAZObjectType, ".os_image_notification_profile")
                scheduled_events_profile.set_prop("terminateNotificationProfile", AAZObjectType, ".terminate_notification_profile")

            os_image_notification_profile = _builder.get(".properties.scheduledEventsProfile.osImageNotificationProfile")
            if os_image_notification_profile is not None:
                os_image_notification_profile.set_prop("enable", AAZBoolType, ".enable")
                os_image_notification_profile.set_prop("notBeforeTimeout", AAZStrType, ".not_before_timeout")

            terminate_notification_profile = _builder.get(".properties.scheduledEventsProfile.terminateNotificationProfile")
            if terminate_notification_profile is not None:
                terminate_notification_profile.set_prop("enable", AAZBoolType, ".enable")
                terminate_notification_profile.set_prop("notBeforeTimeout", AAZStrType, ".not_before_timeout")

            security_profile = _builder.get(".properties.securityProfile")
            if security_profile is not None:
                security_profile.set_prop("encryptionAtHost", AAZBoolType, ".encryption_at_host")
                security_profile.set_prop("encryptionIdentity", AAZObjectType, ".encryption_identity")
                security_profile.set_prop("proxyAgentSettings", AAZObjectType, ".proxy_agent_settings")
                security_profile.set_prop("securityType", AAZStrType, ".security_type")
                security_profile.set_prop("uefiSettings", AAZObjectType, ".uefi_settings")

            encryption_identity = _builder.get(".properties.securityProfile.encryptionIdentity")
            if encryption_identity is not None:
                encryption_identity.set_prop("userAssignedIdentityResourceId", AAZStrType, ".user_assigned_identity_resource_id")

            proxy_agent_settings = _builder.get(".properties.securityProfile.proxyAgentSettings")
            if proxy_agent_settings is not None:
                proxy_agent_settings.set_prop("enabled", AAZBoolType, ".enabled")
                _PatchHelper._build_schema_host_endpoint_settings_update(proxy_agent_settings.set_prop("imds", AAZObjectType, ".imds"))
                proxy_agent_settings.set_prop("keyIncarnationId", AAZIntType, ".key_incarnation_id")
                proxy_agent_settings.set_prop("mode", AAZStrType, ".mode")
                _PatchHelper._build_schema_host_endpoint_settings_update(proxy_agent_settings.set_prop("wireServer", AAZObjectType, ".wire_server"))

            uefi_settings = _builder.get(".properties.securityProfile.uefiSettings")
            if uefi_settings is not None:
                uefi_settings.set_prop("secureBootEnabled", AAZBoolType, ".secure_boot_enabled")
                uefi_settings.set_prop("vTpmEnabled", AAZBoolType, ".v_tpm_enabled")

            storage_profile = _builder.get(".properties.storageProfile")
            if storage_profile is not None:
                storage_profile.set_prop("alignRegionalDisksToVMZone", AAZBoolType, ".align_regional_disks_to_vm_zone")
                storage_profile.set_prop("dataDisks", AAZListType, ".data_disks")
                storage_profile.set_prop("diskControllerType", AAZStrType, ".disk_controller_type")
                storage_profile.set_prop("imageReference", AAZObjectType, ".image_reference")
                storage_profile.set_prop("osDisk", AAZObjectType, ".os_disk")

            data_disks = _builder.get(".properties.storageProfile.dataDisks")
            if data_disks is not None:
                data_disks.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.storageProfile.dataDisks[]")
            if _elements is not None:
                _elements.set_prop("caching", AAZStrType, ".caching")
                _elements.set_prop("createOption", AAZStrType, ".create_option", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("deleteOption", AAZStrType, ".delete_option")
                _elements.set_prop("detachOption", AAZStrType, ".detach_option")
                _elements.set_prop("diskSizeGB", AAZIntType, ".disk_size_gb")
                _PatchHelper._build_schema_virtual_hard_disk_update(_elements.set_prop("image", AAZObjectType, ".image"))
                _elements.set_prop("lun", AAZIntType, ".lun", typ_kwargs={"flags": {"required": True}})
                _PatchHelper._build_schema_managed_disk_parameters_update(_elements.set_prop("managedDisk", AAZObjectType, ".managed_disk"))
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("sourceResource", AAZObjectType, ".source_resource")
                _elements.set_prop("toBeDetached", AAZBoolType, ".to_be_detached")
                _PatchHelper._build_schema_virtual_hard_disk_update(_elements.set_prop("vhd", AAZObjectType, ".vhd"))
                _elements.set_prop("writeAcceleratorEnabled", AAZBoolType, ".write_accelerator_enabled")

            source_resource = _builder.get(".properties.storageProfile.dataDisks[].sourceResource")
            if source_resource is not None:
                source_resource.set_prop("id", AAZStrType, ".id")

            image_reference = _builder.get(".properties.storageProfile.imageReference")
            if image_reference is not None:
                image_reference.set_prop("communityGalleryImageId", AAZStrType, ".community_gallery_image_id")
                image_reference.set_prop("id", AAZStrType, ".id")
                image_reference.set_prop("offer", AAZStrType, ".offer")
                image_reference.set_prop("publisher", AAZStrType, ".publisher")
                image_reference.set_prop("sharedGalleryImageId", AAZStrType, ".shared_gallery_image_id")
                image_reference.set_prop("sku", AAZStrType, ".sku")
                image_reference.set_prop("version", AAZStrType, ".version")

            os_disk = _builder.get(".properties.storageProfile.osDisk")
            if os_disk is not None:
                os_disk.set_prop("caching", AAZStrType, ".caching")
                os_disk.set_prop("createOption", AAZStrType, ".create_option", typ_kwargs={"flags": {"required": True}})
                os_disk.set_prop("deleteOption", AAZStrType, ".delete_option")
                os_disk.set_prop("diffDiskSettings", AAZObjectType, ".diff_disk_settings")
                os_disk.set_prop("diskSizeGB", AAZIntType, ".disk_size_gb")
                os_disk.set_prop("encryptionSettings", AAZObjectType, ".encryption_settings")
                _PatchHelper._build_schema_virtual_hard_disk_update(os_disk.set_prop("image", AAZObjectType, ".image"))
                _PatchHelper._build_schema_managed_disk_parameters_update(os_disk.set_prop("managedDisk", AAZObjectType, ".managed_disk"))
                os_disk.set_prop("name", AAZStrType, ".name")
                os_disk.set_prop("osType", AAZStrType, ".os_type")
                _PatchHelper._build_schema_virtual_hard_disk_update(os_disk.set_prop("vhd", AAZObjectType, ".vhd"))
                os_disk.set_prop("writeAcceleratorEnabled", AAZBoolType, ".write_accelerator_enabled")

            diff_disk_settings = _builder.get(".properties.storageProfile.osDisk.diffDiskSettings")
            if diff_disk_settings is not None:
                diff_disk_settings.set_prop("option", AAZStrType, ".option")
                diff_disk_settings.set_prop("placement", AAZStrType, ".placement")

            encryption_settings = _builder.get(".properties.storageProfile.osDisk.encryptionSettings")
            if encryption_settings is not None:
                encryption_settings.set_prop("diskEncryptionKey", AAZObjectType, ".disk_encryption_key")
                encryption_settings.set_prop("enabled", AAZBoolType, ".enabled")
                encryption_settings.set_prop("keyEncryptionKey", AAZObjectType, ".key_encryption_key")

            disk_encryption_key = _builder.get(".properties.storageProfile.osDisk.encryptionSettings.diskEncryptionKey")
            if disk_encryption_key is not None:
                disk_encryption_key.set_prop("secretUrl", AAZStrType, ".secret_url", typ_kwargs={"flags": {"required": True}})
                _PatchHelper._build_schema_sub_resource_update(disk_encryption_key.set_prop("sourceVault", AAZObjectType, ".source_vault", typ_kwargs={"flags": {"required": True}}))

            key_encryption_key = _builder.get(".properties.storageProfile.osDisk.encryptionSettings.keyEncryptionKey")
            if key_encryption_key is not None:
                key_encryption_key.set_prop("keyUrl", AAZStrType, ".key_url", typ_kwargs={"flags": {"required": True}})
                _PatchHelper._build_schema_sub_resource_update(key_encryption_key.set_prop("sourceVault", AAZObjectType, ".source_vault", typ_kwargs={"flags": {"required": True}}))

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.extended_location = AAZObjectType(
                serialized_name="extendedLocation",
            )
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.identity = AAZIdentityObjectType()
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.managed_by = AAZStrType(
                serialized_name="managedBy",
                flags={"read_only": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.placement = AAZObjectType()
            _schema_on_200.plan = AAZObjectType()
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.resources = AAZListType(
                flags={"read_only": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.zones = AAZListType()

            extended_location = cls._schema_on_200.extended_location
            extended_location.name = AAZStrType()
            extended_location.type = AAZStrType()

            identity = cls._schema_on_200.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            placement = cls._schema_on_200.placement
            placement.exclude_zones = AAZListType(
                serialized_name="excludeZones",
            )
            placement.include_zones = AAZListType(
                serialized_name="includeZones",
            )
            placement.zone_placement_policy = AAZStrType(
                serialized_name="zonePlacementPolicy",
            )

            exclude_zones = cls._schema_on_200.placement.exclude_zones
            exclude_zones.Element = AAZStrType()

            include_zones = cls._schema_on_200.placement.include_zones
            include_zones.Element = AAZStrType()

            plan = cls._schema_on_200.plan
            plan.name = AAZStrType()
            plan.product = AAZStrType()
            plan.promotion_code = AAZStrType(
                serialized_name="promotionCode",
            )
            plan.publisher = AAZStrType()

            properties = cls._schema_on_200.properties
            properties.additional_capabilities = AAZObjectType(
                serialized_name="additionalCapabilities",
            )
            properties.application_profile = AAZObjectType(
                serialized_name="applicationProfile",
            )
            properties.availability_set = AAZObjectType(
                serialized_name="availabilitySet",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.availability_set)
            properties.billing_profile = AAZObjectType(
                serialized_name="billingProfile",
            )
            properties.capacity_reservation = AAZObjectType(
                serialized_name="capacityReservation",
            )
            properties.diagnostics_profile = AAZObjectType(
                serialized_name="diagnosticsProfile",
            )
            properties.eviction_policy = AAZStrType(
                serialized_name="evictionPolicy",
            )
            properties.extensions_time_budget = AAZStrType(
                serialized_name="extensionsTimeBudget",
            )
            properties.hardware_profile = AAZObjectType(
                serialized_name="hardwareProfile",
            )
            properties.host = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(properties.host)
            properties.host_group = AAZObjectType(
                serialized_name="hostGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.host_group)
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
                flags={"read_only": True},
            )
            properties.license_type = AAZStrType(
                serialized_name="licenseType",
            )
            properties.network_profile = AAZObjectType(
                serialized_name="networkProfile",
            )
            properties.os_profile = AAZObjectType(
                serialized_name="osProfile",
            )
            properties.platform_fault_domain = AAZIntType(
                serialized_name="platformFaultDomain",
            )
            properties.priority = AAZStrType()
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.proximity_placement_group = AAZObjectType(
                serialized_name="proximityPlacementGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.proximity_placement_group)
            properties.scheduled_events_policy = AAZObjectType(
                serialized_name="scheduledEventsPolicy",
            )
            properties.scheduled_events_profile = AAZObjectType(
                serialized_name="scheduledEventsProfile",
            )
            properties.security_profile = AAZObjectType(
                serialized_name="securityProfile",
            )
            properties.storage_profile = AAZObjectType(
                serialized_name="storageProfile",
            )
            properties.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            properties.user_data = AAZStrType(
                serialized_name="userData",
            )
            properties.virtual_machine_scale_set = AAZObjectType(
                serialized_name="virtualMachineScaleSet",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.virtual_machine_scale_set)
            properties.vm_id = AAZStrType(
                serialized_name="vmId",
                flags={"read_only": True},
            )

            additional_capabilities = cls._schema_on_200.properties.additional_capabilities
            additional_capabilities.hibernation_enabled = AAZBoolType(
                serialized_name="hibernationEnabled",
            )
            additional_capabilities.ultra_ssd_enabled = AAZBoolType(
                serialized_name="ultraSSDEnabled",
            )

            application_profile = cls._schema_on_200.properties.application_profile
            application_profile.gallery_applications = AAZListType(
                serialized_name="galleryApplications",
            )

            gallery_applications = cls._schema_on_200.properties.application_profile.gallery_applications
            gallery_applications.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.application_profile.gallery_applications.Element
            _element.configuration_reference = AAZStrType(
                serialized_name="configurationReference",
            )
            _element.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            _element.order = AAZIntType()
            _element.package_reference_id = AAZStrType(
                serialized_name="packageReferenceId",
                flags={"required": True},
            )
            _element.tags = AAZStrType()
            _element.treat_failure_as_deployment_failure = AAZBoolType(
                serialized_name="treatFailureAsDeploymentFailure",
            )

            billing_profile = cls._schema_on_200.properties.billing_profile
            billing_profile.max_price = AAZFloatType(
                serialized_name="maxPrice",
            )

            capacity_reservation = cls._schema_on_200.properties.capacity_reservation
            capacity_reservation.capacity_reservation_group = AAZObjectType(
                serialized_name="capacityReservationGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(capacity_reservation.capacity_reservation_group)

            diagnostics_profile = cls._schema_on_200.properties.diagnostics_profile
            diagnostics_profile.boot_diagnostics = AAZObjectType(
                serialized_name="bootDiagnostics",
            )

            boot_diagnostics = cls._schema_on_200.properties.diagnostics_profile.boot_diagnostics
            boot_diagnostics.enabled = AAZBoolType()
            boot_diagnostics.storage_uri = AAZStrType(
                serialized_name="storageUri",
            )

            hardware_profile = cls._schema_on_200.properties.hardware_profile
            hardware_profile.vm_size = AAZStrType(
                serialized_name="vmSize",
            )
            hardware_profile.vm_size_properties = AAZObjectType(
                serialized_name="vmSizeProperties",
            )

            vm_size_properties = cls._schema_on_200.properties.hardware_profile.vm_size_properties
            vm_size_properties.v_cp_us_available = AAZIntType(
                serialized_name="vCPUsAvailable",
            )
            vm_size_properties.v_cp_us_per_core = AAZIntType(
                serialized_name="vCPUsPerCore",
            )

            instance_view = cls._schema_on_200.properties.instance_view
            instance_view.assigned_host = AAZStrType(
                serialized_name="assignedHost",
                flags={"read_only": True},
            )
            instance_view.boot_diagnostics = AAZObjectType(
                serialized_name="bootDiagnostics",
            )
            instance_view.computer_name = AAZStrType(
                serialized_name="computerName",
            )
            instance_view.disks = AAZListType()
            instance_view.extensions = AAZListType()
            instance_view.hyper_v_generation = AAZStrType(
                serialized_name="hyperVGeneration",
            )
            instance_view.is_vm_in_standby_pool = AAZBoolType(
                serialized_name="isVMInStandbyPool",
                flags={"read_only": True},
            )
            instance_view.maintenance_redeploy_status = AAZObjectType(
                serialized_name="maintenanceRedeployStatus",
            )
            instance_view.os_name = AAZStrType(
                serialized_name="osName",
            )
            instance_view.os_version = AAZStrType(
                serialized_name="osVersion",
            )
            instance_view.patch_status = AAZObjectType(
                serialized_name="patchStatus",
            )
            instance_view.platform_fault_domain = AAZIntType(
                serialized_name="platformFaultDomain",
            )
            instance_view.platform_update_domain = AAZIntType(
                serialized_name="platformUpdateDomain",
            )
            instance_view.rdp_thumb_print = AAZStrType(
                serialized_name="rdpThumbPrint",
            )
            instance_view.statuses = AAZListType()
            instance_view.vm_agent = AAZObjectType(
                serialized_name="vmAgent",
            )
            instance_view.vm_health = AAZObjectType(
                serialized_name="vmHealth",
                flags={"read_only": True},
            )

            boot_diagnostics = cls._schema_on_200.properties.instance_view.boot_diagnostics
            boot_diagnostics.console_screenshot_blob_uri = AAZStrType(
                serialized_name="consoleScreenshotBlobUri",
                flags={"read_only": True},
            )
            boot_diagnostics.serial_console_log_blob_uri = AAZStrType(
                serialized_name="serialConsoleLogBlobUri",
                flags={"read_only": True},
            )
            boot_diagnostics.status = AAZObjectType(
                flags={"read_only": True},
            )
            _PatchHelper._build_schema_instance_view_status_read(boot_diagnostics.status)

            disks = cls._schema_on_200.properties.instance_view.disks
            disks.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.instance_view.disks.Element
            _element.encryption_settings = AAZListType(
                serialized_name="encryptionSettings",
            )
            _element.name = AAZStrType()
            _element.statuses = AAZListType()

            encryption_settings = cls._schema_on_200.properties.instance_view.disks.Element.encryption_settings
            encryption_settings.Element = AAZObjectType()
            _PatchHelper._build_schema_disk_encryption_settings_read(encryption_settings.Element)

            statuses = cls._schema_on_200.properties.instance_view.disks.Element.statuses
            statuses.Element = AAZObjectType()
            _PatchHelper._build_schema_instance_view_status_read(statuses.Element)

            extensions = cls._schema_on_200.properties.instance_view.extensions
            extensions.Element = AAZObjectType()
            _PatchHelper._build_schema_virtual_machine_extension_instance_view_read(extensions.Element)

            maintenance_redeploy_status = cls._schema_on_200.properties.instance_view.maintenance_redeploy_status
            maintenance_redeploy_status.is_customer_initiated_maintenance_allowed = AAZBoolType(
                serialized_name="isCustomerInitiatedMaintenanceAllowed",
            )
            maintenance_redeploy_status.last_operation_message = AAZStrType(
                serialized_name="lastOperationMessage",
            )
            maintenance_redeploy_status.last_operation_result_code = AAZStrType(
                serialized_name="lastOperationResultCode",
            )
            maintenance_redeploy_status.maintenance_window_end_time = AAZStrType(
                serialized_name="maintenanceWindowEndTime",
            )
            maintenance_redeploy_status.maintenance_window_start_time = AAZStrType(
                serialized_name="maintenanceWindowStartTime",
            )
            maintenance_redeploy_status.pre_maintenance_window_end_time = AAZStrType(
                serialized_name="preMaintenanceWindowEndTime",
            )
            maintenance_redeploy_status.pre_maintenance_window_start_time = AAZStrType(
                serialized_name="preMaintenanceWindowStartTime",
            )

            patch_status = cls._schema_on_200.properties.instance_view.patch_status
            patch_status.available_patch_summary = AAZObjectType(
                serialized_name="availablePatchSummary",
            )
            patch_status.configuration_statuses = AAZListType(
                serialized_name="configurationStatuses",
                flags={"read_only": True},
            )
            patch_status.last_patch_installation_summary = AAZObjectType(
                serialized_name="lastPatchInstallationSummary",
            )

            available_patch_summary = cls._schema_on_200.properties.instance_view.patch_status.available_patch_summary
            available_patch_summary.assessment_activity_id = AAZStrType(
                serialized_name="assessmentActivityId",
                flags={"read_only": True},
            )
            available_patch_summary.critical_and_security_patch_count = AAZIntType(
                serialized_name="criticalAndSecurityPatchCount",
                flags={"read_only": True},
            )
            available_patch_summary.error = AAZObjectType(
                flags={"read_only": True},
            )
            _PatchHelper._build_schema_api_error_read(available_patch_summary.error)
            available_patch_summary.last_modified_time = AAZStrType(
                serialized_name="lastModifiedTime",
                flags={"read_only": True},
            )
            available_patch_summary.other_patch_count = AAZIntType(
                serialized_name="otherPatchCount",
                flags={"read_only": True},
            )
            available_patch_summary.reboot_pending = AAZBoolType(
                serialized_name="rebootPending",
                flags={"read_only": True},
            )
            available_patch_summary.start_time = AAZStrType(
                serialized_name="startTime",
                flags={"read_only": True},
            )
            available_patch_summary.status = AAZStrType(
                flags={"read_only": True},
            )

            configuration_statuses = cls._schema_on_200.properties.instance_view.patch_status.configuration_statuses
            configuration_statuses.Element = AAZObjectType()
            _PatchHelper._build_schema_instance_view_status_read(configuration_statuses.Element)

            last_patch_installation_summary = cls._schema_on_200.properties.instance_view.patch_status.last_patch_installation_summary
            last_patch_installation_summary.error = AAZObjectType(
                flags={"read_only": True},
            )
            _PatchHelper._build_schema_api_error_read(last_patch_installation_summary.error)
            last_patch_installation_summary.excluded_patch_count = AAZIntType(
                serialized_name="excludedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.failed_patch_count = AAZIntType(
                serialized_name="failedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.installation_activity_id = AAZStrType(
                serialized_name="installationActivityId",
                flags={"read_only": True},
            )
            last_patch_installation_summary.installed_patch_count = AAZIntType(
                serialized_name="installedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.last_modified_time = AAZStrType(
                serialized_name="lastModifiedTime",
                flags={"read_only": True},
            )
            last_patch_installation_summary.maintenance_window_exceeded = AAZBoolType(
                serialized_name="maintenanceWindowExceeded",
                flags={"read_only": True},
            )
            last_patch_installation_summary.not_selected_patch_count = AAZIntType(
                serialized_name="notSelectedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.pending_patch_count = AAZIntType(
                serialized_name="pendingPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.start_time = AAZStrType(
                serialized_name="startTime",
                flags={"read_only": True},
            )
            last_patch_installation_summary.status = AAZStrType(
                flags={"read_only": True},
            )

            statuses = cls._schema_on_200.properties.instance_view.statuses
            statuses.Element = AAZObjectType()
            _PatchHelper._build_schema_instance_view_status_read(statuses.Element)

            vm_agent = cls._schema_on_200.properties.instance_view.vm_agent
            vm_agent.extension_handlers = AAZListType(
                serialized_name="extensionHandlers",
            )
            vm_agent.statuses = AAZListType()
            vm_agent.vm_agent_version = AAZStrType(
                serialized_name="vmAgentVersion",
            )

            extension_handlers = cls._schema_on_200.properties.instance_view.vm_agent.extension_handlers
            extension_handlers.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.instance_view.vm_agent.extension_handlers.Element
            _element.status = AAZObjectType()
            _PatchHelper._build_schema_instance_view_status_read(_element.status)
            _element.type = AAZStrType()
            _element.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            statuses = cls._schema_on_200.properties.instance_view.vm_agent.statuses
            statuses.Element = AAZObjectType()
            _PatchHelper._build_schema_instance_view_status_read(statuses.Element)

            vm_health = cls._schema_on_200.properties.instance_view.vm_health
            vm_health.status = AAZObjectType(
                flags={"read_only": True},
            )
            _PatchHelper._build_schema_instance_view_status_read(vm_health.status)

            network_profile = cls._schema_on_200.properties.network_profile
            network_profile.network_api_version = AAZStrType(
                serialized_name="networkApiVersion",
            )
            network_profile.network_interface_configurations = AAZListType(
                serialized_name="networkInterfaceConfigurations",
            )
            network_profile.network_interfaces = AAZListType(
                serialized_name="networkInterfaces",
            )

            network_interface_configurations = cls._schema_on_200.properties.network_profile.network_interface_configurations
            network_interface_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties
            properties.auxiliary_mode = AAZStrType(
                serialized_name="auxiliaryMode",
            )
            properties.auxiliary_sku = AAZStrType(
                serialized_name="auxiliarySku",
            )
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.disable_tcp_state_tracking = AAZBoolType(
                serialized_name="disableTcpStateTracking",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.dscp_configuration = AAZObjectType(
                serialized_name="dscpConfiguration",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.dscp_configuration)
            properties.enable_accelerated_networking = AAZBoolType(
                serialized_name="enableAcceleratedNetworking",
            )
            properties.enable_fpga = AAZBoolType(
                serialized_name="enableFpga",
            )
            properties.enable_ip_forwarding = AAZBoolType(
                serialized_name="enableIPForwarding",
            )
            properties.ip_configurations = AAZListType(
                serialized_name="ipConfigurations",
                flags={"required": True},
            )
            properties.network_security_group = AAZObjectType(
                serialized_name="networkSecurityGroup",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.network_security_group)
            properties.primary = AAZBoolType()

            dns_settings = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.dns_settings
            dns_settings.dns_servers = AAZListType(
                serialized_name="dnsServers",
            )

            dns_servers = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.dns_settings.dns_servers
            dns_servers.Element = AAZStrType()

            ip_configurations = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations
            ip_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties
            properties.application_gateway_backend_address_pools = AAZListType(
                serialized_name="applicationGatewayBackendAddressPools",
            )
            properties.application_security_groups = AAZListType(
                serialized_name="applicationSecurityGroups",
            )
            properties.load_balancer_backend_address_pools = AAZListType(
                serialized_name="loadBalancerBackendAddressPools",
            )
            properties.primary = AAZBoolType()
            properties.private_ip_address_version = AAZStrType(
                serialized_name="privateIPAddressVersion",
            )
            properties.public_ip_address_configuration = AAZObjectType(
                serialized_name="publicIPAddressConfiguration",
            )
            properties.subnet = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(properties.subnet)

            application_gateway_backend_address_pools = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_gateway_backend_address_pools
            application_gateway_backend_address_pools.Element = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(application_gateway_backend_address_pools.Element)

            application_security_groups = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_security_groups
            application_security_groups.Element = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(application_security_groups.Element)

            load_balancer_backend_address_pools = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_backend_address_pools
            load_balancer_backend_address_pools.Element = AAZObjectType()
            _PatchHelper._build_schema_sub_resource_read(load_balancer_backend_address_pools.Element)

            public_ip_address_configuration = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration
            public_ip_address_configuration.name = AAZStrType(
                flags={"required": True},
            )
            public_ip_address_configuration.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            public_ip_address_configuration.sku = AAZObjectType()

            properties = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.idle_timeout_in_minutes = AAZIntType(
                serialized_name="idleTimeoutInMinutes",
            )
            properties.ip_tags = AAZListType(
                serialized_name="ipTags",
            )
            properties.public_ip_address_version = AAZStrType(
                serialized_name="publicIPAddressVersion",
            )
            properties.public_ip_allocation_method = AAZStrType(
                serialized_name="publicIPAllocationMethod",
            )
            properties.public_ip_prefix = AAZObjectType(
                serialized_name="publicIPPrefix",
            )
            _PatchHelper._build_schema_sub_resource_read(properties.public_ip_prefix)

            dns_settings = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.dns_settings
            dns_settings.domain_name_label = AAZStrType(
                serialized_name="domainNameLabel",
                flags={"required": True},
            )
            dns_settings.domain_name_label_scope = AAZStrType(
                serialized_name="domainNameLabelScope",
            )

            ip_tags = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags
            ip_tags.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags.Element
            _element.ip_tag_type = AAZStrType(
                serialized_name="ipTagType",
            )
            _element.tag = AAZStrType()

            sku = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            network_interfaces = cls._schema_on_200.properties.network_profile.network_interfaces
            network_interfaces.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interfaces.Element
            _element.id = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.network_profile.network_interfaces.Element.properties
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.primary = AAZBoolType()

            os_profile = cls._schema_on_200.properties.os_profile
            os_profile.admin_password = AAZStrType(
                serialized_name="adminPassword",
                flags={"secret": True},
            )
            os_profile.admin_username = AAZStrType(
                serialized_name="adminUsername",
            )
            os_profile.allow_extension_operations = AAZBoolType(
                serialized_name="allowExtensionOperations",
            )
            os_profile.computer_name = AAZStrType(
                serialized_name="computerName",
            )
            os_profile.custom_data = AAZStrType(
                serialized_name="customData",
            )
            os_profile.linux_configuration = AAZObjectType(
                serialized_name="linuxConfiguration",
            )
            os_profile.require_guest_provision_signal = AAZBoolType(
                serialized_name="requireGuestProvisionSignal",
            )
            os_profile.secrets = AAZListType()
            os_profile.windows_configuration = AAZObjectType(
                serialized_name="windowsConfiguration",
            )

            linux_configuration = cls._schema_on_200.properties.os_profile.linux_configuration
            linux_configuration.disable_password_authentication = AAZBoolType(
                serialized_name="disablePasswordAuthentication",
            )
            linux_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
            )
            linux_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            linux_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            linux_configuration.ssh = AAZObjectType()

            patch_settings = cls._schema_on_200.properties.os_profile.linux_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.properties.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            ssh = cls._schema_on_200.properties.os_profile.linux_configuration.ssh
            ssh.public_keys = AAZListType(
                serialized_name="publicKeys",
            )

            public_keys = cls._schema_on_200.properties.os_profile.linux_configuration.ssh.public_keys
            public_keys.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.linux_configuration.ssh.public_keys.Element
            _element.key_data = AAZStrType(
                serialized_name="keyData",
            )
            _element.path = AAZStrType()

            secrets = cls._schema_on_200.properties.os_profile.secrets
            secrets.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.secrets.Element
            _element.source_vault = AAZObjectType(
                serialized_name="sourceVault",
            )
            _PatchHelper._build_schema_sub_resource_read(_element.source_vault)
            _element.vault_certificates = AAZListType(
                serialized_name="vaultCertificates",
            )

            vault_certificates = cls._schema_on_200.properties.os_profile.secrets.Element.vault_certificates
            vault_certificates.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.secrets.Element.vault_certificates.Element
            _element.certificate_store = AAZStrType(
                serialized_name="certificateStore",
            )
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )

            windows_configuration = cls._schema_on_200.properties.os_profile.windows_configuration
            windows_configuration.additional_unattend_content = AAZListType(
                serialized_name="additionalUnattendContent",
            )
            windows_configuration.enable_automatic_updates = AAZBoolType(
                serialized_name="enableAutomaticUpdates",
            )
            windows_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
                flags={"read_only": True},
            )
            windows_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            windows_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            windows_configuration.time_zone = AAZStrType(
                serialized_name="timeZone",
            )
            windows_configuration.win_rm = AAZObjectType(
                serialized_name="winRM",
            )

            additional_unattend_content = cls._schema_on_200.properties.os_profile.windows_configuration.additional_unattend_content
            additional_unattend_content.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.windows_configuration.additional_unattend_content.Element
            _element.component_name = AAZStrType(
                serialized_name="componentName",
            )
            _element.content = AAZStrType()
            _element.pass_name = AAZStrType(
                serialized_name="passName",
            )
            _element.setting_name = AAZStrType(
                serialized_name="settingName",
            )

            patch_settings = cls._schema_on_200.properties.os_profile.windows_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.enable_hotpatching = AAZBoolType(
                serialized_name="enableHotpatching",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.properties.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            win_rm = cls._schema_on_200.properties.os_profile.windows_configuration.win_rm
            win_rm.listeners = AAZListType()

            listeners = cls._schema_on_200.properties.os_profile.windows_configuration.win_rm.listeners
            listeners.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.windows_configuration.win_rm.listeners.Element
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )
            _element.protocol = AAZStrType()

            scheduled_events_policy = cls._schema_on_200.properties.scheduled_events_policy
            scheduled_events_policy.scheduled_events_additional_publishing_targets = AAZObjectType(
                serialized_name="scheduledEventsAdditionalPublishingTargets",
            )
            scheduled_events_policy.user_initiated_reboot = AAZObjectType(
                serialized_name="userInitiatedReboot",
            )
            scheduled_events_policy.user_initiated_redeploy = AAZObjectType(
                serialized_name="userInitiatedRedeploy",
            )

            scheduled_events_additional_publishing_targets = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets
            scheduled_events_additional_publishing_targets.event_grid_and_resource_graph = AAZObjectType(
                serialized_name="eventGridAndResourceGraph",
            )

            event_grid_and_resource_graph = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets.event_grid_and_resource_graph
            event_grid_and_resource_graph.enable = AAZBoolType()

            user_initiated_reboot = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_reboot
            user_initiated_reboot.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            user_initiated_redeploy = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_redeploy
            user_initiated_redeploy.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            scheduled_events_profile = cls._schema_on_200.properties.scheduled_events_profile
            scheduled_events_profile.os_image_notification_profile = AAZObjectType(
                serialized_name="osImageNotificationProfile",
            )
            scheduled_events_profile.terminate_notification_profile = AAZObjectType(
                serialized_name="terminateNotificationProfile",
            )

            os_image_notification_profile = cls._schema_on_200.properties.scheduled_events_profile.os_image_notification_profile
            os_image_notification_profile.enable = AAZBoolType()
            os_image_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            terminate_notification_profile = cls._schema_on_200.properties.scheduled_events_profile.terminate_notification_profile
            terminate_notification_profile.enable = AAZBoolType()
            terminate_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            security_profile = cls._schema_on_200.properties.security_profile
            security_profile.encryption_at_host = AAZBoolType(
                serialized_name="encryptionAtHost",
            )
            security_profile.encryption_identity = AAZObjectType(
                serialized_name="encryptionIdentity",
            )
            security_profile.proxy_agent_settings = AAZObjectType(
                serialized_name="proxyAgentSettings",
            )
            security_profile.security_type = AAZStrType(
                serialized_name="securityType",
            )
            security_profile.uefi_settings = AAZObjectType(
                serialized_name="uefiSettings",
            )

            encryption_identity = cls._schema_on_200.properties.security_profile.encryption_identity
            encryption_identity.user_assigned_identity_resource_id = AAZStrType(
                serialized_name="userAssignedIdentityResourceId",
            )

            proxy_agent_settings = cls._schema_on_200.properties.security_profile.proxy_agent_settings
            proxy_agent_settings.enabled = AAZBoolType()
            proxy_agent_settings.imds = AAZObjectType()
            _PatchHelper._build_schema_host_endpoint_settings_read(proxy_agent_settings.imds)
            proxy_agent_settings.key_incarnation_id = AAZIntType(
                serialized_name="keyIncarnationId",
            )
            proxy_agent_settings.mode = AAZStrType()
            proxy_agent_settings.wire_server = AAZObjectType(
                serialized_name="wireServer",
            )
            _PatchHelper._build_schema_host_endpoint_settings_read(proxy_agent_settings.wire_server)

            uefi_settings = cls._schema_on_200.properties.security_profile.uefi_settings
            uefi_settings.secure_boot_enabled = AAZBoolType(
                serialized_name="secureBootEnabled",
            )
            uefi_settings.v_tpm_enabled = AAZBoolType(
                serialized_name="vTpmEnabled",
            )

            storage_profile = cls._schema_on_200.properties.storage_profile
            storage_profile.align_regional_disks_to_vm_zone = AAZBoolType(
                serialized_name="alignRegionalDisksToVMZone",
            )
            storage_profile.data_disks = AAZListType(
                serialized_name="dataDisks",
            )
            storage_profile.disk_controller_type = AAZStrType(
                serialized_name="diskControllerType",
            )
            storage_profile.image_reference = AAZObjectType(
                serialized_name="imageReference",
            )
            storage_profile.os_disk = AAZObjectType(
                serialized_name="osDisk",
            )

            data_disks = cls._schema_on_200.properties.storage_profile.data_disks
            data_disks.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.storage_profile.data_disks.Element
            _element.caching = AAZStrType()
            _element.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            _element.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            _element.detach_option = AAZStrType(
                serialized_name="detachOption",
            )
            _element.disk_iops_read_write = AAZIntType(
                serialized_name="diskIOPSReadWrite",
                flags={"read_only": True},
            )
            _element.disk_m_bps_read_write = AAZIntType(
                serialized_name="diskMBpsReadWrite",
                flags={"read_only": True},
            )
            _element.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            _element.image = AAZObjectType()
            _PatchHelper._build_schema_virtual_hard_disk_read(_element.image)
            _element.lun = AAZIntType(
                flags={"required": True},
            )
            _element.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _PatchHelper._build_schema_managed_disk_parameters_read(_element.managed_disk)
            _element.name = AAZStrType()
            _element.source_resource = AAZObjectType(
                serialized_name="sourceResource",
            )
            _element.to_be_detached = AAZBoolType(
                serialized_name="toBeDetached",
            )
            _element.vhd = AAZObjectType()
            _PatchHelper._build_schema_virtual_hard_disk_read(_element.vhd)
            _element.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            source_resource = cls._schema_on_200.properties.storage_profile.data_disks.Element.source_resource
            source_resource.id = AAZStrType()

            image_reference = cls._schema_on_200.properties.storage_profile.image_reference
            image_reference.community_gallery_image_id = AAZStrType(
                serialized_name="communityGalleryImageId",
            )
            image_reference.exact_version = AAZStrType(
                serialized_name="exactVersion",
                flags={"read_only": True},
            )
            image_reference.id = AAZStrType()
            image_reference.offer = AAZStrType()
            image_reference.publisher = AAZStrType()
            image_reference.shared_gallery_image_id = AAZStrType(
                serialized_name="sharedGalleryImageId",
            )
            image_reference.sku = AAZStrType()
            image_reference.version = AAZStrType()

            os_disk = cls._schema_on_200.properties.storage_profile.os_disk
            os_disk.caching = AAZStrType()
            os_disk.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            os_disk.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            os_disk.diff_disk_settings = AAZObjectType(
                serialized_name="diffDiskSettings",
            )
            os_disk.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            os_disk.encryption_settings = AAZObjectType(
                serialized_name="encryptionSettings",
            )
            _PatchHelper._build_schema_disk_encryption_settings_read(os_disk.encryption_settings)
            os_disk.image = AAZObjectType()
            _PatchHelper._build_schema_virtual_hard_disk_read(os_disk.image)
            os_disk.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _PatchHelper._build_schema_managed_disk_parameters_read(os_disk.managed_disk)
            os_disk.name = AAZStrType()
            os_disk.os_type = AAZStrType(
                serialized_name="osType",
            )
            os_disk.vhd = AAZObjectType()
            _PatchHelper._build_schema_virtual_hard_disk_read(os_disk.vhd)
            os_disk.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            diff_disk_settings = cls._schema_on_200.properties.storage_profile.os_disk.diff_disk_settings
            diff_disk_settings.option = AAZStrType()
            diff_disk_settings.placement = AAZStrType()

            resources = cls._schema_on_200.resources
            resources.Element = AAZObjectType()

            _element = cls._schema_on_200.resources.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.location = AAZStrType()
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.resources.Element.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
            )
            _PatchHelper._build_schema_virtual_machine_extension_instance_view_read(properties.instance_view)
            properties.protected_settings = AAZFreeFormDictType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            _PatchHelper._build_schema_key_vault_secret_reference_read(properties.protected_settings_from_key_vault)
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZFreeFormDictType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            provision_after_extensions = cls._schema_on_200.resources.Element.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            tags = cls._schema_on_200.resources.Element.tags
            tags.Element = AAZStrType()

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200


class _PatchHelper:
    """Helper class for Patch"""

    @classmethod
    def _build_schema_disk_encryption_set_parameters_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    @classmethod
    def _build_schema_host_endpoint_settings_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("inVMAccessControlProfileReferenceId", AAZStrType, ".in_vm_access_control_profile_reference_id")
        _builder.set_prop("mode", AAZStrType, ".mode")

    @classmethod
    def _build_schema_managed_disk_parameters_update(cls, _builder):
        if _builder is None:
            return
        cls._build_schema_disk_encryption_set_parameters_update(_builder.set_prop("diskEncryptionSet", AAZObjectType, ".disk_encryption_set"))
        _builder.set_prop("id", AAZStrType, ".id")
        _builder.set_prop("securityProfile", AAZObjectType, ".security_profile")
        _builder.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

        security_profile = _builder.get(".securityProfile")
        if security_profile is not None:
            cls._build_schema_disk_encryption_set_parameters_update(security_profile.set_prop("diskEncryptionSet", AAZObjectType, ".disk_encryption_set"))
            security_profile.set_prop("securityEncryptionType", AAZStrType, ".security_encryption_type")

    @classmethod
    def _build_schema_sub_resource_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    @classmethod
    def _build_schema_virtual_hard_disk_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("uri", AAZStrType, ".uri")

    _schema_api_error_read = None

    @classmethod
    def _build_schema_api_error_read(cls, _schema):
        if cls._schema_api_error_read is not None:
            _schema.code = cls._schema_api_error_read.code
            _schema.details = cls._schema_api_error_read.details
            _schema.innererror = cls._schema_api_error_read.innererror
            _schema.message = cls._schema_api_error_read.message
            _schema.target = cls._schema_api_error_read.target
            return

        cls._schema_api_error_read = _schema_api_error_read = AAZObjectType(
            flags={"read_only": True}
        )

        api_error_read = _schema_api_error_read
        api_error_read.code = AAZStrType()
        api_error_read.details = AAZListType()
        api_error_read.innererror = AAZObjectType()
        api_error_read.message = AAZStrType()
        api_error_read.target = AAZStrType()

        details = _schema_api_error_read.details
        details.Element = AAZObjectType()

        _element = _schema_api_error_read.details.Element
        _element.code = AAZStrType()
        _element.message = AAZStrType()
        _element.target = AAZStrType()

        innererror = _schema_api_error_read.innererror
        innererror.errordetail = AAZStrType()
        innererror.exceptiontype = AAZStrType()

        _schema.code = cls._schema_api_error_read.code
        _schema.details = cls._schema_api_error_read.details
        _schema.innererror = cls._schema_api_error_read.innererror
        _schema.message = cls._schema_api_error_read.message
        _schema.target = cls._schema_api_error_read.target

    _schema_disk_encryption_set_parameters_read = None

    @classmethod
    def _build_schema_disk_encryption_set_parameters_read(cls, _schema):
        if cls._schema_disk_encryption_set_parameters_read is not None:
            _schema.id = cls._schema_disk_encryption_set_parameters_read.id
            return

        cls._schema_disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read = AAZObjectType()

        disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read
        disk_encryption_set_parameters_read.id = AAZStrType()

        _schema.id = cls._schema_disk_encryption_set_parameters_read.id

    _schema_disk_encryption_settings_read = None

    @classmethod
    def _build_schema_disk_encryption_settings_read(cls, _schema):
        if cls._schema_disk_encryption_settings_read is not None:
            _schema.disk_encryption_key = cls._schema_disk_encryption_settings_read.disk_encryption_key
            _schema.enabled = cls._schema_disk_encryption_settings_read.enabled
            _schema.key_encryption_key = cls._schema_disk_encryption_settings_read.key_encryption_key
            return

        cls._schema_disk_encryption_settings_read = _schema_disk_encryption_settings_read = AAZObjectType()

        disk_encryption_settings_read = _schema_disk_encryption_settings_read
        disk_encryption_settings_read.disk_encryption_key = AAZObjectType(
            serialized_name="diskEncryptionKey",
        )
        cls._build_schema_key_vault_secret_reference_read(disk_encryption_settings_read.disk_encryption_key)
        disk_encryption_settings_read.enabled = AAZBoolType()
        disk_encryption_settings_read.key_encryption_key = AAZObjectType(
            serialized_name="keyEncryptionKey",
        )

        key_encryption_key = _schema_disk_encryption_settings_read.key_encryption_key
        key_encryption_key.key_url = AAZStrType(
            serialized_name="keyUrl",
            flags={"required": True},
        )
        key_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_sub_resource_read(key_encryption_key.source_vault)

        _schema.disk_encryption_key = cls._schema_disk_encryption_settings_read.disk_encryption_key
        _schema.enabled = cls._schema_disk_encryption_settings_read.enabled
        _schema.key_encryption_key = cls._schema_disk_encryption_settings_read.key_encryption_key

    _schema_host_endpoint_settings_read = None

    @classmethod
    def _build_schema_host_endpoint_settings_read(cls, _schema):
        if cls._schema_host_endpoint_settings_read is not None:
            _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
            _schema.mode = cls._schema_host_endpoint_settings_read.mode
            return

        cls._schema_host_endpoint_settings_read = _schema_host_endpoint_settings_read = AAZObjectType()

        host_endpoint_settings_read = _schema_host_endpoint_settings_read
        host_endpoint_settings_read.in_vm_access_control_profile_reference_id = AAZStrType(
            serialized_name="inVMAccessControlProfileReferenceId",
        )
        host_endpoint_settings_read.mode = AAZStrType()

        _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
        _schema.mode = cls._schema_host_endpoint_settings_read.mode

    _schema_instance_view_status_read = None

    @classmethod
    def _build_schema_instance_view_status_read(cls, _schema):
        if cls._schema_instance_view_status_read is not None:
            _schema.code = cls._schema_instance_view_status_read.code
            _schema.display_status = cls._schema_instance_view_status_read.display_status
            _schema.level = cls._schema_instance_view_status_read.level
            _schema.message = cls._schema_instance_view_status_read.message
            _schema.time = cls._schema_instance_view_status_read.time
            return

        cls._schema_instance_view_status_read = _schema_instance_view_status_read = AAZObjectType()

        instance_view_status_read = _schema_instance_view_status_read
        instance_view_status_read.code = AAZStrType()
        instance_view_status_read.display_status = AAZStrType(
            serialized_name="displayStatus",
        )
        instance_view_status_read.level = AAZStrType()
        instance_view_status_read.message = AAZStrType()
        instance_view_status_read.time = AAZStrType()

        _schema.code = cls._schema_instance_view_status_read.code
        _schema.display_status = cls._schema_instance_view_status_read.display_status
        _schema.level = cls._schema_instance_view_status_read.level
        _schema.message = cls._schema_instance_view_status_read.message
        _schema.time = cls._schema_instance_view_status_read.time

    _schema_key_vault_secret_reference_read = None

    @classmethod
    def _build_schema_key_vault_secret_reference_read(cls, _schema):
        if cls._schema_key_vault_secret_reference_read is not None:
            _schema.secret_url = cls._schema_key_vault_secret_reference_read.secret_url
            _schema.source_vault = cls._schema_key_vault_secret_reference_read.source_vault
            return

        cls._schema_key_vault_secret_reference_read = _schema_key_vault_secret_reference_read = AAZObjectType()

        key_vault_secret_reference_read = _schema_key_vault_secret_reference_read
        key_vault_secret_reference_read.secret_url = AAZStrType(
            serialized_name="secretUrl",
            flags={"required": True},
        )
        key_vault_secret_reference_read.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_sub_resource_read(key_vault_secret_reference_read.source_vault)

        _schema.secret_url = cls._schema_key_vault_secret_reference_read.secret_url
        _schema.source_vault = cls._schema_key_vault_secret_reference_read.source_vault

    _schema_managed_disk_parameters_read = None

    @classmethod
    def _build_schema_managed_disk_parameters_read(cls, _schema):
        if cls._schema_managed_disk_parameters_read is not None:
            _schema.disk_encryption_set = cls._schema_managed_disk_parameters_read.disk_encryption_set
            _schema.id = cls._schema_managed_disk_parameters_read.id
            _schema.security_profile = cls._schema_managed_disk_parameters_read.security_profile
            _schema.storage_account_type = cls._schema_managed_disk_parameters_read.storage_account_type
            return

        cls._schema_managed_disk_parameters_read = _schema_managed_disk_parameters_read = AAZObjectType()

        managed_disk_parameters_read = _schema_managed_disk_parameters_read
        managed_disk_parameters_read.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(managed_disk_parameters_read.disk_encryption_set)
        managed_disk_parameters_read.id = AAZStrType()
        managed_disk_parameters_read.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        managed_disk_parameters_read.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        security_profile = _schema_managed_disk_parameters_read.security_profile
        security_profile.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(security_profile.disk_encryption_set)
        security_profile.security_encryption_type = AAZStrType(
            serialized_name="securityEncryptionType",
        )

        _schema.disk_encryption_set = cls._schema_managed_disk_parameters_read.disk_encryption_set
        _schema.id = cls._schema_managed_disk_parameters_read.id
        _schema.security_profile = cls._schema_managed_disk_parameters_read.security_profile
        _schema.storage_account_type = cls._schema_managed_disk_parameters_read.storage_account_type

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_virtual_hard_disk_read = None

    @classmethod
    def _build_schema_virtual_hard_disk_read(cls, _schema):
        if cls._schema_virtual_hard_disk_read is not None:
            _schema.uri = cls._schema_virtual_hard_disk_read.uri
            return

        cls._schema_virtual_hard_disk_read = _schema_virtual_hard_disk_read = AAZObjectType()

        virtual_hard_disk_read = _schema_virtual_hard_disk_read
        virtual_hard_disk_read.uri = AAZStrType()

        _schema.uri = cls._schema_virtual_hard_disk_read.uri

    _schema_virtual_machine_extension_instance_view_read = None

    @classmethod
    def _build_schema_virtual_machine_extension_instance_view_read(cls, _schema):
        if cls._schema_virtual_machine_extension_instance_view_read is not None:
            _schema.name = cls._schema_virtual_machine_extension_instance_view_read.name
            _schema.statuses = cls._schema_virtual_machine_extension_instance_view_read.statuses
            _schema.substatuses = cls._schema_virtual_machine_extension_instance_view_read.substatuses
            _schema.type = cls._schema_virtual_machine_extension_instance_view_read.type
            _schema.type_handler_version = cls._schema_virtual_machine_extension_instance_view_read.type_handler_version
            return

        cls._schema_virtual_machine_extension_instance_view_read = _schema_virtual_machine_extension_instance_view_read = AAZObjectType()

        virtual_machine_extension_instance_view_read = _schema_virtual_machine_extension_instance_view_read
        virtual_machine_extension_instance_view_read.name = AAZStrType()
        virtual_machine_extension_instance_view_read.statuses = AAZListType()
        virtual_machine_extension_instance_view_read.substatuses = AAZListType()
        virtual_machine_extension_instance_view_read.type = AAZStrType()
        virtual_machine_extension_instance_view_read.type_handler_version = AAZStrType(
            serialized_name="typeHandlerVersion",
        )

        statuses = _schema_virtual_machine_extension_instance_view_read.statuses
        statuses.Element = AAZObjectType()
        cls._build_schema_instance_view_status_read(statuses.Element)

        substatuses = _schema_virtual_machine_extension_instance_view_read.substatuses
        substatuses.Element = AAZObjectType()
        cls._build_schema_instance_view_status_read(substatuses.Element)

        _schema.name = cls._schema_virtual_machine_extension_instance_view_read.name
        _schema.statuses = cls._schema_virtual_machine_extension_instance_view_read.statuses
        _schema.substatuses = cls._schema_virtual_machine_extension_instance_view_read.substatuses
        _schema.type = cls._schema_virtual_machine_extension_instance_view_read.type
        _schema.type_handler_version = cls._schema_virtual_machine_extension_instance_view_read.type_handler_version


__all__ = ["Patch"]
