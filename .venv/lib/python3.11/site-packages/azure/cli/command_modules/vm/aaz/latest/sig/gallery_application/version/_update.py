# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "sig gallery-application version update",
)
class Update(AAZCommand):
    """Update a gallery application version.

    :example: Update a simple gallery Application Version.
        az sig gallery-application version update --gallery-name myGalleryName --application-name myGalleryApplicationName -n 1.0.0 -g myResourceGroup --package-file-link https://{myStorageAccount}.blob.core.windows.net/{myStorageContainer}/{myStorageBlob} --end-of-life-date "2050-07-01T07:00:00Z"
    """

    _aaz_info = {
        "version": "2022-01-03",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/galleries/{}/applications/{}/versions/{}", "2022-01-03"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.gallery_application_name = AAZStrArg(
            options=["--application-name", "--gallery-application-name"],
            help="The name of the gallery application.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.gallery_application_version_name = AAZStrArg(
            options=["-n", "--name", "--version-name", "--gallery-application-version-name"],
            help="The name of the gallery application version.",
            required=True,
            id_part="child_name_2",
        )
        _args_schema.gallery_name = AAZStrArg(
            options=["-r", "--gallery-name"],
            help="Gallery name.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.location = AAZResourceLocationArg(
            help="Resource location",
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.default_file_link = AAZStrArg(
            options=["--default-file-link"],
            help="Optional. The default configuration link of the artifact, must be a readable storage page blob.",
            nullable=True,
        )
        _args_schema.package_file_link = AAZStrArg(
            options=["--package-file-link"],
            help="Required. The mediaLink of the artifact, must be a readable storage page blob.",
        )
        _args_schema.target_regions = AAZListArg(
            options=["--target-regions"],
            help="The target regions where the Image Version is going to be replicated to. This property is updatable.",
            nullable=True,
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="Space-separated tags: key[=value] [key[=value] ...].",
            nullable=True,
        )

        target_regions = cls._args_schema.target_regions
        target_regions.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.target_regions.Element
        _element.encryption = AAZObjectArg(
            options=["encryption"],
            help="Optional. Allows users to provide customer managed keys for encrypting the OS and data disks in the gallery artifact.",
            nullable=True,
        )
        cls._build_args_encryption_images_update(_element.encryption)
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the region.",
        )
        _element.regional_replica_count = AAZIntArg(
            options=["regional-replica-count"],
            help="The number of replicas of the Image Version to be created per region. This property is updatable.",
            nullable=True,
        )
        _element.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type to be used to store the image. This property is not updatable.",
            nullable=True,
            enum={"Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg(
            nullable=True,
        )

        # define Arg Group "PublishingProfile"

        _args_schema = cls._args_schema
        _args_schema.end_of_life_date = AAZDateTimeArg(
            options=["--end-of-life-date"],
            arg_group="PublishingProfile",
            help="The end of life date of the gallery image version. This property can be used for decommissioning purposes. This property is updatable.",
            nullable=True,
        )
        _args_schema.exclude_from = AAZBoolArg(
            options=["--exclude-from"],
            arg_group="PublishingProfile",
            help="If set to true, Virtual Machines deployed from the latest version of the Image Definition won't use this Image Version.",
            nullable=True,
        )
        return cls._args_schema

    _args_encryption_images_update = None

    @classmethod
    def _build_args_encryption_images_update(cls, _schema):
        if cls._args_encryption_images_update is not None:
            _schema.data_disk_images = cls._args_encryption_images_update.data_disk_images
            _schema.os_disk_image = cls._args_encryption_images_update.os_disk_image
            return

        cls._args_encryption_images_update = AAZObjectArg(
            nullable=True,
        )

        encryption_images_update = cls._args_encryption_images_update
        encryption_images_update.data_disk_images = AAZListArg(
            options=["data-disk-images"],
            help="A list of encryption specifications for data disk images.",
            nullable=True,
        )
        encryption_images_update.os_disk_image = AAZObjectArg(
            options=["os-disk-image"],
            help="Contains encryption settings for an OS disk image.",
            nullable=True,
        )

        data_disk_images = cls._args_encryption_images_update.data_disk_images
        data_disk_images.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_encryption_images_update.data_disk_images.Element
        _element.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="A relative URI containing the resource ID of the disk encryption set.",
            nullable=True,
        )
        _element.lun = AAZIntArg(
            options=["lun"],
            help="This property specifies the logical unit number of the data disk. This value is used to identify data disks within the Virtual Machine and therefore must be unique for each data disk attached to the Virtual Machine.",
        )

        os_disk_image = cls._args_encryption_images_update.os_disk_image
        os_disk_image.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="A relative URI containing the resource ID of the disk encryption set.",
            nullable=True,
        )
        os_disk_image.security_profile = AAZObjectArg(
            options=["security-profile"],
            help="This property specifies the security profile of an OS disk image.",
            nullable=True,
        )

        security_profile = cls._args_encryption_images_update.os_disk_image.security_profile
        security_profile.confidential_vm_encryption_type = AAZStrArg(
            options=["confidential-vm-encryption-type"],
            help="confidential VM encryption types",
            nullable=True,
            enum={"EncryptedVMGuestStateOnlyWithPmk": "EncryptedVMGuestStateOnlyWithPmk", "EncryptedWithCmk": "EncryptedWithCmk", "EncryptedWithPmk": "EncryptedWithPmk"},
        )
        security_profile.secure_vm_disk_encryption_set_id = AAZStrArg(
            options=["secure-vm-disk-encryption-set-id"],
            help="secure VM disk encryption set id",
            nullable=True,
        )

        _schema.data_disk_images = cls._args_encryption_images_update.data_disk_images
        _schema.os_disk_image = cls._args_encryption_images_update.os_disk_image

    def _execute_operations(self):
        self.pre_operations()
        self.GalleryApplicationVersionsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.GalleryApplicationVersionsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class GalleryApplicationVersionsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/applications/{galleryApplicationName}/versions/{galleryApplicationVersionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryApplicationName", self.ctx.args.gallery_application_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "galleryApplicationVersionName", self.ctx.args.gallery_application_version_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2022-01-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_gallery_application_version_read(cls._schema_on_200)

            return cls._schema_on_200

    class GalleryApplicationVersionsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/applications/{galleryApplicationName}/versions/{galleryApplicationVersionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryApplicationName", self.ctx.args.gallery_application_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "galleryApplicationVersionName", self.ctx.args.gallery_application_version_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2022-01-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_gallery_application_version_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("publishingProfile", AAZObjectType, ".", typ_kwargs={"flags": {"required": True}})

            publishing_profile = _builder.get(".properties.publishingProfile")
            if publishing_profile is not None:
                publishing_profile.set_prop("endOfLifeDate", AAZStrType, ".end_of_life_date")
                publishing_profile.set_prop("excludeFromLatest", AAZBoolType, ".exclude_from")
                publishing_profile.set_prop("manageActions", AAZObjectType)
                publishing_profile.set_prop("settings", AAZObjectType)
                publishing_profile.set_prop("source", AAZObjectType, ".", typ_kwargs={"flags": {"required": True}})
                publishing_profile.set_prop("targetRegions", AAZListType, ".target_regions")

            source = _builder.get(".properties.publishingProfile.source")
            if source is not None:
                source.set_prop("defaultConfigurationLink", AAZStrType, ".default_file_link")
                source.set_prop("mediaLink", AAZStrType, ".package_file_link", typ_kwargs={"flags": {"required": True}})

            target_regions = _builder.get(".properties.publishingProfile.targetRegions")
            if target_regions is not None:
                target_regions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.publishingProfile.targetRegions[]")
            if _elements is not None:
                _UpdateHelper._build_schema_encryption_images_update(_elements.set_prop("encryption", AAZObjectType, ".encryption"))
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("regionalReplicaCount", AAZIntType, ".regional_replica_count")
                _elements.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    @classmethod
    def _build_schema_encryption_images_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("dataDiskImages", AAZListType, ".data_disk_images")
        _builder.set_prop("osDiskImage", AAZObjectType, ".os_disk_image")

        data_disk_images = _builder.get(".dataDiskImages")
        if data_disk_images is not None:
            data_disk_images.set_elements(AAZObjectType, ".")

        _elements = _builder.get(".dataDiskImages[]")
        if _elements is not None:
            _elements.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
            _elements.set_prop("lun", AAZIntType, ".lun", typ_kwargs={"flags": {"required": True}})

        os_disk_image = _builder.get(".osDiskImage")
        if os_disk_image is not None:
            os_disk_image.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
            os_disk_image.set_prop("securityProfile", AAZObjectType, ".security_profile")

        security_profile = _builder.get(".osDiskImage.securityProfile")
        if security_profile is not None:
            security_profile.set_prop("confidentialVMEncryptionType", AAZStrType, ".confidential_vm_encryption_type")
            security_profile.set_prop("secureVMDiskEncryptionSetId", AAZStrType, ".secure_vm_disk_encryption_set_id")

    _schema_encryption_images_read = None

    @classmethod
    def _build_schema_encryption_images_read(cls, _schema):
        if cls._schema_encryption_images_read is not None:
            _schema.data_disk_images = cls._schema_encryption_images_read.data_disk_images
            _schema.os_disk_image = cls._schema_encryption_images_read.os_disk_image
            return

        cls._schema_encryption_images_read = _schema_encryption_images_read = AAZObjectType()

        encryption_images_read = _schema_encryption_images_read
        encryption_images_read.data_disk_images = AAZListType(
            serialized_name="dataDiskImages",
        )
        encryption_images_read.os_disk_image = AAZObjectType(
            serialized_name="osDiskImage",
        )

        data_disk_images = _schema_encryption_images_read.data_disk_images
        data_disk_images.Element = AAZObjectType()

        _element = _schema_encryption_images_read.data_disk_images.Element
        _element.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        _element.lun = AAZIntType(
            flags={"required": True},
        )

        os_disk_image = _schema_encryption_images_read.os_disk_image
        os_disk_image.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        os_disk_image.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )

        security_profile = _schema_encryption_images_read.os_disk_image.security_profile
        security_profile.confidential_vm_encryption_type = AAZStrType(
            serialized_name="confidentialVMEncryptionType",
        )
        security_profile.secure_vm_disk_encryption_set_id = AAZStrType(
            serialized_name="secureVMDiskEncryptionSetId",
        )

        _schema.data_disk_images = cls._schema_encryption_images_read.data_disk_images
        _schema.os_disk_image = cls._schema_encryption_images_read.os_disk_image

    _schema_gallery_application_version_read = None

    @classmethod
    def _build_schema_gallery_application_version_read(cls, _schema):
        if cls._schema_gallery_application_version_read is not None:
            _schema.id = cls._schema_gallery_application_version_read.id
            _schema.location = cls._schema_gallery_application_version_read.location
            _schema.name = cls._schema_gallery_application_version_read.name
            _schema.properties = cls._schema_gallery_application_version_read.properties
            _schema.tags = cls._schema_gallery_application_version_read.tags
            _schema.type = cls._schema_gallery_application_version_read.type
            return

        cls._schema_gallery_application_version_read = _schema_gallery_application_version_read = AAZObjectType()

        gallery_application_version_read = _schema_gallery_application_version_read
        gallery_application_version_read.id = AAZStrType(
            flags={"read_only": True},
        )
        gallery_application_version_read.location = AAZStrType(
            flags={"required": True},
        )
        gallery_application_version_read.name = AAZStrType(
            flags={"read_only": True},
        )
        gallery_application_version_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        gallery_application_version_read.tags = AAZDictType()
        gallery_application_version_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_gallery_application_version_read.properties
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.publishing_profile = AAZObjectType(
            serialized_name="publishingProfile",
            flags={"required": True},
        )
        properties.replication_status = AAZObjectType(
            serialized_name="replicationStatus",
            flags={"read_only": True},
        )

        publishing_profile = _schema_gallery_application_version_read.properties.publishing_profile
        publishing_profile.advanced_settings = AAZDictType(
            serialized_name="advancedSettings",
        )
        publishing_profile.enable_health_check = AAZBoolType(
            serialized_name="enableHealthCheck",
        )
        publishing_profile.end_of_life_date = AAZStrType(
            serialized_name="endOfLifeDate",
        )
        publishing_profile.exclude_from_latest = AAZBoolType(
            serialized_name="excludeFromLatest",
        )
        publishing_profile.manage_actions = AAZObjectType(
            serialized_name="manageActions",
        )
        publishing_profile.published_date = AAZStrType(
            serialized_name="publishedDate",
            flags={"read_only": True},
        )
        publishing_profile.replica_count = AAZIntType(
            serialized_name="replicaCount",
        )
        publishing_profile.replication_mode = AAZStrType(
            serialized_name="replicationMode",
        )
        publishing_profile.settings = AAZObjectType()
        publishing_profile.source = AAZObjectType(
            flags={"required": True},
        )
        publishing_profile.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )
        publishing_profile.target_extended_locations = AAZListType(
            serialized_name="targetExtendedLocations",
        )
        publishing_profile.target_regions = AAZListType(
            serialized_name="targetRegions",
        )

        advanced_settings = _schema_gallery_application_version_read.properties.publishing_profile.advanced_settings
        advanced_settings.Element = AAZStrType()

        manage_actions = _schema_gallery_application_version_read.properties.publishing_profile.manage_actions
        manage_actions.install = AAZStrType(
            flags={"required": True},
        )
        manage_actions.remove = AAZStrType(
            flags={"required": True},
        )
        manage_actions.update = AAZStrType()

        settings = _schema_gallery_application_version_read.properties.publishing_profile.settings
        settings.config_file_name = AAZStrType(
            serialized_name="configFileName",
        )
        settings.package_file_name = AAZStrType(
            serialized_name="packageFileName",
        )

        source = _schema_gallery_application_version_read.properties.publishing_profile.source
        source.default_configuration_link = AAZStrType(
            serialized_name="defaultConfigurationLink",
        )
        source.media_link = AAZStrType(
            serialized_name="mediaLink",
            flags={"required": True},
        )

        target_extended_locations = _schema_gallery_application_version_read.properties.publishing_profile.target_extended_locations
        target_extended_locations.Element = AAZObjectType()

        _element = _schema_gallery_application_version_read.properties.publishing_profile.target_extended_locations.Element
        _element.encryption = AAZObjectType()
        cls._build_schema_encryption_images_read(_element.encryption)
        _element.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        _element.extended_location_replica_count = AAZIntType(
            serialized_name="extendedLocationReplicaCount",
        )
        _element.name = AAZStrType()
        _element.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        extended_location = _schema_gallery_application_version_read.properties.publishing_profile.target_extended_locations.Element.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        target_regions = _schema_gallery_application_version_read.properties.publishing_profile.target_regions
        target_regions.Element = AAZObjectType()

        _element = _schema_gallery_application_version_read.properties.publishing_profile.target_regions.Element
        _element.encryption = AAZObjectType()
        cls._build_schema_encryption_images_read(_element.encryption)
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.regional_replica_count = AAZIntType(
            serialized_name="regionalReplicaCount",
        )
        _element.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        replication_status = _schema_gallery_application_version_read.properties.replication_status
        replication_status.aggregated_state = AAZStrType(
            serialized_name="aggregatedState",
            flags={"read_only": True},
        )
        replication_status.summary = AAZListType(
            flags={"read_only": True},
        )

        summary = _schema_gallery_application_version_read.properties.replication_status.summary
        summary.Element = AAZObjectType()

        _element = _schema_gallery_application_version_read.properties.replication_status.summary.Element
        _element.details = AAZStrType(
            flags={"read_only": True},
        )
        _element.progress = AAZIntType(
            flags={"read_only": True},
        )
        _element.region = AAZStrType(
            flags={"read_only": True},
        )
        _element.state = AAZStrType(
            flags={"read_only": True},
        )

        tags = _schema_gallery_application_version_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_gallery_application_version_read.id
        _schema.location = cls._schema_gallery_application_version_read.location
        _schema.name = cls._schema_gallery_application_version_read.name
        _schema.properties = cls._schema_gallery_application_version_read.properties
        _schema.tags = cls._schema_gallery_application_version_read.tags
        _schema.type = cls._schema_gallery_application_version_read.type


__all__ = ["Update"]
