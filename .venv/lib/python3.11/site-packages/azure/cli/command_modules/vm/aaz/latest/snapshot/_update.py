# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "snapshot update",
)
class Update(AAZCommand):
    """Update a snapshot.

    :example: Update a snapshot and associate it with a disk access resource.
        az snapshot update --name MySnapshot --resource-group MyResourceGroup --network-access-policy AllowPrivate --disk-access MyDiskAccessID

    :example: Update a snapshot.
        az snapshot update --name MySnapshot --resource-group MyResourceGroup --subscription MySubscription
    """

    _aaz_info = {
        "version": "2025-01-02",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/snapshots/{}", "2025-01-02"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.snapshot_name = AAZStrArg(
            options=["-n", "--name", "--snapshot-name"],
            help="The name of the snapshot that is being created. The name can't be changed after the snapshot is created. Supported characters for the name are a-z, A-Z, 0-9, _ and -. The max name length is 80 characters.",
            required=True,
            id_part="name",
        )
        _args_schema.sku = AAZStrArg(
            options=["--sku"],
            help="The sku name.",
            nullable=True,
            enum={"Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        # define Arg Group "Encryption"

        _args_schema = cls._args_schema
        _args_schema.disk_encryption_set_id = AAZStrArg(
            options=["--disk-encryption-set-id"],
            arg_group="Encryption",
            help="ID of disk encryption set that is used to encrypt the disk.",
            nullable=True,
        )
        _args_schema.encryption_type = AAZStrArg(
            options=["--encryption-type"],
            arg_group="Encryption",
            help={"short-summary": "Encryption type.", "long-summary": "EncryptionAtRestWithPlatformKey: Disk is encrypted with XStore managed key at rest. It is the default encryption type. EncryptionAtRestWithCustomerKey: Disk is encrypted with Customer managed key at rest."},
            nullable=True,
            enum={"EncryptionAtRestWithCustomerKey": "EncryptionAtRestWithCustomerKey", "EncryptionAtRestWithPlatformAndCustomerKeys": "EncryptionAtRestWithPlatformAndCustomerKeys", "EncryptionAtRestWithPlatformKey": "EncryptionAtRestWithPlatformKey"},
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.disk_access_id = AAZStrArg(
            options=["--disk-access-id"],
            arg_group="Properties",
            help="ARM id of the DiskAccess resource for using private endpoints on disks.",
            nullable=True,
        )
        _args_schema.network_access_policy = AAZStrArg(
            options=["--network-access-policy"],
            arg_group="Properties",
            help="Policy for accessing the disk via network.",
            nullable=True,
            enum={"AllowAll": "AllowAll", "AllowPrivate": "AllowPrivate", "DenyAll": "DenyAll"},
        )
        _args_schema.public_network_access = AAZStrArg(
            options=["--public-network-access"],
            arg_group="Properties",
            help="Policy for controlling export on the disk.",
            is_preview=True,
            nullable=True,
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )

        # define Arg Group "Snapshot"

        # define Arg Group "SupportedCapabilities"

        _args_schema = cls._args_schema
        _args_schema.accelerated_network = AAZBoolArg(
            options=["--accelerated-network"],
            arg_group="SupportedCapabilities",
            help="Customers can set on Managed Disks or Snapshots to enable the accelerated networking if the OS disk image support.",
            is_preview=True,
            nullable=True,
        )
        _args_schema.architecture = AAZStrArg(
            options=["--architecture"],
            arg_group="SupportedCapabilities",
            help="CPU architecture.",
            nullable=True,
            enum={"Arm64": "Arm64", "x64": "x64"},
        )
        return cls._args_schema

    _args_image_disk_reference_update = None

    @classmethod
    def _build_args_image_disk_reference_update(cls, _schema):
        if cls._args_image_disk_reference_update is not None:
            _schema.community_gallery_image_id = cls._args_image_disk_reference_update.community_gallery_image_id
            _schema.id = cls._args_image_disk_reference_update.id
            _schema.lun = cls._args_image_disk_reference_update.lun
            _schema.shared_gallery_image_id = cls._args_image_disk_reference_update.shared_gallery_image_id
            return

        cls._args_image_disk_reference_update = AAZObjectArg(
            nullable=True,
        )

        image_disk_reference_update = cls._args_image_disk_reference_update
        image_disk_reference_update.community_gallery_image_id = AAZStrArg(
            options=["community-gallery-image-id"],
            help="A relative uri containing a community Azure Compute Gallery image reference.",
            nullable=True,
        )
        image_disk_reference_update.id = AAZStrArg(
            options=["id"],
            help="A relative uri containing either a Platform Image Repository, user image, or Azure Compute Gallery image reference.",
            nullable=True,
        )
        image_disk_reference_update.lun = AAZIntArg(
            options=["lun"],
            help="If the disk is created from an image's data disk, this is an index that indicates which of the data disks in the image to use. For OS disks, this field is null.",
            nullable=True,
        )
        image_disk_reference_update.shared_gallery_image_id = AAZStrArg(
            options=["shared-gallery-image-id"],
            help="A relative uri containing a direct shared Azure Compute Gallery image reference.",
            nullable=True,
        )

        _schema.community_gallery_image_id = cls._args_image_disk_reference_update.community_gallery_image_id
        _schema.id = cls._args_image_disk_reference_update.id
        _schema.lun = cls._args_image_disk_reference_update.lun
        _schema.shared_gallery_image_id = cls._args_image_disk_reference_update.shared_gallery_image_id

    _args_source_vault_update = None

    @classmethod
    def _build_args_source_vault_update(cls, _schema):
        if cls._args_source_vault_update is not None:
            _schema.id = cls._args_source_vault_update.id
            return

        cls._args_source_vault_update = AAZObjectArg()

        source_vault_update = cls._args_source_vault_update
        source_vault_update.id = AAZStrArg(
            options=["id"],
            help="Resource Id",
            nullable=True,
        )

        _schema.id = cls._args_source_vault_update.id

    def _execute_operations(self):
        self.pre_operations()
        self.SnapshotsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.SnapshotsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class SnapshotsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/snapshots/{snapshotName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "snapshotName", self.ctx.args.snapshot_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-02",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_snapshot_read(cls._schema_on_200)

            return cls._schema_on_200

    class SnapshotsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/snapshots/{snapshotName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "snapshotName", self.ctx.args.snapshot_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-02",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_snapshot_read(cls._schema_on_200)

            return cls._schema_on_200

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("sku", AAZObjectType)

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("diskAccessId", AAZStrType, ".disk_access_id")
                properties.set_prop("encryption", AAZObjectType)
                properties.set_prop("networkAccessPolicy", AAZStrType, ".network_access_policy")
                properties.set_prop("publicNetworkAccess", AAZStrType, ".public_network_access")
                properties.set_prop("supportedCapabilities", AAZObjectType)

            encryption = _builder.get(".properties.encryption")
            if encryption is not None:
                encryption.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
                encryption.set_prop("type", AAZStrType, ".encryption_type")

            supported_capabilities = _builder.get(".properties.supportedCapabilities")
            if supported_capabilities is not None:
                supported_capabilities.set_prop("acceleratedNetwork", AAZBoolType, ".accelerated_network")
                supported_capabilities.set_prop("architecture", AAZStrType, ".architecture")

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("name", AAZStrType, ".sku")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    @classmethod
    def _build_schema_image_disk_reference_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("communityGalleryImageId", AAZStrType, ".community_gallery_image_id")
        _builder.set_prop("id", AAZStrType, ".id")
        _builder.set_prop("lun", AAZIntType, ".lun")
        _builder.set_prop("sharedGalleryImageId", AAZStrType, ".shared_gallery_image_id")

    @classmethod
    def _build_schema_source_vault_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_image_disk_reference_read = None

    @classmethod
    def _build_schema_image_disk_reference_read(cls, _schema):
        if cls._schema_image_disk_reference_read is not None:
            _schema.community_gallery_image_id = cls._schema_image_disk_reference_read.community_gallery_image_id
            _schema.id = cls._schema_image_disk_reference_read.id
            _schema.lun = cls._schema_image_disk_reference_read.lun
            _schema.shared_gallery_image_id = cls._schema_image_disk_reference_read.shared_gallery_image_id
            return

        cls._schema_image_disk_reference_read = _schema_image_disk_reference_read = AAZObjectType()

        image_disk_reference_read = _schema_image_disk_reference_read
        image_disk_reference_read.community_gallery_image_id = AAZStrType(
            serialized_name="communityGalleryImageId",
        )
        image_disk_reference_read.id = AAZStrType()
        image_disk_reference_read.lun = AAZIntType()
        image_disk_reference_read.shared_gallery_image_id = AAZStrType(
            serialized_name="sharedGalleryImageId",
        )

        _schema.community_gallery_image_id = cls._schema_image_disk_reference_read.community_gallery_image_id
        _schema.id = cls._schema_image_disk_reference_read.id
        _schema.lun = cls._schema_image_disk_reference_read.lun
        _schema.shared_gallery_image_id = cls._schema_image_disk_reference_read.shared_gallery_image_id

    _schema_snapshot_read = None

    @classmethod
    def _build_schema_snapshot_read(cls, _schema):
        if cls._schema_snapshot_read is not None:
            _schema.extended_location = cls._schema_snapshot_read.extended_location
            _schema.id = cls._schema_snapshot_read.id
            _schema.location = cls._schema_snapshot_read.location
            _schema.managed_by = cls._schema_snapshot_read.managed_by
            _schema.name = cls._schema_snapshot_read.name
            _schema.properties = cls._schema_snapshot_read.properties
            _schema.sku = cls._schema_snapshot_read.sku
            _schema.system_data = cls._schema_snapshot_read.system_data
            _schema.tags = cls._schema_snapshot_read.tags
            _schema.type = cls._schema_snapshot_read.type
            return

        cls._schema_snapshot_read = _schema_snapshot_read = AAZObjectType()

        snapshot_read = _schema_snapshot_read
        snapshot_read.extended_location = AAZObjectType(
            serialized_name="extendedLocation",
        )
        snapshot_read.id = AAZStrType(
            flags={"read_only": True},
        )
        snapshot_read.location = AAZStrType(
            flags={"required": True},
        )
        snapshot_read.managed_by = AAZStrType(
            serialized_name="managedBy",
            flags={"read_only": True},
        )
        snapshot_read.name = AAZStrType(
            flags={"read_only": True},
        )
        snapshot_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        snapshot_read.sku = AAZObjectType()
        snapshot_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        snapshot_read.tags = AAZDictType()
        snapshot_read.type = AAZStrType(
            flags={"read_only": True},
        )

        extended_location = _schema_snapshot_read.extended_location
        extended_location.name = AAZStrType()
        extended_location.type = AAZStrType()

        properties = _schema_snapshot_read.properties
        properties.completion_percent = AAZFloatType(
            serialized_name="completionPercent",
        )
        properties.copy_completion_error = AAZObjectType(
            serialized_name="copyCompletionError",
        )
        properties.creation_data = AAZObjectType(
            serialized_name="creationData",
            flags={"required": True},
        )
        properties.data_access_auth_mode = AAZStrType(
            serialized_name="dataAccessAuthMode",
        )
        properties.disk_access_id = AAZStrType(
            serialized_name="diskAccessId",
        )
        properties.disk_size_bytes = AAZIntType(
            serialized_name="diskSizeBytes",
            flags={"read_only": True},
        )
        properties.disk_size_gb = AAZIntType(
            serialized_name="diskSizeGB",
        )
        properties.disk_state = AAZStrType(
            serialized_name="diskState",
            flags={"read_only": True},
        )
        properties.encryption = AAZObjectType()
        properties.encryption_settings_collection = AAZObjectType(
            serialized_name="encryptionSettingsCollection",
        )
        properties.hyper_v_generation = AAZStrType(
            serialized_name="hyperVGeneration",
        )
        properties.incremental = AAZBoolType()
        properties.incremental_snapshot_family_id = AAZStrType(
            serialized_name="incrementalSnapshotFamilyId",
            flags={"read_only": True},
        )
        properties.network_access_policy = AAZStrType(
            serialized_name="networkAccessPolicy",
        )
        properties.os_type = AAZStrType(
            serialized_name="osType",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.public_network_access = AAZStrType(
            serialized_name="publicNetworkAccess",
        )
        properties.purchase_plan = AAZObjectType(
            serialized_name="purchasePlan",
        )
        properties.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        properties.snapshot_access_state = AAZStrType(
            serialized_name="snapshotAccessState",
            flags={"read_only": True},
        )
        properties.supported_capabilities = AAZObjectType(
            serialized_name="supportedCapabilities",
        )
        properties.supports_hibernation = AAZBoolType(
            serialized_name="supportsHibernation",
        )
        properties.time_created = AAZStrType(
            serialized_name="timeCreated",
            flags={"read_only": True},
        )
        properties.unique_id = AAZStrType(
            serialized_name="uniqueId",
            flags={"read_only": True},
        )

        copy_completion_error = _schema_snapshot_read.properties.copy_completion_error
        copy_completion_error.error_code = AAZStrType(
            serialized_name="errorCode",
            flags={"required": True},
        )
        copy_completion_error.error_message = AAZStrType(
            serialized_name="errorMessage",
            flags={"required": True},
        )

        creation_data = _schema_snapshot_read.properties.creation_data
        creation_data.create_option = AAZStrType(
            serialized_name="createOption",
            flags={"required": True},
        )
        creation_data.elastic_san_resource_id = AAZStrType(
            serialized_name="elasticSanResourceId",
        )
        creation_data.gallery_image_reference = AAZObjectType(
            serialized_name="galleryImageReference",
        )
        cls._build_schema_image_disk_reference_read(creation_data.gallery_image_reference)
        creation_data.image_reference = AAZObjectType(
            serialized_name="imageReference",
        )
        cls._build_schema_image_disk_reference_read(creation_data.image_reference)
        creation_data.instant_access_duration_minutes = AAZIntType(
            serialized_name="instantAccessDurationMinutes",
        )
        creation_data.logical_sector_size = AAZIntType(
            serialized_name="logicalSectorSize",
        )
        creation_data.performance_plus = AAZBoolType(
            serialized_name="performancePlus",
        )
        creation_data.provisioned_bandwidth_copy_speed = AAZStrType(
            serialized_name="provisionedBandwidthCopySpeed",
        )
        creation_data.security_data_uri = AAZStrType(
            serialized_name="securityDataUri",
        )
        creation_data.security_metadata_uri = AAZStrType(
            serialized_name="securityMetadataUri",
        )
        creation_data.source_resource_id = AAZStrType(
            serialized_name="sourceResourceId",
        )
        creation_data.source_unique_id = AAZStrType(
            serialized_name="sourceUniqueId",
            flags={"read_only": True},
        )
        creation_data.source_uri = AAZStrType(
            serialized_name="sourceUri",
        )
        creation_data.storage_account_id = AAZStrType(
            serialized_name="storageAccountId",
        )
        creation_data.upload_size_bytes = AAZIntType(
            serialized_name="uploadSizeBytes",
        )

        encryption = _schema_snapshot_read.properties.encryption
        encryption.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        encryption.type = AAZStrType()

        encryption_settings_collection = _schema_snapshot_read.properties.encryption_settings_collection
        encryption_settings_collection.enabled = AAZBoolType(
            flags={"required": True},
        )
        encryption_settings_collection.encryption_settings = AAZListType(
            serialized_name="encryptionSettings",
        )
        encryption_settings_collection.encryption_settings_version = AAZStrType(
            serialized_name="encryptionSettingsVersion",
        )

        encryption_settings = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings
        encryption_settings.Element = AAZObjectType()

        _element = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings.Element
        _element.disk_encryption_key = AAZObjectType(
            serialized_name="diskEncryptionKey",
        )
        _element.key_encryption_key = AAZObjectType(
            serialized_name="keyEncryptionKey",
        )

        disk_encryption_key = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings.Element.disk_encryption_key
        disk_encryption_key.secret_url = AAZStrType(
            serialized_name="secretUrl",
            flags={"required": True},
        )
        disk_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_source_vault_read(disk_encryption_key.source_vault)

        key_encryption_key = _schema_snapshot_read.properties.encryption_settings_collection.encryption_settings.Element.key_encryption_key
        key_encryption_key.key_url = AAZStrType(
            serialized_name="keyUrl",
            flags={"required": True},
        )
        key_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_source_vault_read(key_encryption_key.source_vault)

        purchase_plan = _schema_snapshot_read.properties.purchase_plan
        purchase_plan.name = AAZStrType(
            flags={"required": True},
        )
        purchase_plan.product = AAZStrType(
            flags={"required": True},
        )
        purchase_plan.promotion_code = AAZStrType(
            serialized_name="promotionCode",
        )
        purchase_plan.publisher = AAZStrType(
            flags={"required": True},
        )

        security_profile = _schema_snapshot_read.properties.security_profile
        security_profile.secure_vm_disk_encryption_set_id = AAZStrType(
            serialized_name="secureVMDiskEncryptionSetId",
        )
        security_profile.security_type = AAZStrType(
            serialized_name="securityType",
        )

        supported_capabilities = _schema_snapshot_read.properties.supported_capabilities
        supported_capabilities.accelerated_network = AAZBoolType(
            serialized_name="acceleratedNetwork",
        )
        supported_capabilities.architecture = AAZStrType()
        supported_capabilities.disk_controller_types = AAZStrType(
            serialized_name="diskControllerTypes",
        )
        supported_capabilities.supported_security_option = AAZStrType(
            serialized_name="supportedSecurityOption",
        )

        sku = _schema_snapshot_read.sku
        sku.name = AAZStrType()
        sku.tier = AAZStrType(
            flags={"read_only": True},
        )

        system_data = _schema_snapshot_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_snapshot_read.tags
        tags.Element = AAZStrType()

        _schema.extended_location = cls._schema_snapshot_read.extended_location
        _schema.id = cls._schema_snapshot_read.id
        _schema.location = cls._schema_snapshot_read.location
        _schema.managed_by = cls._schema_snapshot_read.managed_by
        _schema.name = cls._schema_snapshot_read.name
        _schema.properties = cls._schema_snapshot_read.properties
        _schema.sku = cls._schema_snapshot_read.sku
        _schema.system_data = cls._schema_snapshot_read.system_data
        _schema.tags = cls._schema_snapshot_read.tags
        _schema.type = cls._schema_snapshot_read.type

    _schema_source_vault_read = None

    @classmethod
    def _build_schema_source_vault_read(cls, _schema):
        if cls._schema_source_vault_read is not None:
            _schema.id = cls._schema_source_vault_read.id
            return

        cls._schema_source_vault_read = _schema_source_vault_read = AAZObjectType()

        source_vault_read = _schema_source_vault_read
        source_vault_read.id = AAZStrType()

        _schema.id = cls._schema_source_vault_read.id


__all__ = ["Update"]
