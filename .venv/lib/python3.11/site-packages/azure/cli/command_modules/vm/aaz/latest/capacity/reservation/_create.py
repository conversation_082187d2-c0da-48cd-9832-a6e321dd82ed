# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "capacity reservation create",
)
class Create(AAZCommand):
    """Create operation to create or update a capacity reservation. Please note some properties can be set only during capacity reservation creation. Please refer to https://aka.ms/CapacityReservation for more details.

    :example: Create a capacity reservation.
        az capacity reservation create -c ReservationGroupName -n ReservationName -g MyResourceGroup --sku Standard_A0

    :example: Create a capacity reservation with specific capacity and zones.
        az capacity reservation create -c ReservationGroupName -n ReservationName -l centraluseuap -g MyResourceGroup  --sku Standard_A1_v2 --capacity 5 --zone 1 --tags key=val
    """

    _aaz_info = {
        "version": "2024-11-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/capacityreservationgroups/{}/capacityreservations/{}", "2024-11-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.capacity_reservation_group = AAZStrArg(
            options=["-c", "--capacity-reservation-group"],
            help="The name of the capacity reservation group.",
            required=True,
        )
        _args_schema.capacity_reservation_name = AAZStrArg(
            options=["-n", "--name", "--capacity-reservation-name"],
            help="The name of the capacity reservation.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Parameters",
            help="Resource location",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Parameters",
            help="Resource tags",
        )
        _args_schema.zone = AAZListArg(
            options=["-z", "--zone"],
            arg_group="Parameters",
            help="Availability Zone to use for this capacity reservation. The zone has to be single value and also should be part for the list of zones specified during the capacity reservation group creation. The zone can be assigned only during creation. If not provided, the reservation supports only non-zonal deployments. If provided, enforces VM/VMSS using this capacity reservation to be in same zone.",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        zone = cls._args_schema.zone
        zone.Element = AAZStrArg()

        # define Arg Group "Sku"

        _args_schema = cls._args_schema
        _args_schema.capacity = AAZIntArg(
            options=["--capacity"],
            arg_group="Sku",
            help="Specify the number of virtual machines in the scale set.",
        )
        _args_schema.sku = AAZStrArg(
            options=["-s", "--sku"],
            arg_group="Sku",
            help="The SKU of the resource for which capacity needs be reserved. Currently VM Skus with the capability called \\\"CapacityReservationSupported\\\" set to true are supported. Refer to List Microsoft.Compute SKUs in a region (https://learn.microsoft.com/rest/api/compute/resourceskus/list) for supported values.",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.CapacityReservationsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class CapacityReservationsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/capacityReservationGroups/{capacityReservationGroupName}/capacityReservations/{capacityReservationName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "capacityReservationGroupName", self.ctx.args.capacity_reservation_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "capacityReservationName", self.ctx.args.capacity_reservation_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-11-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("sku", AAZObjectType, ".", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zone")

            sku = _builder.get(".sku")
            if sku is not None:
                sku.set_prop("capacity", AAZIntType, ".capacity")
                sku.set_prop("name", AAZStrType, ".sku")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200_201.sku = AAZObjectType(
                flags={"required": True},
            )
            _schema_on_200_201.tags = AAZDictType()
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.zones = AAZListType()

            properties = cls._schema_on_200_201.properties
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
                flags={"read_only": True},
            )
            properties.platform_fault_domain_count = AAZIntType(
                serialized_name="platformFaultDomainCount",
                flags={"read_only": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.provisioning_time = AAZStrType(
                serialized_name="provisioningTime",
                flags={"read_only": True},
            )
            properties.reservation_id = AAZStrType(
                serialized_name="reservationId",
                flags={"read_only": True},
            )
            properties.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            properties.virtual_machines_associated = AAZListType(
                serialized_name="virtualMachinesAssociated",
                flags={"read_only": True},
            )

            instance_view = cls._schema_on_200_201.properties.instance_view
            instance_view.statuses = AAZListType()
            instance_view.utilization_info = AAZObjectType(
                serialized_name="utilizationInfo",
            )

            statuses = cls._schema_on_200_201.properties.instance_view.statuses
            statuses.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.instance_view.statuses.Element
            _element.code = AAZStrType()
            _element.display_status = AAZStrType(
                serialized_name="displayStatus",
            )
            _element.level = AAZStrType()
            _element.message = AAZStrType()
            _element.time = AAZStrType()

            utilization_info = cls._schema_on_200_201.properties.instance_view.utilization_info
            utilization_info.current_capacity = AAZIntType(
                serialized_name="currentCapacity",
                flags={"read_only": True},
            )
            utilization_info.virtual_machines_allocated = AAZListType(
                serialized_name="virtualMachinesAllocated",
                flags={"read_only": True},
            )

            virtual_machines_allocated = cls._schema_on_200_201.properties.instance_view.utilization_info.virtual_machines_allocated
            virtual_machines_allocated.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read_only_read(virtual_machines_allocated.Element)

            virtual_machines_associated = cls._schema_on_200_201.properties.virtual_machines_associated
            virtual_machines_associated.Element = AAZObjectType()
            _CreateHelper._build_schema_sub_resource_read_only_read(virtual_machines_associated.Element)

            sku = cls._schema_on_200_201.sku
            sku.capacity = AAZIntType()
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            tags = cls._schema_on_200_201.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200_201.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    _schema_sub_resource_read_only_read = None

    @classmethod
    def _build_schema_sub_resource_read_only_read(cls, _schema):
        if cls._schema_sub_resource_read_only_read is not None:
            _schema.id = cls._schema_sub_resource_read_only_read.id
            return

        cls._schema_sub_resource_read_only_read = _schema_sub_resource_read_only_read = AAZObjectType()

        sub_resource_read_only_read = _schema_sub_resource_read_only_read
        sub_resource_read_only_read.id = AAZStrType(
            flags={"read_only": True},
        )

        _schema.id = cls._schema_sub_resource_read_only_read.id


__all__ = ["Create"]
