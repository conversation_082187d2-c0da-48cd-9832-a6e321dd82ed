# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class Show(AAZCommand):
    """Get information about the model view or the instance view of a virtual machine.
    """

    _aaz_info = {
        "version": "2024-11-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachines/{}", "2024-11-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.vm_name = AAZStrArg(
            options=["-n", "--name", "--vm-name"],
            help="The name of the virtual machine.",
            required=True,
            id_part="name",
        )
        _args_schema.expand = AAZStrArg(
            options=["--expand"],
            help="The expand expression to apply on the operation. 'InstanceView' retrieves a snapshot of the runtime properties of the virtual machine that is managed by the platform and can change outside of control plane operations. 'UserData' retrieves the UserData property as part of the VM model view that was provided by the user during the VM Create/Update operation.",
            enum={"instanceView": "instanceView", "userData": "userData"},
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.VirtualMachinesGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VirtualMachinesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachines/{vmName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "vmName", self.ctx.args.vm_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$expand", self.ctx.args.expand,
                ),
                **self.serialize_query_param(
                    "api-version", "2024-11-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.extended_location = AAZObjectType(
                serialized_name="extendedLocation",
            )
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.identity = AAZIdentityObjectType()
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.managed_by = AAZStrType(
                serialized_name="managedBy",
                flags={"read_only": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.placement = AAZObjectType()
            _schema_on_200.plan = AAZObjectType()
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.resources = AAZListType(
                flags={"read_only": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.zones = AAZListType()

            extended_location = cls._schema_on_200.extended_location
            extended_location.name = AAZStrType()
            extended_location.type = AAZStrType()

            identity = cls._schema_on_200.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            placement = cls._schema_on_200.placement
            placement.exclude_zones = AAZListType(
                serialized_name="excludeZones",
            )
            placement.include_zones = AAZListType(
                serialized_name="includeZones",
            )
            placement.zone_placement_policy = AAZStrType(
                serialized_name="zonePlacementPolicy",
            )

            exclude_zones = cls._schema_on_200.placement.exclude_zones
            exclude_zones.Element = AAZStrType()

            include_zones = cls._schema_on_200.placement.include_zones
            include_zones.Element = AAZStrType()

            plan = cls._schema_on_200.plan
            plan.name = AAZStrType()
            plan.product = AAZStrType()
            plan.promotion_code = AAZStrType(
                serialized_name="promotionCode",
            )
            plan.publisher = AAZStrType()

            properties = cls._schema_on_200.properties
            properties.additional_capabilities = AAZObjectType(
                serialized_name="additionalCapabilities",
            )
            properties.application_profile = AAZObjectType(
                serialized_name="applicationProfile",
            )
            properties.availability_set = AAZObjectType(
                serialized_name="availabilitySet",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.availability_set)
            properties.billing_profile = AAZObjectType(
                serialized_name="billingProfile",
            )
            properties.capacity_reservation = AAZObjectType(
                serialized_name="capacityReservation",
            )
            properties.diagnostics_profile = AAZObjectType(
                serialized_name="diagnosticsProfile",
            )
            properties.eviction_policy = AAZStrType(
                serialized_name="evictionPolicy",
            )
            properties.extensions_time_budget = AAZStrType(
                serialized_name="extensionsTimeBudget",
            )
            properties.hardware_profile = AAZObjectType(
                serialized_name="hardwareProfile",
            )
            properties.host = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_read(properties.host)
            properties.host_group = AAZObjectType(
                serialized_name="hostGroup",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.host_group)
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
                flags={"read_only": True},
            )
            properties.license_type = AAZStrType(
                serialized_name="licenseType",
            )
            properties.network_profile = AAZObjectType(
                serialized_name="networkProfile",
            )
            properties.os_profile = AAZObjectType(
                serialized_name="osProfile",
            )
            properties.platform_fault_domain = AAZIntType(
                serialized_name="platformFaultDomain",
            )
            properties.priority = AAZStrType()
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.proximity_placement_group = AAZObjectType(
                serialized_name="proximityPlacementGroup",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.proximity_placement_group)
            properties.scheduled_events_policy = AAZObjectType(
                serialized_name="scheduledEventsPolicy",
            )
            properties.scheduled_events_profile = AAZObjectType(
                serialized_name="scheduledEventsProfile",
            )
            properties.security_profile = AAZObjectType(
                serialized_name="securityProfile",
            )
            properties.storage_profile = AAZObjectType(
                serialized_name="storageProfile",
            )
            properties.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            properties.user_data = AAZStrType(
                serialized_name="userData",
            )
            properties.virtual_machine_scale_set = AAZObjectType(
                serialized_name="virtualMachineScaleSet",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.virtual_machine_scale_set)
            properties.vm_id = AAZStrType(
                serialized_name="vmId",
                flags={"read_only": True},
            )

            additional_capabilities = cls._schema_on_200.properties.additional_capabilities
            additional_capabilities.hibernation_enabled = AAZBoolType(
                serialized_name="hibernationEnabled",
            )
            additional_capabilities.ultra_ssd_enabled = AAZBoolType(
                serialized_name="ultraSSDEnabled",
            )

            application_profile = cls._schema_on_200.properties.application_profile
            application_profile.gallery_applications = AAZListType(
                serialized_name="galleryApplications",
            )

            gallery_applications = cls._schema_on_200.properties.application_profile.gallery_applications
            gallery_applications.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.application_profile.gallery_applications.Element
            _element.configuration_reference = AAZStrType(
                serialized_name="configurationReference",
            )
            _element.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            _element.order = AAZIntType()
            _element.package_reference_id = AAZStrType(
                serialized_name="packageReferenceId",
                flags={"required": True},
            )
            _element.tags = AAZStrType()
            _element.treat_failure_as_deployment_failure = AAZBoolType(
                serialized_name="treatFailureAsDeploymentFailure",
            )

            billing_profile = cls._schema_on_200.properties.billing_profile
            billing_profile.max_price = AAZFloatType(
                serialized_name="maxPrice",
            )

            capacity_reservation = cls._schema_on_200.properties.capacity_reservation
            capacity_reservation.capacity_reservation_group = AAZObjectType(
                serialized_name="capacityReservationGroup",
            )
            _ShowHelper._build_schema_sub_resource_read(capacity_reservation.capacity_reservation_group)

            diagnostics_profile = cls._schema_on_200.properties.diagnostics_profile
            diagnostics_profile.boot_diagnostics = AAZObjectType(
                serialized_name="bootDiagnostics",
            )

            boot_diagnostics = cls._schema_on_200.properties.diagnostics_profile.boot_diagnostics
            boot_diagnostics.enabled = AAZBoolType()
            boot_diagnostics.storage_uri = AAZStrType(
                serialized_name="storageUri",
            )

            hardware_profile = cls._schema_on_200.properties.hardware_profile
            hardware_profile.vm_size = AAZStrType(
                serialized_name="vmSize",
            )
            hardware_profile.vm_size_properties = AAZObjectType(
                serialized_name="vmSizeProperties",
            )

            vm_size_properties = cls._schema_on_200.properties.hardware_profile.vm_size_properties
            vm_size_properties.v_cp_us_available = AAZIntType(
                serialized_name="vCPUsAvailable",
            )
            vm_size_properties.v_cp_us_per_core = AAZIntType(
                serialized_name="vCPUsPerCore",
            )

            instance_view = cls._schema_on_200.properties.instance_view
            instance_view.assigned_host = AAZStrType(
                serialized_name="assignedHost",
                flags={"read_only": True},
            )
            instance_view.boot_diagnostics = AAZObjectType(
                serialized_name="bootDiagnostics",
            )
            instance_view.computer_name = AAZStrType(
                serialized_name="computerName",
            )
            instance_view.disks = AAZListType()
            instance_view.extensions = AAZListType()
            instance_view.hyper_v_generation = AAZStrType(
                serialized_name="hyperVGeneration",
            )
            instance_view.is_vm_in_standby_pool = AAZBoolType(
                serialized_name="isVMInStandbyPool",
                flags={"read_only": True},
            )
            instance_view.maintenance_redeploy_status = AAZObjectType(
                serialized_name="maintenanceRedeployStatus",
            )
            instance_view.os_name = AAZStrType(
                serialized_name="osName",
            )
            instance_view.os_version = AAZStrType(
                serialized_name="osVersion",
            )
            instance_view.patch_status = AAZObjectType(
                serialized_name="patchStatus",
            )
            instance_view.platform_fault_domain = AAZIntType(
                serialized_name="platformFaultDomain",
            )
            instance_view.platform_update_domain = AAZIntType(
                serialized_name="platformUpdateDomain",
            )
            instance_view.rdp_thumb_print = AAZStrType(
                serialized_name="rdpThumbPrint",
            )
            instance_view.statuses = AAZListType()
            instance_view.vm_agent = AAZObjectType(
                serialized_name="vmAgent",
            )
            instance_view.vm_health = AAZObjectType(
                serialized_name="vmHealth",
                flags={"read_only": True},
            )

            boot_diagnostics = cls._schema_on_200.properties.instance_view.boot_diagnostics
            boot_diagnostics.console_screenshot_blob_uri = AAZStrType(
                serialized_name="consoleScreenshotBlobUri",
                flags={"read_only": True},
            )
            boot_diagnostics.serial_console_log_blob_uri = AAZStrType(
                serialized_name="serialConsoleLogBlobUri",
                flags={"read_only": True},
            )
            boot_diagnostics.status = AAZObjectType(
                flags={"read_only": True},
            )
            _ShowHelper._build_schema_instance_view_status_read(boot_diagnostics.status)

            disks = cls._schema_on_200.properties.instance_view.disks
            disks.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.instance_view.disks.Element
            _element.encryption_settings = AAZListType(
                serialized_name="encryptionSettings",
            )
            _element.name = AAZStrType()
            _element.statuses = AAZListType()

            encryption_settings = cls._schema_on_200.properties.instance_view.disks.Element.encryption_settings
            encryption_settings.Element = AAZObjectType()
            _ShowHelper._build_schema_disk_encryption_settings_read(encryption_settings.Element)

            statuses = cls._schema_on_200.properties.instance_view.disks.Element.statuses
            statuses.Element = AAZObjectType()
            _ShowHelper._build_schema_instance_view_status_read(statuses.Element)

            extensions = cls._schema_on_200.properties.instance_view.extensions
            extensions.Element = AAZObjectType()
            _ShowHelper._build_schema_virtual_machine_extension_instance_view_read(extensions.Element)

            maintenance_redeploy_status = cls._schema_on_200.properties.instance_view.maintenance_redeploy_status
            maintenance_redeploy_status.is_customer_initiated_maintenance_allowed = AAZBoolType(
                serialized_name="isCustomerInitiatedMaintenanceAllowed",
            )
            maintenance_redeploy_status.last_operation_message = AAZStrType(
                serialized_name="lastOperationMessage",
            )
            maintenance_redeploy_status.last_operation_result_code = AAZStrType(
                serialized_name="lastOperationResultCode",
            )
            maintenance_redeploy_status.maintenance_window_end_time = AAZStrType(
                serialized_name="maintenanceWindowEndTime",
            )
            maintenance_redeploy_status.maintenance_window_start_time = AAZStrType(
                serialized_name="maintenanceWindowStartTime",
            )
            maintenance_redeploy_status.pre_maintenance_window_end_time = AAZStrType(
                serialized_name="preMaintenanceWindowEndTime",
            )
            maintenance_redeploy_status.pre_maintenance_window_start_time = AAZStrType(
                serialized_name="preMaintenanceWindowStartTime",
            )

            patch_status = cls._schema_on_200.properties.instance_view.patch_status
            patch_status.available_patch_summary = AAZObjectType(
                serialized_name="availablePatchSummary",
            )
            patch_status.configuration_statuses = AAZListType(
                serialized_name="configurationStatuses",
                flags={"read_only": True},
            )
            patch_status.last_patch_installation_summary = AAZObjectType(
                serialized_name="lastPatchInstallationSummary",
            )

            available_patch_summary = cls._schema_on_200.properties.instance_view.patch_status.available_patch_summary
            available_patch_summary.assessment_activity_id = AAZStrType(
                serialized_name="assessmentActivityId",
                flags={"read_only": True},
            )
            available_patch_summary.critical_and_security_patch_count = AAZIntType(
                serialized_name="criticalAndSecurityPatchCount",
                flags={"read_only": True},
            )
            available_patch_summary.error = AAZObjectType(
                flags={"read_only": True},
            )
            _ShowHelper._build_schema_api_error_read(available_patch_summary.error)
            available_patch_summary.last_modified_time = AAZStrType(
                serialized_name="lastModifiedTime",
                flags={"read_only": True},
            )
            available_patch_summary.other_patch_count = AAZIntType(
                serialized_name="otherPatchCount",
                flags={"read_only": True},
            )
            available_patch_summary.reboot_pending = AAZBoolType(
                serialized_name="rebootPending",
                flags={"read_only": True},
            )
            available_patch_summary.start_time = AAZStrType(
                serialized_name="startTime",
                flags={"read_only": True},
            )
            available_patch_summary.status = AAZStrType(
                flags={"read_only": True},
            )

            configuration_statuses = cls._schema_on_200.properties.instance_view.patch_status.configuration_statuses
            configuration_statuses.Element = AAZObjectType()
            _ShowHelper._build_schema_instance_view_status_read(configuration_statuses.Element)

            last_patch_installation_summary = cls._schema_on_200.properties.instance_view.patch_status.last_patch_installation_summary
            last_patch_installation_summary.error = AAZObjectType(
                flags={"read_only": True},
            )
            _ShowHelper._build_schema_api_error_read(last_patch_installation_summary.error)
            last_patch_installation_summary.excluded_patch_count = AAZIntType(
                serialized_name="excludedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.failed_patch_count = AAZIntType(
                serialized_name="failedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.installation_activity_id = AAZStrType(
                serialized_name="installationActivityId",
                flags={"read_only": True},
            )
            last_patch_installation_summary.installed_patch_count = AAZIntType(
                serialized_name="installedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.last_modified_time = AAZStrType(
                serialized_name="lastModifiedTime",
                flags={"read_only": True},
            )
            last_patch_installation_summary.maintenance_window_exceeded = AAZBoolType(
                serialized_name="maintenanceWindowExceeded",
                flags={"read_only": True},
            )
            last_patch_installation_summary.not_selected_patch_count = AAZIntType(
                serialized_name="notSelectedPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.pending_patch_count = AAZIntType(
                serialized_name="pendingPatchCount",
                flags={"read_only": True},
            )
            last_patch_installation_summary.start_time = AAZStrType(
                serialized_name="startTime",
                flags={"read_only": True},
            )
            last_patch_installation_summary.status = AAZStrType(
                flags={"read_only": True},
            )

            statuses = cls._schema_on_200.properties.instance_view.statuses
            statuses.Element = AAZObjectType()
            _ShowHelper._build_schema_instance_view_status_read(statuses.Element)

            vm_agent = cls._schema_on_200.properties.instance_view.vm_agent
            vm_agent.extension_handlers = AAZListType(
                serialized_name="extensionHandlers",
            )
            vm_agent.statuses = AAZListType()
            vm_agent.vm_agent_version = AAZStrType(
                serialized_name="vmAgentVersion",
            )

            extension_handlers = cls._schema_on_200.properties.instance_view.vm_agent.extension_handlers
            extension_handlers.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.instance_view.vm_agent.extension_handlers.Element
            _element.status = AAZObjectType()
            _ShowHelper._build_schema_instance_view_status_read(_element.status)
            _element.type = AAZStrType()
            _element.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            statuses = cls._schema_on_200.properties.instance_view.vm_agent.statuses
            statuses.Element = AAZObjectType()
            _ShowHelper._build_schema_instance_view_status_read(statuses.Element)

            vm_health = cls._schema_on_200.properties.instance_view.vm_health
            vm_health.status = AAZObjectType(
                flags={"read_only": True},
            )
            _ShowHelper._build_schema_instance_view_status_read(vm_health.status)

            network_profile = cls._schema_on_200.properties.network_profile
            network_profile.network_api_version = AAZStrType(
                serialized_name="networkApiVersion",
            )
            network_profile.network_interface_configurations = AAZListType(
                serialized_name="networkInterfaceConfigurations",
            )
            network_profile.network_interfaces = AAZListType(
                serialized_name="networkInterfaces",
            )

            network_interface_configurations = cls._schema_on_200.properties.network_profile.network_interface_configurations
            network_interface_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties
            properties.auxiliary_mode = AAZStrType(
                serialized_name="auxiliaryMode",
            )
            properties.auxiliary_sku = AAZStrType(
                serialized_name="auxiliarySku",
            )
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.disable_tcp_state_tracking = AAZBoolType(
                serialized_name="disableTcpStateTracking",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.dscp_configuration = AAZObjectType(
                serialized_name="dscpConfiguration",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.dscp_configuration)
            properties.enable_accelerated_networking = AAZBoolType(
                serialized_name="enableAcceleratedNetworking",
            )
            properties.enable_fpga = AAZBoolType(
                serialized_name="enableFpga",
            )
            properties.enable_ip_forwarding = AAZBoolType(
                serialized_name="enableIPForwarding",
            )
            properties.ip_configurations = AAZListType(
                serialized_name="ipConfigurations",
                flags={"required": True},
            )
            properties.network_security_group = AAZObjectType(
                serialized_name="networkSecurityGroup",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.network_security_group)
            properties.primary = AAZBoolType()

            dns_settings = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.dns_settings
            dns_settings.dns_servers = AAZListType(
                serialized_name="dnsServers",
            )

            dns_servers = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.dns_settings.dns_servers
            dns_servers.Element = AAZStrType()

            ip_configurations = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations
            ip_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties
            properties.application_gateway_backend_address_pools = AAZListType(
                serialized_name="applicationGatewayBackendAddressPools",
            )
            properties.application_security_groups = AAZListType(
                serialized_name="applicationSecurityGroups",
            )
            properties.load_balancer_backend_address_pools = AAZListType(
                serialized_name="loadBalancerBackendAddressPools",
            )
            properties.primary = AAZBoolType()
            properties.private_ip_address_version = AAZStrType(
                serialized_name="privateIPAddressVersion",
            )
            properties.public_ip_address_configuration = AAZObjectType(
                serialized_name="publicIPAddressConfiguration",
            )
            properties.subnet = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_read(properties.subnet)

            application_gateway_backend_address_pools = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_gateway_backend_address_pools
            application_gateway_backend_address_pools.Element = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_read(application_gateway_backend_address_pools.Element)

            application_security_groups = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_security_groups
            application_security_groups.Element = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_read(application_security_groups.Element)

            load_balancer_backend_address_pools = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_backend_address_pools
            load_balancer_backend_address_pools.Element = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_read(load_balancer_backend_address_pools.Element)

            public_ip_address_configuration = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration
            public_ip_address_configuration.name = AAZStrType(
                flags={"required": True},
            )
            public_ip_address_configuration.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            public_ip_address_configuration.sku = AAZObjectType()

            properties = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.idle_timeout_in_minutes = AAZIntType(
                serialized_name="idleTimeoutInMinutes",
            )
            properties.ip_tags = AAZListType(
                serialized_name="ipTags",
            )
            properties.public_ip_address_version = AAZStrType(
                serialized_name="publicIPAddressVersion",
            )
            properties.public_ip_allocation_method = AAZStrType(
                serialized_name="publicIPAllocationMethod",
            )
            properties.public_ip_prefix = AAZObjectType(
                serialized_name="publicIPPrefix",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.public_ip_prefix)

            dns_settings = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.dns_settings
            dns_settings.domain_name_label = AAZStrType(
                serialized_name="domainNameLabel",
                flags={"required": True},
            )
            dns_settings.domain_name_label_scope = AAZStrType(
                serialized_name="domainNameLabelScope",
            )

            ip_tags = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags
            ip_tags.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags.Element
            _element.ip_tag_type = AAZStrType(
                serialized_name="ipTagType",
            )
            _element.tag = AAZStrType()

            sku = cls._schema_on_200.properties.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            network_interfaces = cls._schema_on_200.properties.network_profile.network_interfaces
            network_interfaces.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.network_profile.network_interfaces.Element
            _element.id = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.properties.network_profile.network_interfaces.Element.properties
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.primary = AAZBoolType()

            os_profile = cls._schema_on_200.properties.os_profile
            os_profile.admin_password = AAZStrType(
                serialized_name="adminPassword",
                flags={"secret": True},
            )
            os_profile.admin_username = AAZStrType(
                serialized_name="adminUsername",
            )
            os_profile.allow_extension_operations = AAZBoolType(
                serialized_name="allowExtensionOperations",
            )
            os_profile.computer_name = AAZStrType(
                serialized_name="computerName",
            )
            os_profile.custom_data = AAZStrType(
                serialized_name="customData",
            )
            os_profile.linux_configuration = AAZObjectType(
                serialized_name="linuxConfiguration",
            )
            os_profile.require_guest_provision_signal = AAZBoolType(
                serialized_name="requireGuestProvisionSignal",
            )
            os_profile.secrets = AAZListType()
            os_profile.windows_configuration = AAZObjectType(
                serialized_name="windowsConfiguration",
            )

            linux_configuration = cls._schema_on_200.properties.os_profile.linux_configuration
            linux_configuration.disable_password_authentication = AAZBoolType(
                serialized_name="disablePasswordAuthentication",
            )
            linux_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
            )
            linux_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            linux_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            linux_configuration.ssh = AAZObjectType()

            patch_settings = cls._schema_on_200.properties.os_profile.linux_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.properties.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            ssh = cls._schema_on_200.properties.os_profile.linux_configuration.ssh
            ssh.public_keys = AAZListType(
                serialized_name="publicKeys",
            )

            public_keys = cls._schema_on_200.properties.os_profile.linux_configuration.ssh.public_keys
            public_keys.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.linux_configuration.ssh.public_keys.Element
            _element.key_data = AAZStrType(
                serialized_name="keyData",
            )
            _element.path = AAZStrType()

            secrets = cls._schema_on_200.properties.os_profile.secrets
            secrets.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.secrets.Element
            _element.source_vault = AAZObjectType(
                serialized_name="sourceVault",
            )
            _ShowHelper._build_schema_sub_resource_read(_element.source_vault)
            _element.vault_certificates = AAZListType(
                serialized_name="vaultCertificates",
            )

            vault_certificates = cls._schema_on_200.properties.os_profile.secrets.Element.vault_certificates
            vault_certificates.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.secrets.Element.vault_certificates.Element
            _element.certificate_store = AAZStrType(
                serialized_name="certificateStore",
            )
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )

            windows_configuration = cls._schema_on_200.properties.os_profile.windows_configuration
            windows_configuration.additional_unattend_content = AAZListType(
                serialized_name="additionalUnattendContent",
            )
            windows_configuration.enable_automatic_updates = AAZBoolType(
                serialized_name="enableAutomaticUpdates",
            )
            windows_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
                flags={"read_only": True},
            )
            windows_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            windows_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            windows_configuration.time_zone = AAZStrType(
                serialized_name="timeZone",
            )
            windows_configuration.win_rm = AAZObjectType(
                serialized_name="winRM",
            )

            additional_unattend_content = cls._schema_on_200.properties.os_profile.windows_configuration.additional_unattend_content
            additional_unattend_content.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.windows_configuration.additional_unattend_content.Element
            _element.component_name = AAZStrType(
                serialized_name="componentName",
            )
            _element.content = AAZStrType()
            _element.pass_name = AAZStrType(
                serialized_name="passName",
            )
            _element.setting_name = AAZStrType(
                serialized_name="settingName",
            )

            patch_settings = cls._schema_on_200.properties.os_profile.windows_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.enable_hotpatching = AAZBoolType(
                serialized_name="enableHotpatching",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.properties.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            win_rm = cls._schema_on_200.properties.os_profile.windows_configuration.win_rm
            win_rm.listeners = AAZListType()

            listeners = cls._schema_on_200.properties.os_profile.windows_configuration.win_rm.listeners
            listeners.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.os_profile.windows_configuration.win_rm.listeners.Element
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )
            _element.protocol = AAZStrType()

            scheduled_events_policy = cls._schema_on_200.properties.scheduled_events_policy
            scheduled_events_policy.scheduled_events_additional_publishing_targets = AAZObjectType(
                serialized_name="scheduledEventsAdditionalPublishingTargets",
            )
            scheduled_events_policy.user_initiated_reboot = AAZObjectType(
                serialized_name="userInitiatedReboot",
            )
            scheduled_events_policy.user_initiated_redeploy = AAZObjectType(
                serialized_name="userInitiatedRedeploy",
            )

            scheduled_events_additional_publishing_targets = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets
            scheduled_events_additional_publishing_targets.event_grid_and_resource_graph = AAZObjectType(
                serialized_name="eventGridAndResourceGraph",
            )

            event_grid_and_resource_graph = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets.event_grid_and_resource_graph
            event_grid_and_resource_graph.enable = AAZBoolType()

            user_initiated_reboot = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_reboot
            user_initiated_reboot.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            user_initiated_redeploy = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_redeploy
            user_initiated_redeploy.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            scheduled_events_profile = cls._schema_on_200.properties.scheduled_events_profile
            scheduled_events_profile.os_image_notification_profile = AAZObjectType(
                serialized_name="osImageNotificationProfile",
            )
            scheduled_events_profile.terminate_notification_profile = AAZObjectType(
                serialized_name="terminateNotificationProfile",
            )

            os_image_notification_profile = cls._schema_on_200.properties.scheduled_events_profile.os_image_notification_profile
            os_image_notification_profile.enable = AAZBoolType()
            os_image_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            terminate_notification_profile = cls._schema_on_200.properties.scheduled_events_profile.terminate_notification_profile
            terminate_notification_profile.enable = AAZBoolType()
            terminate_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            security_profile = cls._schema_on_200.properties.security_profile
            security_profile.encryption_at_host = AAZBoolType(
                serialized_name="encryptionAtHost",
            )
            security_profile.encryption_identity = AAZObjectType(
                serialized_name="encryptionIdentity",
            )
            security_profile.proxy_agent_settings = AAZObjectType(
                serialized_name="proxyAgentSettings",
            )
            security_profile.security_type = AAZStrType(
                serialized_name="securityType",
            )
            security_profile.uefi_settings = AAZObjectType(
                serialized_name="uefiSettings",
            )

            encryption_identity = cls._schema_on_200.properties.security_profile.encryption_identity
            encryption_identity.user_assigned_identity_resource_id = AAZStrType(
                serialized_name="userAssignedIdentityResourceId",
            )

            proxy_agent_settings = cls._schema_on_200.properties.security_profile.proxy_agent_settings
            proxy_agent_settings.enabled = AAZBoolType()
            proxy_agent_settings.imds = AAZObjectType()
            _ShowHelper._build_schema_host_endpoint_settings_read(proxy_agent_settings.imds)
            proxy_agent_settings.key_incarnation_id = AAZIntType(
                serialized_name="keyIncarnationId",
            )
            proxy_agent_settings.mode = AAZStrType()
            proxy_agent_settings.wire_server = AAZObjectType(
                serialized_name="wireServer",
            )
            _ShowHelper._build_schema_host_endpoint_settings_read(proxy_agent_settings.wire_server)

            uefi_settings = cls._schema_on_200.properties.security_profile.uefi_settings
            uefi_settings.secure_boot_enabled = AAZBoolType(
                serialized_name="secureBootEnabled",
            )
            uefi_settings.v_tpm_enabled = AAZBoolType(
                serialized_name="vTpmEnabled",
            )

            storage_profile = cls._schema_on_200.properties.storage_profile
            storage_profile.align_regional_disks_to_vm_zone = AAZBoolType(
                serialized_name="alignRegionalDisksToVMZone",
            )
            storage_profile.data_disks = AAZListType(
                serialized_name="dataDisks",
            )
            storage_profile.disk_controller_type = AAZStrType(
                serialized_name="diskControllerType",
            )
            storage_profile.image_reference = AAZObjectType(
                serialized_name="imageReference",
            )
            storage_profile.os_disk = AAZObjectType(
                serialized_name="osDisk",
            )

            data_disks = cls._schema_on_200.properties.storage_profile.data_disks
            data_disks.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.storage_profile.data_disks.Element
            _element.caching = AAZStrType()
            _element.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            _element.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            _element.detach_option = AAZStrType(
                serialized_name="detachOption",
            )
            _element.disk_iops_read_write = AAZIntType(
                serialized_name="diskIOPSReadWrite",
                flags={"read_only": True},
            )
            _element.disk_m_bps_read_write = AAZIntType(
                serialized_name="diskMBpsReadWrite",
                flags={"read_only": True},
            )
            _element.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            _element.image = AAZObjectType()
            _ShowHelper._build_schema_virtual_hard_disk_read(_element.image)
            _element.lun = AAZIntType(
                flags={"required": True},
            )
            _element.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _ShowHelper._build_schema_managed_disk_parameters_read(_element.managed_disk)
            _element.name = AAZStrType()
            _element.source_resource = AAZObjectType(
                serialized_name="sourceResource",
            )
            _element.to_be_detached = AAZBoolType(
                serialized_name="toBeDetached",
            )
            _element.vhd = AAZObjectType()
            _ShowHelper._build_schema_virtual_hard_disk_read(_element.vhd)
            _element.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            source_resource = cls._schema_on_200.properties.storage_profile.data_disks.Element.source_resource
            source_resource.id = AAZStrType()

            image_reference = cls._schema_on_200.properties.storage_profile.image_reference
            image_reference.community_gallery_image_id = AAZStrType(
                serialized_name="communityGalleryImageId",
            )
            image_reference.exact_version = AAZStrType(
                serialized_name="exactVersion",
                flags={"read_only": True},
            )
            image_reference.id = AAZStrType()
            image_reference.offer = AAZStrType()
            image_reference.publisher = AAZStrType()
            image_reference.shared_gallery_image_id = AAZStrType(
                serialized_name="sharedGalleryImageId",
            )
            image_reference.sku = AAZStrType()
            image_reference.version = AAZStrType()

            os_disk = cls._schema_on_200.properties.storage_profile.os_disk
            os_disk.caching = AAZStrType()
            os_disk.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            os_disk.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            os_disk.diff_disk_settings = AAZObjectType(
                serialized_name="diffDiskSettings",
            )
            os_disk.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            os_disk.encryption_settings = AAZObjectType(
                serialized_name="encryptionSettings",
            )
            _ShowHelper._build_schema_disk_encryption_settings_read(os_disk.encryption_settings)
            os_disk.image = AAZObjectType()
            _ShowHelper._build_schema_virtual_hard_disk_read(os_disk.image)
            os_disk.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _ShowHelper._build_schema_managed_disk_parameters_read(os_disk.managed_disk)
            os_disk.name = AAZStrType()
            os_disk.os_type = AAZStrType(
                serialized_name="osType",
            )
            os_disk.vhd = AAZObjectType()
            _ShowHelper._build_schema_virtual_hard_disk_read(os_disk.vhd)
            os_disk.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            diff_disk_settings = cls._schema_on_200.properties.storage_profile.os_disk.diff_disk_settings
            diff_disk_settings.option = AAZStrType()
            diff_disk_settings.placement = AAZStrType()

            resources = cls._schema_on_200.resources
            resources.Element = AAZObjectType()

            _element = cls._schema_on_200.resources.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.location = AAZStrType()
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.resources.Element.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
            )
            _ShowHelper._build_schema_virtual_machine_extension_instance_view_read(properties.instance_view)
            properties.protected_settings = AAZDictType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            _ShowHelper._build_schema_key_vault_secret_reference_read(properties.protected_settings_from_key_vault)
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZDictType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            protected_settings = cls._schema_on_200.resources.Element.properties.protected_settings
            protected_settings.Element = AAZAnyType()

            provision_after_extensions = cls._schema_on_200.resources.Element.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            settings = cls._schema_on_200.resources.Element.properties.settings
            settings.Element = AAZAnyType()

            tags = cls._schema_on_200.resources.Element.tags
            tags.Element = AAZStrType()

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200


class _ShowHelper:
    """Helper class for Show"""

    _schema_api_error_read = None

    @classmethod
    def _build_schema_api_error_read(cls, _schema):
        if cls._schema_api_error_read is not None:
            _schema.code = cls._schema_api_error_read.code
            _schema.details = cls._schema_api_error_read.details
            _schema.innererror = cls._schema_api_error_read.innererror
            _schema.message = cls._schema_api_error_read.message
            _schema.target = cls._schema_api_error_read.target
            return

        cls._schema_api_error_read = _schema_api_error_read = AAZObjectType(
            flags={"read_only": True}
        )

        api_error_read = _schema_api_error_read
        api_error_read.code = AAZStrType()
        api_error_read.details = AAZListType()
        api_error_read.innererror = AAZObjectType()
        api_error_read.message = AAZStrType()
        api_error_read.target = AAZStrType()

        details = _schema_api_error_read.details
        details.Element = AAZObjectType()

        _element = _schema_api_error_read.details.Element
        _element.code = AAZStrType()
        _element.message = AAZStrType()
        _element.target = AAZStrType()

        innererror = _schema_api_error_read.innererror
        innererror.errordetail = AAZStrType()
        innererror.exceptiontype = AAZStrType()

        _schema.code = cls._schema_api_error_read.code
        _schema.details = cls._schema_api_error_read.details
        _schema.innererror = cls._schema_api_error_read.innererror
        _schema.message = cls._schema_api_error_read.message
        _schema.target = cls._schema_api_error_read.target

    _schema_disk_encryption_set_parameters_read = None

    @classmethod
    def _build_schema_disk_encryption_set_parameters_read(cls, _schema):
        if cls._schema_disk_encryption_set_parameters_read is not None:
            _schema.id = cls._schema_disk_encryption_set_parameters_read.id
            return

        cls._schema_disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read = AAZObjectType()

        disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read
        disk_encryption_set_parameters_read.id = AAZStrType()

        _schema.id = cls._schema_disk_encryption_set_parameters_read.id

    _schema_disk_encryption_settings_read = None

    @classmethod
    def _build_schema_disk_encryption_settings_read(cls, _schema):
        if cls._schema_disk_encryption_settings_read is not None:
            _schema.disk_encryption_key = cls._schema_disk_encryption_settings_read.disk_encryption_key
            _schema.enabled = cls._schema_disk_encryption_settings_read.enabled
            _schema.key_encryption_key = cls._schema_disk_encryption_settings_read.key_encryption_key
            return

        cls._schema_disk_encryption_settings_read = _schema_disk_encryption_settings_read = AAZObjectType()

        disk_encryption_settings_read = _schema_disk_encryption_settings_read
        disk_encryption_settings_read.disk_encryption_key = AAZObjectType(
            serialized_name="diskEncryptionKey",
        )
        cls._build_schema_key_vault_secret_reference_read(disk_encryption_settings_read.disk_encryption_key)
        disk_encryption_settings_read.enabled = AAZBoolType()
        disk_encryption_settings_read.key_encryption_key = AAZObjectType(
            serialized_name="keyEncryptionKey",
        )

        key_encryption_key = _schema_disk_encryption_settings_read.key_encryption_key
        key_encryption_key.key_url = AAZStrType(
            serialized_name="keyUrl",
            flags={"required": True},
        )
        key_encryption_key.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_sub_resource_read(key_encryption_key.source_vault)

        _schema.disk_encryption_key = cls._schema_disk_encryption_settings_read.disk_encryption_key
        _schema.enabled = cls._schema_disk_encryption_settings_read.enabled
        _schema.key_encryption_key = cls._schema_disk_encryption_settings_read.key_encryption_key

    _schema_host_endpoint_settings_read = None

    @classmethod
    def _build_schema_host_endpoint_settings_read(cls, _schema):
        if cls._schema_host_endpoint_settings_read is not None:
            _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
            _schema.mode = cls._schema_host_endpoint_settings_read.mode
            return

        cls._schema_host_endpoint_settings_read = _schema_host_endpoint_settings_read = AAZObjectType()

        host_endpoint_settings_read = _schema_host_endpoint_settings_read
        host_endpoint_settings_read.in_vm_access_control_profile_reference_id = AAZStrType(
            serialized_name="inVMAccessControlProfileReferenceId",
        )
        host_endpoint_settings_read.mode = AAZStrType()

        _schema.in_vm_access_control_profile_reference_id = cls._schema_host_endpoint_settings_read.in_vm_access_control_profile_reference_id
        _schema.mode = cls._schema_host_endpoint_settings_read.mode

    _schema_instance_view_status_read = None

    @classmethod
    def _build_schema_instance_view_status_read(cls, _schema):
        if cls._schema_instance_view_status_read is not None:
            _schema.code = cls._schema_instance_view_status_read.code
            _schema.display_status = cls._schema_instance_view_status_read.display_status
            _schema.level = cls._schema_instance_view_status_read.level
            _schema.message = cls._schema_instance_view_status_read.message
            _schema.time = cls._schema_instance_view_status_read.time
            return

        cls._schema_instance_view_status_read = _schema_instance_view_status_read = AAZObjectType()

        instance_view_status_read = _schema_instance_view_status_read
        instance_view_status_read.code = AAZStrType()
        instance_view_status_read.display_status = AAZStrType(
            serialized_name="displayStatus",
        )
        instance_view_status_read.level = AAZStrType()
        instance_view_status_read.message = AAZStrType()
        instance_view_status_read.time = AAZStrType()

        _schema.code = cls._schema_instance_view_status_read.code
        _schema.display_status = cls._schema_instance_view_status_read.display_status
        _schema.level = cls._schema_instance_view_status_read.level
        _schema.message = cls._schema_instance_view_status_read.message
        _schema.time = cls._schema_instance_view_status_read.time

    _schema_key_vault_secret_reference_read = None

    @classmethod
    def _build_schema_key_vault_secret_reference_read(cls, _schema):
        if cls._schema_key_vault_secret_reference_read is not None:
            _schema.secret_url = cls._schema_key_vault_secret_reference_read.secret_url
            _schema.source_vault = cls._schema_key_vault_secret_reference_read.source_vault
            return

        cls._schema_key_vault_secret_reference_read = _schema_key_vault_secret_reference_read = AAZObjectType()

        key_vault_secret_reference_read = _schema_key_vault_secret_reference_read
        key_vault_secret_reference_read.secret_url = AAZStrType(
            serialized_name="secretUrl",
            flags={"required": True},
        )
        key_vault_secret_reference_read.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_sub_resource_read(key_vault_secret_reference_read.source_vault)

        _schema.secret_url = cls._schema_key_vault_secret_reference_read.secret_url
        _schema.source_vault = cls._schema_key_vault_secret_reference_read.source_vault

    _schema_managed_disk_parameters_read = None

    @classmethod
    def _build_schema_managed_disk_parameters_read(cls, _schema):
        if cls._schema_managed_disk_parameters_read is not None:
            _schema.disk_encryption_set = cls._schema_managed_disk_parameters_read.disk_encryption_set
            _schema.id = cls._schema_managed_disk_parameters_read.id
            _schema.security_profile = cls._schema_managed_disk_parameters_read.security_profile
            _schema.storage_account_type = cls._schema_managed_disk_parameters_read.storage_account_type
            return

        cls._schema_managed_disk_parameters_read = _schema_managed_disk_parameters_read = AAZObjectType()

        managed_disk_parameters_read = _schema_managed_disk_parameters_read
        managed_disk_parameters_read.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(managed_disk_parameters_read.disk_encryption_set)
        managed_disk_parameters_read.id = AAZStrType()
        managed_disk_parameters_read.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        managed_disk_parameters_read.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        security_profile = _schema_managed_disk_parameters_read.security_profile
        security_profile.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(security_profile.disk_encryption_set)
        security_profile.security_encryption_type = AAZStrType(
            serialized_name="securityEncryptionType",
        )

        _schema.disk_encryption_set = cls._schema_managed_disk_parameters_read.disk_encryption_set
        _schema.id = cls._schema_managed_disk_parameters_read.id
        _schema.security_profile = cls._schema_managed_disk_parameters_read.security_profile
        _schema.storage_account_type = cls._schema_managed_disk_parameters_read.storage_account_type

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_virtual_hard_disk_read = None

    @classmethod
    def _build_schema_virtual_hard_disk_read(cls, _schema):
        if cls._schema_virtual_hard_disk_read is not None:
            _schema.uri = cls._schema_virtual_hard_disk_read.uri
            return

        cls._schema_virtual_hard_disk_read = _schema_virtual_hard_disk_read = AAZObjectType()

        virtual_hard_disk_read = _schema_virtual_hard_disk_read
        virtual_hard_disk_read.uri = AAZStrType()

        _schema.uri = cls._schema_virtual_hard_disk_read.uri

    _schema_virtual_machine_extension_instance_view_read = None

    @classmethod
    def _build_schema_virtual_machine_extension_instance_view_read(cls, _schema):
        if cls._schema_virtual_machine_extension_instance_view_read is not None:
            _schema.name = cls._schema_virtual_machine_extension_instance_view_read.name
            _schema.statuses = cls._schema_virtual_machine_extension_instance_view_read.statuses
            _schema.substatuses = cls._schema_virtual_machine_extension_instance_view_read.substatuses
            _schema.type = cls._schema_virtual_machine_extension_instance_view_read.type
            _schema.type_handler_version = cls._schema_virtual_machine_extension_instance_view_read.type_handler_version
            return

        cls._schema_virtual_machine_extension_instance_view_read = _schema_virtual_machine_extension_instance_view_read = AAZObjectType()

        virtual_machine_extension_instance_view_read = _schema_virtual_machine_extension_instance_view_read
        virtual_machine_extension_instance_view_read.name = AAZStrType()
        virtual_machine_extension_instance_view_read.statuses = AAZListType()
        virtual_machine_extension_instance_view_read.substatuses = AAZListType()
        virtual_machine_extension_instance_view_read.type = AAZStrType()
        virtual_machine_extension_instance_view_read.type_handler_version = AAZStrType(
            serialized_name="typeHandlerVersion",
        )

        statuses = _schema_virtual_machine_extension_instance_view_read.statuses
        statuses.Element = AAZObjectType()
        cls._build_schema_instance_view_status_read(statuses.Element)

        substatuses = _schema_virtual_machine_extension_instance_view_read.substatuses
        substatuses.Element = AAZObjectType()
        cls._build_schema_instance_view_status_read(substatuses.Element)

        _schema.name = cls._schema_virtual_machine_extension_instance_view_read.name
        _schema.statuses = cls._schema_virtual_machine_extension_instance_view_read.statuses
        _schema.substatuses = cls._schema_virtual_machine_extension_instance_view_read.substatuses
        _schema.type = cls._schema_virtual_machine_extension_instance_view_read.type
        _schema.type_handler_version = cls._schema_virtual_machine_extension_instance_view_read.type_handler_version


__all__ = ["Show"]
