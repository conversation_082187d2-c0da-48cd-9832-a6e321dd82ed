# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from .__cmd_group import *
from ._assess_patches import *
from ._capture import *
from ._convert import *
from ._deallocate import *
from ._delete import *
from ._generalize import *
from ._list import *
from ._list_all import *
from ._list_sizes import *
from ._list_skus import *
from ._list_vm_resize_options import *
from ._patch import *
from ._perform_maintenance import *
from ._reapply import *
from ._redeploy import *
from ._reimage import *
from ._show import *
from ._simulate_eviction import *
from ._start import *
from ._update import *
from ._wait import *
