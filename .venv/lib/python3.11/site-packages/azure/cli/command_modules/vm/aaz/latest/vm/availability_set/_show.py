# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "vm availability-set show",
)
class Show(AAZCommand):
    """Get information about an availability set.

    :example: Get information about an availability set.
        az vm availability-set show -n MyAvSet -g MyResourceGroup
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/availabilitysets/{}", "2024-07-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.availability_set_name = AAZStrArg(
            options=["-n", "--name", "--availability-set-name"],
            help="The name of the availability set.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.AvailabilitySetsGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class AvailabilitySetsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/availabilitySets/{availabilitySetName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "availabilitySetName", self.ctx.args.availability_set_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.sku = AAZObjectType()
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.platform_fault_domain_count = AAZIntType(
                serialized_name="platformFaultDomainCount",
            )
            properties.platform_update_domain_count = AAZIntType(
                serialized_name="platformUpdateDomainCount",
            )
            properties.proximity_placement_group = AAZObjectType(
                serialized_name="proximityPlacementGroup",
            )
            _ShowHelper._build_schema_sub_resource_read(properties.proximity_placement_group)
            properties.scheduled_events_policy = AAZObjectType(
                serialized_name="scheduledEventsPolicy",
            )
            properties.statuses = AAZListType(
                flags={"read_only": True},
            )
            properties.virtual_machines = AAZListType(
                serialized_name="virtualMachines",
            )

            scheduled_events_policy = cls._schema_on_200.properties.scheduled_events_policy
            scheduled_events_policy.scheduled_events_additional_publishing_targets = AAZObjectType(
                serialized_name="scheduledEventsAdditionalPublishingTargets",
            )
            scheduled_events_policy.user_initiated_reboot = AAZObjectType(
                serialized_name="userInitiatedReboot",
            )
            scheduled_events_policy.user_initiated_redeploy = AAZObjectType(
                serialized_name="userInitiatedRedeploy",
            )

            scheduled_events_additional_publishing_targets = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets
            scheduled_events_additional_publishing_targets.event_grid_and_resource_graph = AAZObjectType(
                serialized_name="eventGridAndResourceGraph",
            )

            event_grid_and_resource_graph = cls._schema_on_200.properties.scheduled_events_policy.scheduled_events_additional_publishing_targets.event_grid_and_resource_graph
            event_grid_and_resource_graph.enable = AAZBoolType()

            user_initiated_reboot = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_reboot
            user_initiated_reboot.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            user_initiated_redeploy = cls._schema_on_200.properties.scheduled_events_policy.user_initiated_redeploy
            user_initiated_redeploy.automatically_approve = AAZBoolType(
                serialized_name="automaticallyApprove",
            )

            statuses = cls._schema_on_200.properties.statuses
            statuses.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.statuses.Element
            _element.code = AAZStrType()
            _element.display_status = AAZStrType(
                serialized_name="displayStatus",
            )
            _element.level = AAZStrType()
            _element.message = AAZStrType()
            _element.time = AAZStrType()

            virtual_machines = cls._schema_on_200.properties.virtual_machines
            virtual_machines.Element = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_read(virtual_machines.Element)

            sku = cls._schema_on_200.sku
            sku.capacity = AAZIntType()
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _ShowHelper:
    """Helper class for Show"""

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id


__all__ = ["Show"]
