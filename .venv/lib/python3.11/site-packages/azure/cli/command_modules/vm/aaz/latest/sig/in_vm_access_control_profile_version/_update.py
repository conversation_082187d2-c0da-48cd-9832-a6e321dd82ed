# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class Update(AAZCommand):
    """Update a gallery in VM access control profile version.

    :example: Update a Gallery in VM access control profile version.
        az sig in-vm-access-control-profile-version update --resource-group myResourceGroup --gallery-name myGalleryName --profile-name myInVMAccessControlProfileName --profile-version 1.0.0 --exclude-from-latest true
    """

    _aaz_info = {
        "version": "2024-03-03",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/galleries/{}/invmaccesscontrolprofiles/{}/versions/{}", "2024-03-03"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.gallery_name = AAZStrArg(
            options=["--gallery-name"],
            help="The name of the Shared Image Gallery in which the in VM access control profile resides.",
            required=True,
            id_part="name",
        )
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="The name of the gallery in VM access control profile in which the in VM access control profile version is to be created.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.profile_version = AAZStrArg(
            options=["--version-name", "--profile-version"],
            help="The name of the gallery in VM access control profile version to be created. Needs to follow semantic version name pattern: The allowed characters are digit and period. Digits must be within the range of a 32-bit integer. Format: MajorVersion.MinorVersion.Patch",
            required=True,
            id_part="child_name_2",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "GalleryInVMAccessControlProfileVersion"

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.default_access = AAZStrArg(
            options=["--default-access"],
            arg_group="Properties",
            help="This property allows you to specify if the requests will be allowed to access the host endpoints. Possible values are: 'Allow', 'Deny'.",
            enum={"Allow": "Allow", "Deny": "Deny"},
        )
        _args_schema.exclude_from_latest = AAZBoolArg(
            options=["--exclude-from-latest"],
            arg_group="Properties",
            help="If set to true, Virtual Machines deployed from the latest version of the Resource Profile won't use this Profile version.",
            nullable=True,
        )
        _args_schema.mode = AAZStrArg(
            options=["--mode"],
            arg_group="Properties",
            help="This property allows you to specify whether the access control rules are in Audit mode, in Enforce mode or Disabled. Possible values are: 'Audit', 'Enforce' or 'Disabled'.",
            enum={"Audit": "Audit", "Disabled": "Disabled", "Enforce": "Enforce"},
        )
        _args_schema.rules = AAZObjectArg(
            options=["--rules"],
            arg_group="Properties",
            help="This is the Access Control Rules specification for an in VM access control profile version.",
            nullable=True,
        )
        _args_schema.target_locations = AAZListArg(
            options=["--target-locations"],
            arg_group="Properties",
            help="The target regions where the Resource Profile version is going to be replicated to. This property is updatable.",
            nullable=True,
        )

        rules = cls._args_schema.rules
        rules.identities = AAZListArg(
            options=["identities"],
            help="A list of identities.",
            nullable=True,
        )
        rules.privileges = AAZListArg(
            options=["privileges"],
            help="A list of privileges.",
            nullable=True,
        )
        rules.role_assignments = AAZListArg(
            options=["role-assignments"],
            help="A list of role assignments.",
            nullable=True,
        )
        rules.roles = AAZListArg(
            options=["roles"],
            help="A list of roles.",
            nullable=True,
        )

        identities = cls._args_schema.rules.identities
        identities.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.rules.identities.Element
        _element.exe_path = AAZStrArg(
            options=["exe-path"],
            help="The path to the executable.",
            nullable=True,
        )
        _element.group_name = AAZStrArg(
            options=["group-name"],
            help="The groupName corresponding to this identity.",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the identity.",
        )
        _element.process_name = AAZStrArg(
            options=["process-name"],
            help="The process name of the executable.",
            nullable=True,
        )
        _element.user_name = AAZStrArg(
            options=["user-name"],
            help="The username corresponding to this identity.",
            nullable=True,
        )

        privileges = cls._args_schema.rules.privileges
        privileges.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.rules.privileges.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the privilege.",
        )
        _element.path = AAZStrArg(
            options=["path"],
            help="The HTTP path corresponding to the privilege.",
        )
        _element.query_parameters = AAZDictArg(
            options=["query-parameters"],
            help="The query parameters to match in the path.",
            nullable=True,
        )

        query_parameters = cls._args_schema.rules.privileges.Element.query_parameters
        query_parameters.Element = AAZStrArg(
            nullable=True,
        )

        role_assignments = cls._args_schema.rules.role_assignments
        role_assignments.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.rules.role_assignments.Element
        _element.identities = AAZListArg(
            options=["identities"],
            help="A list of identities that can access the privileges defined by the role.",
        )
        _element.role = AAZStrArg(
            options=["role"],
            help="The name of the role.",
        )

        identities = cls._args_schema.rules.role_assignments.Element.identities
        identities.Element = AAZStrArg(
            nullable=True,
        )

        roles = cls._args_schema.rules.roles
        roles.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.rules.roles.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the role.",
        )
        _element.privileges = AAZListArg(
            options=["privileges"],
            help="A list of privileges needed by this role.",
        )

        privileges = cls._args_schema.rules.roles.Element.privileges
        privileges.Element = AAZStrArg(
            nullable=True,
        )

        target_locations = cls._args_schema.target_locations
        target_locations.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.target_locations.Element
        _element.additional_replica_sets = AAZListArg(
            options=["additional-replica-sets"],
            help="List of storage sku with replica count to create direct drive replicas.",
            nullable=True,
        )
        _element.encryption = AAZObjectArg(
            options=["encryption"],
            help="Optional. Allows users to provide customer managed keys for encrypting the OS and data disks in the gallery artifact.",
            nullable=True,
        )
        _element.exclude_from_latest = AAZBoolArg(
            options=["exclude-from-latest"],
            help="Contains the flag setting to hide an image when users specify version='latest'",
            nullable=True,
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the region.",
        )
        _element.regional_replica_count = AAZIntArg(
            options=["regional-replica-count"],
            help="The number of replicas of the Image Version to be created per region. This property is updatable.",
            nullable=True,
        )
        _element.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type to be used to store the image. This property is not updatable.",
            nullable=True,
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        additional_replica_sets = cls._args_schema.target_locations.Element.additional_replica_sets
        additional_replica_sets.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.target_locations.Element.additional_replica_sets.Element
        _element.regional_replica_count = AAZIntArg(
            options=["regional-replica-count"],
            help="The number of direct drive replicas of the Image Version to be created.This Property is updatable",
            nullable=True,
        )
        _element.storage_account_type = AAZStrArg(
            options=["storage-account-type"],
            help="Specifies the storage account type to be used to create the direct drive replicas",
            nullable=True,
            enum={"PremiumV2_LRS": "PremiumV2_LRS", "Premium_LRS": "Premium_LRS", "Standard_LRS": "Standard_LRS", "Standard_ZRS": "Standard_ZRS"},
        )

        encryption = cls._args_schema.target_locations.Element.encryption
        encryption.data_disk_images = AAZListArg(
            options=["data-disk-images"],
            help="A list of encryption specifications for data disk images.",
            nullable=True,
        )
        encryption.os_disk_image = AAZObjectArg(
            options=["os-disk-image"],
            help="Contains encryption settings for an OS disk image.",
            nullable=True,
        )

        data_disk_images = cls._args_schema.target_locations.Element.encryption.data_disk_images
        data_disk_images.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.target_locations.Element.encryption.data_disk_images.Element
        _element.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="A relative URI containing the resource ID of the disk encryption set.",
            nullable=True,
        )
        _element.lun = AAZIntArg(
            options=["lun"],
            help="This property specifies the logical unit number of the data disk. This value is used to identify data disks within the Virtual Machine and therefore must be unique for each data disk attached to the Virtual Machine.",
        )

        os_disk_image = cls._args_schema.target_locations.Element.encryption.os_disk_image
        os_disk_image.disk_encryption_set_id = AAZStrArg(
            options=["disk-encryption-set-id"],
            help="A relative URI containing the resource ID of the disk encryption set.",
            nullable=True,
        )
        os_disk_image.security_profile = AAZObjectArg(
            options=["security-profile"],
            help="This property specifies the security profile of an OS disk image.",
            nullable=True,
        )

        security_profile = cls._args_schema.target_locations.Element.encryption.os_disk_image.security_profile
        security_profile.confidential_vm_encryption_type = AAZStrArg(
            options=["confidential-vm-encryption-type"],
            help="confidential VM encryption types",
            nullable=True,
            enum={"EncryptedVMGuestStateOnlyWithPmk": "EncryptedVMGuestStateOnlyWithPmk", "EncryptedWithCmk": "EncryptedWithCmk", "EncryptedWithPmk": "EncryptedWithPmk", "NonPersistedTPM": "NonPersistedTPM"},
        )
        security_profile.secure_vm_disk_encryption_set_id = AAZStrArg(
            options=["secure-vm-disk-encryption-set-id"],
            help="secure VM disk encryption set id",
            nullable=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.GalleryInVMAccessControlProfileVersionsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.GalleryInVMAccessControlProfileVersionsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class GalleryInVMAccessControlProfileVersionsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/inVMAccessControlProfiles/{inVMAccessControlProfileName}/versions/{inVMAccessControlProfileVersionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileVersionName", self.ctx.args.profile_version,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-03-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_gallery_in_vm_access_control_profile_version_read(cls._schema_on_200)

            return cls._schema_on_200

    class GalleryInVMAccessControlProfileVersionsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/inVMAccessControlProfiles/{inVMAccessControlProfileName}/versions/{inVMAccessControlProfileVersionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileVersionName", self.ctx.args.profile_version,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-03-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_gallery_in_vm_access_control_profile_version_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("defaultAccess", AAZStrType, ".default_access", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("excludeFromLatest", AAZBoolType, ".exclude_from_latest")
                properties.set_prop("mode", AAZStrType, ".mode", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("rules", AAZObjectType, ".rules")
                properties.set_prop("targetLocations", AAZListType, ".target_locations")

            rules = _builder.get(".properties.rules")
            if rules is not None:
                rules.set_prop("identities", AAZListType, ".identities")
                rules.set_prop("privileges", AAZListType, ".privileges")
                rules.set_prop("roleAssignments", AAZListType, ".role_assignments")
                rules.set_prop("roles", AAZListType, ".roles")

            identities = _builder.get(".properties.rules.identities")
            if identities is not None:
                identities.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.rules.identities[]")
            if _elements is not None:
                _elements.set_prop("exePath", AAZStrType, ".exe_path")
                _elements.set_prop("groupName", AAZStrType, ".group_name")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("processName", AAZStrType, ".process_name")
                _elements.set_prop("userName", AAZStrType, ".user_name")

            privileges = _builder.get(".properties.rules.privileges")
            if privileges is not None:
                privileges.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.rules.privileges[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("path", AAZStrType, ".path", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("queryParameters", AAZDictType, ".query_parameters")

            query_parameters = _builder.get(".properties.rules.privileges[].queryParameters")
            if query_parameters is not None:
                query_parameters.set_elements(AAZStrType, ".")

            role_assignments = _builder.get(".properties.rules.roleAssignments")
            if role_assignments is not None:
                role_assignments.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.rules.roleAssignments[]")
            if _elements is not None:
                _elements.set_prop("identities", AAZListType, ".identities", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("role", AAZStrType, ".role", typ_kwargs={"flags": {"required": True}})

            identities = _builder.get(".properties.rules.roleAssignments[].identities")
            if identities is not None:
                identities.set_elements(AAZStrType, ".")

            roles = _builder.get(".properties.rules.roles")
            if roles is not None:
                roles.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.rules.roles[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("privileges", AAZListType, ".privileges", typ_kwargs={"flags": {"required": True}})

            privileges = _builder.get(".properties.rules.roles[].privileges")
            if privileges is not None:
                privileges.set_elements(AAZStrType, ".")

            target_locations = _builder.get(".properties.targetLocations")
            if target_locations is not None:
                target_locations.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.targetLocations[]")
            if _elements is not None:
                _elements.set_prop("additionalReplicaSets", AAZListType, ".additional_replica_sets")
                _elements.set_prop("encryption", AAZObjectType, ".encryption")
                _elements.set_prop("excludeFromLatest", AAZBoolType, ".exclude_from_latest")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("regionalReplicaCount", AAZIntType, ".regional_replica_count")
                _elements.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

            additional_replica_sets = _builder.get(".properties.targetLocations[].additionalReplicaSets")
            if additional_replica_sets is not None:
                additional_replica_sets.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.targetLocations[].additionalReplicaSets[]")
            if _elements is not None:
                _elements.set_prop("regionalReplicaCount", AAZIntType, ".regional_replica_count")
                _elements.set_prop("storageAccountType", AAZStrType, ".storage_account_type")

            encryption = _builder.get(".properties.targetLocations[].encryption")
            if encryption is not None:
                encryption.set_prop("dataDiskImages", AAZListType, ".data_disk_images")
                encryption.set_prop("osDiskImage", AAZObjectType, ".os_disk_image")

            data_disk_images = _builder.get(".properties.targetLocations[].encryption.dataDiskImages")
            if data_disk_images is not None:
                data_disk_images.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.targetLocations[].encryption.dataDiskImages[]")
            if _elements is not None:
                _elements.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
                _elements.set_prop("lun", AAZIntType, ".lun", typ_kwargs={"flags": {"required": True}})

            os_disk_image = _builder.get(".properties.targetLocations[].encryption.osDiskImage")
            if os_disk_image is not None:
                os_disk_image.set_prop("diskEncryptionSetId", AAZStrType, ".disk_encryption_set_id")
                os_disk_image.set_prop("securityProfile", AAZObjectType, ".security_profile")

            security_profile = _builder.get(".properties.targetLocations[].encryption.osDiskImage.securityProfile")
            if security_profile is not None:
                security_profile.set_prop("confidentialVMEncryptionType", AAZStrType, ".confidential_vm_encryption_type")
                security_profile.set_prop("secureVMDiskEncryptionSetId", AAZStrType, ".secure_vm_disk_encryption_set_id")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_gallery_in_vm_access_control_profile_version_read = None

    @classmethod
    def _build_schema_gallery_in_vm_access_control_profile_version_read(cls, _schema):
        if cls._schema_gallery_in_vm_access_control_profile_version_read is not None:
            _schema.id = cls._schema_gallery_in_vm_access_control_profile_version_read.id
            _schema.location = cls._schema_gallery_in_vm_access_control_profile_version_read.location
            _schema.name = cls._schema_gallery_in_vm_access_control_profile_version_read.name
            _schema.properties = cls._schema_gallery_in_vm_access_control_profile_version_read.properties
            _schema.tags = cls._schema_gallery_in_vm_access_control_profile_version_read.tags
            _schema.type = cls._schema_gallery_in_vm_access_control_profile_version_read.type
            return

        cls._schema_gallery_in_vm_access_control_profile_version_read = _schema_gallery_in_vm_access_control_profile_version_read = AAZObjectType()

        gallery_in_vm_access_control_profile_version_read = _schema_gallery_in_vm_access_control_profile_version_read
        gallery_in_vm_access_control_profile_version_read.id = AAZStrType(
            flags={"read_only": True},
        )
        gallery_in_vm_access_control_profile_version_read.location = AAZStrType(
            flags={"required": True},
        )
        gallery_in_vm_access_control_profile_version_read.name = AAZStrType(
            flags={"read_only": True},
        )
        gallery_in_vm_access_control_profile_version_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        gallery_in_vm_access_control_profile_version_read.tags = AAZDictType()
        gallery_in_vm_access_control_profile_version_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_gallery_in_vm_access_control_profile_version_read.properties
        properties.default_access = AAZStrType(
            serialized_name="defaultAccess",
            flags={"required": True},
        )
        properties.exclude_from_latest = AAZBoolType(
            serialized_name="excludeFromLatest",
        )
        properties.mode = AAZStrType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.published_date = AAZStrType(
            serialized_name="publishedDate",
            flags={"read_only": True},
        )
        properties.replication_status = AAZObjectType(
            serialized_name="replicationStatus",
            flags={"read_only": True},
        )
        properties.rules = AAZObjectType()
        properties.target_locations = AAZListType(
            serialized_name="targetLocations",
        )

        replication_status = _schema_gallery_in_vm_access_control_profile_version_read.properties.replication_status
        replication_status.aggregated_state = AAZStrType(
            serialized_name="aggregatedState",
            flags={"read_only": True},
        )
        replication_status.summary = AAZListType(
            flags={"read_only": True},
        )

        summary = _schema_gallery_in_vm_access_control_profile_version_read.properties.replication_status.summary
        summary.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.replication_status.summary.Element
        _element.details = AAZStrType(
            flags={"read_only": True},
        )
        _element.progress = AAZIntType(
            flags={"read_only": True},
        )
        _element.region = AAZStrType(
            flags={"read_only": True},
        )
        _element.state = AAZStrType(
            flags={"read_only": True},
        )

        rules = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules
        rules.identities = AAZListType()
        rules.privileges = AAZListType()
        rules.role_assignments = AAZListType(
            serialized_name="roleAssignments",
        )
        rules.roles = AAZListType()

        identities = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.identities
        identities.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.identities.Element
        _element.exe_path = AAZStrType(
            serialized_name="exePath",
        )
        _element.group_name = AAZStrType(
            serialized_name="groupName",
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.process_name = AAZStrType(
            serialized_name="processName",
        )
        _element.user_name = AAZStrType(
            serialized_name="userName",
        )

        privileges = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.privileges
        privileges.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.privileges.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.path = AAZStrType(
            flags={"required": True},
        )
        _element.query_parameters = AAZDictType(
            serialized_name="queryParameters",
        )

        query_parameters = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.privileges.Element.query_parameters
        query_parameters.Element = AAZStrType()

        role_assignments = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.role_assignments
        role_assignments.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.role_assignments.Element
        _element.identities = AAZListType(
            flags={"required": True},
        )
        _element.role = AAZStrType(
            flags={"required": True},
        )

        identities = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.role_assignments.Element.identities
        identities.Element = AAZStrType()

        roles = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.roles
        roles.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.roles.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.privileges = AAZListType(
            flags={"required": True},
        )

        privileges = _schema_gallery_in_vm_access_control_profile_version_read.properties.rules.roles.Element.privileges
        privileges.Element = AAZStrType()

        target_locations = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations
        target_locations.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element
        _element.additional_replica_sets = AAZListType(
            serialized_name="additionalReplicaSets",
        )
        _element.encryption = AAZObjectType()
        _element.exclude_from_latest = AAZBoolType(
            serialized_name="excludeFromLatest",
        )
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.regional_replica_count = AAZIntType(
            serialized_name="regionalReplicaCount",
        )
        _element.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        additional_replica_sets = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element.additional_replica_sets
        additional_replica_sets.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element.additional_replica_sets.Element
        _element.regional_replica_count = AAZIntType(
            serialized_name="regionalReplicaCount",
        )
        _element.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        encryption = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element.encryption
        encryption.data_disk_images = AAZListType(
            serialized_name="dataDiskImages",
        )
        encryption.os_disk_image = AAZObjectType(
            serialized_name="osDiskImage",
        )

        data_disk_images = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element.encryption.data_disk_images
        data_disk_images.Element = AAZObjectType()

        _element = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element.encryption.data_disk_images.Element
        _element.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        _element.lun = AAZIntType(
            flags={"required": True},
        )

        os_disk_image = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element.encryption.os_disk_image
        os_disk_image.disk_encryption_set_id = AAZStrType(
            serialized_name="diskEncryptionSetId",
        )
        os_disk_image.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )

        security_profile = _schema_gallery_in_vm_access_control_profile_version_read.properties.target_locations.Element.encryption.os_disk_image.security_profile
        security_profile.confidential_vm_encryption_type = AAZStrType(
            serialized_name="confidentialVMEncryptionType",
        )
        security_profile.secure_vm_disk_encryption_set_id = AAZStrType(
            serialized_name="secureVMDiskEncryptionSetId",
        )

        tags = _schema_gallery_in_vm_access_control_profile_version_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_gallery_in_vm_access_control_profile_version_read.id
        _schema.location = cls._schema_gallery_in_vm_access_control_profile_version_read.location
        _schema.name = cls._schema_gallery_in_vm_access_control_profile_version_read.name
        _schema.properties = cls._schema_gallery_in_vm_access_control_profile_version_read.properties
        _schema.tags = cls._schema_gallery_in_vm_access_control_profile_version_read.tags
        _schema.type = cls._schema_gallery_in_vm_access_control_profile_version_read.type


__all__ = ["Update"]
