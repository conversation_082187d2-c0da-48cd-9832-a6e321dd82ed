# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "ppg show",
)
class Show(AAZCommand):
    """Get a proximity placement group.

    :example: Get a proximity placement group (commonly used with --output). (autogenerated)
        az ppg show --name MyProximityPlacementGroup --resource-group MyResourceGroup
    """

    _aaz_info = {
        "version": "2024-07-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/proximityplacementgroups/{}", "2024-07-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.proximity_placement_group_name = AAZStrArg(
            options=["-n", "--name", "--proximity-placement-group-name"],
            help="The name of the proximity placement group.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.include_colocation_status = AAZStrArg(
            options=["--include-colocation-status"],
            help="Enable fetching the colocation status of all the resources in the proximity placement group.",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.ProximityPlacementGroupsGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class ProximityPlacementGroupsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/proximityPlacementGroups/{proximityPlacementGroupName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "proximityPlacementGroupName", self.ctx.args.proximity_placement_group_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "includeColocationStatus", self.ctx.args.include_colocation_status,
                ),
                **self.serialize_query_param(
                    "api-version", "2024-07-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.zones = AAZListType()

            properties = cls._schema_on_200.properties
            properties.availability_sets = AAZListType(
                serialized_name="availabilitySets",
                flags={"read_only": True},
            )
            properties.colocation_status = AAZObjectType(
                serialized_name="colocationStatus",
            )
            _ShowHelper._build_schema_instance_view_status_read(properties.colocation_status)
            properties.intent = AAZObjectType()
            properties.proximity_placement_group_type = AAZStrType(
                serialized_name="proximityPlacementGroupType",
            )
            properties.virtual_machine_scale_sets = AAZListType(
                serialized_name="virtualMachineScaleSets",
                flags={"read_only": True},
            )
            properties.virtual_machines = AAZListType(
                serialized_name="virtualMachines",
                flags={"read_only": True},
            )

            availability_sets = cls._schema_on_200.properties.availability_sets
            availability_sets.Element = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_with_colocation_status_read(availability_sets.Element)

            intent = cls._schema_on_200.properties.intent
            intent.vm_sizes = AAZListType(
                serialized_name="vmSizes",
            )

            vm_sizes = cls._schema_on_200.properties.intent.vm_sizes
            vm_sizes.Element = AAZStrType()

            virtual_machine_scale_sets = cls._schema_on_200.properties.virtual_machine_scale_sets
            virtual_machine_scale_sets.Element = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_with_colocation_status_read(virtual_machine_scale_sets.Element)

            virtual_machines = cls._schema_on_200.properties.virtual_machines
            virtual_machines.Element = AAZObjectType()
            _ShowHelper._build_schema_sub_resource_with_colocation_status_read(virtual_machines.Element)

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200


class _ShowHelper:
    """Helper class for Show"""

    _schema_instance_view_status_read = None

    @classmethod
    def _build_schema_instance_view_status_read(cls, _schema):
        if cls._schema_instance_view_status_read is not None:
            _schema.code = cls._schema_instance_view_status_read.code
            _schema.display_status = cls._schema_instance_view_status_read.display_status
            _schema.level = cls._schema_instance_view_status_read.level
            _schema.message = cls._schema_instance_view_status_read.message
            _schema.time = cls._schema_instance_view_status_read.time
            return

        cls._schema_instance_view_status_read = _schema_instance_view_status_read = AAZObjectType()

        instance_view_status_read = _schema_instance_view_status_read
        instance_view_status_read.code = AAZStrType()
        instance_view_status_read.display_status = AAZStrType(
            serialized_name="displayStatus",
        )
        instance_view_status_read.level = AAZStrType()
        instance_view_status_read.message = AAZStrType()
        instance_view_status_read.time = AAZStrType()

        _schema.code = cls._schema_instance_view_status_read.code
        _schema.display_status = cls._schema_instance_view_status_read.display_status
        _schema.level = cls._schema_instance_view_status_read.level
        _schema.message = cls._schema_instance_view_status_read.message
        _schema.time = cls._schema_instance_view_status_read.time

    _schema_sub_resource_with_colocation_status_read = None

    @classmethod
    def _build_schema_sub_resource_with_colocation_status_read(cls, _schema):
        if cls._schema_sub_resource_with_colocation_status_read is not None:
            _schema.colocation_status = cls._schema_sub_resource_with_colocation_status_read.colocation_status
            _schema.id = cls._schema_sub_resource_with_colocation_status_read.id
            return

        cls._schema_sub_resource_with_colocation_status_read = _schema_sub_resource_with_colocation_status_read = AAZObjectType()

        sub_resource_with_colocation_status_read = _schema_sub_resource_with_colocation_status_read
        sub_resource_with_colocation_status_read.colocation_status = AAZObjectType(
            serialized_name="colocationStatus",
        )
        cls._build_schema_instance_view_status_read(sub_resource_with_colocation_status_read.colocation_status)
        sub_resource_with_colocation_status_read.id = AAZStrType()

        _schema.colocation_status = cls._schema_sub_resource_with_colocation_status_read.colocation_status
        _schema.id = cls._schema_sub_resource_with_colocation_status_read.id


__all__ = ["Show"]
