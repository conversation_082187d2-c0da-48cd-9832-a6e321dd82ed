# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "vmss list",
)
class List(AAZCommand):
    """List all VM scale sets under a resource group.

    :example: List VMSS
        az vmss list --resource-group MyResourceGroup
    """

    _aaz_info = {
        "version": "2023-09-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.compute/virtualmachinescalesets", "2023-09-01"],
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/virtualmachinescalesets", "2023-09-01"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg()
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        condition_0 = has_value(self.ctx.args.resource_group) and has_value(self.ctx.subscription_id)
        condition_1 = has_value(self.ctx.subscription_id) and has_value(self.ctx.args.resource_group) is not True
        if condition_0:
            self.VirtualMachineScaleSetsList(ctx=self.ctx)()
        if condition_1:
            self.VirtualMachineScaleSetsListAll(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class VirtualMachineScaleSetsList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachineScaleSets",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2023-09-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
            )
            _schema_on_200.value = AAZListType(
                flags={"required": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.extended_location = AAZObjectType(
                serialized_name="extendedLocation",
            )
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.identity = AAZIdentityObjectType()
            _element.location = AAZStrType(
                flags={"required": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.plan = AAZObjectType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.sku = AAZObjectType()
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )
            _element.zones = AAZListType()

            extended_location = cls._schema_on_200.value.Element.extended_location
            extended_location.name = AAZStrType()
            extended_location.type = AAZStrType()

            identity = cls._schema_on_200.value.Element.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.value.Element.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            plan = cls._schema_on_200.value.Element.plan
            plan.name = AAZStrType()
            plan.product = AAZStrType()
            plan.promotion_code = AAZStrType(
                serialized_name="promotionCode",
            )
            plan.publisher = AAZStrType()

            properties = cls._schema_on_200.value.Element.properties
            properties.additional_capabilities = AAZObjectType(
                serialized_name="additionalCapabilities",
            )
            properties.automatic_repairs_policy = AAZObjectType(
                serialized_name="automaticRepairsPolicy",
            )
            properties.constrained_maximum_capacity = AAZBoolType(
                serialized_name="constrainedMaximumCapacity",
            )
            properties.do_not_run_extensions_on_overprovisioned_v_ms = AAZBoolType(
                serialized_name="doNotRunExtensionsOnOverprovisionedVMs",
            )
            properties.host_group = AAZObjectType(
                serialized_name="hostGroup",
            )
            _ListHelper._build_schema_sub_resource_read(properties.host_group)
            properties.orchestration_mode = AAZStrType(
                serialized_name="orchestrationMode",
            )
            properties.overprovision = AAZBoolType()
            properties.platform_fault_domain_count = AAZIntType(
                serialized_name="platformFaultDomainCount",
            )
            properties.priority_mix_policy = AAZObjectType(
                serialized_name="priorityMixPolicy",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.proximity_placement_group = AAZObjectType(
                serialized_name="proximityPlacementGroup",
            )
            _ListHelper._build_schema_sub_resource_read(properties.proximity_placement_group)
            properties.resiliency_policy = AAZObjectType(
                serialized_name="resiliencyPolicy",
            )
            properties.scale_in_policy = AAZObjectType(
                serialized_name="scaleInPolicy",
            )
            properties.single_placement_group = AAZBoolType(
                serialized_name="singlePlacementGroup",
            )
            properties.spot_restore_policy = AAZObjectType(
                serialized_name="spotRestorePolicy",
            )
            properties.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            properties.unique_id = AAZStrType(
                serialized_name="uniqueId",
                flags={"read_only": True},
            )
            properties.upgrade_policy = AAZObjectType(
                serialized_name="upgradePolicy",
            )
            properties.virtual_machine_profile = AAZObjectType(
                serialized_name="virtualMachineProfile",
            )
            properties.zone_balance = AAZBoolType(
                serialized_name="zoneBalance",
            )

            additional_capabilities = cls._schema_on_200.value.Element.properties.additional_capabilities
            additional_capabilities.hibernation_enabled = AAZBoolType(
                serialized_name="hibernationEnabled",
            )
            additional_capabilities.ultra_ssd_enabled = AAZBoolType(
                serialized_name="ultraSSDEnabled",
            )

            automatic_repairs_policy = cls._schema_on_200.value.Element.properties.automatic_repairs_policy
            automatic_repairs_policy.enabled = AAZBoolType()
            automatic_repairs_policy.grace_period = AAZStrType(
                serialized_name="gracePeriod",
            )
            automatic_repairs_policy.repair_action = AAZStrType(
                serialized_name="repairAction",
            )

            priority_mix_policy = cls._schema_on_200.value.Element.properties.priority_mix_policy
            priority_mix_policy.base_regular_priority_count = AAZIntType(
                serialized_name="baseRegularPriorityCount",
            )
            priority_mix_policy.regular_priority_percentage_above_base = AAZIntType(
                serialized_name="regularPriorityPercentageAboveBase",
            )

            resiliency_policy = cls._schema_on_200.value.Element.properties.resiliency_policy
            resiliency_policy.resilient_vm_creation_policy = AAZObjectType(
                serialized_name="resilientVMCreationPolicy",
            )
            resiliency_policy.resilient_vm_deletion_policy = AAZObjectType(
                serialized_name="resilientVMDeletionPolicy",
            )

            resilient_vm_creation_policy = cls._schema_on_200.value.Element.properties.resiliency_policy.resilient_vm_creation_policy
            resilient_vm_creation_policy.enabled = AAZBoolType()

            resilient_vm_deletion_policy = cls._schema_on_200.value.Element.properties.resiliency_policy.resilient_vm_deletion_policy
            resilient_vm_deletion_policy.enabled = AAZBoolType()

            scale_in_policy = cls._schema_on_200.value.Element.properties.scale_in_policy
            scale_in_policy.force_deletion = AAZBoolType(
                serialized_name="forceDeletion",
            )
            scale_in_policy.rules = AAZListType()

            rules = cls._schema_on_200.value.Element.properties.scale_in_policy.rules
            rules.Element = AAZStrType()

            spot_restore_policy = cls._schema_on_200.value.Element.properties.spot_restore_policy
            spot_restore_policy.enabled = AAZBoolType()
            spot_restore_policy.restore_timeout = AAZStrType(
                serialized_name="restoreTimeout",
            )

            upgrade_policy = cls._schema_on_200.value.Element.properties.upgrade_policy
            upgrade_policy.automatic_os_upgrade_policy = AAZObjectType(
                serialized_name="automaticOSUpgradePolicy",
            )
            upgrade_policy.mode = AAZStrType()
            upgrade_policy.rolling_upgrade_policy = AAZObjectType(
                serialized_name="rollingUpgradePolicy",
            )

            automatic_os_upgrade_policy = cls._schema_on_200.value.Element.properties.upgrade_policy.automatic_os_upgrade_policy
            automatic_os_upgrade_policy.disable_automatic_rollback = AAZBoolType(
                serialized_name="disableAutomaticRollback",
            )
            automatic_os_upgrade_policy.enable_automatic_os_upgrade = AAZBoolType(
                serialized_name="enableAutomaticOSUpgrade",
            )
            automatic_os_upgrade_policy.os_rolling_upgrade_deferral = AAZBoolType(
                serialized_name="osRollingUpgradeDeferral",
            )
            automatic_os_upgrade_policy.use_rolling_upgrade_policy = AAZBoolType(
                serialized_name="useRollingUpgradePolicy",
            )

            rolling_upgrade_policy = cls._schema_on_200.value.Element.properties.upgrade_policy.rolling_upgrade_policy
            rolling_upgrade_policy.enable_cross_zone_upgrade = AAZBoolType(
                serialized_name="enableCrossZoneUpgrade",
            )
            rolling_upgrade_policy.max_batch_instance_percent = AAZIntType(
                serialized_name="maxBatchInstancePercent",
            )
            rolling_upgrade_policy.max_surge = AAZBoolType(
                serialized_name="maxSurge",
            )
            rolling_upgrade_policy.max_unhealthy_instance_percent = AAZIntType(
                serialized_name="maxUnhealthyInstancePercent",
            )
            rolling_upgrade_policy.max_unhealthy_upgraded_instance_percent = AAZIntType(
                serialized_name="maxUnhealthyUpgradedInstancePercent",
            )
            rolling_upgrade_policy.pause_time_between_batches = AAZStrType(
                serialized_name="pauseTimeBetweenBatches",
            )
            rolling_upgrade_policy.prioritize_unhealthy_instances = AAZBoolType(
                serialized_name="prioritizeUnhealthyInstances",
            )
            rolling_upgrade_policy.rollback_failed_instances_on_policy_breach = AAZBoolType(
                serialized_name="rollbackFailedInstancesOnPolicyBreach",
            )

            virtual_machine_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile
            virtual_machine_profile.application_profile = AAZObjectType(
                serialized_name="applicationProfile",
            )
            virtual_machine_profile.billing_profile = AAZObjectType(
                serialized_name="billingProfile",
            )
            virtual_machine_profile.capacity_reservation = AAZObjectType(
                serialized_name="capacityReservation",
            )
            virtual_machine_profile.diagnostics_profile = AAZObjectType(
                serialized_name="diagnosticsProfile",
            )
            virtual_machine_profile.eviction_policy = AAZStrType(
                serialized_name="evictionPolicy",
            )
            virtual_machine_profile.extension_profile = AAZObjectType(
                serialized_name="extensionProfile",
            )
            virtual_machine_profile.hardware_profile = AAZObjectType(
                serialized_name="hardwareProfile",
            )
            virtual_machine_profile.license_type = AAZStrType(
                serialized_name="licenseType",
            )
            virtual_machine_profile.network_profile = AAZObjectType(
                serialized_name="networkProfile",
            )
            virtual_machine_profile.os_profile = AAZObjectType(
                serialized_name="osProfile",
            )
            virtual_machine_profile.priority = AAZStrType()
            virtual_machine_profile.scheduled_events_profile = AAZObjectType(
                serialized_name="scheduledEventsProfile",
            )
            virtual_machine_profile.security_posture_reference = AAZObjectType(
                serialized_name="securityPostureReference",
            )
            virtual_machine_profile.security_profile = AAZObjectType(
                serialized_name="securityProfile",
            )
            virtual_machine_profile.service_artifact_reference = AAZObjectType(
                serialized_name="serviceArtifactReference",
            )
            virtual_machine_profile.storage_profile = AAZObjectType(
                serialized_name="storageProfile",
            )
            virtual_machine_profile.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            virtual_machine_profile.user_data = AAZStrType(
                serialized_name="userData",
            )

            application_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.application_profile
            application_profile.gallery_applications = AAZListType(
                serialized_name="galleryApplications",
            )

            gallery_applications = cls._schema_on_200.value.Element.properties.virtual_machine_profile.application_profile.gallery_applications
            gallery_applications.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.application_profile.gallery_applications.Element
            _element.configuration_reference = AAZStrType(
                serialized_name="configurationReference",
            )
            _element.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            _element.order = AAZIntType()
            _element.package_reference_id = AAZStrType(
                serialized_name="packageReferenceId",
                flags={"required": True},
            )
            _element.tags = AAZStrType()
            _element.treat_failure_as_deployment_failure = AAZBoolType(
                serialized_name="treatFailureAsDeploymentFailure",
            )

            billing_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.billing_profile
            billing_profile.max_price = AAZFloatType(
                serialized_name="maxPrice",
            )

            capacity_reservation = cls._schema_on_200.value.Element.properties.virtual_machine_profile.capacity_reservation
            capacity_reservation.capacity_reservation_group = AAZObjectType(
                serialized_name="capacityReservationGroup",
            )
            _ListHelper._build_schema_sub_resource_read(capacity_reservation.capacity_reservation_group)

            diagnostics_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.diagnostics_profile
            diagnostics_profile.boot_diagnostics = AAZObjectType(
                serialized_name="bootDiagnostics",
            )

            boot_diagnostics = cls._schema_on_200.value.Element.properties.virtual_machine_profile.diagnostics_profile.boot_diagnostics
            boot_diagnostics.enabled = AAZBoolType()
            boot_diagnostics.storage_uri = AAZStrType(
                serialized_name="storageUri",
            )

            extension_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile
            extension_profile.extensions = AAZListType()
            extension_profile.extensions_time_budget = AAZStrType(
                serialized_name="extensionsTimeBudget",
            )

            extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions
            extensions.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions.Element.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.protected_settings = AAZObjectType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            _ListHelper._build_schema_key_vault_secret_reference_read(properties.protected_settings_from_key_vault)
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZObjectType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            provision_after_extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions.Element.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            hardware_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.hardware_profile
            hardware_profile.vm_size_properties = AAZObjectType(
                serialized_name="vmSizeProperties",
            )

            vm_size_properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.hardware_profile.vm_size_properties
            vm_size_properties.v_cp_us_available = AAZIntType(
                serialized_name="vCPUsAvailable",
            )
            vm_size_properties.v_cp_us_per_core = AAZIntType(
                serialized_name="vCPUsPerCore",
            )

            network_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile
            network_profile.health_probe = AAZObjectType(
                serialized_name="healthProbe",
            )
            _ListHelper._build_schema_api_entity_reference_read(network_profile.health_probe)
            network_profile.network_api_version = AAZStrType(
                serialized_name="networkApiVersion",
            )
            network_profile.network_interface_configurations = AAZListType(
                serialized_name="networkInterfaceConfigurations",
            )

            network_interface_configurations = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations
            network_interface_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties
            properties.auxiliary_mode = AAZStrType(
                serialized_name="auxiliaryMode",
            )
            properties.auxiliary_sku = AAZStrType(
                serialized_name="auxiliarySku",
            )
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.disable_tcp_state_tracking = AAZBoolType(
                serialized_name="disableTcpStateTracking",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.enable_accelerated_networking = AAZBoolType(
                serialized_name="enableAcceleratedNetworking",
            )
            properties.enable_fpga = AAZBoolType(
                serialized_name="enableFpga",
            )
            properties.enable_ip_forwarding = AAZBoolType(
                serialized_name="enableIPForwarding",
            )
            properties.ip_configurations = AAZListType(
                serialized_name="ipConfigurations",
                flags={"required": True},
            )
            properties.network_security_group = AAZObjectType(
                serialized_name="networkSecurityGroup",
            )
            _ListHelper._build_schema_sub_resource_read(properties.network_security_group)
            properties.primary = AAZBoolType()

            dns_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings
            dns_settings.dns_servers = AAZListType(
                serialized_name="dnsServers",
            )

            dns_servers = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings.dns_servers
            dns_servers.Element = AAZStrType()

            ip_configurations = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations
            ip_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties
            properties.application_gateway_backend_address_pools = AAZListType(
                serialized_name="applicationGatewayBackendAddressPools",
            )
            properties.application_security_groups = AAZListType(
                serialized_name="applicationSecurityGroups",
            )
            properties.load_balancer_backend_address_pools = AAZListType(
                serialized_name="loadBalancerBackendAddressPools",
            )
            properties.load_balancer_inbound_nat_pools = AAZListType(
                serialized_name="loadBalancerInboundNatPools",
            )
            properties.primary = AAZBoolType()
            properties.private_ip_address_version = AAZStrType(
                serialized_name="privateIPAddressVersion",
            )
            properties.public_ip_address_configuration = AAZObjectType(
                serialized_name="publicIPAddressConfiguration",
            )
            properties.subnet = AAZObjectType()
            _ListHelper._build_schema_api_entity_reference_read(properties.subnet)

            application_gateway_backend_address_pools = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_gateway_backend_address_pools
            application_gateway_backend_address_pools.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(application_gateway_backend_address_pools.Element)

            application_security_groups = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_security_groups
            application_security_groups.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(application_security_groups.Element)

            load_balancer_backend_address_pools = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_backend_address_pools
            load_balancer_backend_address_pools.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(load_balancer_backend_address_pools.Element)

            load_balancer_inbound_nat_pools = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_inbound_nat_pools
            load_balancer_inbound_nat_pools.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(load_balancer_inbound_nat_pools.Element)

            public_ip_address_configuration = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration
            public_ip_address_configuration.name = AAZStrType(
                flags={"required": True},
            )
            public_ip_address_configuration.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            public_ip_address_configuration.sku = AAZObjectType()

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.idle_timeout_in_minutes = AAZIntType(
                serialized_name="idleTimeoutInMinutes",
            )
            properties.ip_tags = AAZListType(
                serialized_name="ipTags",
            )
            properties.public_ip_address_version = AAZStrType(
                serialized_name="publicIPAddressVersion",
            )
            properties.public_ip_prefix = AAZObjectType(
                serialized_name="publicIPPrefix",
            )
            _ListHelper._build_schema_sub_resource_read(properties.public_ip_prefix)

            dns_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.dns_settings
            dns_settings.domain_name_label = AAZStrType(
                serialized_name="domainNameLabel",
                flags={"required": True},
            )
            dns_settings.domain_name_label_scope = AAZStrType(
                serialized_name="domainNameLabelScope",
            )

            ip_tags = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags
            ip_tags.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags.Element
            _element.ip_tag_type = AAZStrType(
                serialized_name="ipTagType",
            )
            _element.tag = AAZStrType()

            sku = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            os_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile
            os_profile.admin_password = AAZStrType(
                serialized_name="adminPassword",
                flags={"secret": True},
            )
            os_profile.admin_username = AAZStrType(
                serialized_name="adminUsername",
            )
            os_profile.allow_extension_operations = AAZBoolType(
                serialized_name="allowExtensionOperations",
            )
            os_profile.computer_name_prefix = AAZStrType(
                serialized_name="computerNamePrefix",
            )
            os_profile.custom_data = AAZStrType(
                serialized_name="customData",
            )
            os_profile.linux_configuration = AAZObjectType(
                serialized_name="linuxConfiguration",
            )
            os_profile.require_guest_provision_signal = AAZBoolType(
                serialized_name="requireGuestProvisionSignal",
            )
            os_profile.secrets = AAZListType()
            os_profile.windows_configuration = AAZObjectType(
                serialized_name="windowsConfiguration",
            )

            linux_configuration = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration
            linux_configuration.disable_password_authentication = AAZBoolType(
                serialized_name="disablePasswordAuthentication",
            )
            linux_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
            )
            linux_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            linux_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            linux_configuration.ssh = AAZObjectType()

            patch_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            ssh = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.ssh
            ssh.public_keys = AAZListType(
                serialized_name="publicKeys",
            )

            public_keys = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys
            public_keys.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys.Element
            _element.key_data = AAZStrType(
                serialized_name="keyData",
            )
            _element.path = AAZStrType()

            secrets = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets
            secrets.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets.Element
            _element.source_vault = AAZObjectType(
                serialized_name="sourceVault",
            )
            _ListHelper._build_schema_sub_resource_read(_element.source_vault)
            _element.vault_certificates = AAZListType(
                serialized_name="vaultCertificates",
            )

            vault_certificates = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates
            vault_certificates.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates.Element
            _element.certificate_store = AAZStrType(
                serialized_name="certificateStore",
            )
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )

            windows_configuration = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration
            windows_configuration.additional_unattend_content = AAZListType(
                serialized_name="additionalUnattendContent",
            )
            windows_configuration.enable_automatic_updates = AAZBoolType(
                serialized_name="enableAutomaticUpdates",
            )
            windows_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
            )
            windows_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            windows_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            windows_configuration.time_zone = AAZStrType(
                serialized_name="timeZone",
            )
            windows_configuration.win_rm = AAZObjectType(
                serialized_name="winRM",
            )

            additional_unattend_content = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content
            additional_unattend_content.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content.Element
            _element.component_name = AAZStrType(
                serialized_name="componentName",
            )
            _element.content = AAZStrType()
            _element.pass_name = AAZStrType(
                serialized_name="passName",
            )
            _element.setting_name = AAZStrType(
                serialized_name="settingName",
            )

            patch_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.enable_hotpatching = AAZBoolType(
                serialized_name="enableHotpatching",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            win_rm = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm
            win_rm.listeners = AAZListType()

            listeners = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners
            listeners.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners.Element
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )
            _element.protocol = AAZStrType()

            scheduled_events_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.scheduled_events_profile
            scheduled_events_profile.os_image_notification_profile = AAZObjectType(
                serialized_name="osImageNotificationProfile",
            )
            scheduled_events_profile.terminate_notification_profile = AAZObjectType(
                serialized_name="terminateNotificationProfile",
            )

            os_image_notification_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.scheduled_events_profile.os_image_notification_profile
            os_image_notification_profile.enable = AAZBoolType()
            os_image_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            terminate_notification_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.scheduled_events_profile.terminate_notification_profile
            terminate_notification_profile.enable = AAZBoolType()
            terminate_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            security_posture_reference = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference
            security_posture_reference.exclude_extensions = AAZListType(
                serialized_name="excludeExtensions",
            )
            security_posture_reference.id = AAZStrType()

            exclude_extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions
            exclude_extensions.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.location = AAZStrType()
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
            )
            properties.protected_settings = AAZObjectType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            _ListHelper._build_schema_key_vault_secret_reference_read(properties.protected_settings_from_key_vault)
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZObjectType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            instance_view = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.instance_view
            instance_view.name = AAZStrType()
            instance_view.statuses = AAZListType()
            instance_view.substatuses = AAZListType()
            instance_view.type = AAZStrType()
            instance_view.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            statuses = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.instance_view.statuses
            statuses.Element = AAZObjectType()
            _ListHelper._build_schema_instance_view_status_read(statuses.Element)

            substatuses = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.instance_view.substatuses
            substatuses.Element = AAZObjectType()
            _ListHelper._build_schema_instance_view_status_read(substatuses.Element)

            provision_after_extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            tags = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.tags
            tags.Element = AAZStrType()

            security_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile
            security_profile.encryption_at_host = AAZBoolType(
                serialized_name="encryptionAtHost",
            )
            security_profile.encryption_identity = AAZObjectType(
                serialized_name="encryptionIdentity",
            )
            security_profile.proxy_agent_settings = AAZObjectType(
                serialized_name="proxyAgentSettings",
            )
            security_profile.security_type = AAZStrType(
                serialized_name="securityType",
            )
            security_profile.uefi_settings = AAZObjectType(
                serialized_name="uefiSettings",
            )

            encryption_identity = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile.encryption_identity
            encryption_identity.user_assigned_identity_resource_id = AAZStrType(
                serialized_name="userAssignedIdentityResourceId",
            )

            proxy_agent_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile.proxy_agent_settings
            proxy_agent_settings.enabled = AAZBoolType()
            proxy_agent_settings.key_incarnation_id = AAZIntType(
                serialized_name="keyIncarnationId",
            )
            proxy_agent_settings.mode = AAZStrType()

            uefi_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile.uefi_settings
            uefi_settings.secure_boot_enabled = AAZBoolType(
                serialized_name="secureBootEnabled",
            )
            uefi_settings.v_tpm_enabled = AAZBoolType(
                serialized_name="vTpmEnabled",
            )

            service_artifact_reference = cls._schema_on_200.value.Element.properties.virtual_machine_profile.service_artifact_reference
            service_artifact_reference.id = AAZStrType()

            storage_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile
            storage_profile.data_disks = AAZListType(
                serialized_name="dataDisks",
            )
            storage_profile.disk_controller_type = AAZStrType(
                serialized_name="diskControllerType",
            )
            storage_profile.image_reference = AAZObjectType(
                serialized_name="imageReference",
            )
            storage_profile.os_disk = AAZObjectType(
                serialized_name="osDisk",
            )

            data_disks = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.data_disks
            data_disks.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.data_disks.Element
            _element.caching = AAZStrType()
            _element.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            _element.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            _element.disk_iops_read_write = AAZIntType(
                serialized_name="diskIOPSReadWrite",
            )
            _element.disk_m_bps_read_write = AAZIntType(
                serialized_name="diskMBpsReadWrite",
            )
            _element.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            _element.lun = AAZIntType(
                flags={"required": True},
            )
            _element.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _ListHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(_element.managed_disk)
            _element.name = AAZStrType()
            _element.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            image_reference = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.image_reference
            image_reference.community_gallery_image_id = AAZStrType(
                serialized_name="communityGalleryImageId",
            )
            image_reference.exact_version = AAZStrType(
                serialized_name="exactVersion",
                flags={"read_only": True},
            )
            image_reference.id = AAZStrType()
            image_reference.offer = AAZStrType()
            image_reference.publisher = AAZStrType()
            image_reference.shared_gallery_image_id = AAZStrType(
                serialized_name="sharedGalleryImageId",
            )
            image_reference.sku = AAZStrType()
            image_reference.version = AAZStrType()

            os_disk = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk
            os_disk.caching = AAZStrType()
            os_disk.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            os_disk.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            os_disk.diff_disk_settings = AAZObjectType(
                serialized_name="diffDiskSettings",
            )
            os_disk.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            os_disk.image = AAZObjectType()
            os_disk.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _ListHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(os_disk.managed_disk)
            os_disk.name = AAZStrType()
            os_disk.os_type = AAZStrType(
                serialized_name="osType",
            )
            os_disk.vhd_containers = AAZListType(
                serialized_name="vhdContainers",
            )
            os_disk.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            diff_disk_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk.diff_disk_settings
            diff_disk_settings.option = AAZStrType()
            diff_disk_settings.placement = AAZStrType()

            image = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk.image
            image.uri = AAZStrType()

            vhd_containers = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk.vhd_containers
            vhd_containers.Element = AAZStrType()

            sku = cls._schema_on_200.value.Element.sku
            sku.capacity = AAZIntType()
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            tags = cls._schema_on_200.value.Element.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200.value.Element.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200

    class VirtualMachineScaleSetsListAll(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.Compute/virtualMachineScaleSets",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2023-09-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
            )
            _schema_on_200.value = AAZListType(
                flags={"required": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.extended_location = AAZObjectType(
                serialized_name="extendedLocation",
            )
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.identity = AAZIdentityObjectType()
            _element.location = AAZStrType(
                flags={"required": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.plan = AAZObjectType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.sku = AAZObjectType()
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )
            _element.zones = AAZListType()

            extended_location = cls._schema_on_200.value.Element.extended_location
            extended_location.name = AAZStrType()
            extended_location.type = AAZStrType()

            identity = cls._schema_on_200.value.Element.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()
            identity.user_assigned_identities = AAZDictType(
                serialized_name="userAssignedIdentities",
            )

            user_assigned_identities = cls._schema_on_200.value.Element.identity.user_assigned_identities
            user_assigned_identities.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.identity.user_assigned_identities.Element
            _element.client_id = AAZStrType(
                serialized_name="clientId",
                flags={"read_only": True},
            )
            _element.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )

            plan = cls._schema_on_200.value.Element.plan
            plan.name = AAZStrType()
            plan.product = AAZStrType()
            plan.promotion_code = AAZStrType(
                serialized_name="promotionCode",
            )
            plan.publisher = AAZStrType()

            properties = cls._schema_on_200.value.Element.properties
            properties.additional_capabilities = AAZObjectType(
                serialized_name="additionalCapabilities",
            )
            properties.automatic_repairs_policy = AAZObjectType(
                serialized_name="automaticRepairsPolicy",
            )
            properties.constrained_maximum_capacity = AAZBoolType(
                serialized_name="constrainedMaximumCapacity",
            )
            properties.do_not_run_extensions_on_overprovisioned_v_ms = AAZBoolType(
                serialized_name="doNotRunExtensionsOnOverprovisionedVMs",
            )
            properties.host_group = AAZObjectType(
                serialized_name="hostGroup",
            )
            _ListHelper._build_schema_sub_resource_read(properties.host_group)
            properties.orchestration_mode = AAZStrType(
                serialized_name="orchestrationMode",
            )
            properties.overprovision = AAZBoolType()
            properties.platform_fault_domain_count = AAZIntType(
                serialized_name="platformFaultDomainCount",
            )
            properties.priority_mix_policy = AAZObjectType(
                serialized_name="priorityMixPolicy",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.proximity_placement_group = AAZObjectType(
                serialized_name="proximityPlacementGroup",
            )
            _ListHelper._build_schema_sub_resource_read(properties.proximity_placement_group)
            properties.resiliency_policy = AAZObjectType(
                serialized_name="resiliencyPolicy",
            )
            properties.scale_in_policy = AAZObjectType(
                serialized_name="scaleInPolicy",
            )
            properties.single_placement_group = AAZBoolType(
                serialized_name="singlePlacementGroup",
            )
            properties.spot_restore_policy = AAZObjectType(
                serialized_name="spotRestorePolicy",
            )
            properties.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            properties.unique_id = AAZStrType(
                serialized_name="uniqueId",
                flags={"read_only": True},
            )
            properties.upgrade_policy = AAZObjectType(
                serialized_name="upgradePolicy",
            )
            properties.virtual_machine_profile = AAZObjectType(
                serialized_name="virtualMachineProfile",
            )
            properties.zone_balance = AAZBoolType(
                serialized_name="zoneBalance",
            )

            additional_capabilities = cls._schema_on_200.value.Element.properties.additional_capabilities
            additional_capabilities.hibernation_enabled = AAZBoolType(
                serialized_name="hibernationEnabled",
            )
            additional_capabilities.ultra_ssd_enabled = AAZBoolType(
                serialized_name="ultraSSDEnabled",
            )

            automatic_repairs_policy = cls._schema_on_200.value.Element.properties.automatic_repairs_policy
            automatic_repairs_policy.enabled = AAZBoolType()
            automatic_repairs_policy.grace_period = AAZStrType(
                serialized_name="gracePeriod",
            )
            automatic_repairs_policy.repair_action = AAZStrType(
                serialized_name="repairAction",
            )

            priority_mix_policy = cls._schema_on_200.value.Element.properties.priority_mix_policy
            priority_mix_policy.base_regular_priority_count = AAZIntType(
                serialized_name="baseRegularPriorityCount",
            )
            priority_mix_policy.regular_priority_percentage_above_base = AAZIntType(
                serialized_name="regularPriorityPercentageAboveBase",
            )

            resiliency_policy = cls._schema_on_200.value.Element.properties.resiliency_policy
            resiliency_policy.resilient_vm_creation_policy = AAZObjectType(
                serialized_name="resilientVMCreationPolicy",
            )
            resiliency_policy.resilient_vm_deletion_policy = AAZObjectType(
                serialized_name="resilientVMDeletionPolicy",
            )

            resilient_vm_creation_policy = cls._schema_on_200.value.Element.properties.resiliency_policy.resilient_vm_creation_policy
            resilient_vm_creation_policy.enabled = AAZBoolType()

            resilient_vm_deletion_policy = cls._schema_on_200.value.Element.properties.resiliency_policy.resilient_vm_deletion_policy
            resilient_vm_deletion_policy.enabled = AAZBoolType()

            scale_in_policy = cls._schema_on_200.value.Element.properties.scale_in_policy
            scale_in_policy.force_deletion = AAZBoolType(
                serialized_name="forceDeletion",
            )
            scale_in_policy.rules = AAZListType()

            rules = cls._schema_on_200.value.Element.properties.scale_in_policy.rules
            rules.Element = AAZStrType()

            spot_restore_policy = cls._schema_on_200.value.Element.properties.spot_restore_policy
            spot_restore_policy.enabled = AAZBoolType()
            spot_restore_policy.restore_timeout = AAZStrType(
                serialized_name="restoreTimeout",
            )

            upgrade_policy = cls._schema_on_200.value.Element.properties.upgrade_policy
            upgrade_policy.automatic_os_upgrade_policy = AAZObjectType(
                serialized_name="automaticOSUpgradePolicy",
            )
            upgrade_policy.mode = AAZStrType()
            upgrade_policy.rolling_upgrade_policy = AAZObjectType(
                serialized_name="rollingUpgradePolicy",
            )

            automatic_os_upgrade_policy = cls._schema_on_200.value.Element.properties.upgrade_policy.automatic_os_upgrade_policy
            automatic_os_upgrade_policy.disable_automatic_rollback = AAZBoolType(
                serialized_name="disableAutomaticRollback",
            )
            automatic_os_upgrade_policy.enable_automatic_os_upgrade = AAZBoolType(
                serialized_name="enableAutomaticOSUpgrade",
            )
            automatic_os_upgrade_policy.os_rolling_upgrade_deferral = AAZBoolType(
                serialized_name="osRollingUpgradeDeferral",
            )
            automatic_os_upgrade_policy.use_rolling_upgrade_policy = AAZBoolType(
                serialized_name="useRollingUpgradePolicy",
            )

            rolling_upgrade_policy = cls._schema_on_200.value.Element.properties.upgrade_policy.rolling_upgrade_policy
            rolling_upgrade_policy.enable_cross_zone_upgrade = AAZBoolType(
                serialized_name="enableCrossZoneUpgrade",
            )
            rolling_upgrade_policy.max_batch_instance_percent = AAZIntType(
                serialized_name="maxBatchInstancePercent",
            )
            rolling_upgrade_policy.max_surge = AAZBoolType(
                serialized_name="maxSurge",
            )
            rolling_upgrade_policy.max_unhealthy_instance_percent = AAZIntType(
                serialized_name="maxUnhealthyInstancePercent",
            )
            rolling_upgrade_policy.max_unhealthy_upgraded_instance_percent = AAZIntType(
                serialized_name="maxUnhealthyUpgradedInstancePercent",
            )
            rolling_upgrade_policy.pause_time_between_batches = AAZStrType(
                serialized_name="pauseTimeBetweenBatches",
            )
            rolling_upgrade_policy.prioritize_unhealthy_instances = AAZBoolType(
                serialized_name="prioritizeUnhealthyInstances",
            )
            rolling_upgrade_policy.rollback_failed_instances_on_policy_breach = AAZBoolType(
                serialized_name="rollbackFailedInstancesOnPolicyBreach",
            )

            virtual_machine_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile
            virtual_machine_profile.application_profile = AAZObjectType(
                serialized_name="applicationProfile",
            )
            virtual_machine_profile.billing_profile = AAZObjectType(
                serialized_name="billingProfile",
            )
            virtual_machine_profile.capacity_reservation = AAZObjectType(
                serialized_name="capacityReservation",
            )
            virtual_machine_profile.diagnostics_profile = AAZObjectType(
                serialized_name="diagnosticsProfile",
            )
            virtual_machine_profile.eviction_policy = AAZStrType(
                serialized_name="evictionPolicy",
            )
            virtual_machine_profile.extension_profile = AAZObjectType(
                serialized_name="extensionProfile",
            )
            virtual_machine_profile.hardware_profile = AAZObjectType(
                serialized_name="hardwareProfile",
            )
            virtual_machine_profile.license_type = AAZStrType(
                serialized_name="licenseType",
            )
            virtual_machine_profile.network_profile = AAZObjectType(
                serialized_name="networkProfile",
            )
            virtual_machine_profile.os_profile = AAZObjectType(
                serialized_name="osProfile",
            )
            virtual_machine_profile.priority = AAZStrType()
            virtual_machine_profile.scheduled_events_profile = AAZObjectType(
                serialized_name="scheduledEventsProfile",
            )
            virtual_machine_profile.security_posture_reference = AAZObjectType(
                serialized_name="securityPostureReference",
            )
            virtual_machine_profile.security_profile = AAZObjectType(
                serialized_name="securityProfile",
            )
            virtual_machine_profile.service_artifact_reference = AAZObjectType(
                serialized_name="serviceArtifactReference",
            )
            virtual_machine_profile.storage_profile = AAZObjectType(
                serialized_name="storageProfile",
            )
            virtual_machine_profile.time_created = AAZStrType(
                serialized_name="timeCreated",
                flags={"read_only": True},
            )
            virtual_machine_profile.user_data = AAZStrType(
                serialized_name="userData",
            )

            application_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.application_profile
            application_profile.gallery_applications = AAZListType(
                serialized_name="galleryApplications",
            )

            gallery_applications = cls._schema_on_200.value.Element.properties.virtual_machine_profile.application_profile.gallery_applications
            gallery_applications.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.application_profile.gallery_applications.Element
            _element.configuration_reference = AAZStrType(
                serialized_name="configurationReference",
            )
            _element.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            _element.order = AAZIntType()
            _element.package_reference_id = AAZStrType(
                serialized_name="packageReferenceId",
                flags={"required": True},
            )
            _element.tags = AAZStrType()
            _element.treat_failure_as_deployment_failure = AAZBoolType(
                serialized_name="treatFailureAsDeploymentFailure",
            )

            billing_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.billing_profile
            billing_profile.max_price = AAZFloatType(
                serialized_name="maxPrice",
            )

            capacity_reservation = cls._schema_on_200.value.Element.properties.virtual_machine_profile.capacity_reservation
            capacity_reservation.capacity_reservation_group = AAZObjectType(
                serialized_name="capacityReservationGroup",
            )
            _ListHelper._build_schema_sub_resource_read(capacity_reservation.capacity_reservation_group)

            diagnostics_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.diagnostics_profile
            diagnostics_profile.boot_diagnostics = AAZObjectType(
                serialized_name="bootDiagnostics",
            )

            boot_diagnostics = cls._schema_on_200.value.Element.properties.virtual_machine_profile.diagnostics_profile.boot_diagnostics
            boot_diagnostics.enabled = AAZBoolType()
            boot_diagnostics.storage_uri = AAZStrType(
                serialized_name="storageUri",
            )

            extension_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile
            extension_profile.extensions = AAZListType()
            extension_profile.extensions_time_budget = AAZStrType(
                serialized_name="extensionsTimeBudget",
            )

            extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions
            extensions.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType()
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions.Element.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.protected_settings = AAZObjectType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            _ListHelper._build_schema_key_vault_secret_reference_read(properties.protected_settings_from_key_vault)
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZObjectType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            provision_after_extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.extension_profile.extensions.Element.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            hardware_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.hardware_profile
            hardware_profile.vm_size_properties = AAZObjectType(
                serialized_name="vmSizeProperties",
            )

            vm_size_properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.hardware_profile.vm_size_properties
            vm_size_properties.v_cp_us_available = AAZIntType(
                serialized_name="vCPUsAvailable",
            )
            vm_size_properties.v_cp_us_per_core = AAZIntType(
                serialized_name="vCPUsPerCore",
            )

            network_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile
            network_profile.health_probe = AAZObjectType(
                serialized_name="healthProbe",
            )
            _ListHelper._build_schema_api_entity_reference_read(network_profile.health_probe)
            network_profile.network_api_version = AAZStrType(
                serialized_name="networkApiVersion",
            )
            network_profile.network_interface_configurations = AAZListType(
                serialized_name="networkInterfaceConfigurations",
            )

            network_interface_configurations = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations
            network_interface_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties
            properties.auxiliary_mode = AAZStrType(
                serialized_name="auxiliaryMode",
            )
            properties.auxiliary_sku = AAZStrType(
                serialized_name="auxiliarySku",
            )
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.disable_tcp_state_tracking = AAZBoolType(
                serialized_name="disableTcpStateTracking",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.enable_accelerated_networking = AAZBoolType(
                serialized_name="enableAcceleratedNetworking",
            )
            properties.enable_fpga = AAZBoolType(
                serialized_name="enableFpga",
            )
            properties.enable_ip_forwarding = AAZBoolType(
                serialized_name="enableIPForwarding",
            )
            properties.ip_configurations = AAZListType(
                serialized_name="ipConfigurations",
                flags={"required": True},
            )
            properties.network_security_group = AAZObjectType(
                serialized_name="networkSecurityGroup",
            )
            _ListHelper._build_schema_sub_resource_read(properties.network_security_group)
            properties.primary = AAZBoolType()

            dns_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings
            dns_settings.dns_servers = AAZListType(
                serialized_name="dnsServers",
            )

            dns_servers = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.dns_settings.dns_servers
            dns_servers.Element = AAZStrType()

            ip_configurations = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations
            ip_configurations.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties
            properties.application_gateway_backend_address_pools = AAZListType(
                serialized_name="applicationGatewayBackendAddressPools",
            )
            properties.application_security_groups = AAZListType(
                serialized_name="applicationSecurityGroups",
            )
            properties.load_balancer_backend_address_pools = AAZListType(
                serialized_name="loadBalancerBackendAddressPools",
            )
            properties.load_balancer_inbound_nat_pools = AAZListType(
                serialized_name="loadBalancerInboundNatPools",
            )
            properties.primary = AAZBoolType()
            properties.private_ip_address_version = AAZStrType(
                serialized_name="privateIPAddressVersion",
            )
            properties.public_ip_address_configuration = AAZObjectType(
                serialized_name="publicIPAddressConfiguration",
            )
            properties.subnet = AAZObjectType()
            _ListHelper._build_schema_api_entity_reference_read(properties.subnet)

            application_gateway_backend_address_pools = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_gateway_backend_address_pools
            application_gateway_backend_address_pools.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(application_gateway_backend_address_pools.Element)

            application_security_groups = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.application_security_groups
            application_security_groups.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(application_security_groups.Element)

            load_balancer_backend_address_pools = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_backend_address_pools
            load_balancer_backend_address_pools.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(load_balancer_backend_address_pools.Element)

            load_balancer_inbound_nat_pools = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.load_balancer_inbound_nat_pools
            load_balancer_inbound_nat_pools.Element = AAZObjectType()
            _ListHelper._build_schema_sub_resource_read(load_balancer_inbound_nat_pools.Element)

            public_ip_address_configuration = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration
            public_ip_address_configuration.name = AAZStrType(
                flags={"required": True},
            )
            public_ip_address_configuration.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            public_ip_address_configuration.sku = AAZObjectType()

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties
            properties.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            properties.dns_settings = AAZObjectType(
                serialized_name="dnsSettings",
            )
            properties.idle_timeout_in_minutes = AAZIntType(
                serialized_name="idleTimeoutInMinutes",
            )
            properties.ip_tags = AAZListType(
                serialized_name="ipTags",
            )
            properties.public_ip_address_version = AAZStrType(
                serialized_name="publicIPAddressVersion",
            )
            properties.public_ip_prefix = AAZObjectType(
                serialized_name="publicIPPrefix",
            )
            _ListHelper._build_schema_sub_resource_read(properties.public_ip_prefix)

            dns_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.dns_settings
            dns_settings.domain_name_label = AAZStrType(
                serialized_name="domainNameLabel",
                flags={"required": True},
            )
            dns_settings.domain_name_label_scope = AAZStrType(
                serialized_name="domainNameLabelScope",
            )

            ip_tags = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags
            ip_tags.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.properties.ip_tags.Element
            _element.ip_tag_type = AAZStrType(
                serialized_name="ipTagType",
            )
            _element.tag = AAZStrType()

            sku = cls._schema_on_200.value.Element.properties.virtual_machine_profile.network_profile.network_interface_configurations.Element.properties.ip_configurations.Element.properties.public_ip_address_configuration.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            os_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile
            os_profile.admin_password = AAZStrType(
                serialized_name="adminPassword",
                flags={"secret": True},
            )
            os_profile.admin_username = AAZStrType(
                serialized_name="adminUsername",
            )
            os_profile.allow_extension_operations = AAZBoolType(
                serialized_name="allowExtensionOperations",
            )
            os_profile.computer_name_prefix = AAZStrType(
                serialized_name="computerNamePrefix",
            )
            os_profile.custom_data = AAZStrType(
                serialized_name="customData",
            )
            os_profile.linux_configuration = AAZObjectType(
                serialized_name="linuxConfiguration",
            )
            os_profile.require_guest_provision_signal = AAZBoolType(
                serialized_name="requireGuestProvisionSignal",
            )
            os_profile.secrets = AAZListType()
            os_profile.windows_configuration = AAZObjectType(
                serialized_name="windowsConfiguration",
            )

            linux_configuration = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration
            linux_configuration.disable_password_authentication = AAZBoolType(
                serialized_name="disablePasswordAuthentication",
            )
            linux_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
            )
            linux_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            linux_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            linux_configuration.ssh = AAZObjectType()

            patch_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            ssh = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.ssh
            ssh.public_keys = AAZListType(
                serialized_name="publicKeys",
            )

            public_keys = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys
            public_keys.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.linux_configuration.ssh.public_keys.Element
            _element.key_data = AAZStrType(
                serialized_name="keyData",
            )
            _element.path = AAZStrType()

            secrets = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets
            secrets.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets.Element
            _element.source_vault = AAZObjectType(
                serialized_name="sourceVault",
            )
            _ListHelper._build_schema_sub_resource_read(_element.source_vault)
            _element.vault_certificates = AAZListType(
                serialized_name="vaultCertificates",
            )

            vault_certificates = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates
            vault_certificates.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.secrets.Element.vault_certificates.Element
            _element.certificate_store = AAZStrType(
                serialized_name="certificateStore",
            )
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )

            windows_configuration = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration
            windows_configuration.additional_unattend_content = AAZListType(
                serialized_name="additionalUnattendContent",
            )
            windows_configuration.enable_automatic_updates = AAZBoolType(
                serialized_name="enableAutomaticUpdates",
            )
            windows_configuration.enable_vm_agent_platform_updates = AAZBoolType(
                serialized_name="enableVMAgentPlatformUpdates",
            )
            windows_configuration.patch_settings = AAZObjectType(
                serialized_name="patchSettings",
            )
            windows_configuration.provision_vm_agent = AAZBoolType(
                serialized_name="provisionVMAgent",
            )
            windows_configuration.time_zone = AAZStrType(
                serialized_name="timeZone",
            )
            windows_configuration.win_rm = AAZObjectType(
                serialized_name="winRM",
            )

            additional_unattend_content = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content
            additional_unattend_content.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.additional_unattend_content.Element
            _element.component_name = AAZStrType(
                serialized_name="componentName",
            )
            _element.content = AAZStrType()
            _element.pass_name = AAZStrType(
                serialized_name="passName",
            )
            _element.setting_name = AAZStrType(
                serialized_name="settingName",
            )

            patch_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings
            patch_settings.assessment_mode = AAZStrType(
                serialized_name="assessmentMode",
            )
            patch_settings.automatic_by_platform_settings = AAZObjectType(
                serialized_name="automaticByPlatformSettings",
            )
            patch_settings.enable_hotpatching = AAZBoolType(
                serialized_name="enableHotpatching",
            )
            patch_settings.patch_mode = AAZStrType(
                serialized_name="patchMode",
            )

            automatic_by_platform_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.patch_settings.automatic_by_platform_settings
            automatic_by_platform_settings.bypass_platform_safety_checks_on_user_schedule = AAZBoolType(
                serialized_name="bypassPlatformSafetyChecksOnUserSchedule",
            )
            automatic_by_platform_settings.reboot_setting = AAZStrType(
                serialized_name="rebootSetting",
            )

            win_rm = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm
            win_rm.listeners = AAZListType()

            listeners = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners
            listeners.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.os_profile.windows_configuration.win_rm.listeners.Element
            _element.certificate_url = AAZStrType(
                serialized_name="certificateUrl",
            )
            _element.protocol = AAZStrType()

            scheduled_events_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.scheduled_events_profile
            scheduled_events_profile.os_image_notification_profile = AAZObjectType(
                serialized_name="osImageNotificationProfile",
            )
            scheduled_events_profile.terminate_notification_profile = AAZObjectType(
                serialized_name="terminateNotificationProfile",
            )

            os_image_notification_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.scheduled_events_profile.os_image_notification_profile
            os_image_notification_profile.enable = AAZBoolType()
            os_image_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            terminate_notification_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.scheduled_events_profile.terminate_notification_profile
            terminate_notification_profile.enable = AAZBoolType()
            terminate_notification_profile.not_before_timeout = AAZStrType(
                serialized_name="notBeforeTimeout",
            )

            security_posture_reference = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference
            security_posture_reference.exclude_extensions = AAZListType(
                serialized_name="excludeExtensions",
            )
            security_posture_reference.id = AAZStrType()

            exclude_extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions
            exclude_extensions.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.location = AAZStrType()
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties
            properties.auto_upgrade_minor_version = AAZBoolType(
                serialized_name="autoUpgradeMinorVersion",
            )
            properties.enable_automatic_upgrade = AAZBoolType(
                serialized_name="enableAutomaticUpgrade",
            )
            properties.force_update_tag = AAZStrType(
                serialized_name="forceUpdateTag",
            )
            properties.instance_view = AAZObjectType(
                serialized_name="instanceView",
            )
            properties.protected_settings = AAZObjectType(
                serialized_name="protectedSettings",
            )
            properties.protected_settings_from_key_vault = AAZObjectType(
                serialized_name="protectedSettingsFromKeyVault",
            )
            _ListHelper._build_schema_key_vault_secret_reference_read(properties.protected_settings_from_key_vault)
            properties.provision_after_extensions = AAZListType(
                serialized_name="provisionAfterExtensions",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.publisher = AAZStrType()
            properties.settings = AAZObjectType()
            properties.suppress_failures = AAZBoolType(
                serialized_name="suppressFailures",
            )
            properties.type = AAZStrType()
            properties.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            instance_view = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.instance_view
            instance_view.name = AAZStrType()
            instance_view.statuses = AAZListType()
            instance_view.substatuses = AAZListType()
            instance_view.type = AAZStrType()
            instance_view.type_handler_version = AAZStrType(
                serialized_name="typeHandlerVersion",
            )

            statuses = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.instance_view.statuses
            statuses.Element = AAZObjectType()
            _ListHelper._build_schema_instance_view_status_read(statuses.Element)

            substatuses = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.instance_view.substatuses
            substatuses.Element = AAZObjectType()
            _ListHelper._build_schema_instance_view_status_read(substatuses.Element)

            provision_after_extensions = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.properties.provision_after_extensions
            provision_after_extensions.Element = AAZStrType()

            tags = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_posture_reference.exclude_extensions.Element.tags
            tags.Element = AAZStrType()

            security_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile
            security_profile.encryption_at_host = AAZBoolType(
                serialized_name="encryptionAtHost",
            )
            security_profile.encryption_identity = AAZObjectType(
                serialized_name="encryptionIdentity",
            )
            security_profile.proxy_agent_settings = AAZObjectType(
                serialized_name="proxyAgentSettings",
            )
            security_profile.security_type = AAZStrType(
                serialized_name="securityType",
            )
            security_profile.uefi_settings = AAZObjectType(
                serialized_name="uefiSettings",
            )

            encryption_identity = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile.encryption_identity
            encryption_identity.user_assigned_identity_resource_id = AAZStrType(
                serialized_name="userAssignedIdentityResourceId",
            )

            proxy_agent_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile.proxy_agent_settings
            proxy_agent_settings.enabled = AAZBoolType()
            proxy_agent_settings.key_incarnation_id = AAZIntType(
                serialized_name="keyIncarnationId",
            )
            proxy_agent_settings.mode = AAZStrType()

            uefi_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.security_profile.uefi_settings
            uefi_settings.secure_boot_enabled = AAZBoolType(
                serialized_name="secureBootEnabled",
            )
            uefi_settings.v_tpm_enabled = AAZBoolType(
                serialized_name="vTpmEnabled",
            )

            service_artifact_reference = cls._schema_on_200.value.Element.properties.virtual_machine_profile.service_artifact_reference
            service_artifact_reference.id = AAZStrType()

            storage_profile = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile
            storage_profile.data_disks = AAZListType(
                serialized_name="dataDisks",
            )
            storage_profile.disk_controller_type = AAZStrType(
                serialized_name="diskControllerType",
            )
            storage_profile.image_reference = AAZObjectType(
                serialized_name="imageReference",
            )
            storage_profile.os_disk = AAZObjectType(
                serialized_name="osDisk",
            )

            data_disks = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.data_disks
            data_disks.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.data_disks.Element
            _element.caching = AAZStrType()
            _element.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            _element.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            _element.disk_iops_read_write = AAZIntType(
                serialized_name="diskIOPSReadWrite",
            )
            _element.disk_m_bps_read_write = AAZIntType(
                serialized_name="diskMBpsReadWrite",
            )
            _element.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            _element.lun = AAZIntType(
                flags={"required": True},
            )
            _element.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _ListHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(_element.managed_disk)
            _element.name = AAZStrType()
            _element.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            image_reference = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.image_reference
            image_reference.community_gallery_image_id = AAZStrType(
                serialized_name="communityGalleryImageId",
            )
            image_reference.exact_version = AAZStrType(
                serialized_name="exactVersion",
                flags={"read_only": True},
            )
            image_reference.id = AAZStrType()
            image_reference.offer = AAZStrType()
            image_reference.publisher = AAZStrType()
            image_reference.shared_gallery_image_id = AAZStrType(
                serialized_name="sharedGalleryImageId",
            )
            image_reference.sku = AAZStrType()
            image_reference.version = AAZStrType()

            os_disk = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk
            os_disk.caching = AAZStrType()
            os_disk.create_option = AAZStrType(
                serialized_name="createOption",
                flags={"required": True},
            )
            os_disk.delete_option = AAZStrType(
                serialized_name="deleteOption",
            )
            os_disk.diff_disk_settings = AAZObjectType(
                serialized_name="diffDiskSettings",
            )
            os_disk.disk_size_gb = AAZIntType(
                serialized_name="diskSizeGB",
            )
            os_disk.image = AAZObjectType()
            os_disk.managed_disk = AAZObjectType(
                serialized_name="managedDisk",
            )
            _ListHelper._build_schema_virtual_machine_scale_set_managed_disk_parameters_read(os_disk.managed_disk)
            os_disk.name = AAZStrType()
            os_disk.os_type = AAZStrType(
                serialized_name="osType",
            )
            os_disk.vhd_containers = AAZListType(
                serialized_name="vhdContainers",
            )
            os_disk.write_accelerator_enabled = AAZBoolType(
                serialized_name="writeAcceleratorEnabled",
            )

            diff_disk_settings = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk.diff_disk_settings
            diff_disk_settings.option = AAZStrType()
            diff_disk_settings.placement = AAZStrType()

            image = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk.image
            image.uri = AAZStrType()

            vhd_containers = cls._schema_on_200.value.Element.properties.virtual_machine_profile.storage_profile.os_disk.vhd_containers
            vhd_containers.Element = AAZStrType()

            sku = cls._schema_on_200.value.Element.sku
            sku.capacity = AAZIntType()
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            tags = cls._schema_on_200.value.Element.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200.value.Element.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""

    _schema_api_entity_reference_read = None

    @classmethod
    def _build_schema_api_entity_reference_read(cls, _schema):
        if cls._schema_api_entity_reference_read is not None:
            _schema.id = cls._schema_api_entity_reference_read.id
            return

        cls._schema_api_entity_reference_read = _schema_api_entity_reference_read = AAZObjectType()

        api_entity_reference_read = _schema_api_entity_reference_read
        api_entity_reference_read.id = AAZStrType()

        _schema.id = cls._schema_api_entity_reference_read.id

    _schema_disk_encryption_set_parameters_read = None

    @classmethod
    def _build_schema_disk_encryption_set_parameters_read(cls, _schema):
        if cls._schema_disk_encryption_set_parameters_read is not None:
            _schema.id = cls._schema_disk_encryption_set_parameters_read.id
            return

        cls._schema_disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read = AAZObjectType()

        disk_encryption_set_parameters_read = _schema_disk_encryption_set_parameters_read
        disk_encryption_set_parameters_read.id = AAZStrType()

        _schema.id = cls._schema_disk_encryption_set_parameters_read.id

    _schema_instance_view_status_read = None

    @classmethod
    def _build_schema_instance_view_status_read(cls, _schema):
        if cls._schema_instance_view_status_read is not None:
            _schema.code = cls._schema_instance_view_status_read.code
            _schema.display_status = cls._schema_instance_view_status_read.display_status
            _schema.level = cls._schema_instance_view_status_read.level
            _schema.message = cls._schema_instance_view_status_read.message
            _schema.time = cls._schema_instance_view_status_read.time
            return

        cls._schema_instance_view_status_read = _schema_instance_view_status_read = AAZObjectType()

        instance_view_status_read = _schema_instance_view_status_read
        instance_view_status_read.code = AAZStrType()
        instance_view_status_read.display_status = AAZStrType(
            serialized_name="displayStatus",
        )
        instance_view_status_read.level = AAZStrType()
        instance_view_status_read.message = AAZStrType()
        instance_view_status_read.time = AAZStrType()

        _schema.code = cls._schema_instance_view_status_read.code
        _schema.display_status = cls._schema_instance_view_status_read.display_status
        _schema.level = cls._schema_instance_view_status_read.level
        _schema.message = cls._schema_instance_view_status_read.message
        _schema.time = cls._schema_instance_view_status_read.time

    _schema_key_vault_secret_reference_read = None

    @classmethod
    def _build_schema_key_vault_secret_reference_read(cls, _schema):
        if cls._schema_key_vault_secret_reference_read is not None:
            _schema.secret_url = cls._schema_key_vault_secret_reference_read.secret_url
            _schema.source_vault = cls._schema_key_vault_secret_reference_read.source_vault
            return

        cls._schema_key_vault_secret_reference_read = _schema_key_vault_secret_reference_read = AAZObjectType()

        key_vault_secret_reference_read = _schema_key_vault_secret_reference_read
        key_vault_secret_reference_read.secret_url = AAZStrType(
            serialized_name="secretUrl",
            flags={"required": True},
        )
        key_vault_secret_reference_read.source_vault = AAZObjectType(
            serialized_name="sourceVault",
            flags={"required": True},
        )
        cls._build_schema_sub_resource_read(key_vault_secret_reference_read.source_vault)

        _schema.secret_url = cls._schema_key_vault_secret_reference_read.secret_url
        _schema.source_vault = cls._schema_key_vault_secret_reference_read.source_vault

    _schema_sub_resource_read = None

    @classmethod
    def _build_schema_sub_resource_read(cls, _schema):
        if cls._schema_sub_resource_read is not None:
            _schema.id = cls._schema_sub_resource_read.id
            return

        cls._schema_sub_resource_read = _schema_sub_resource_read = AAZObjectType()

        sub_resource_read = _schema_sub_resource_read
        sub_resource_read.id = AAZStrType()

        _schema.id = cls._schema_sub_resource_read.id

    _schema_virtual_machine_scale_set_managed_disk_parameters_read = None

    @classmethod
    def _build_schema_virtual_machine_scale_set_managed_disk_parameters_read(cls, _schema):
        if cls._schema_virtual_machine_scale_set_managed_disk_parameters_read is not None:
            _schema.disk_encryption_set = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set
            _schema.security_profile = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
            _schema.storage_account_type = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type
            return

        cls._schema_virtual_machine_scale_set_managed_disk_parameters_read = _schema_virtual_machine_scale_set_managed_disk_parameters_read = AAZObjectType()

        virtual_machine_scale_set_managed_disk_parameters_read = _schema_virtual_machine_scale_set_managed_disk_parameters_read
        virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set)
        virtual_machine_scale_set_managed_disk_parameters_read.security_profile = AAZObjectType(
            serialized_name="securityProfile",
        )
        virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type = AAZStrType(
            serialized_name="storageAccountType",
        )

        security_profile = _schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
        security_profile.disk_encryption_set = AAZObjectType(
            serialized_name="diskEncryptionSet",
        )
        cls._build_schema_disk_encryption_set_parameters_read(security_profile.disk_encryption_set)
        security_profile.security_encryption_type = AAZStrType(
            serialized_name="securityEncryptionType",
        )

        _schema.disk_encryption_set = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.disk_encryption_set
        _schema.security_profile = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.security_profile
        _schema.storage_account_type = cls._schema_virtual_machine_scale_set_managed_disk_parameters_read.storage_account_type


__all__ = ["List"]
