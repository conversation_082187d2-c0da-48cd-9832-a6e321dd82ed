# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "sig in-vm-access-control-profile-version wait",
)
class Wait(AAZWaitCommand):
    """Place the CLI in a waiting state until a condition is met.
    """

    _aaz_info = {
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.compute/galleries/{}/invmaccesscontrolprofiles/{}/versions/{}", "2024-03-03"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.gallery_name = AAZStrArg(
            options=["--gallery-name"],
            help="The name of the Shared Image Gallery in which the in VM access control profile resides.",
            required=True,
            id_part="name",
        )
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="The name of the gallery in VM access control profile in which the in VM access control profile version is to be created.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.profile_version = AAZStrArg(
            options=["--version-name", "--profile-version"],
            help="The name of the gallery in VM access control profile version to be created. Needs to follow semantic version name pattern: The allowed characters are digit and period. Digits must be within the range of a 32-bit integer. Format: MajorVersion.MinorVersion.Patch",
            required=True,
            id_part="child_name_2",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.GalleryInVMAccessControlProfileVersionsGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=False)
        return result

    class GalleryInVMAccessControlProfileVersionsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/galleries/{galleryName}/inVMAccessControlProfiles/{inVMAccessControlProfileName}/versions/{inVMAccessControlProfileVersionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryName", self.ctx.args.gallery_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "inVMAccessControlProfileVersionName", self.ctx.args.profile_version,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-03-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.default_access = AAZStrType(
                serialized_name="defaultAccess",
                flags={"required": True},
            )
            properties.exclude_from_latest = AAZBoolType(
                serialized_name="excludeFromLatest",
            )
            properties.mode = AAZStrType(
                flags={"required": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.published_date = AAZStrType(
                serialized_name="publishedDate",
                flags={"read_only": True},
            )
            properties.replication_status = AAZObjectType(
                serialized_name="replicationStatus",
                flags={"read_only": True},
            )
            properties.rules = AAZObjectType()
            properties.target_locations = AAZListType(
                serialized_name="targetLocations",
            )

            replication_status = cls._schema_on_200.properties.replication_status
            replication_status.aggregated_state = AAZStrType(
                serialized_name="aggregatedState",
                flags={"read_only": True},
            )
            replication_status.summary = AAZListType(
                flags={"read_only": True},
            )

            summary = cls._schema_on_200.properties.replication_status.summary
            summary.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.replication_status.summary.Element
            _element.details = AAZStrType(
                flags={"read_only": True},
            )
            _element.progress = AAZIntType(
                flags={"read_only": True},
            )
            _element.region = AAZStrType(
                flags={"read_only": True},
            )
            _element.state = AAZStrType(
                flags={"read_only": True},
            )

            rules = cls._schema_on_200.properties.rules
            rules.identities = AAZListType()
            rules.privileges = AAZListType()
            rules.role_assignments = AAZListType(
                serialized_name="roleAssignments",
            )
            rules.roles = AAZListType()

            identities = cls._schema_on_200.properties.rules.identities
            identities.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.rules.identities.Element
            _element.exe_path = AAZStrType(
                serialized_name="exePath",
            )
            _element.group_name = AAZStrType(
                serialized_name="groupName",
            )
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.process_name = AAZStrType(
                serialized_name="processName",
            )
            _element.user_name = AAZStrType(
                serialized_name="userName",
            )

            privileges = cls._schema_on_200.properties.rules.privileges
            privileges.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.rules.privileges.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.path = AAZStrType(
                flags={"required": True},
            )
            _element.query_parameters = AAZDictType(
                serialized_name="queryParameters",
            )

            query_parameters = cls._schema_on_200.properties.rules.privileges.Element.query_parameters
            query_parameters.Element = AAZStrType()

            role_assignments = cls._schema_on_200.properties.rules.role_assignments
            role_assignments.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.rules.role_assignments.Element
            _element.identities = AAZListType(
                flags={"required": True},
            )
            _element.role = AAZStrType(
                flags={"required": True},
            )

            identities = cls._schema_on_200.properties.rules.role_assignments.Element.identities
            identities.Element = AAZStrType()

            roles = cls._schema_on_200.properties.rules.roles
            roles.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.rules.roles.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.privileges = AAZListType(
                flags={"required": True},
            )

            privileges = cls._schema_on_200.properties.rules.roles.Element.privileges
            privileges.Element = AAZStrType()

            target_locations = cls._schema_on_200.properties.target_locations
            target_locations.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.target_locations.Element
            _element.additional_replica_sets = AAZListType(
                serialized_name="additionalReplicaSets",
            )
            _element.encryption = AAZObjectType()
            _element.exclude_from_latest = AAZBoolType(
                serialized_name="excludeFromLatest",
            )
            _element.name = AAZStrType(
                flags={"required": True},
            )
            _element.regional_replica_count = AAZIntType(
                serialized_name="regionalReplicaCount",
            )
            _element.storage_account_type = AAZStrType(
                serialized_name="storageAccountType",
            )

            additional_replica_sets = cls._schema_on_200.properties.target_locations.Element.additional_replica_sets
            additional_replica_sets.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.target_locations.Element.additional_replica_sets.Element
            _element.regional_replica_count = AAZIntType(
                serialized_name="regionalReplicaCount",
            )
            _element.storage_account_type = AAZStrType(
                serialized_name="storageAccountType",
            )

            encryption = cls._schema_on_200.properties.target_locations.Element.encryption
            encryption.data_disk_images = AAZListType(
                serialized_name="dataDiskImages",
            )
            encryption.os_disk_image = AAZObjectType(
                serialized_name="osDiskImage",
            )

            data_disk_images = cls._schema_on_200.properties.target_locations.Element.encryption.data_disk_images
            data_disk_images.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.target_locations.Element.encryption.data_disk_images.Element
            _element.disk_encryption_set_id = AAZStrType(
                serialized_name="diskEncryptionSetId",
            )
            _element.lun = AAZIntType(
                flags={"required": True},
            )

            os_disk_image = cls._schema_on_200.properties.target_locations.Element.encryption.os_disk_image
            os_disk_image.disk_encryption_set_id = AAZStrType(
                serialized_name="diskEncryptionSetId",
            )
            os_disk_image.security_profile = AAZObjectType(
                serialized_name="securityProfile",
            )

            security_profile = cls._schema_on_200.properties.target_locations.Element.encryption.os_disk_image.security_profile
            security_profile.confidential_vm_encryption_type = AAZStrType(
                serialized_name="confidentialVMEncryptionType",
            )
            security_profile.secure_vm_disk_encryption_set_id = AAZStrType(
                serialized_name="secureVMDiskEncryptionSetId",
            )

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _WaitHelper:
    """Helper class for Wait"""


__all__ = ["Wait"]
