# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "sig image-definition list-shared",
)
class ListShared(AAZCommand):
    """List VM Image definitions in a gallery shared directly to your subscription or tenant

    :example: List an image definition in a gallery shared directly to your subscription in the given location.
        az sig image-definition list-shared --gallery-unique-name galleryUniqueName --location myLocation

    :example: List an image definition in a gallery shared directly to your tenant in the given location.
        az sig image-definition list-shared --gallery-unique-name galleryUniqueName --location myLocation --shared-to tenant
    """

    _aaz_info = {
        "version": "2023-07-03",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.compute/locations/{}/sharedgalleries/{}/images", "2023-07-03"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.gallery_unique_name = AAZStrArg(
            options=["--gallery-unique-name"],
            help="The unique name of the Shared Gallery.",
            required=True,
        )
        _args_schema.location = AAZResourceLocationArg(
            required=True,
        )
        _args_schema.shared_to = AAZStrArg(
            options=["--shared-to"],
            help="The query parameter to decide what shared galleries to fetch when doing listing operations. If not specified, list by subscription id.",
            enum={"tenant": "tenant"},
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.SharedGalleryImagesList(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class SharedGalleryImagesList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.Compute/locations/{location}/sharedGalleries/{galleryUniqueName}/images",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "galleryUniqueName", self.ctx.args.gallery_unique_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "location", self.ctx.args.location,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "sharedTo", self.ctx.args.shared_to,
                ),
                **self.serialize_query_param(
                    "api-version", "2023-07-03",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
            )
            _schema_on_200.value = AAZListType(
                flags={"required": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.identifier = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.location = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )

            identifier = cls._schema_on_200.value.Element.identifier
            identifier.unique_id = AAZStrType(
                serialized_name="uniqueId",
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.architecture = AAZStrType()
            properties.artifact_tags = AAZDictType(
                serialized_name="artifactTags",
            )
            properties.disallowed = AAZObjectType()
            properties.end_of_life_date = AAZStrType(
                serialized_name="endOfLifeDate",
            )
            properties.eula = AAZStrType()
            properties.features = AAZListType()
            properties.hyper_v_generation = AAZStrType(
                serialized_name="hyperVGeneration",
            )
            properties.identifier = AAZObjectType(
                flags={"required": True},
            )
            properties.os_state = AAZStrType(
                serialized_name="osState",
                flags={"required": True},
            )
            properties.os_type = AAZStrType(
                serialized_name="osType",
                flags={"required": True},
            )
            properties.privacy_statement_uri = AAZStrType(
                serialized_name="privacyStatementUri",
            )
            properties.purchase_plan = AAZObjectType(
                serialized_name="purchasePlan",
            )
            properties.recommended = AAZObjectType()

            artifact_tags = cls._schema_on_200.value.Element.properties.artifact_tags
            artifact_tags.Element = AAZStrType()

            disallowed = cls._schema_on_200.value.Element.properties.disallowed
            disallowed.disk_types = AAZListType(
                serialized_name="diskTypes",
            )

            disk_types = cls._schema_on_200.value.Element.properties.disallowed.disk_types
            disk_types.Element = AAZStrType()

            features = cls._schema_on_200.value.Element.properties.features
            features.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.features.Element
            _element.name = AAZStrType()
            _element.value = AAZStrType()

            identifier = cls._schema_on_200.value.Element.properties.identifier
            identifier.offer = AAZStrType(
                flags={"required": True},
            )
            identifier.publisher = AAZStrType(
                flags={"required": True},
            )
            identifier.sku = AAZStrType(
                flags={"required": True},
            )

            purchase_plan = cls._schema_on_200.value.Element.properties.purchase_plan
            purchase_plan.name = AAZStrType()
            purchase_plan.product = AAZStrType()
            purchase_plan.publisher = AAZStrType()

            recommended = cls._schema_on_200.value.Element.properties.recommended
            recommended.memory = AAZObjectType()
            _ListSharedHelper._build_schema_resource_range_read(recommended.memory)
            recommended.v_cp_us = AAZObjectType(
                serialized_name="vCPUs",
            )
            _ListSharedHelper._build_schema_resource_range_read(recommended.v_cp_us)

            return cls._schema_on_200


class _ListSharedHelper:
    """Helper class for ListShared"""

    _schema_resource_range_read = None

    @classmethod
    def _build_schema_resource_range_read(cls, _schema):
        if cls._schema_resource_range_read is not None:
            _schema.max = cls._schema_resource_range_read.max
            _schema.min = cls._schema_resource_range_read.min
            return

        cls._schema_resource_range_read = _schema_resource_range_read = AAZObjectType()

        resource_range_read = _schema_resource_range_read
        resource_range_read.max = AAZIntType()
        resource_range_read.min = AAZIntType()

        _schema.max = cls._schema_resource_range_read.max
        _schema.min = cls._schema_resource_range_read.min


__all__ = ["ListShared"]
