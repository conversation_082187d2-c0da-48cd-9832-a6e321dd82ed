# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class ListBySubscription(AAZCommand):
    """List the invoices for a subscription.
    """

    _aaz_info = {
        "version": "2020-05-01",
        "resources": [
            ["mgmt-plane", "/providers/microsoft.billing/billingaccounts/default/billingsubscriptions/{}/invoices", "2020-05-01"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.period_end_date = AAZStrArg(
            options=["--period-end-date"],
            help="Invoice period end date.",
            required=True,
        )
        _args_schema.period_start_date = AAZStrArg(
            options=["--period-start-date"],
            help="Invoice period start date.",
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.InvoicesListByBillingSubscription(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class InvoicesListByBillingSubscription(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/providers/Microsoft.Billing/billingAccounts/default/billingSubscriptions/{subscriptionId}/invoices",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "periodEndDate", self.ctx.args.period_end_date,
                    required=True,
                ),
                **self.serialize_query_param(
                    "periodStartDate", self.ctx.args.period_start_date,
                    required=True,
                ),
                **self.serialize_query_param(
                    "api-version", "2020-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.total_count = AAZFloatType(
                serialized_name="totalCount",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType(
                flags={"read_only": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.amount_due = AAZObjectType(
                serialized_name="amountDue",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.amount_due)
            properties.azure_prepayment_applied = AAZObjectType(
                serialized_name="azurePrepaymentApplied",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.azure_prepayment_applied)
            properties.billed_amount = AAZObjectType(
                serialized_name="billedAmount",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.billed_amount)
            properties.billed_document_id = AAZStrType(
                serialized_name="billedDocumentId",
                flags={"read_only": True},
            )
            properties.billing_profile_display_name = AAZStrType(
                serialized_name="billingProfileDisplayName",
                flags={"read_only": True},
            )
            properties.billing_profile_id = AAZStrType(
                serialized_name="billingProfileId",
                flags={"read_only": True},
            )
            properties.credit_amount = AAZObjectType(
                serialized_name="creditAmount",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.credit_amount)
            properties.credit_for_document_id = AAZStrType(
                serialized_name="creditForDocumentId",
                flags={"read_only": True},
            )
            properties.document_type = AAZStrType(
                serialized_name="documentType",
                flags={"read_only": True},
            )
            properties.documents = AAZListType(
                flags={"read_only": True},
            )
            properties.due_date = AAZStrType(
                serialized_name="dueDate",
                flags={"read_only": True},
            )
            properties.free_azure_credit_applied = AAZObjectType(
                serialized_name="freeAzureCreditApplied",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.free_azure_credit_applied)
            properties.invoice_date = AAZStrType(
                serialized_name="invoiceDate",
                flags={"read_only": True},
            )
            properties.invoice_period_end_date = AAZStrType(
                serialized_name="invoicePeriodEndDate",
                flags={"read_only": True},
            )
            properties.invoice_period_start_date = AAZStrType(
                serialized_name="invoicePeriodStartDate",
                flags={"read_only": True},
            )
            properties.invoice_type = AAZStrType(
                serialized_name="invoiceType",
                flags={"read_only": True},
            )
            properties.is_monthly_invoice = AAZBoolType(
                serialized_name="isMonthlyInvoice",
                flags={"read_only": True},
            )
            properties.payments = AAZListType(
                flags={"read_only": True},
            )
            properties.purchase_order_number = AAZStrType(
                serialized_name="purchaseOrderNumber",
                flags={"read_only": True},
            )
            properties.rebill_details = AAZObjectType(
                serialized_name="rebillDetails",
                flags={"read_only": True},
            )
            properties.status = AAZStrType(
                flags={"read_only": True},
            )
            properties.sub_total = AAZObjectType(
                serialized_name="subTotal",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.sub_total)
            properties.subscription_id = AAZStrType(
                serialized_name="subscriptionId",
                flags={"read_only": True},
            )
            properties.tax_amount = AAZObjectType(
                serialized_name="taxAmount",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.tax_amount)
            properties.total_amount = AAZObjectType(
                serialized_name="totalAmount",
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(properties.total_amount)

            documents = cls._schema_on_200.value.Element.properties.documents
            documents.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.documents.Element
            _element.kind = AAZStrType(
                flags={"read_only": True},
            )
            _element.source = AAZStrType(
                flags={"read_only": True},
            )
            _element.url = AAZStrType(
                flags={"read_only": True},
            )

            payments = cls._schema_on_200.value.Element.properties.payments
            payments.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.payments.Element
            _element.amount = AAZObjectType(
                flags={"read_only": True},
            )
            _ListBySubscriptionHelper._build_schema_amount_read(_element.amount)
            _element.date = AAZStrType(
                flags={"read_only": True},
            )
            _element.payment_method_family = AAZStrType(
                serialized_name="paymentMethodFamily",
            )
            _element.payment_method_type = AAZStrType(
                serialized_name="paymentMethodType",
                flags={"read_only": True},
            )
            _element.payment_type = AAZStrType(
                serialized_name="paymentType",
                flags={"read_only": True},
            )

            rebill_details = cls._schema_on_200.value.Element.properties.rebill_details
            rebill_details.credit_note_document_id = AAZStrType(
                serialized_name="creditNoteDocumentId",
                flags={"read_only": True},
            )
            rebill_details.invoice_document_id = AAZStrType(
                serialized_name="invoiceDocumentId",
                flags={"read_only": True},
            )

            return cls._schema_on_200


class _ListBySubscriptionHelper:
    """Helper class for ListBySubscription"""

    _schema_amount_read = None

    @classmethod
    def _build_schema_amount_read(cls, _schema):
        if cls._schema_amount_read is not None:
            _schema.currency = cls._schema_amount_read.currency
            _schema.value = cls._schema_amount_read.value
            return

        cls._schema_amount_read = _schema_amount_read = AAZObjectType(
            flags={"read_only": True}
        )

        amount_read = _schema_amount_read
        amount_read.currency = AAZStrType(
            flags={"read_only": True},
        )
        amount_read.value = AAZFloatType()

        _schema.currency = cls._schema_amount_read.currency
        _schema.value = cls._schema_amount_read.value


__all__ = ["ListBySubscription"]
