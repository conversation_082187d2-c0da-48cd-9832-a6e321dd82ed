# coding=utf-8
# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# --------------------------------------------------------------------------------------------

from knack.help_files import helps  # pylint: disable=unused-import
# pylint: disable=line-too-long, too-many-lines

helps['dms'] = """
type: group
short-summary: Manage Azure Data Migration Service (classic) instances.
"""

helps['dms check-name'] = """
type: command
short-summary: Check if a given DMS instance name is available in a given region as well as the name's validity.
parameters:
  - name: --name -n
    type: string
    short-summary: >
        The service name to check.
examples:
  - name: Check if a given DMS instance name is available in a given region as well as the name's validity. (autogenerated)
    text: az dms check-name --location westus2 --name MyService
    crafted: true
"""

helps['dms check-status'] = """
type: command
short-summary: Perform a health check and return the status of the service and virtual machine size.
examples:
  - name: Perform a health check and return the status of the service and virtual machine size. (autogenerated)
    text: az dms check-status --name MyService --resource-group MyResourceGroup
    crafted: true
"""

helps['dms create'] = """
type: command
short-summary: Create an instance of the Azure Database Migration Service (classic).
parameters:
  - name: --sku-name
    type: string
    short-summary: >
        The name of the CPU SKU on which the service's virtual machine will run. Check the name and the availability of SKUs in your area with "az dms list-skus".
  - name: --subnet
    type: string
    short-summary: >
        The Resource ID of the VNet's Subnet you will use to connect the source and target DBs.
        Use "az network vnet subnet show -h" for help to get your subnet's ID.
examples:
  - name: Create an instance of DMS.
    text: >
        az dms create -l westus -n mydms -g myresourcegroup --sku-name Basic_2vCores --subnet /subscriptions/{vnetSubscriptionId}/resourceGroups/{vnetResourceGroup}/providers/Microsoft.Network/virtualNetworks/{vnetName}/subnets/{subnetName} --tags tagName1=tagValue1 tagWithNoValue
"""

helps['dms delete'] = """
type: command
short-summary: Delete an instance of the Azure Database Migration Service (classic).
parameters:
  - name: --delete-running-tasks
    type: bool
    short-summary: >
        Cancel any running tasks before deleting the service.
examples:
  - name: Delete an instance of the Azure Database Migration Service (classic). (autogenerated)
    text: az dms delete --name MyService --resource-group MyResourceGroup
    crafted: true
"""

helps['dms list'] = """
type: command
short-summary: List the DMS instances within your currently configured subscription (to set this use "az account set"). If provided, only show the instances within a given resource group.
examples:
  - name: List all the instances in your subscription.
    text: >
        az dms list
  - name: List all the instances in a given resource group.
    text: >
        az dms list -g myresourcegroup
"""

helps['dms list-skus'] = """
type: command
short-summary: List the SKUs that are supported by the Azure Database Migration Service (classic).
"""

helps['dms project'] = """
type: group
short-summary: Manage projects for an instance of the Azure Database Migration Service (classic).
"""

helps['dms project check-name'] = """
type: command
short-summary: Check if a given project name is available within a given instance of DMS as well as the name's validity.
parameters:
  - name: --name -n
    type: string
    short-summary: >
        The project name to check.
"""

helps['dms project create'] = """
type: command
short-summary: Create a migration project which can contain multiple tasks.
long-summary: |
  The following project configurations are supported:
    -) source -> target
    1) SQL -> SQLDB
    2) PostgreSQL -> AzureDbForPostgreSQL
    3) MySQL -> AzureDbForMySQL

parameters:
  - name: --source-platform
    type: string
    short-summary: >
        The type of server for the source database. The supported types are: SQL, PostgreSQL, MySQL.
  - name: --target-platform
    type: string
    short-summary: >
        The type of service for the target database. The supported types are: SQLDB, AzureDbForPostgreSQL, AzureDbForMySQL.
examples:
  - name: Create a SQL to SQLDB project for a DMS instance.
    text: >
        az dms project create -l westus -n sqlproject -g myresourcegroup --service-name mydms --source-platform SQL --target-platform SQLDB --tags tagName1=tagValue1 tagWithNoValue
  - name: Create a PostgreSql to AzureDbForPostgreSql project for a DMS instance.
    text: >
        az dms project create -l westus -n pgproject -g myresourcegroup --service-name mydms --source-platform PostgreSQL --target-platform AzureDbForPostgreSQL --tags tagName1=tagValue1 tagWithNoValue
  - name: Create a MySQL to AzureDbForMySQL project for a DMS instance.
    text: >
        az dms project create -l westus -n mysqlproject -g myresourcegroup --service-name mydms --source-platform MySQL --target-platform AzureDbForMySQL --tags tagName1=tagValue1 tagWithNoValue
"""

helps['dms project delete'] = """
type: command
short-summary: Delete a project.
parameters:
  - name: --delete-running-tasks
    type: bool
    short-summary: >
        Cancel any running tasks before deleting the project.
examples:
  - name: Delete a project. (autogenerated)
    text: az dms project delete --name MyProject --resource-group MyResourceGroup --service-name MyService
    crafted: true
"""

helps['dms project list'] = """
type: command
short-summary: List the projects within an instance of DMS.
examples:
  - name: List the projects within an instance of DMS. (autogenerated)
    text: az dms project list --resource-group MyResourceGroup --service-name MyService
    crafted: true
"""

helps['dms project show'] = """
type: command
short-summary: Show the details of a migration project.
examples:
  - name: Show the details of a migration project. (autogenerated)
    text: az dms project show --name MyProject --resource-group MyResourceGroup --service-name MyService
    crafted: true
"""

helps['dms project task'] = """
type: group
short-summary: Manage tasks for an Azure Database Migration Service (classic) instance's project.
"""

helps['dms project task cancel'] = """
type: command
short-summary: Cancel a task if it's currently queued or running.
"""

helps['dms project task check-name'] = """
type: command
short-summary: Check if a given task name is available within a given instance of DMS as well as the name's validity.
parameters:
  - name: --name -n
    type: string
    short-summary: >
        The task name to check.
"""

helps['dms project task create'] = """
type: command
short-summary: Create and start a migration task.
long-summary: |
  The following task configurations are supported:
    -) source -> target :: task type
    1) SQL -> SQLDB :: OfflineMigration
    2) PostgreSQL -> AzureDbForPostgreSql :: OnlineMigration
    3) MySQL -> AzureDbForMySQL :: OfflineMigration, OnlineMigration, ReplicateChanges
parameters:
  - name: --task-type
    type: string
    short-summary: >
        The type of data movement the task will support. The supported types are: OnlineMigration, OfflineMigration. If not provided, will default to OfflineMigration for SQL, MySQL and OnlineMigration for PostgreSQL.
  - name: --database-options-json
    type: string
    short-summary: >
        Database and table information. This can be either a JSON-formatted string or the location to a file containing the JSON object. See example below for the format.
    long-summary: >
        For SQL we support per table migrations. To use this, specify the tables names in the 'table_map' as below.
        You can also set the source as read only.
            [
                {
                    "name": "source database",
                    "target_database_name": "target database",
                    "make_source_db_read_only": false|true,
                    "table_map": {
                        "schema.SourceTableName1": "schema.TargetTableName1",
                        "schema.SourceTableName2": "schema.TargetTableName2",
                        ...n
                    }
                },
                ...n
            ]

        For PostgreSQL, the format of the database options JSON object.
            [
                {
                    "name": "source database",
                    "target_database_name": "target database",
                    // Used for manipulating the underlying migration engine.
                    // Only provide if instructed to do so or if you really know what you are doing.
                    "migrationSetting": {
                        "setting1": "value1",
                        ...n
                    },
                    // Used for manipulating the underlying migration engine.
                    // Only provide if instructed to do so or if you really know what you are doing.
                    "sourceSetting": {
                        "setting1": "value1",
                        ...n
                    },
                    // Used for manipulating the underlying migration engine.
                    // Only provide if instructed to do so or if you really know what you are doing.
                    "targetSetting": {
                        "setting1": "value1",
                        ...n
                    },
                    // Optional parameter to list tables that you want included in the migration.
                    "selectedTables": [
                        "schemaName1.tableName1",
                        ...n
                    ]
                },
                ...n
            ]

        For MySQL, the format of the database options JSON object.
        {
            // Details of mapped schemas that needs to be migrated. Multiple schemas can be migrated at a time.
            "selected_databases":[
                // database/schema 1 details
                {
                  "name": "sourceSchema1",
                  "target_database_name": "targetSchema1",
                  // Table mapping from source to target schemas [Optional]
                  // Don't add it if all tables of this database needs to be migrated
                  "table_map": {
                        "sourceSchema1.table1": "targetSchema1.table1",
                        "sourceSchema1.table2": "targetSchema1.table2",
                        "sourceSchema1.table3": "targetSchema1.table3",
                        ..n
                    }
                    // the below items are only necessary for selective schema migration
                    // optional, migrates schema for the following tables
                    'tables_to_migrate_schema': {
                        "sourceSchema1.table2": "targetSchema1.table2",
                        "sourceSchema1.table3": "targetSchema1.table3"
                    },
                    // optional, migrates the enumerated views
                    'selected_views': [
                        'sourceSchema1.view1'
                    ],
                    // optional, migrates the enumerated triggers
                    'selected_triggers': [
                        'sourceSchema1.on_table1_updated'
                    ],
                    // optional, migrates the enumerated routines
                    'selected_routines': [
                        'sourceSchema1.build_report'
                    ],
                    // optional, migrates the enumerated events
                    'selected_events': [
                        'sourceSchema1.nightly_maintenance'
                    ],
                    // Optional. If true, DMS will migrate the source database schema to the target.
                    "select_database_for_schema_migration": "true|false"
                },
                ...n
            ],

            // Used for manipulating the underlying migration engine. [Optional]
            // Only provide if instructed to do so or if you really know what you are doing.
            "migration_level_settings": {
                // Optional setting that configures the maximum number of parallel reads on tables located on the source database.
                "DesiredRangesCount": "4",
                // Optional setting that configures that size of the largest batch that will be committed to the target server.
                "MaxBatchSizeKb": "4096",
                // Optional setting that configures the minimum number of rows in each batch written to the target.
                "MinBatchRows": null,
                // Optional setting that configures the number of databases that will be prepared for migration in parallel.
                "PrepareDatabaseForBulkImportTaskCount": null,
                // Optional setting that configures the number of tables that will be prepared for migration in parallel.
                "PrepareTableForBulkImportTaskCount": null,
                // Optional setting that configures the number of threads available to read ranges on the source.
                "QueryTableDataRangeTaskCount": "8",
                // Optional setting that configures the number of threads available to write batches to the target.
                "WriteDataRangeBatchTaskCount": "12",
                // Optional setting that configures how much memory will be used to cache batches in memory before reads on the source are throttled.
                "MaxBatchCacheSizeMb": null,
                // Optional setting that configures the amount of available memory at which point reads on the source will be throttled.
                "ThrottleQueryTableDataRangeTaskAtAvailableMemoryMb": null,
                // Optional setting that configures the number of batches cached in memory that will trigger read throttling on the source.
                "ThrottleQueryTableDataRangeTaskAtBatchCount": 36,
                // Optional setting that configures the delay between updates of result objects in Azure Table Storage.
                "DelayProgressUpdatesInStorageInterval": "00:00:30",
            },
            // Optional setting to set the source server read only.
            "make_source_server_read_only": "true|false",
            // Optional setting to enable consistent backup. True by default for the sync migration, unless lockless is enabled.
            "enable_consistent_backup": "true|false",
            // Optional setting to enable lockless snapshot.
            "enable_consistent_backup_without_locks": "true|false",
            // Optional. If true, all view definitions will be migrated in the selected databases.
            "migrate_all_views": "true|false",
            // Optional. If true, all trigger definitions will be migrated in the selected databases.
            "migrate_all_triggers": "true|false",
            // Optional. If true, all event definitions will be migrated in the selected databases.
            "migrate_all_events": "true|false",
            // Optional. If true, all stored proc definitions will be migrated in the selected databases.
            "migrate_all_routines": "true|false",
            // Optional. If true, all table's schemas will be migrated.
            "migrate_all_tables_schema": "true|false",
            // Optional. If true, all users/grants will be migrated.
            "migrate_user_system_tables": "true|false",
            // Binlog position to start the migration from. Only applicable for the ReplicateChanges migration.
            "binLogInfo": {
                "filename": "binlog.0004523",
                "position": 283287
            }
        }

  - name: --source-connection-json
    type: string
    short-summary: >
        The connection information to the source server. This can be either a JSON-formatted string or the location to a file containing the JSON object. See examples below for the format.
    long-summary: |
      The format of the connection JSON object for SQL connections.
        {
            "userName": "user name",    // if this is missing or null, you will be prompted
            "password": null,           // if this is missing or null (highly recommended) you will be prompted
            "dataSource": "server name[,port]",
            "authentication": "SqlAuthentication|WindowsAuthentication",
            "encryptConnection": true,      // highly recommended to leave as true
            "trustServerCertificate": false  // highly recommended to leave as false
        }

      The format of the connection JSON object for PostgreSQL connections.
        {
            "userName": "user name",    // if this is missing or null, you will be prompted
            "password": null,           // if this is missing or null (highly recommended) you will be prompted
            "serverName": "server name",
            "databaseName": "database name", // if this is missing, it will default to the 'postgres' database
            "port": 5432,                // if this is missing, it will default to 5432
            "encryptConnection": true,      // highly recommended to leave as true
            "trustServerCertificate": false  // highly recommended to leave as false
        }

      The format of the connection JSON object for MySQL connections.
        {
            "userName": "user name",    // if this is missing or null, you will be prompted
            "password": null,           // if this is missing or null (highly recommended) you will be prompted
            "serverName": "server name",
            "port": 3306                // if this is missing, it will default to 3306
        }
  - name: --target-connection-json
    type: string
    short-summary: >
        The connection information to the target server. This can be either a JSON-formatted string or the location to a file containing the JSON object. See 'source-connection-json' for examples of connection formats.
  - name: --enable-data-integrity-validation
    type: bool
    short-summary: >
        For SQL only. Whether to perform a checksum based data integrity validation between source and target for the selected database and tables.
  - name: --enable-query-analysis-validation
    type: bool
    short-summary: >
        For SQL only. Whether to perform a quick and intelligent query analysis by retrieving queries from the source database and
        executing them in the target. The result will have execution statistics for executions in source and target databases
        for the extracted queries.
  - name: --enable-schema-validation
    type: bool
    short-summary: >
        For SQL only. Whether to compare the schema information between source and target.
examples:
  - name: Create and start an offline SQL migration task. For a SQL migration, this will perform no validation checks.
    text: >
        az dms project task create --task-type OfflineMigration --database-options-json "C:\\CLI Files\\databaseOptions.json" -n mytask --project-name myproject -g myresourcegroup --service-name mydms --source-connection-json "{'dataSource': 'myserver', 'authentication': 'SqlAuthentication', 'encryptConnection': 'true', 'trustServerCertificate': 'true'}" --target-connection-json "C:\\CLI Files\\targetConnection.json"
  - name: Create and start a SQL task which performs all validation checks.
    text: >
        az dms project task create --task-type OfflineMigration --database-options-json "C:\\CLI Files\\databaseOptions.json" -n mytask --project-name myproject -g myresourcegroup --service-name mydms --source-connection-json "C:\\CLI Files\\sourceConnection.json" --target-connection-json "C:\\CLI Files\\targetConnection.json" --enable-data-integrity-validation --enable-query-analysis-validation --enable-schema-validation
"""

helps['dms project task delete'] = """
type: command
short-summary: Delete a migration task.
parameters:
  - name: --delete-running-tasks
    type: bool
    short-summary: >
        If the task is currently running, cancel the task before deleting the project.
examples:
  - name: Delete a migration task. (autogenerated)
    text: az dms project task delete --name MyTask --project-name MyProject --resource-group MyResourceGroup --service-name MyService
    crafted: true
"""

helps['dms project task list'] = """
type: command
short-summary: List the tasks within a project. Some tasks may have a status of Unknown, which indicates that an error occurred while querying the status of that task.
parameters:
  - name: --task-type
    type: string
    short-summary: >
        Filters the list by the type of task. For the list of possible types see "az dms check-status".
examples:
  - name: List all tasks within a project.
    text: >
        az dms project task list --project-name myproject -g myresourcegroup --service-name mydms
  - name: List only the SQL to SQL migration tasks within a project.
    text: >
        az dms project task list --project-name myproject -g myresourcegroup --service-name mydms --task-type Migrate.SqlServer.SqlDb
"""

helps['dms project task show'] = """
type: command
short-summary: Show the details of a migration task. Use the "--expand" to get more details.
parameters:
  - name: --expand
    type: string
    short-summary: >
        Expand the response to provide more details. Use with "command" to see more details of the task.
        Use with "output" to see the results of the task's migration.
examples:
  - name: Show the details of a migration task. Use the "--expand" to get more details. (autogenerated)
    text: az dms project task show --name MyTask --project-name MyProject --resource-group MyResourceGroup --service-name MyService
    crafted: true
"""

helps['dms project task cutover'] = """
type: command
short-summary: For an online migration task, complete the migration by performing a cutover.
long-summary: |
  To see the result of the request, please use the 'task show' command:
      az dms project task show ... --expand command

parameters:
  - name: --object-name
    type: string
    short-summary: >
      The name of the database on the source you wish to cutover.
"""

helps['dms show'] = """
type: command
short-summary: Show the details for an instance of the Azure Database Migration Service (classic).
"""

helps['dms start'] = """
type: command
short-summary: Start an instance of the Azure Database Migration Service (classic). It can then be used to run data migrations.
examples:
  - name: Start an instance of the Azure Database Migration Service (classic). It can then be used to run data migrations. (autogenerated)
    text: az dms start --name MyService --resource-group MyResourceGroup
    crafted: true
"""

helps['dms stop'] = """
type: command
short-summary: Stop an instance of the Azure Database Migration Service (classic). While stopped, it can't be used to run data migrations and the owner won't be billed.
"""

helps['dms wait'] = """
type: command
short-summary: Place the CLI in a waiting state until a condition of the DMS instance is met.
"""
