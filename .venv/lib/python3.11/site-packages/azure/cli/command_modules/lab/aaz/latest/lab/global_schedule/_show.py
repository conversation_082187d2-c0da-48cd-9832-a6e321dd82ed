# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


class Show(AAZCommand):
    """Get schedule.
    """

    _aaz_info = {
        "version": "2018-09-15",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.devtestlab/schedules/{}", "2018-09-15"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The name of the schedule.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.expand = AAZStrArg(
            options=["--expand"],
            help="Specify the $expand query. Example: 'properties($select=status)'",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.GlobalSchedulesGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class GlobalSchedulesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DevTestLab/schedules/{name}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "name", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$expand", self.ctx.args.expand,
                ),
                **self.serialize_query_param(
                    "api-version", "2018-09-15",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.location = AAZStrType()
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.created_date = AAZStrType(
                serialized_name="createdDate",
                flags={"read_only": True},
            )
            properties.daily_recurrence = AAZObjectType(
                serialized_name="dailyRecurrence",
            )
            properties.hourly_recurrence = AAZObjectType(
                serialized_name="hourlyRecurrence",
            )
            properties.notification_settings = AAZObjectType(
                serialized_name="notificationSettings",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.status = AAZStrType()
            properties.target_resource_id = AAZStrType(
                serialized_name="targetResourceId",
            )
            properties.task_type = AAZStrType(
                serialized_name="taskType",
            )
            properties.time_zone_id = AAZStrType(
                serialized_name="timeZoneId",
            )
            properties.unique_identifier = AAZStrType(
                serialized_name="uniqueIdentifier",
                flags={"read_only": True},
            )
            properties.weekly_recurrence = AAZObjectType(
                serialized_name="weeklyRecurrence",
            )

            daily_recurrence = cls._schema_on_200.properties.daily_recurrence
            daily_recurrence.time = AAZStrType()

            hourly_recurrence = cls._schema_on_200.properties.hourly_recurrence
            hourly_recurrence.minute = AAZIntType()

            notification_settings = cls._schema_on_200.properties.notification_settings
            notification_settings.email_recipient = AAZStrType(
                serialized_name="emailRecipient",
            )
            notification_settings.notification_locale = AAZStrType(
                serialized_name="notificationLocale",
            )
            notification_settings.status = AAZStrType()
            notification_settings.time_in_minutes = AAZIntType(
                serialized_name="timeInMinutes",
            )
            notification_settings.webhook_url = AAZStrType(
                serialized_name="webhookUrl",
            )

            weekly_recurrence = cls._schema_on_200.properties.weekly_recurrence
            weekly_recurrence.time = AAZStrType()
            weekly_recurrence.weekdays = AAZListType()

            weekdays = cls._schema_on_200.properties.weekly_recurrence.weekdays
            weekdays.Element = AAZStrType()

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _ShowHelper:
    """Helper class for Show"""


__all__ = ["Show"]
