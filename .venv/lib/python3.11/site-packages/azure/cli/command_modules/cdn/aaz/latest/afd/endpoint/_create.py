# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "afd endpoint create",
)
class Create(AAZCommand):
    """Create a new AzureFrontDoor endpoint with the specified endpoint name under the specified subscription, resource group and profile.

    :example: Creates an enabled endpoint
        az afd endpoint create -g group --endpoint-name endpoint1 --profile-name profile --enabled-state Enabled
    """

    _aaz_info = {
        "version": "2025-06-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}/afdendpoints/{}", "2025-06-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.endpoint_name = AAZStrArg(
            options=["-n", "--name", "--endpoint-name"],
            help="Name of the endpoint under the profile which is unique globally.",
            required=True,
        )
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="Name of the Azure Front Door Standard or Azure Front Door Premium profile which is unique within the resource group.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9]+(-*[a-zA-Z0-9])*$",
                max_length=260,
                min_length=1,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "Endpoint"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Endpoint",
            help="Resource location.",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Endpoint",
            help="Resource tags.",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.name_reuse_scope = AAZStrArg(
            options=["--name-reuse-scope"],
            arg_group="Properties",
            help="Indicates the endpoint name reuse scope. The default value is TenantReuse.",
            enum={"NoReuse": "NoReuse", "ResourceGroupReuse": "ResourceGroupReuse", "SubscriptionReuse": "SubscriptionReuse", "TenantReuse": "TenantReuse"},
        )
        _args_schema.enabled_state = AAZStrArg(
            options=["--enabled-state"],
            arg_group="Properties",
            help="Whether to enable use of this rule. Permitted values are 'Enabled' or 'Disabled'",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.AFDEndpointsCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class AFDEndpointsCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}/afdEndpoints/{endpointName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "endpointName", self.ctx.args.endpoint_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-06-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("autoGeneratedDomainNameLabelScope", AAZStrType, ".name_reuse_scope")
                properties.set_prop("enabledState", AAZStrType, ".enabled_state")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_afd_endpoint_read(cls._schema_on_200_201)

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    _schema_afd_endpoint_read = None

    @classmethod
    def _build_schema_afd_endpoint_read(cls, _schema):
        if cls._schema_afd_endpoint_read is not None:
            _schema.id = cls._schema_afd_endpoint_read.id
            _schema.location = cls._schema_afd_endpoint_read.location
            _schema.name = cls._schema_afd_endpoint_read.name
            _schema.properties = cls._schema_afd_endpoint_read.properties
            _schema.system_data = cls._schema_afd_endpoint_read.system_data
            _schema.tags = cls._schema_afd_endpoint_read.tags
            _schema.type = cls._schema_afd_endpoint_read.type
            return

        cls._schema_afd_endpoint_read = _schema_afd_endpoint_read = AAZObjectType()

        afd_endpoint_read = _schema_afd_endpoint_read
        afd_endpoint_read.id = AAZStrType(
            flags={"read_only": True},
        )
        afd_endpoint_read.location = AAZStrType(
            flags={"required": True},
        )
        afd_endpoint_read.name = AAZStrType(
            flags={"read_only": True},
        )
        afd_endpoint_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        afd_endpoint_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        afd_endpoint_read.tags = AAZDictType()
        afd_endpoint_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_afd_endpoint_read.properties
        properties.auto_generated_domain_name_label_scope = AAZStrType(
            serialized_name="autoGeneratedDomainNameLabelScope",
        )
        properties.deployment_status = AAZStrType(
            serialized_name="deploymentStatus",
            flags={"read_only": True},
        )
        properties.enabled_state = AAZStrType(
            serialized_name="enabledState",
        )
        properties.host_name = AAZStrType(
            serialized_name="hostName",
            flags={"read_only": True},
        )
        properties.profile_name = AAZStrType(
            serialized_name="profileName",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        system_data = _schema_afd_endpoint_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_afd_endpoint_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_afd_endpoint_read.id
        _schema.location = cls._schema_afd_endpoint_read.location
        _schema.name = cls._schema_afd_endpoint_read.name
        _schema.properties = cls._schema_afd_endpoint_read.properties
        _schema.system_data = cls._schema_afd_endpoint_read.system_data
        _schema.tags = cls._schema_afd_endpoint_read.tags
        _schema.type = cls._schema_afd_endpoint_read.type


__all__ = ["Create"]
