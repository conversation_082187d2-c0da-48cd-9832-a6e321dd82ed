# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "afd secret create",
)
class Create(AAZCommand):
    """Create a new Secret within the specified profile.

    :example: Creates a secret using the specified certificate version.
        az afd secret create -g group --profile-name profile --secret-name secret1 --secret-version version1 --secret-source /subscriptions/sub1/resourceGroups/rg1/providers/Microsoft.KeyVault/vaults/vault1/secrets/cert1
    """

    _aaz_info = {
        "version": "2025-06-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}/secrets/{}", "2025-06-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="Name of the Azure Front Door Standard or Azure Front Door Premium profile which is unique within the resource group.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9]+(-*[a-zA-Z0-9])*$",
                max_length=260,
                min_length=1,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.secret_name = AAZStrArg(
            options=["-n", "--name", "--secret-name"],
            help="Name of the Secret under the profile.",
            required=True,
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.parameters = AAZObjectArg(
            options=["--parameters"],
            arg_group="Properties",
            help="object which contains secret parameters",
        )

        parameters = cls._args_schema.parameters
        parameters.azure_first_party_managed_certificate = AAZObjectArg(
            options=["azure-first-party-managed-certificate"],
            blank={},
        )
        parameters.customer_certificate = AAZObjectArg(
            options=["customer-certificate"],
        )
        parameters.managed_certificate = AAZObjectArg(
            options=["managed-certificate"],
            blank={},
        )
        parameters.url_signing_key = AAZObjectArg(
            options=["url-signing-key"],
        )

        customer_certificate = cls._args_schema.parameters.customer_certificate
        customer_certificate.secret_source = AAZObjectArg(
            options=["secret-source"],
            help="Resource reference to the Azure Key Vault certificate. Expected to be in format of /subscriptions/{â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹subscriptionId}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹/resourceGroups/{â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹resourceGroupName}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹/providers/Microsoft.KeyVault/vaults/{vaultName}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹/secrets/{certificateName}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹",
            required=True,
        )
        cls._build_args_resource_reference_create(customer_certificate.secret_source)
        customer_certificate.secret_version = AAZStrArg(
            options=["secret-version"],
            help="Version of the secret to be used",
        )
        customer_certificate.use_latest_version = AAZBoolArg(
            options=["use-latest-version"],
            help="Whether to use the latest version for the certificate",
        )

        url_signing_key = cls._args_schema.parameters.url_signing_key
        url_signing_key.key_id = AAZStrArg(
            options=["key-id"],
            help="Defines the customer defined key Id. This id will exist in the incoming request to indicate the key used to form the hash.",
            required=True,
        )
        url_signing_key.secret_source = AAZObjectArg(
            options=["secret-source"],
            help="Resource reference to the Azure Key Vault secret. Expected to be in format of /subscriptions/{â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹subscriptionId}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹/resourceGroups/{â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹resourceGroupName}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹/providers/Microsoft.KeyVault/vaults/{vaultName}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹/secrets/{secretName}â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹â€‹",
            required=True,
        )
        cls._build_args_resource_reference_create(url_signing_key.secret_source)
        url_signing_key.secret_version = AAZStrArg(
            options=["secret-version"],
            help="Version of the secret to be used",
            required=True,
        )
        return cls._args_schema

    _args_resource_reference_create = None

    @classmethod
    def _build_args_resource_reference_create(cls, _schema):
        if cls._args_resource_reference_create is not None:
            _schema.id = cls._args_resource_reference_create.id
            return

        cls._args_resource_reference_create = AAZObjectArg()

        resource_reference_create = cls._args_resource_reference_create
        resource_reference_create.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        _schema.id = cls._args_resource_reference_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.SecretsCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class SecretsCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}/secrets/{secretName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "secretName", self.ctx.args.secret_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-06-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("parameters", AAZObjectType, ".parameters")

            parameters = _builder.get(".properties.parameters")
            if parameters is not None:
                parameters.set_const("type", "AzureFirstPartyManagedCertificate", AAZStrType, ".azure_first_party_managed_certificate", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("type", "CustomerCertificate", AAZStrType, ".customer_certificate", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("type", "ManagedCertificate", AAZStrType, ".managed_certificate", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("type", "UrlSigningKey", AAZStrType, ".url_signing_key", typ_kwargs={"flags": {"required": True}})
                parameters.discriminate_by("type", "AzureFirstPartyManagedCertificate")
                parameters.discriminate_by("type", "CustomerCertificate")
                parameters.discriminate_by("type", "ManagedCertificate")
                parameters.discriminate_by("type", "UrlSigningKey")

            disc_customer_certificate = _builder.get(".properties.parameters{type:CustomerCertificate}")
            if disc_customer_certificate is not None:
                _CreateHelper._build_schema_resource_reference_create(disc_customer_certificate.set_prop("secretSource", AAZObjectType, ".customer_certificate.secret_source", typ_kwargs={"flags": {"required": True}}))
                disc_customer_certificate.set_prop("secretVersion", AAZStrType, ".customer_certificate.secret_version")
                disc_customer_certificate.set_prop("useLatestVersion", AAZBoolType, ".customer_certificate.use_latest_version")

            disc_url_signing_key = _builder.get(".properties.parameters{type:UrlSigningKey}")
            if disc_url_signing_key is not None:
                disc_url_signing_key.set_prop("keyId", AAZStrType, ".url_signing_key.key_id", typ_kwargs={"flags": {"required": True}})
                _CreateHelper._build_schema_resource_reference_create(disc_url_signing_key.set_prop("secretSource", AAZObjectType, ".url_signing_key.secret_source", typ_kwargs={"flags": {"required": True}}))
                disc_url_signing_key.set_prop("secretVersion", AAZStrType, ".url_signing_key.secret_version", typ_kwargs={"flags": {"required": True}})

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_secret_read(cls._schema_on_200_201)

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_resource_reference_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_resource_reference_read = None

    @classmethod
    def _build_schema_resource_reference_read(cls, _schema):
        if cls._schema_resource_reference_read is not None:
            _schema.id = cls._schema_resource_reference_read.id
            return

        cls._schema_resource_reference_read = _schema_resource_reference_read = AAZObjectType()

        resource_reference_read = _schema_resource_reference_read
        resource_reference_read.id = AAZStrType()

        _schema.id = cls._schema_resource_reference_read.id

    _schema_secret_read = None

    @classmethod
    def _build_schema_secret_read(cls, _schema):
        if cls._schema_secret_read is not None:
            _schema.id = cls._schema_secret_read.id
            _schema.name = cls._schema_secret_read.name
            _schema.properties = cls._schema_secret_read.properties
            _schema.system_data = cls._schema_secret_read.system_data
            _schema.type = cls._schema_secret_read.type
            return

        cls._schema_secret_read = _schema_secret_read = AAZObjectType()

        secret_read = _schema_secret_read
        secret_read.id = AAZStrType(
            flags={"read_only": True},
        )
        secret_read.name = AAZStrType(
            flags={"read_only": True},
        )
        secret_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        secret_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        secret_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_secret_read.properties
        properties.deployment_status = AAZStrType(
            serialized_name="deploymentStatus",
            flags={"read_only": True},
        )
        properties.parameters = AAZObjectType()
        properties.profile_name = AAZStrType(
            serialized_name="profileName",
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )

        parameters = _schema_secret_read.properties.parameters
        parameters.type = AAZStrType(
            flags={"required": True},
        )

        disc_azure_first_party_managed_certificate = _schema_secret_read.properties.parameters.discriminate_by("type", "AzureFirstPartyManagedCertificate")
        disc_azure_first_party_managed_certificate.certificate_authority = AAZStrType(
            serialized_name="certificateAuthority",
            flags={"read_only": True},
        )
        disc_azure_first_party_managed_certificate.expiration_date = AAZStrType(
            serialized_name="expirationDate",
            flags={"read_only": True},
        )
        disc_azure_first_party_managed_certificate.secret_source = AAZObjectType(
            serialized_name="secretSource",
            flags={"read_only": True},
        )
        cls._build_schema_resource_reference_read(disc_azure_first_party_managed_certificate.secret_source)
        disc_azure_first_party_managed_certificate.subject = AAZStrType(
            flags={"read_only": True},
        )
        disc_azure_first_party_managed_certificate.subject_alternative_names = AAZListType(
            serialized_name="subjectAlternativeNames",
        )
        disc_azure_first_party_managed_certificate.thumbprint = AAZStrType(
            flags={"read_only": True},
        )

        subject_alternative_names = _schema_secret_read.properties.parameters.discriminate_by("type", "AzureFirstPartyManagedCertificate").subject_alternative_names
        subject_alternative_names.Element = AAZStrType(
            flags={"read_only": True},
        )

        disc_customer_certificate = _schema_secret_read.properties.parameters.discriminate_by("type", "CustomerCertificate")
        disc_customer_certificate.certificate_authority = AAZStrType(
            serialized_name="certificateAuthority",
            flags={"read_only": True},
        )
        disc_customer_certificate.expiration_date = AAZStrType(
            serialized_name="expirationDate",
            flags={"read_only": True},
        )
        disc_customer_certificate.secret_source = AAZObjectType(
            serialized_name="secretSource",
            flags={"required": True},
        )
        cls._build_schema_resource_reference_read(disc_customer_certificate.secret_source)
        disc_customer_certificate.secret_version = AAZStrType(
            serialized_name="secretVersion",
        )
        disc_customer_certificate.subject = AAZStrType(
            flags={"read_only": True},
        )
        disc_customer_certificate.subject_alternative_names = AAZListType(
            serialized_name="subjectAlternativeNames",
        )
        disc_customer_certificate.thumbprint = AAZStrType(
            flags={"read_only": True},
        )
        disc_customer_certificate.use_latest_version = AAZBoolType(
            serialized_name="useLatestVersion",
        )

        subject_alternative_names = _schema_secret_read.properties.parameters.discriminate_by("type", "CustomerCertificate").subject_alternative_names
        subject_alternative_names.Element = AAZStrType(
            flags={"read_only": True},
        )

        disc_managed_certificate = _schema_secret_read.properties.parameters.discriminate_by("type", "ManagedCertificate")
        disc_managed_certificate.expiration_date = AAZStrType(
            serialized_name="expirationDate",
            flags={"read_only": True},
        )
        disc_managed_certificate.subject = AAZStrType(
            flags={"read_only": True},
        )

        disc_url_signing_key = _schema_secret_read.properties.parameters.discriminate_by("type", "UrlSigningKey")
        disc_url_signing_key.key_id = AAZStrType(
            serialized_name="keyId",
            flags={"required": True},
        )
        disc_url_signing_key.secret_source = AAZObjectType(
            serialized_name="secretSource",
            flags={"required": True},
        )
        cls._build_schema_resource_reference_read(disc_url_signing_key.secret_source)
        disc_url_signing_key.secret_version = AAZStrType(
            serialized_name="secretVersion",
            flags={"required": True},
        )

        system_data = _schema_secret_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        _schema.id = cls._schema_secret_read.id
        _schema.name = cls._schema_secret_read.name
        _schema.properties = cls._schema_secret_read.properties
        _schema.system_data = cls._schema_secret_read.system_data
        _schema.type = cls._schema_secret_read.type


__all__ = ["Create"]
