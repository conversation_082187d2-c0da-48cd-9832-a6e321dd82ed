# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "afd rule create",
)
class Create(AAZCommand):
    """Create a new delivery rule within the specified rule set.

    :example: Create a rule to append a response header for requests from Thailand.
        az afd rule create -g group --rule-set-name ruleset1 --profile-name profile --order 2 --match-variable RemoteAddress --operator G<PERSON><PERSON>atch --match-values TH --rule-name disablecaching --action-name ModifyResponseHeader --header-action Append --header-name X-CDN --header-value AFDX

    :example: Create a rule for http to https redirect
        az afd rule create -g group --rule-set-name ruleset1 --profile-name profile --order 1 --rule-name "redirect" --match-variable RequestScheme --operator Equal --match-values HTTP --action-name "UrlRedirect" --redirect-protocol Https --redirect-type Moved
    """

    _aaz_info = {
        "version": "2025-06-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}/rulesets/{}/rules/{}", "2025-06-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="Name of the Azure Front Door Standard or Azure Front Door Premium profile which is unique within the resource group.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9]+(-*[a-zA-Z0-9])*$",
                max_length=260,
                min_length=1,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.rule_name = AAZStrArg(
            options=["-n", "--name", "--rule-name"],
            help="Name of the delivery rule which is unique within the endpoint.",
            required=True,
        )
        _args_schema.rule_set_name = AAZStrArg(
            options=["--rule-set-name"],
            help="Name of the rule set under the profile.",
            required=True,
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.actions = AAZListArg(
            options=["--actions"],
            arg_group="Properties",
            help="A list of actions that are executed when all the conditions of a rule are satisfied.",
        )
        _args_schema.conditions = AAZListArg(
            options=["--conditions"],
            arg_group="Properties",
            help="A list of conditions that must be matched for the actions to be executed",
        )
        _args_schema.match_processing_behavior = AAZStrArg(
            options=["--match-processing-behavior"],
            arg_group="Properties",
            help="If this rule is a match should the rules engine continue running the remaining rules or stop. If not present, defaults to Continue.",
            default="Continue",
            enum={"Continue": "Continue", "Stop": "Stop"},
        )
        _args_schema.order = AAZIntArg(
            options=["--order"],
            arg_group="Properties",
            help="The order in which the rules are applied for the endpoint. Possible values {0,1,2,3,Ã¢â‚¬Â¦Ã¢â‚¬Â¦Ã¢â‚¬Â¦}. A rule with a lesser order will be applied before a rule with a greater order. Rule with order 0 is a special rule. It does not require any condition and actions listed in it will always be applied.",
        )

        actions = cls._args_schema.actions
        actions.Element = AAZObjectArg()

        _element = cls._args_schema.actions.Element
        _element.cache_expiration = AAZObjectArg(
            options=["cache-expiration"],
        )
        _element.cache_key_query_string = AAZObjectArg(
            options=["cache-key-query-string"],
        )
        _element.modify_request_header = AAZObjectArg(
            options=["modify-request-header"],
        )
        _element.modify_response_header = AAZObjectArg(
            options=["modify-response-header"],
        )
        _element.origin_group_override = AAZObjectArg(
            options=["origin-group-override"],
        )
        _element.route_configuration_override = AAZObjectArg(
            options=["route-configuration-override"],
        )
        _element.url_redirect = AAZObjectArg(
            options=["url-redirect"],
        )
        _element.url_rewrite = AAZObjectArg(
            options=["url-rewrite"],
        )
        _element.url_signing = AAZObjectArg(
            options=["url-signing"],
        )

        cache_expiration = cls._args_schema.actions.Element.cache_expiration
        cache_expiration.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.actions.Element.cache_expiration.parameters
        parameters.cache_behavior = AAZStrArg(
            options=["cache-behavior"],
            help="Caching behavior for the requests",
            required=True,
            enum={"BypassCache": "BypassCache", "Override": "Override", "SetIfMissing": "SetIfMissing"},
        )
        parameters.cache_duration = AAZStrArg(
            options=["cache-duration"],
            help="The duration for which the content needs to be cached. Allowed format is [d.]hh:mm:ss",
            nullable=True,
        )
        parameters.cache_type = AAZStrArg(
            options=["cache-type"],
            help="The level at which the content needs to be cached.",
            required=True,
            enum={"All": "All"},
        )

        cache_key_query_string = cls._args_schema.actions.Element.cache_key_query_string
        cache_key_query_string.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.actions.Element.cache_key_query_string.parameters
        parameters.query_parameters = AAZStrArg(
            options=["query-parameters"],
            help="query parameters to include or exclude (comma separated).",
            nullable=True,
        )
        parameters.query_string_behavior = AAZStrArg(
            options=["query-string-behavior"],
            help="Caching behavior for the requests",
            required=True,
            enum={"Exclude": "Exclude", "ExcludeAll": "ExcludeAll", "Include": "Include", "IncludeAll": "IncludeAll"},
        )

        modify_request_header = cls._args_schema.actions.Element.modify_request_header
        modify_request_header.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )
        cls._build_args_header_action_parameters_create(modify_request_header.parameters)

        modify_response_header = cls._args_schema.actions.Element.modify_response_header
        modify_response_header.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )
        cls._build_args_header_action_parameters_create(modify_response_header.parameters)

        origin_group_override = cls._args_schema.actions.Element.origin_group_override
        origin_group_override.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.actions.Element.origin_group_override.parameters
        parameters.origin_group = AAZObjectArg(
            options=["origin-group"],
            help="defines the OriginGroup that would override the DefaultOriginGroup.",
            required=True,
        )

        origin_group = cls._args_schema.actions.Element.origin_group_override.parameters.origin_group
        origin_group.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        route_configuration_override = cls._args_schema.actions.Element.route_configuration_override
        route_configuration_override.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.actions.Element.route_configuration_override.parameters
        parameters.cache_configuration = AAZObjectArg(
            options=["cache-configuration"],
            help="The caching configuration associated with this rule. To disable caching, do not provide a cacheConfiguration object.",
        )
        parameters.origin_group_override = AAZObjectArg(
            options=["origin-group-override"],
            help="A reference to the origin group override configuration. Leave empty to use the default origin group on route.",
        )

        cache_configuration = cls._args_schema.actions.Element.route_configuration_override.parameters.cache_configuration
        cache_configuration.cache_behavior = AAZStrArg(
            options=["cache-behavior"],
            help="Caching behavior for the requests",
            enum={"HonorOrigin": "HonorOrigin", "OverrideAlways": "OverrideAlways", "OverrideIfOriginMissing": "OverrideIfOriginMissing"},
        )
        cache_configuration.cache_duration = AAZStrArg(
            options=["cache-duration"],
            help="The duration for which the content needs to be cached. Allowed format is [d.]hh:mm:ss",
        )
        cache_configuration.is_compression_enabled = AAZStrArg(
            options=["is-compression-enabled"],
            help="Indicates whether content compression is enabled. If compression is enabled, content will be served as compressed if user requests for a compressed version. Content won't be compressed on AzureFrontDoor when requested content is smaller than 1 byte or larger than 1 MB.",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        cache_configuration.query_parameters = AAZStrArg(
            options=["query-parameters"],
            help="query parameters to include or exclude (comma separated).",
        )
        cache_configuration.query_string_caching_behavior = AAZStrArg(
            options=["query-string-caching-behavior"],
            help="Defines how Frontdoor caches requests that include query strings. You can ignore any query strings when caching, ignore specific query strings, cache every request with a unique URL, or cache specific query strings.",
            enum={"IgnoreQueryString": "IgnoreQueryString", "IgnoreSpecifiedQueryStrings": "IgnoreSpecifiedQueryStrings", "IncludeSpecifiedQueryStrings": "IncludeSpecifiedQueryStrings", "UseQueryString": "UseQueryString"},
        )

        origin_group_override = cls._args_schema.actions.Element.route_configuration_override.parameters.origin_group_override
        origin_group_override.forwarding_protocol = AAZStrArg(
            options=["forwarding-protocol"],
            help="Protocol this rule will use when forwarding traffic to backends.",
            enum={"HttpOnly": "HttpOnly", "HttpsOnly": "HttpsOnly", "MatchRequest": "MatchRequest"},
        )
        origin_group_override.origin_group = AAZObjectArg(
            options=["origin-group"],
            help="defines the OriginGroup that would override the DefaultOriginGroup on route.",
        )

        origin_group = cls._args_schema.actions.Element.route_configuration_override.parameters.origin_group_override.origin_group
        origin_group.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        url_redirect = cls._args_schema.actions.Element.url_redirect
        url_redirect.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.actions.Element.url_redirect.parameters
        parameters.custom_fragment = AAZStrArg(
            options=["custom-fragment"],
            help="Fragment to add to the redirect URL. Fragment is the part of the URL that comes after #. Do not include the #.",
        )
        parameters.custom_hostname = AAZStrArg(
            options=["custom-hostname"],
            help="Host to redirect. Leave empty to use the incoming host as the destination host.",
        )
        parameters.custom_path = AAZStrArg(
            options=["custom-path"],
            help="The full path to redirect. Path cannot be empty and must start with /. Leave empty to use the incoming path as destination path.",
        )
        parameters.custom_querystring = AAZStrArg(
            options=["custom-querystring"],
            help="The set of query strings to be placed in the redirect URL. Setting this value would replace any existing query string; leave empty to preserve the incoming query string. Query string must be in <key>=<value> format. ? and & will be added automatically so do not include them.",
        )
        parameters.destination_protocol = AAZStrArg(
            options=["destination-protocol"],
            help="Protocol to use for the redirect. The default value is MatchRequest",
            enum={"Http": "Http", "Https": "Https", "MatchRequest": "MatchRequest"},
        )
        parameters.redirect_type = AAZStrArg(
            options=["redirect-type"],
            help="The redirect type the rule will use when redirecting traffic.",
            required=True,
            enum={"Found": "Found", "Moved": "Moved", "PermanentRedirect": "PermanentRedirect", "TemporaryRedirect": "TemporaryRedirect"},
        )

        url_rewrite = cls._args_schema.actions.Element.url_rewrite
        url_rewrite.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.actions.Element.url_rewrite.parameters
        parameters.destination = AAZStrArg(
            options=["destination"],
            help="Define the relative URL to which the above requests will be rewritten by.",
            required=True,
        )
        parameters.preserve_unmatched_path = AAZBoolArg(
            options=["preserve-unmatched-path"],
            help="Whether to preserve unmatched path. Default value is true.",
        )
        parameters.source_pattern = AAZStrArg(
            options=["source-pattern"],
            help="define a request URI pattern that identifies the type of requests that may be rewritten. If value is blank, all strings are matched.",
            required=True,
        )

        url_signing = cls._args_schema.actions.Element.url_signing
        url_signing.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.actions.Element.url_signing.parameters
        parameters.algorithm = AAZStrArg(
            options=["algorithm"],
            help="Algorithm to use for URL signing",
            enum={"SHA256": "SHA256"},
        )
        parameters.parameter_name_override = AAZListArg(
            options=["parameter-name-override"],
            help="Defines which query string parameters in the url to be considered for expires, key id etc. ",
        )

        parameter_name_override = cls._args_schema.actions.Element.url_signing.parameters.parameter_name_override
        parameter_name_override.Element = AAZObjectArg()

        _element = cls._args_schema.actions.Element.url_signing.parameters.parameter_name_override.Element
        _element.param_indicator = AAZStrArg(
            options=["param-indicator"],
            help="Indicates the purpose of the parameter",
            required=True,
            enum={"Expires": "Expires", "KeyId": "KeyId", "Signature": "Signature"},
        )
        _element.param_name = AAZStrArg(
            options=["param-name"],
            help="Parameter name",
            required=True,
        )

        conditions = cls._args_schema.conditions
        conditions.Element = AAZObjectArg()

        _element = cls._args_schema.conditions.Element
        _element.client_port = AAZObjectArg(
            options=["client-port"],
        )
        _element.cookies = AAZObjectArg(
            options=["cookies"],
        )
        _element.host_name = AAZObjectArg(
            options=["host-name"],
        )
        _element.http_version = AAZObjectArg(
            options=["http-version"],
        )
        _element.is_device = AAZObjectArg(
            options=["is-device"],
        )
        _element.post_args = AAZObjectArg(
            options=["post-args"],
        )
        _element.query_string = AAZObjectArg(
            options=["query-string"],
        )
        _element.remote_address = AAZObjectArg(
            options=["remote-address"],
        )
        _element.request_body = AAZObjectArg(
            options=["request-body"],
        )
        _element.request_header = AAZObjectArg(
            options=["request-header"],
        )
        _element.request_method = AAZObjectArg(
            options=["request-method"],
        )
        _element.request_scheme = AAZObjectArg(
            options=["request-scheme"],
        )
        _element.request_uri = AAZObjectArg(
            options=["request-uri"],
        )
        _element.server_port = AAZObjectArg(
            options=["server-port"],
        )
        _element.socket_addr = AAZObjectArg(
            options=["socket-addr"],
        )
        _element.ssl_protocol = AAZObjectArg(
            options=["ssl-protocol"],
        )
        _element.url_file_extension = AAZObjectArg(
            options=["url-file-extension"],
        )
        _element.url_file_name = AAZObjectArg(
            options=["url-file-name"],
        )
        _element.url_path = AAZObjectArg(
            options=["url-path"],
        )

        client_port = cls._args_schema.conditions.Element.client_port
        client_port.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.client_port.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.client_port.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.client_port.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        cookies = cls._args_schema.conditions.Element.cookies
        cookies.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.cookies.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.selector = AAZStrArg(
            options=["selector"],
            help="Name of Cookies to be matched",
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.cookies.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.cookies.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        host_name = cls._args_schema.conditions.Element.host_name
        host_name.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.host_name.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.host_name.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.host_name.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        http_version = cls._args_schema.conditions.Element.http_version
        http_version.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.http_version.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.http_version.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.http_version.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        is_device = cls._args_schema.conditions.Element.is_device
        is_device.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.is_device.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.is_device.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"Desktop": "Desktop", "Mobile": "Mobile"},
        )

        transforms = cls._args_schema.conditions.Element.is_device.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        post_args = cls._args_schema.conditions.Element.post_args
        post_args.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.post_args.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.selector = AAZStrArg(
            options=["selector"],
            help="Name of PostArg to be matched",
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.post_args.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.post_args.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        query_string = cls._args_schema.conditions.Element.query_string
        query_string.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.query_string.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.query_string.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.query_string.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        remote_address = cls._args_schema.conditions.Element.remote_address
        remote_address.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.remote_address.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="Match values to match against. The operator will apply to each value in here with OR semantics. If any of them match the variable with the given operator this match condition is considered a match.",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "GeoMatch": "GeoMatch", "IPMatch": "IPMatch"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.remote_address.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.remote_address.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_body = cls._args_schema.conditions.Element.request_body
        request_body.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.request_body.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.request_body.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.request_body.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_header = cls._args_schema.conditions.Element.request_header
        request_header.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.request_header.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.selector = AAZStrArg(
            options=["selector"],
            help="Name of Header to be matched",
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.request_header.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.request_header.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_method = cls._args_schema.conditions.Element.request_method
        request_method.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.request_method.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.request_method.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"DELETE": "DELETE", "GET": "GET", "HEAD": "HEAD", "OPTIONS": "OPTIONS", "POST": "POST", "PUT": "PUT", "TRACE": "TRACE"},
        )

        transforms = cls._args_schema.conditions.Element.request_method.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_scheme = cls._args_schema.conditions.Element.request_scheme
        request_scheme.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.request_scheme.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.request_scheme.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"HTTP": "HTTP", "HTTPS": "HTTPS"},
        )

        transforms = cls._args_schema.conditions.Element.request_scheme.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_uri = cls._args_schema.conditions.Element.request_uri
        request_uri.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.request_uri.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.request_uri.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.request_uri.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        server_port = cls._args_schema.conditions.Element.server_port
        server_port.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.server_port.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.server_port.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.server_port.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        socket_addr = cls._args_schema.conditions.Element.socket_addr
        socket_addr.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.socket_addr.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "IPMatch": "IPMatch"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.socket_addr.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.socket_addr.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        ssl_protocol = cls._args_schema.conditions.Element.ssl_protocol
        ssl_protocol.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.ssl_protocol.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.ssl_protocol.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"TLSv1": "TLSv1", "TLSv1.1": "TLSv1.1", "TLSv1.2": "TLSv1.2"},
        )

        transforms = cls._args_schema.conditions.Element.ssl_protocol.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        url_file_extension = cls._args_schema.conditions.Element.url_file_extension
        url_file_extension.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.url_file_extension.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.url_file_extension.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.url_file_extension.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        url_file_name = cls._args_schema.conditions.Element.url_file_name
        url_file_name.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.url_file_name.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.url_file_name.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.url_file_name.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        url_path = cls._args_schema.conditions.Element.url_path
        url_path.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.conditions.Element.url_path.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx", "Wildcard": "Wildcard"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.conditions.Element.url_path.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.conditions.Element.url_path.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )
        return cls._args_schema

    _args_header_action_parameters_create = None

    @classmethod
    def _build_args_header_action_parameters_create(cls, _schema):
        if cls._args_header_action_parameters_create is not None:
            _schema.header_action = cls._args_header_action_parameters_create.header_action
            _schema.header_name = cls._args_header_action_parameters_create.header_name
            _schema.value = cls._args_header_action_parameters_create.value
            return

        cls._args_header_action_parameters_create = AAZObjectArg()

        header_action_parameters_create = cls._args_header_action_parameters_create
        header_action_parameters_create.header_action = AAZStrArg(
            options=["header-action"],
            help="Action to perform",
            required=True,
            enum={"Append": "Append", "Delete": "Delete", "Overwrite": "Overwrite"},
        )
        header_action_parameters_create.header_name = AAZStrArg(
            options=["header-name"],
            help="Name of the header to modify",
            required=True,
        )
        header_action_parameters_create.value = AAZStrArg(
            options=["value"],
            help="Value for the specified action",
        )

        _schema.header_action = cls._args_header_action_parameters_create.header_action
        _schema.header_name = cls._args_header_action_parameters_create.header_name
        _schema.value = cls._args_header_action_parameters_create.value

    def _execute_operations(self):
        self.pre_operations()
        yield self.RulesCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class RulesCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}/ruleSets/{ruleSetName}/rules/{ruleName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "ruleName", self.ctx.args.rule_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "ruleSetName", self.ctx.args.rule_set_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-06-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("actions", AAZListType, ".actions", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("conditions", AAZListType, ".conditions")
                properties.set_prop("matchProcessingBehavior", AAZStrType, ".match_processing_behavior")
                properties.set_prop("order", AAZIntType, ".order", typ_kwargs={"flags": {"required": True}})

            actions = _builder.get(".properties.actions")
            if actions is not None:
                actions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.actions[]")
            if _elements is not None:
                _elements.set_const("name", "CacheExpiration", AAZStrType, ".cache_expiration", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "CacheKeyQueryString", AAZStrType, ".cache_key_query_string", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "ModifyRequestHeader", AAZStrType, ".modify_request_header", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "ModifyResponseHeader", AAZStrType, ".modify_response_header", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "OriginGroupOverride", AAZStrType, ".origin_group_override", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RouteConfigurationOverride", AAZStrType, ".route_configuration_override", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlRedirect", AAZStrType, ".url_redirect", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlRewrite", AAZStrType, ".url_rewrite", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlSigning", AAZStrType, ".url_signing", typ_kwargs={"flags": {"required": True}})
                _elements.discriminate_by("name", "CacheExpiration")
                _elements.discriminate_by("name", "CacheKeyQueryString")
                _elements.discriminate_by("name", "ModifyRequestHeader")
                _elements.discriminate_by("name", "ModifyResponseHeader")
                _elements.discriminate_by("name", "OriginGroupOverride")
                _elements.discriminate_by("name", "RouteConfigurationOverride")
                _elements.discriminate_by("name", "UrlRedirect")
                _elements.discriminate_by("name", "UrlRewrite")
                _elements.discriminate_by("name", "UrlSigning")

            disc_cache_expiration = _builder.get(".properties.actions[]{name:CacheExpiration}")
            if disc_cache_expiration is not None:
                disc_cache_expiration.set_prop("parameters", AAZObjectType, ".cache_expiration.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.actions[]{name:CacheExpiration}.parameters")
            if parameters is not None:
                parameters.set_prop("cacheBehavior", AAZStrType, ".cache_behavior", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("cacheDuration", AAZStrType, ".cache_duration", typ_kwargs={"nullable": True})
                parameters.set_prop("cacheType", AAZStrType, ".cache_type", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleCacheExpirationActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_cache_key_query_string = _builder.get(".properties.actions[]{name:CacheKeyQueryString}")
            if disc_cache_key_query_string is not None:
                disc_cache_key_query_string.set_prop("parameters", AAZObjectType, ".cache_key_query_string.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.actions[]{name:CacheKeyQueryString}.parameters")
            if parameters is not None:
                parameters.set_prop("queryParameters", AAZStrType, ".query_parameters", typ_kwargs={"nullable": True})
                parameters.set_prop("queryStringBehavior", AAZStrType, ".query_string_behavior", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleCacheKeyQueryStringBehaviorActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_modify_request_header = _builder.get(".properties.actions[]{name:ModifyRequestHeader}")
            if disc_modify_request_header is not None:
                _CreateHelper._build_schema_header_action_parameters_create(disc_modify_request_header.set_prop("parameters", AAZObjectType, ".modify_request_header.parameters", typ_kwargs={"flags": {"required": True}}))

            disc_modify_response_header = _builder.get(".properties.actions[]{name:ModifyResponseHeader}")
            if disc_modify_response_header is not None:
                _CreateHelper._build_schema_header_action_parameters_create(disc_modify_response_header.set_prop("parameters", AAZObjectType, ".modify_response_header.parameters", typ_kwargs={"flags": {"required": True}}))

            disc_origin_group_override = _builder.get(".properties.actions[]{name:OriginGroupOverride}")
            if disc_origin_group_override is not None:
                disc_origin_group_override.set_prop("parameters", AAZObjectType, ".origin_group_override.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.actions[]{name:OriginGroupOverride}.parameters")
            if parameters is not None:
                parameters.set_prop("originGroup", AAZObjectType, ".origin_group", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleOriginGroupOverrideActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            origin_group = _builder.get(".properties.actions[]{name:OriginGroupOverride}.parameters.originGroup")
            if origin_group is not None:
                origin_group.set_prop("id", AAZStrType, ".id")

            disc_route_configuration_override = _builder.get(".properties.actions[]{name:RouteConfigurationOverride}")
            if disc_route_configuration_override is not None:
                disc_route_configuration_override.set_prop("parameters", AAZObjectType, ".route_configuration_override.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.actions[]{name:RouteConfigurationOverride}.parameters")
            if parameters is not None:
                parameters.set_prop("cacheConfiguration", AAZObjectType, ".cache_configuration")
                parameters.set_prop("originGroupOverride", AAZObjectType, ".origin_group_override")
                parameters.set_const("typeName", "DeliveryRuleRouteConfigurationOverrideActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            cache_configuration = _builder.get(".properties.actions[]{name:RouteConfigurationOverride}.parameters.cacheConfiguration")
            if cache_configuration is not None:
                cache_configuration.set_prop("cacheBehavior", AAZStrType, ".cache_behavior")
                cache_configuration.set_prop("cacheDuration", AAZStrType, ".cache_duration")
                cache_configuration.set_prop("isCompressionEnabled", AAZStrType, ".is_compression_enabled")
                cache_configuration.set_prop("queryParameters", AAZStrType, ".query_parameters")
                cache_configuration.set_prop("queryStringCachingBehavior", AAZStrType, ".query_string_caching_behavior")

            origin_group_override = _builder.get(".properties.actions[]{name:RouteConfigurationOverride}.parameters.originGroupOverride")
            if origin_group_override is not None:
                origin_group_override.set_prop("forwardingProtocol", AAZStrType, ".forwarding_protocol")
                origin_group_override.set_prop("originGroup", AAZObjectType, ".origin_group")

            origin_group = _builder.get(".properties.actions[]{name:RouteConfigurationOverride}.parameters.originGroupOverride.originGroup")
            if origin_group is not None:
                origin_group.set_prop("id", AAZStrType, ".id")

            disc_url_redirect = _builder.get(".properties.actions[]{name:UrlRedirect}")
            if disc_url_redirect is not None:
                disc_url_redirect.set_prop("parameters", AAZObjectType, ".url_redirect.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.actions[]{name:UrlRedirect}.parameters")
            if parameters is not None:
                parameters.set_prop("customFragment", AAZStrType, ".custom_fragment")
                parameters.set_prop("customHostname", AAZStrType, ".custom_hostname")
                parameters.set_prop("customPath", AAZStrType, ".custom_path")
                parameters.set_prop("customQueryString", AAZStrType, ".custom_querystring")
                parameters.set_prop("destinationProtocol", AAZStrType, ".destination_protocol")
                parameters.set_prop("redirectType", AAZStrType, ".redirect_type", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleUrlRedirectActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_url_rewrite = _builder.get(".properties.actions[]{name:UrlRewrite}")
            if disc_url_rewrite is not None:
                disc_url_rewrite.set_prop("parameters", AAZObjectType, ".url_rewrite.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.actions[]{name:UrlRewrite}.parameters")
            if parameters is not None:
                parameters.set_prop("destination", AAZStrType, ".destination", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("preserveUnmatchedPath", AAZBoolType, ".preserve_unmatched_path")
                parameters.set_prop("sourcePattern", AAZStrType, ".source_pattern", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleUrlRewriteActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_url_signing = _builder.get(".properties.actions[]{name:UrlSigning}")
            if disc_url_signing is not None:
                disc_url_signing.set_prop("parameters", AAZObjectType, ".url_signing.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.actions[]{name:UrlSigning}.parameters")
            if parameters is not None:
                parameters.set_prop("algorithm", AAZStrType, ".algorithm")
                parameters.set_prop("parameterNameOverride", AAZListType, ".parameter_name_override")
                parameters.set_const("typeName", "DeliveryRuleUrlSigningActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            parameter_name_override = _builder.get(".properties.actions[]{name:UrlSigning}.parameters.parameterNameOverride")
            if parameter_name_override is not None:
                parameter_name_override.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.actions[]{name:UrlSigning}.parameters.parameterNameOverride[]")
            if _elements is not None:
                _elements.set_prop("paramIndicator", AAZStrType, ".param_indicator", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("paramName", AAZStrType, ".param_name", typ_kwargs={"flags": {"required": True}})

            conditions = _builder.get(".properties.conditions")
            if conditions is not None:
                conditions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.conditions[]")
            if _elements is not None:
                _elements.set_const("name", "ClientPort", AAZStrType, ".client_port", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "Cookies", AAZStrType, ".cookies", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "HostName", AAZStrType, ".host_name", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "HttpVersion", AAZStrType, ".http_version", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "IsDevice", AAZStrType, ".is_device", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "PostArgs", AAZStrType, ".post_args", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "QueryString", AAZStrType, ".query_string", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RemoteAddress", AAZStrType, ".remote_address", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestBody", AAZStrType, ".request_body", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestHeader", AAZStrType, ".request_header", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestMethod", AAZStrType, ".request_method", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestScheme", AAZStrType, ".request_scheme", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestUri", AAZStrType, ".request_uri", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "ServerPort", AAZStrType, ".server_port", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "SocketAddr", AAZStrType, ".socket_addr", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "SslProtocol", AAZStrType, ".ssl_protocol", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlFileExtension", AAZStrType, ".url_file_extension", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlFileName", AAZStrType, ".url_file_name", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlPath", AAZStrType, ".url_path", typ_kwargs={"flags": {"required": True}})
                _elements.discriminate_by("name", "ClientPort")
                _elements.discriminate_by("name", "Cookies")
                _elements.discriminate_by("name", "HostName")
                _elements.discriminate_by("name", "HttpVersion")
                _elements.discriminate_by("name", "IsDevice")
                _elements.discriminate_by("name", "PostArgs")
                _elements.discriminate_by("name", "QueryString")
                _elements.discriminate_by("name", "RemoteAddress")
                _elements.discriminate_by("name", "RequestBody")
                _elements.discriminate_by("name", "RequestHeader")
                _elements.discriminate_by("name", "RequestMethod")
                _elements.discriminate_by("name", "RequestScheme")
                _elements.discriminate_by("name", "RequestUri")
                _elements.discriminate_by("name", "ServerPort")
                _elements.discriminate_by("name", "SocketAddr")
                _elements.discriminate_by("name", "SslProtocol")
                _elements.discriminate_by("name", "UrlFileExtension")
                _elements.discriminate_by("name", "UrlFileName")
                _elements.discriminate_by("name", "UrlPath")

            disc_client_port = _builder.get(".properties.conditions[]{name:ClientPort}")
            if disc_client_port is not None:
                disc_client_port.set_prop("parameters", AAZObjectType, ".client_port.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:ClientPort}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleClientPortConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:ClientPort}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:ClientPort}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_cookies = _builder.get(".properties.conditions[]{name:Cookies}")
            if disc_cookies is not None:
                disc_cookies.set_prop("parameters", AAZObjectType, ".cookies.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:Cookies}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("selector", AAZStrType, ".selector")
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleCookiesConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:Cookies}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:Cookies}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_host_name = _builder.get(".properties.conditions[]{name:HostName}")
            if disc_host_name is not None:
                disc_host_name.set_prop("parameters", AAZObjectType, ".host_name.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:HostName}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleHostNameConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:HostName}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:HostName}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_http_version = _builder.get(".properties.conditions[]{name:HttpVersion}")
            if disc_http_version is not None:
                disc_http_version.set_prop("parameters", AAZObjectType, ".http_version.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:HttpVersion}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleHttpVersionConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:HttpVersion}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:HttpVersion}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_is_device = _builder.get(".properties.conditions[]{name:IsDevice}")
            if disc_is_device is not None:
                disc_is_device.set_prop("parameters", AAZObjectType, ".is_device.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:IsDevice}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleIsDeviceConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:IsDevice}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:IsDevice}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_post_args = _builder.get(".properties.conditions[]{name:PostArgs}")
            if disc_post_args is not None:
                disc_post_args.set_prop("parameters", AAZObjectType, ".post_args.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:PostArgs}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("selector", AAZStrType, ".selector")
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRulePostArgsConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:PostArgs}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:PostArgs}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_query_string = _builder.get(".properties.conditions[]{name:QueryString}")
            if disc_query_string is not None:
                disc_query_string.set_prop("parameters", AAZObjectType, ".query_string.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:QueryString}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleQueryStringConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:QueryString}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:QueryString}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_remote_address = _builder.get(".properties.conditions[]{name:RemoteAddress}")
            if disc_remote_address is not None:
                disc_remote_address.set_prop("parameters", AAZObjectType, ".remote_address.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:RemoteAddress}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRemoteAddressConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:RemoteAddress}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:RemoteAddress}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_body = _builder.get(".properties.conditions[]{name:RequestBody}")
            if disc_request_body is not None:
                disc_request_body.set_prop("parameters", AAZObjectType, ".request_body.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:RequestBody}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestBodyConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:RequestBody}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:RequestBody}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_header = _builder.get(".properties.conditions[]{name:RequestHeader}")
            if disc_request_header is not None:
                disc_request_header.set_prop("parameters", AAZObjectType, ".request_header.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:RequestHeader}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("selector", AAZStrType, ".selector")
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestHeaderConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:RequestHeader}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:RequestHeader}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_method = _builder.get(".properties.conditions[]{name:RequestMethod}")
            if disc_request_method is not None:
                disc_request_method.set_prop("parameters", AAZObjectType, ".request_method.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:RequestMethod}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestMethodConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:RequestMethod}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:RequestMethod}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_scheme = _builder.get(".properties.conditions[]{name:RequestScheme}")
            if disc_request_scheme is not None:
                disc_request_scheme.set_prop("parameters", AAZObjectType, ".request_scheme.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:RequestScheme}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestSchemeConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:RequestScheme}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:RequestScheme}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_uri = _builder.get(".properties.conditions[]{name:RequestUri}")
            if disc_request_uri is not None:
                disc_request_uri.set_prop("parameters", AAZObjectType, ".request_uri.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:RequestUri}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestUriConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:RequestUri}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:RequestUri}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_server_port = _builder.get(".properties.conditions[]{name:ServerPort}")
            if disc_server_port is not None:
                disc_server_port.set_prop("parameters", AAZObjectType, ".server_port.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:ServerPort}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleServerPortConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:ServerPort}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:ServerPort}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_socket_addr = _builder.get(".properties.conditions[]{name:SocketAddr}")
            if disc_socket_addr is not None:
                disc_socket_addr.set_prop("parameters", AAZObjectType, ".socket_addr.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:SocketAddr}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleSocketAddrConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:SocketAddr}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:SocketAddr}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_ssl_protocol = _builder.get(".properties.conditions[]{name:SslProtocol}")
            if disc_ssl_protocol is not None:
                disc_ssl_protocol.set_prop("parameters", AAZObjectType, ".ssl_protocol.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:SslProtocol}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleSslProtocolConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:SslProtocol}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:SslProtocol}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_url_file_extension = _builder.get(".properties.conditions[]{name:UrlFileExtension}")
            if disc_url_file_extension is not None:
                disc_url_file_extension.set_prop("parameters", AAZObjectType, ".url_file_extension.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:UrlFileExtension}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleUrlFileExtensionMatchConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:UrlFileExtension}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:UrlFileExtension}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_url_file_name = _builder.get(".properties.conditions[]{name:UrlFileName}")
            if disc_url_file_name is not None:
                disc_url_file_name.set_prop("parameters", AAZObjectType, ".url_file_name.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:UrlFileName}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleUrlFilenameConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:UrlFileName}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:UrlFileName}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_url_path = _builder.get(".properties.conditions[]{name:UrlPath}")
            if disc_url_path is not None:
                disc_url_path.set_prop("parameters", AAZObjectType, ".url_path.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.conditions[]{name:UrlPath}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleUrlPathMatchConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.conditions[]{name:UrlPath}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.conditions[]{name:UrlPath}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_rule_read(cls._schema_on_200_201)

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_header_action_parameters_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("headerAction", AAZStrType, ".header_action", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("headerName", AAZStrType, ".header_name", typ_kwargs={"flags": {"required": True}})
        _builder.set_const("typeName", "DeliveryRuleHeaderActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("value", AAZStrType, ".value")

    _schema_header_action_parameters_read = None

    @classmethod
    def _build_schema_header_action_parameters_read(cls, _schema):
        if cls._schema_header_action_parameters_read is not None:
            _schema.header_action = cls._schema_header_action_parameters_read.header_action
            _schema.header_name = cls._schema_header_action_parameters_read.header_name
            _schema.type_name = cls._schema_header_action_parameters_read.type_name
            _schema.value = cls._schema_header_action_parameters_read.value
            return

        cls._schema_header_action_parameters_read = _schema_header_action_parameters_read = AAZObjectType()

        header_action_parameters_read = _schema_header_action_parameters_read
        header_action_parameters_read.header_action = AAZStrType(
            serialized_name="headerAction",
            flags={"required": True},
        )
        header_action_parameters_read.header_name = AAZStrType(
            serialized_name="headerName",
            flags={"required": True},
        )
        header_action_parameters_read.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )
        header_action_parameters_read.value = AAZStrType()

        _schema.header_action = cls._schema_header_action_parameters_read.header_action
        _schema.header_name = cls._schema_header_action_parameters_read.header_name
        _schema.type_name = cls._schema_header_action_parameters_read.type_name
        _schema.value = cls._schema_header_action_parameters_read.value

    _schema_rule_read = None

    @classmethod
    def _build_schema_rule_read(cls, _schema):
        if cls._schema_rule_read is not None:
            _schema.id = cls._schema_rule_read.id
            _schema.name = cls._schema_rule_read.name
            _schema.properties = cls._schema_rule_read.properties
            _schema.system_data = cls._schema_rule_read.system_data
            _schema.type = cls._schema_rule_read.type
            return

        cls._schema_rule_read = _schema_rule_read = AAZObjectType()

        rule_read = _schema_rule_read
        rule_read.id = AAZStrType(
            flags={"read_only": True},
        )
        rule_read.name = AAZStrType(
            flags={"read_only": True},
        )
        rule_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        rule_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        rule_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_rule_read.properties
        properties.actions = AAZListType(
            flags={"required": True},
        )
        properties.conditions = AAZListType()
        properties.deployment_status = AAZStrType(
            serialized_name="deploymentStatus",
            flags={"read_only": True},
        )
        properties.match_processing_behavior = AAZStrType(
            serialized_name="matchProcessingBehavior",
        )
        properties.order = AAZIntType(
            flags={"required": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.rule_set_name = AAZStrType(
            serialized_name="ruleSetName",
            flags={"read_only": True},
        )

        actions = _schema_rule_read.properties.actions
        actions.Element = AAZObjectType()

        _element = _schema_rule_read.properties.actions.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )

        disc_cache_expiration = _schema_rule_read.properties.actions.Element.discriminate_by("name", "CacheExpiration")
        disc_cache_expiration.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.actions.Element.discriminate_by("name", "CacheExpiration").parameters
        parameters.cache_behavior = AAZStrType(
            serialized_name="cacheBehavior",
            flags={"required": True},
        )
        parameters.cache_duration = AAZStrType(
            serialized_name="cacheDuration",
            nullable=True,
        )
        parameters.cache_type = AAZStrType(
            serialized_name="cacheType",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_cache_key_query_string = _schema_rule_read.properties.actions.Element.discriminate_by("name", "CacheKeyQueryString")
        disc_cache_key_query_string.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.actions.Element.discriminate_by("name", "CacheKeyQueryString").parameters
        parameters.query_parameters = AAZStrType(
            serialized_name="queryParameters",
            nullable=True,
        )
        parameters.query_string_behavior = AAZStrType(
            serialized_name="queryStringBehavior",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_modify_request_header = _schema_rule_read.properties.actions.Element.discriminate_by("name", "ModifyRequestHeader")
        disc_modify_request_header.parameters = AAZObjectType(
            flags={"required": True},
        )
        cls._build_schema_header_action_parameters_read(disc_modify_request_header.parameters)

        disc_modify_response_header = _schema_rule_read.properties.actions.Element.discriminate_by("name", "ModifyResponseHeader")
        disc_modify_response_header.parameters = AAZObjectType(
            flags={"required": True},
        )
        cls._build_schema_header_action_parameters_read(disc_modify_response_header.parameters)

        disc_origin_group_override = _schema_rule_read.properties.actions.Element.discriminate_by("name", "OriginGroupOverride")
        disc_origin_group_override.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.actions.Element.discriminate_by("name", "OriginGroupOverride").parameters
        parameters.origin_group = AAZObjectType(
            serialized_name="originGroup",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        origin_group = _schema_rule_read.properties.actions.Element.discriminate_by("name", "OriginGroupOverride").parameters.origin_group
        origin_group.id = AAZStrType()

        disc_route_configuration_override = _schema_rule_read.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride")
        disc_route_configuration_override.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters
        parameters.cache_configuration = AAZObjectType(
            serialized_name="cacheConfiguration",
        )
        parameters.origin_group_override = AAZObjectType(
            serialized_name="originGroupOverride",
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        cache_configuration = _schema_rule_read.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.cache_configuration
        cache_configuration.cache_behavior = AAZStrType(
            serialized_name="cacheBehavior",
        )
        cache_configuration.cache_duration = AAZStrType(
            serialized_name="cacheDuration",
        )
        cache_configuration.is_compression_enabled = AAZStrType(
            serialized_name="isCompressionEnabled",
        )
        cache_configuration.query_parameters = AAZStrType(
            serialized_name="queryParameters",
        )
        cache_configuration.query_string_caching_behavior = AAZStrType(
            serialized_name="queryStringCachingBehavior",
        )

        origin_group_override = _schema_rule_read.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.origin_group_override
        origin_group_override.forwarding_protocol = AAZStrType(
            serialized_name="forwardingProtocol",
        )
        origin_group_override.origin_group = AAZObjectType(
            serialized_name="originGroup",
        )

        origin_group = _schema_rule_read.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.origin_group_override.origin_group
        origin_group.id = AAZStrType()

        disc_url_redirect = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlRedirect")
        disc_url_redirect.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlRedirect").parameters
        parameters.custom_fragment = AAZStrType(
            serialized_name="customFragment",
        )
        parameters.custom_hostname = AAZStrType(
            serialized_name="customHostname",
        )
        parameters.custom_path = AAZStrType(
            serialized_name="customPath",
        )
        parameters.custom_query_string = AAZStrType(
            serialized_name="customQueryString",
        )
        parameters.destination_protocol = AAZStrType(
            serialized_name="destinationProtocol",
        )
        parameters.redirect_type = AAZStrType(
            serialized_name="redirectType",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_url_rewrite = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlRewrite")
        disc_url_rewrite.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlRewrite").parameters
        parameters.destination = AAZStrType(
            flags={"required": True},
        )
        parameters.preserve_unmatched_path = AAZBoolType(
            serialized_name="preserveUnmatchedPath",
        )
        parameters.source_pattern = AAZStrType(
            serialized_name="sourcePattern",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_url_signing = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlSigning")
        disc_url_signing.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlSigning").parameters
        parameters.algorithm = AAZStrType()
        parameters.parameter_name_override = AAZListType(
            serialized_name="parameterNameOverride",
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        parameter_name_override = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override
        parameter_name_override.Element = AAZObjectType()

        _element = _schema_rule_read.properties.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override.Element
        _element.param_indicator = AAZStrType(
            serialized_name="paramIndicator",
            flags={"required": True},
        )
        _element.param_name = AAZStrType(
            serialized_name="paramName",
            flags={"required": True},
        )

        conditions = _schema_rule_read.properties.conditions
        conditions.Element = AAZObjectType()

        _element = _schema_rule_read.properties.conditions.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )

        disc_client_port = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ClientPort")
        disc_client_port.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ClientPort").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ClientPort").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ClientPort").parameters.transforms
        transforms.Element = AAZStrType()

        disc_cookies = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "Cookies")
        disc_cookies.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "Cookies").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "Cookies").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "Cookies").parameters.transforms
        transforms.Element = AAZStrType()

        disc_host_name = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HostName")
        disc_host_name.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HostName").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HostName").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HostName").parameters.transforms
        transforms.Element = AAZStrType()

        disc_http_version = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HttpVersion")
        disc_http_version.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HttpVersion").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HttpVersion").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "HttpVersion").parameters.transforms
        transforms.Element = AAZStrType()

        disc_is_device = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "IsDevice")
        disc_is_device.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "IsDevice").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "IsDevice").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "IsDevice").parameters.transforms
        transforms.Element = AAZStrType()

        disc_post_args = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "PostArgs")
        disc_post_args.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "PostArgs").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "PostArgs").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "PostArgs").parameters.transforms
        transforms.Element = AAZStrType()

        disc_query_string = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "QueryString")
        disc_query_string.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "QueryString").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "QueryString").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "QueryString").parameters.transforms
        transforms.Element = AAZStrType()

        disc_remote_address = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RemoteAddress")
        disc_remote_address.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RemoteAddress").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_body = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestBody")
        disc_request_body.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestBody").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestBody").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestBody").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_header = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestHeader")
        disc_request_header.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestHeader").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestHeader").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestHeader").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_method = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestMethod")
        disc_request_method.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestMethod").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestMethod").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestMethod").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_scheme = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestScheme")
        disc_request_scheme.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestScheme").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestScheme").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestScheme").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_uri = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestUri")
        disc_request_uri.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestUri").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestUri").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "RequestUri").parameters.transforms
        transforms.Element = AAZStrType()

        disc_server_port = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ServerPort")
        disc_server_port.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ServerPort").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ServerPort").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "ServerPort").parameters.transforms
        transforms.Element = AAZStrType()

        disc_socket_addr = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SocketAddr")
        disc_socket_addr.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SocketAddr").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SocketAddr").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SocketAddr").parameters.transforms
        transforms.Element = AAZStrType()

        disc_ssl_protocol = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SslProtocol")
        disc_ssl_protocol.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SslProtocol").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SslProtocol").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "SslProtocol").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_file_extension = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileExtension")
        disc_url_file_extension.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_file_name = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileName")
        disc_url_file_name.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileName").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileName").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlFileName").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_path = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlPath")
        disc_url_path.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlPath").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlPath").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_rule_read.properties.conditions.Element.discriminate_by("name", "UrlPath").parameters.transforms
        transforms.Element = AAZStrType()

        system_data = _schema_rule_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        _schema.id = cls._schema_rule_read.id
        _schema.name = cls._schema_rule_read.name
        _schema.properties = cls._schema_rule_read.properties
        _schema.system_data = cls._schema_rule_read.system_data
        _schema.type = cls._schema_rule_read.type


__all__ = ["Create"]
