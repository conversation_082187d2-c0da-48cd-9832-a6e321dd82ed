# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "afd rule list",
)
class List(AAZCommand):
    """List all of the existing delivery rules within a rule set.

    :example: List all the routes within the specified endpoint.
        az afd route list -g group --profile-name profile --endpoint-name endpoint1
    """

    _aaz_info = {
        "version": "2025-06-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}/rulesets/{}/rules", "2025-06-01"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="Name of the Azure Front Door Standard or Azure Front Door Premium profile which is unique within the resource group.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9]+(-*[a-zA-Z0-9])*$",
                max_length=260,
                min_length=1,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.rule_set_name = AAZStrArg(
            options=["--rule-set-name"],
            help="Name of the rule set under the profile.",
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.RulesListByRuleSet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class RulesListByRuleSet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}/ruleSets/{ruleSetName}/rules",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "ruleSetName", self.ctx.args.rule_set_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-06-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
            )
            _schema_on_200.value = AAZListType(
                flags={"read_only": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.actions = AAZListType(
                flags={"required": True},
            )
            properties.conditions = AAZListType()
            properties.deployment_status = AAZStrType(
                serialized_name="deploymentStatus",
                flags={"read_only": True},
            )
            properties.match_processing_behavior = AAZStrType(
                serialized_name="matchProcessingBehavior",
            )
            properties.order = AAZIntType(
                flags={"required": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.rule_set_name = AAZStrType(
                serialized_name="ruleSetName",
                flags={"read_only": True},
            )

            actions = cls._schema_on_200.value.Element.properties.actions
            actions.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.actions.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )

            disc_cache_expiration = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "CacheExpiration")
            disc_cache_expiration.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "CacheExpiration").parameters
            parameters.cache_behavior = AAZStrType(
                serialized_name="cacheBehavior",
                flags={"required": True},
            )
            parameters.cache_duration = AAZStrType(
                serialized_name="cacheDuration",
                nullable=True,
            )
            parameters.cache_type = AAZStrType(
                serialized_name="cacheType",
                flags={"required": True},
            )
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            disc_cache_key_query_string = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "CacheKeyQueryString")
            disc_cache_key_query_string.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "CacheKeyQueryString").parameters
            parameters.query_parameters = AAZStrType(
                serialized_name="queryParameters",
                nullable=True,
            )
            parameters.query_string_behavior = AAZStrType(
                serialized_name="queryStringBehavior",
                flags={"required": True},
            )
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            disc_modify_request_header = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "ModifyRequestHeader")
            disc_modify_request_header.parameters = AAZObjectType(
                flags={"required": True},
            )
            _ListHelper._build_schema_header_action_parameters_read(disc_modify_request_header.parameters)

            disc_modify_response_header = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "ModifyResponseHeader")
            disc_modify_response_header.parameters = AAZObjectType(
                flags={"required": True},
            )
            _ListHelper._build_schema_header_action_parameters_read(disc_modify_response_header.parameters)

            disc_origin_group_override = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "OriginGroupOverride")
            disc_origin_group_override.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "OriginGroupOverride").parameters
            parameters.origin_group = AAZObjectType(
                serialized_name="originGroup",
                flags={"required": True},
            )
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            origin_group = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "OriginGroupOverride").parameters.origin_group
            origin_group.id = AAZStrType()

            disc_route_configuration_override = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride")
            disc_route_configuration_override.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters
            parameters.cache_configuration = AAZObjectType(
                serialized_name="cacheConfiguration",
            )
            parameters.origin_group_override = AAZObjectType(
                serialized_name="originGroupOverride",
            )
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            cache_configuration = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.cache_configuration
            cache_configuration.cache_behavior = AAZStrType(
                serialized_name="cacheBehavior",
            )
            cache_configuration.cache_duration = AAZStrType(
                serialized_name="cacheDuration",
            )
            cache_configuration.is_compression_enabled = AAZStrType(
                serialized_name="isCompressionEnabled",
            )
            cache_configuration.query_parameters = AAZStrType(
                serialized_name="queryParameters",
            )
            cache_configuration.query_string_caching_behavior = AAZStrType(
                serialized_name="queryStringCachingBehavior",
            )

            origin_group_override = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.origin_group_override
            origin_group_override.forwarding_protocol = AAZStrType(
                serialized_name="forwardingProtocol",
            )
            origin_group_override.origin_group = AAZObjectType(
                serialized_name="originGroup",
            )

            origin_group = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.origin_group_override.origin_group
            origin_group.id = AAZStrType()

            disc_url_redirect = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlRedirect")
            disc_url_redirect.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlRedirect").parameters
            parameters.custom_fragment = AAZStrType(
                serialized_name="customFragment",
            )
            parameters.custom_hostname = AAZStrType(
                serialized_name="customHostname",
            )
            parameters.custom_path = AAZStrType(
                serialized_name="customPath",
            )
            parameters.custom_query_string = AAZStrType(
                serialized_name="customQueryString",
            )
            parameters.destination_protocol = AAZStrType(
                serialized_name="destinationProtocol",
            )
            parameters.redirect_type = AAZStrType(
                serialized_name="redirectType",
                flags={"required": True},
            )
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            disc_url_rewrite = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlRewrite")
            disc_url_rewrite.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlRewrite").parameters
            parameters.destination = AAZStrType(
                flags={"required": True},
            )
            parameters.preserve_unmatched_path = AAZBoolType(
                serialized_name="preserveUnmatchedPath",
            )
            parameters.source_pattern = AAZStrType(
                serialized_name="sourcePattern",
                flags={"required": True},
            )
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            disc_url_signing = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlSigning")
            disc_url_signing.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlSigning").parameters
            parameters.algorithm = AAZStrType()
            parameters.parameter_name_override = AAZListType(
                serialized_name="parameterNameOverride",
            )
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            parameter_name_override = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override
            parameter_name_override.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override.Element
            _element.param_indicator = AAZStrType(
                serialized_name="paramIndicator",
                flags={"required": True},
            )
            _element.param_name = AAZStrType(
                serialized_name="paramName",
                flags={"required": True},
            )

            conditions = cls._schema_on_200.value.Element.properties.conditions
            conditions.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.conditions.Element
            _element.name = AAZStrType(
                flags={"required": True},
            )

            disc_client_port = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ClientPort")
            disc_client_port.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ClientPort").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ClientPort").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ClientPort").parameters.transforms
            transforms.Element = AAZStrType()

            disc_cookies = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "Cookies")
            disc_cookies.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "Cookies").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.selector = AAZStrType()
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "Cookies").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "Cookies").parameters.transforms
            transforms.Element = AAZStrType()

            disc_host_name = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HostName")
            disc_host_name.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HostName").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HostName").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HostName").parameters.transforms
            transforms.Element = AAZStrType()

            disc_http_version = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HttpVersion")
            disc_http_version.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HttpVersion").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HttpVersion").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "HttpVersion").parameters.transforms
            transforms.Element = AAZStrType()

            disc_is_device = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "IsDevice")
            disc_is_device.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "IsDevice").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "IsDevice").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "IsDevice").parameters.transforms
            transforms.Element = AAZStrType()

            disc_post_args = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "PostArgs")
            disc_post_args.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "PostArgs").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.selector = AAZStrType()
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "PostArgs").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "PostArgs").parameters.transforms
            transforms.Element = AAZStrType()

            disc_query_string = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "QueryString")
            disc_query_string.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "QueryString").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "QueryString").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "QueryString").parameters.transforms
            transforms.Element = AAZStrType()

            disc_remote_address = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RemoteAddress")
            disc_remote_address.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RemoteAddress").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.transforms
            transforms.Element = AAZStrType()

            disc_request_body = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestBody")
            disc_request_body.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestBody").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestBody").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestBody").parameters.transforms
            transforms.Element = AAZStrType()

            disc_request_header = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestHeader")
            disc_request_header.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestHeader").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.selector = AAZStrType()
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestHeader").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestHeader").parameters.transforms
            transforms.Element = AAZStrType()

            disc_request_method = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestMethod")
            disc_request_method.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestMethod").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestMethod").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestMethod").parameters.transforms
            transforms.Element = AAZStrType()

            disc_request_scheme = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestScheme")
            disc_request_scheme.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestScheme").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestScheme").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestScheme").parameters.transforms
            transforms.Element = AAZStrType()

            disc_request_uri = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestUri")
            disc_request_uri.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestUri").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestUri").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "RequestUri").parameters.transforms
            transforms.Element = AAZStrType()

            disc_server_port = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ServerPort")
            disc_server_port.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ServerPort").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ServerPort").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "ServerPort").parameters.transforms
            transforms.Element = AAZStrType()

            disc_socket_addr = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SocketAddr")
            disc_socket_addr.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SocketAddr").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SocketAddr").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SocketAddr").parameters.transforms
            transforms.Element = AAZStrType()

            disc_ssl_protocol = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SslProtocol")
            disc_ssl_protocol.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SslProtocol").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SslProtocol").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "SslProtocol").parameters.transforms
            transforms.Element = AAZStrType()

            disc_url_file_extension = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileExtension")
            disc_url_file_extension.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.transforms
            transforms.Element = AAZStrType()

            disc_url_file_name = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileName")
            disc_url_file_name.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileName").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileName").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlFileName").parameters.transforms
            transforms.Element = AAZStrType()

            disc_url_path = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlPath")
            disc_url_path.parameters = AAZObjectType(
                flags={"required": True},
            )

            parameters = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlPath").parameters
            parameters.match_values = AAZListType(
                serialized_name="matchValues",
            )
            parameters.negate_condition = AAZBoolType(
                serialized_name="negateCondition",
            )
            parameters.operator = AAZStrType(
                flags={"required": True},
            )
            parameters.transforms = AAZListType()
            parameters.type_name = AAZStrType(
                serialized_name="typeName",
                flags={"required": True},
            )

            match_values = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlPath").parameters.match_values
            match_values.Element = AAZStrType()

            transforms = cls._schema_on_200.value.Element.properties.conditions.Element.discriminate_by("name", "UrlPath").parameters.transforms
            transforms.Element = AAZStrType()

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""

    _schema_header_action_parameters_read = None

    @classmethod
    def _build_schema_header_action_parameters_read(cls, _schema):
        if cls._schema_header_action_parameters_read is not None:
            _schema.header_action = cls._schema_header_action_parameters_read.header_action
            _schema.header_name = cls._schema_header_action_parameters_read.header_name
            _schema.type_name = cls._schema_header_action_parameters_read.type_name
            _schema.value = cls._schema_header_action_parameters_read.value
            return

        cls._schema_header_action_parameters_read = _schema_header_action_parameters_read = AAZObjectType()

        header_action_parameters_read = _schema_header_action_parameters_read
        header_action_parameters_read.header_action = AAZStrType(
            serialized_name="headerAction",
            flags={"required": True},
        )
        header_action_parameters_read.header_name = AAZStrType(
            serialized_name="headerName",
            flags={"required": True},
        )
        header_action_parameters_read.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )
        header_action_parameters_read.value = AAZStrType()

        _schema.header_action = cls._schema_header_action_parameters_read.header_action
        _schema.header_name = cls._schema_header_action_parameters_read.header_name
        _schema.type_name = cls._schema_header_action_parameters_read.type_name
        _schema.value = cls._schema_header_action_parameters_read.value


__all__ = ["List"]
