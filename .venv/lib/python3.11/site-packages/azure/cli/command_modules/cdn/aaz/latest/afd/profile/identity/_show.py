# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "afd profile identity show",
)
class Show(AAZCommand):
    """Show the details of managed identities.

    :example: Show afd profile identity info
        az afd profile identity show --resource-group RG --profile-name profile1
    """

    _aaz_info = {
        "version": "2025-06-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}", "2025-06-01", "identity"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self.SubresourceSelector(ctx=self.ctx, name="subresource")
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.profile_name = AAZStrArg(
            options=["-n", "--name", "--profile-name"],
            help="Name of the Azure Front Door Standard or Azure Front Door Premium or CDN profile which is unique within the resource group.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.ProfilesGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.selectors.subresource.required(), client_flatten=True)
        return result

    class SubresourceSelector(AAZJsonSelector):

        def _get(self):
            result = self.ctx.vars.instance
            return result.identity

        def _set(self, value):
            result = self.ctx.vars.instance
            result.identity = value
            return

    class ProfilesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-06-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _ShowHelper._build_schema_profile_read(cls._schema_on_200)

            return cls._schema_on_200


class _ShowHelper:
    """Helper class for Show"""

    _schema_profile_read = None

    @classmethod
    def _build_schema_profile_read(cls, _schema):
        if cls._schema_profile_read is not None:
            _schema.id = cls._schema_profile_read.id
            _schema.identity = cls._schema_profile_read.identity
            _schema.kind = cls._schema_profile_read.kind
            _schema.location = cls._schema_profile_read.location
            _schema.name = cls._schema_profile_read.name
            _schema.properties = cls._schema_profile_read.properties
            _schema.sku = cls._schema_profile_read.sku
            _schema.system_data = cls._schema_profile_read.system_data
            _schema.tags = cls._schema_profile_read.tags
            _schema.type = cls._schema_profile_read.type
            return

        cls._schema_profile_read = _schema_profile_read = AAZObjectType()

        profile_read = _schema_profile_read
        profile_read.id = AAZStrType(
            flags={"read_only": True},
        )
        profile_read.identity = AAZIdentityObjectType()
        profile_read.kind = AAZStrType(
            flags={"read_only": True},
        )
        profile_read.location = AAZStrType(
            flags={"required": True},
        )
        profile_read.name = AAZStrType(
            flags={"read_only": True},
        )
        profile_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        profile_read.sku = AAZObjectType(
            flags={"required": True},
        )
        profile_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        profile_read.tags = AAZDictType()
        profile_read.type = AAZStrType(
            flags={"read_only": True},
        )

        identity = _schema_profile_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType(
            flags={"required": True},
        )
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_profile_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType(
            nullable=True,
        )

        _element = _schema_profile_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_profile_read.properties
        properties.extended_properties = AAZDictType(
            serialized_name="extendedProperties",
            flags={"read_only": True},
        )
        properties.front_door_id = AAZStrType(
            serialized_name="frontDoorId",
            flags={"read_only": True},
        )
        properties.log_scrubbing = AAZObjectType(
            serialized_name="logScrubbing",
        )
        properties.origin_response_timeout_seconds = AAZIntType(
            serialized_name="originResponseTimeoutSeconds",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.resource_state = AAZStrType(
            serialized_name="resourceState",
            flags={"read_only": True},
        )

        extended_properties = _schema_profile_read.properties.extended_properties
        extended_properties.Element = AAZStrType()

        log_scrubbing = _schema_profile_read.properties.log_scrubbing
        log_scrubbing.scrubbing_rules = AAZListType(
            serialized_name="scrubbingRules",
        )
        log_scrubbing.state = AAZStrType()

        scrubbing_rules = _schema_profile_read.properties.log_scrubbing.scrubbing_rules
        scrubbing_rules.Element = AAZObjectType()

        _element = _schema_profile_read.properties.log_scrubbing.scrubbing_rules.Element
        _element.match_variable = AAZStrType(
            serialized_name="matchVariable",
            flags={"required": True},
        )
        _element.selector = AAZStrType()
        _element.selector_match_operator = AAZStrType(
            serialized_name="selectorMatchOperator",
            flags={"required": True},
        )
        _element.state = AAZStrType()

        sku = _schema_profile_read.sku
        sku.name = AAZStrType()

        system_data = _schema_profile_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_profile_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_profile_read.id
        _schema.identity = cls._schema_profile_read.identity
        _schema.kind = cls._schema_profile_read.kind
        _schema.location = cls._schema_profile_read.location
        _schema.name = cls._schema_profile_read.name
        _schema.properties = cls._schema_profile_read.properties
        _schema.sku = cls._schema_profile_read.sku
        _schema.system_data = cls._schema_profile_read.system_data
        _schema.tags = cls._schema_profile_read.tags
        _schema.type = cls._schema_profile_read.type


__all__ = ["Show"]
