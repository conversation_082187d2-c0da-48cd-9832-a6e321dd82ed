# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "cdn endpoint create",
)
class Create(AAZCommand):
    """Create a new CDN endpoint with the specified endpoint name under the specified subscription, resource group and profile.

    :example: Create an endpoint to service content for hostname over HTTP or HTTPS.
        az cdn endpoint create -g group -n endpoint --profile-name profile --origin www.example.com

    :example: Create an endpoint with a custom domain origin with HTTP and HTTPS ports.
        az cdn endpoint create -g group -n endpoint --profile-name profile --origin www.example.com 88 4444

    :example: Create an endpoint with a custom domain origin with private link enabled.
        az cdn endpoint create -g group -n endpoint --profile-name profile --origin www.example.com 80 443 /subscriptions/subid/resourcegroups/rg1/providers/Microsoft.Network/privateLinkServices/pls1 eastus "Please approve this request"

    :example: Create an https-only endpoint with a custom domain origin and support compression for Azure CDN's default compression MIME types.
        az cdn endpoint create -g group -n endpoint --profile-name profile --origin www.example.com --no-http --enable-compression

    :example: Create an endpoint with a custom domain origin and support compression for specific MIME types.
        az cdn endpoint create -g group -n endpoint --profile-name profile --origin www.example.com --enable-compression --content-types-to-compress text/plain text/html
    """

    _aaz_info = {
        "version": "2025-06-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}/endpoints/{}", "2025-06-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.endpoint_name = AAZStrArg(
            options=["-n", "--name", "--endpoint-name"],
            help="Name of the endpoint under the profile which is unique globally.",
            required=True,
        )
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="Name of the CDN profile which is unique within the resource group.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "DefaultOriginGroup"

        _args_schema = cls._args_schema
        _args_schema.default_origin_group = AAZStrArg(
            options=["--default-origin-group"],
            arg_group="DefaultOriginGroup",
            help="The origin group to use for origins not explicitly included in an origin group. Can be specified as a resource ID or the name of an origin group of this endpoint.",
        )

        # define Arg Group "Endpoint"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Endpoint",
            help="Resource location.",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Endpoint",
            help="Resource tags.",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.content_types_to_compress = AAZListArg(
            options=["--content-types-to-compress"],
            arg_group="Properties",
            help="List of content types on which compression applies. The value should be a valid MIME type.",
        )
        _args_schema.delivery_policy = AAZObjectArg(
            options=["--delivery-policy"],
            arg_group="Properties",
            help="A policy that specifies the delivery rules to be used for an endpoint.",
        )
        _args_schema.geo_filters = AAZListArg(
            options=["--geo-filters"],
            arg_group="Properties",
            help="List of rules defining the user's geo access within a CDN endpoint. Each geo filter defines an access rule to a specified path or content, e.g. block APAC for path /pictures/",
        )
        _args_schema.is_compression_enabled = AAZBoolArg(
            options=["--is-compression-enabled"],
            arg_group="Properties",
            help="Indicates whether content compression is enabled on CDN. Default value is false. If compression is enabled, content will be served as compressed if user requests for a compressed version. Content won't be compressed on CDN when requested content is smaller than 1 byte or larger than 1 MB.",
        )
        _args_schema.is_http_allowed = AAZBoolArg(
            options=["--is-http-allowed"],
            arg_group="Properties",
            help="Indicates whether HTTP traffic is allowed on the endpoint. Default value is true. At least one protocol (HTTP or HTTPS) must be allowed.",
            default=True,
        )
        _args_schema.is_https_allowed = AAZBoolArg(
            options=["--is-https-allowed"],
            arg_group="Properties",
            help="Indicates whether HTTPS traffic is allowed on the endpoint. Default value is true. At least one protocol (HTTP or HTTPS) must be allowed.",
            default=True,
        )
        _args_schema.optimization_type = AAZStrArg(
            options=["--optimization-type"],
            arg_group="Properties",
            help="Specifies what scenario the customer wants this CDN endpoint to optimize for, e.g. Download, Media services. With this information, CDN can apply scenario driven optimization.",
            enum={"DynamicSiteAcceleration": "DynamicSiteAcceleration", "GeneralMediaStreaming": "GeneralMediaStreaming", "GeneralWebDelivery": "GeneralWebDelivery", "LargeFileDownload": "LargeFileDownload", "VideoOnDemandMediaStreaming": "VideoOnDemandMediaStreaming"},
        )
        _args_schema.origin_groups = AAZListArg(
            options=["--origin-groups"],
            arg_group="Properties",
            help="The origin groups comprising of origins that are used for load balancing the traffic based on availability.",
        )
        _args_schema.origin_host_header = AAZStrArg(
            options=["--origin-host-header"],
            arg_group="Properties",
            help="The host header value sent to the origin with each request. This property at Endpoint is only allowed when endpoint uses single origin and can be overridden by the same property specified at origin.If you leave this blank, the request hostname determines this value. Azure CDN origins, such as Web Apps, Blob Storage, and Cloud Services require this host header value to match the origin hostname by default.",
        )
        _args_schema.origin_path = AAZStrArg(
            options=["--origin-path"],
            arg_group="Properties",
            help="A directory path on the origin that CDN can use to retrieve content from, e.g. contoso.cloudapp.net/originpath.",
        )
        _args_schema.origins = AAZListArg(
            options=["--origins"],
            arg_group="Properties",
            help="The source of the content being delivered via CDN.",
        )
        _args_schema.probe_path = AAZStrArg(
            options=["--probe-path"],
            arg_group="Properties",
            help="Path to a file hosted on the origin which helps accelerate delivery of the dynamic content and calculate the most optimal routes for the CDN. This is relative to the origin path. This property is only relevant when using a single origin.",
        )
        _args_schema.query_string_caching_behavior = AAZStrArg(
            options=["--query-string-caching-behavior"],
            arg_group="Properties",
            help="Defines how CDN caches requests that include query strings. You can ignore any query strings when caching, bypass caching to prevent requests that contain query strings from being cached, or cache every request with a unique URL.",
            default="NotSet",
            enum={"BypassCaching": "BypassCaching", "IgnoreQueryString": "IgnoreQueryString", "NotSet": "NotSet", "UseQueryString": "UseQueryString"},
        )
        _args_schema.url_signing_keys = AAZListArg(
            options=["--url-signing-keys"],
            arg_group="Properties",
            help="List of keys used to validate the signed URL hashes.",
        )
        _args_schema.web_application_firewall_policy_link = AAZObjectArg(
            options=["--web-application-firewall-policy-link"],
            arg_group="Properties",
            help="Defines the Web Application Firewall policy for the endpoint (if applicable)",
        )

        content_types_to_compress = cls._args_schema.content_types_to_compress
        content_types_to_compress.Element = AAZStrArg()

        delivery_policy = cls._args_schema.delivery_policy
        delivery_policy.description = AAZStrArg(
            options=["description"],
            help="User-friendly description of the policy.",
        )
        delivery_policy.rules = AAZListArg(
            options=["rules"],
            help="A list of the delivery rules.",
            required=True,
        )

        rules = cls._args_schema.delivery_policy.rules
        rules.Element = AAZObjectArg()

        _element = cls._args_schema.delivery_policy.rules.Element
        _element.actions = AAZListArg(
            options=["actions"],
            help="A list of actions that are executed when all the conditions of a rule are satisfied.",
            required=True,
        )
        _element.conditions = AAZListArg(
            options=["conditions"],
            help="A list of conditions that must be matched for the actions to be executed",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="Name of the rule",
        )
        _element.order = AAZIntArg(
            options=["order"],
            help="The order in which the rules are applied for the endpoint. Possible values {0,1,2,3,………}. A rule with a lesser order will be applied before a rule with a greater order. Rule with order 0 is a special rule. It does not require any condition and actions listed in it will always be applied.",
            required=True,
        )

        actions = cls._args_schema.delivery_policy.rules.Element.actions
        actions.Element = AAZObjectArg()

        _element = cls._args_schema.delivery_policy.rules.Element.actions.Element
        _element.cache_expiration = AAZObjectArg(
            options=["cache-expiration"],
        )
        _element.cache_key_query_string = AAZObjectArg(
            options=["cache-key-query-string"],
        )
        _element.modify_request_header = AAZObjectArg(
            options=["modify-request-header"],
        )
        _element.modify_response_header = AAZObjectArg(
            options=["modify-response-header"],
        )
        _element.origin_group_override = AAZObjectArg(
            options=["origin-group-override"],
        )
        _element.route_configuration_override = AAZObjectArg(
            options=["route-configuration-override"],
        )
        _element.url_redirect = AAZObjectArg(
            options=["url-redirect"],
        )
        _element.url_rewrite = AAZObjectArg(
            options=["url-rewrite"],
        )
        _element.url_signing = AAZObjectArg(
            options=["url-signing"],
        )

        cache_expiration = cls._args_schema.delivery_policy.rules.Element.actions.Element.cache_expiration
        cache_expiration.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.actions.Element.cache_expiration.parameters
        parameters.cache_behavior = AAZStrArg(
            options=["cache-behavior"],
            help="Caching behavior for the requests",
            required=True,
            enum={"BypassCache": "BypassCache", "Override": "Override", "SetIfMissing": "SetIfMissing"},
        )
        parameters.cache_duration = AAZStrArg(
            options=["cache-duration"],
            help="The duration for which the content needs to be cached. Allowed format is [d.]hh:mm:ss",
            nullable=True,
        )
        parameters.cache_type = AAZStrArg(
            options=["cache-type"],
            help="The level at which the content needs to be cached.",
            required=True,
            enum={"All": "All"},
        )

        cache_key_query_string = cls._args_schema.delivery_policy.rules.Element.actions.Element.cache_key_query_string
        cache_key_query_string.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.actions.Element.cache_key_query_string.parameters
        parameters.query_parameters = AAZStrArg(
            options=["query-parameters"],
            help="query parameters to include or exclude (comma separated).",
            nullable=True,
        )
        parameters.query_string_behavior = AAZStrArg(
            options=["query-string-behavior"],
            help="Caching behavior for the requests",
            required=True,
            enum={"Exclude": "Exclude", "ExcludeAll": "ExcludeAll", "Include": "Include", "IncludeAll": "IncludeAll"},
        )

        modify_request_header = cls._args_schema.delivery_policy.rules.Element.actions.Element.modify_request_header
        modify_request_header.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )
        cls._build_args_header_action_parameters_create(modify_request_header.parameters)

        modify_response_header = cls._args_schema.delivery_policy.rules.Element.actions.Element.modify_response_header
        modify_response_header.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )
        cls._build_args_header_action_parameters_create(modify_response_header.parameters)

        origin_group_override = cls._args_schema.delivery_policy.rules.Element.actions.Element.origin_group_override
        origin_group_override.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.actions.Element.origin_group_override.parameters
        parameters.origin_group = AAZObjectArg(
            options=["origin-group"],
            help="defines the OriginGroup that would override the DefaultOriginGroup.",
            required=True,
        )
        cls._build_args_resource_reference_create(parameters.origin_group)

        route_configuration_override = cls._args_schema.delivery_policy.rules.Element.actions.Element.route_configuration_override
        route_configuration_override.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.actions.Element.route_configuration_override.parameters
        parameters.cache_configuration = AAZObjectArg(
            options=["cache-configuration"],
            help="The caching configuration associated with this rule. To disable caching, do not provide a cacheConfiguration object.",
        )
        parameters.origin_group_override = AAZObjectArg(
            options=["origin-group-override"],
            help="A reference to the origin group override configuration. Leave empty to use the default origin group on route.",
        )

        cache_configuration = cls._args_schema.delivery_policy.rules.Element.actions.Element.route_configuration_override.parameters.cache_configuration
        cache_configuration.cache_behavior = AAZStrArg(
            options=["cache-behavior"],
            help="Caching behavior for the requests",
            enum={"HonorOrigin": "HonorOrigin", "OverrideAlways": "OverrideAlways", "OverrideIfOriginMissing": "OverrideIfOriginMissing"},
        )
        cache_configuration.cache_duration = AAZStrArg(
            options=["cache-duration"],
            help="The duration for which the content needs to be cached. Allowed format is [d.]hh:mm:ss",
        )
        cache_configuration.is_compression_enabled = AAZStrArg(
            options=["is-compression-enabled"],
            help="Indicates whether content compression is enabled. If compression is enabled, content will be served as compressed if user requests for a compressed version. Content won't be compressed on AzureFrontDoor when requested content is smaller than 1 byte or larger than 1 MB.",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        cache_configuration.query_parameters = AAZStrArg(
            options=["query-parameters"],
            help="query parameters to include or exclude (comma separated).",
        )
        cache_configuration.query_string_caching_behavior = AAZStrArg(
            options=["query-string-caching-behavior"],
            help="Defines how Frontdoor caches requests that include query strings. You can ignore any query strings when caching, ignore specific query strings, cache every request with a unique URL, or cache specific query strings.",
            enum={"IgnoreQueryString": "IgnoreQueryString", "IgnoreSpecifiedQueryStrings": "IgnoreSpecifiedQueryStrings", "IncludeSpecifiedQueryStrings": "IncludeSpecifiedQueryStrings", "UseQueryString": "UseQueryString"},
        )

        origin_group_override = cls._args_schema.delivery_policy.rules.Element.actions.Element.route_configuration_override.parameters.origin_group_override
        origin_group_override.forwarding_protocol = AAZStrArg(
            options=["forwarding-protocol"],
            help="Protocol this rule will use when forwarding traffic to backends.",
            enum={"HttpOnly": "HttpOnly", "HttpsOnly": "HttpsOnly", "MatchRequest": "MatchRequest"},
        )
        origin_group_override.origin_group = AAZObjectArg(
            options=["origin-group"],
            help="defines the OriginGroup that would override the DefaultOriginGroup on route.",
        )
        cls._build_args_resource_reference_create(origin_group_override.origin_group)

        url_redirect = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_redirect
        url_redirect.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_redirect.parameters
        parameters.custom_fragment = AAZStrArg(
            options=["custom-fragment"],
            help="Fragment to add to the redirect URL. Fragment is the part of the URL that comes after #. Do not include the #.",
        )
        parameters.custom_hostname = AAZStrArg(
            options=["custom-hostname"],
            help="Host to redirect. Leave empty to use the incoming host as the destination host.",
        )
        parameters.custom_path = AAZStrArg(
            options=["custom-path"],
            help="The full path to redirect. Path cannot be empty and must start with /. Leave empty to use the incoming path as destination path.",
        )
        parameters.custom_query_string = AAZStrArg(
            options=["custom-query-string"],
            help="The set of query strings to be placed in the redirect URL. Setting this value would replace any existing query string; leave empty to preserve the incoming query string. Query string must be in <key>=<value> format. ? and & will be added automatically so do not include them.",
        )
        parameters.destination_protocol = AAZStrArg(
            options=["destination-protocol"],
            help="Protocol to use for the redirect. The default value is MatchRequest",
            enum={"Http": "Http", "Https": "Https", "MatchRequest": "MatchRequest"},
        )
        parameters.redirect_type = AAZStrArg(
            options=["redirect-type"],
            help="The redirect type the rule will use when redirecting traffic.",
            required=True,
            enum={"Found": "Found", "Moved": "Moved", "PermanentRedirect": "PermanentRedirect", "TemporaryRedirect": "TemporaryRedirect"},
        )

        url_rewrite = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_rewrite
        url_rewrite.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_rewrite.parameters
        parameters.destination = AAZStrArg(
            options=["destination"],
            help="Define the relative URL to which the above requests will be rewritten by.",
            required=True,
        )
        parameters.preserve_unmatched_path = AAZBoolArg(
            options=["preserve-unmatched-path"],
            help="Whether to preserve unmatched path. Default value is true.",
        )
        parameters.source_pattern = AAZStrArg(
            options=["source-pattern"],
            help="define a request URI pattern that identifies the type of requests that may be rewritten. If value is blank, all strings are matched.",
            required=True,
        )

        url_signing = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_signing
        url_signing.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the action.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_signing.parameters
        parameters.algorithm = AAZStrArg(
            options=["algorithm"],
            help="Algorithm to use for URL signing",
            enum={"SHA256": "SHA256"},
        )
        parameters.parameter_name_override = AAZListArg(
            options=["parameter-name-override"],
            help="Defines which query string parameters in the url to be considered for expires, key id etc. ",
        )

        parameter_name_override = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_signing.parameters.parameter_name_override
        parameter_name_override.Element = AAZObjectArg()

        _element = cls._args_schema.delivery_policy.rules.Element.actions.Element.url_signing.parameters.parameter_name_override.Element
        _element.param_indicator = AAZStrArg(
            options=["param-indicator"],
            help="Indicates the purpose of the parameter",
            required=True,
            enum={"Expires": "Expires", "KeyId": "KeyId", "Signature": "Signature"},
        )
        _element.param_name = AAZStrArg(
            options=["param-name"],
            help="Parameter name",
            required=True,
        )

        conditions = cls._args_schema.delivery_policy.rules.Element.conditions
        conditions.Element = AAZObjectArg()

        _element = cls._args_schema.delivery_policy.rules.Element.conditions.Element
        _element.client_port = AAZObjectArg(
            options=["client-port"],
        )
        _element.cookies = AAZObjectArg(
            options=["cookies"],
        )
        _element.host_name = AAZObjectArg(
            options=["host-name"],
        )
        _element.http_version = AAZObjectArg(
            options=["http-version"],
        )
        _element.is_device = AAZObjectArg(
            options=["is-device"],
        )
        _element.post_args = AAZObjectArg(
            options=["post-args"],
        )
        _element.query_string = AAZObjectArg(
            options=["query-string"],
        )
        _element.remote_address = AAZObjectArg(
            options=["remote-address"],
        )
        _element.request_body = AAZObjectArg(
            options=["request-body"],
        )
        _element.request_header = AAZObjectArg(
            options=["request-header"],
        )
        _element.request_method = AAZObjectArg(
            options=["request-method"],
        )
        _element.request_scheme = AAZObjectArg(
            options=["request-scheme"],
        )
        _element.request_uri = AAZObjectArg(
            options=["request-uri"],
        )
        _element.server_port = AAZObjectArg(
            options=["server-port"],
        )
        _element.socket_addr = AAZObjectArg(
            options=["socket-addr"],
        )
        _element.ssl_protocol = AAZObjectArg(
            options=["ssl-protocol"],
        )
        _element.url_file_extension = AAZObjectArg(
            options=["url-file-extension"],
        )
        _element.url_file_name = AAZObjectArg(
            options=["url-file-name"],
        )
        _element.url_path = AAZObjectArg(
            options=["url-path"],
        )

        client_port = cls._args_schema.delivery_policy.rules.Element.conditions.Element.client_port
        client_port.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.client_port.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.client_port.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.client_port.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        cookies = cls._args_schema.delivery_policy.rules.Element.conditions.Element.cookies
        cookies.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.cookies.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.selector = AAZStrArg(
            options=["selector"],
            help="Name of Cookies to be matched",
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.cookies.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.cookies.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        host_name = cls._args_schema.delivery_policy.rules.Element.conditions.Element.host_name
        host_name.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.host_name.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.host_name.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.host_name.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        http_version = cls._args_schema.delivery_policy.rules.Element.conditions.Element.http_version
        http_version.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.http_version.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.http_version.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.http_version.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        is_device = cls._args_schema.delivery_policy.rules.Element.conditions.Element.is_device
        is_device.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.is_device.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.is_device.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"Desktop": "Desktop", "Mobile": "Mobile"},
        )

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.is_device.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        post_args = cls._args_schema.delivery_policy.rules.Element.conditions.Element.post_args
        post_args.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.post_args.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.selector = AAZStrArg(
            options=["selector"],
            help="Name of PostArg to be matched",
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.post_args.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.post_args.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        query_string = cls._args_schema.delivery_policy.rules.Element.conditions.Element.query_string
        query_string.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.query_string.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.query_string.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.query_string.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        remote_address = cls._args_schema.delivery_policy.rules.Element.conditions.Element.remote_address
        remote_address.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.remote_address.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="Match values to match against. The operator will apply to each value in here with OR semantics. If any of them match the variable with the given operator this match condition is considered a match.",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "GeoMatch": "GeoMatch", "IPMatch": "IPMatch"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.remote_address.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.remote_address.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_body = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_body
        request_body.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_body.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_body.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_body.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_header = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_header
        request_header.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_header.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.selector = AAZStrArg(
            options=["selector"],
            help="Name of Header to be matched",
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_header.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_header.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_method = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_method
        request_method.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_method.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_method.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"DELETE": "DELETE", "GET": "GET", "HEAD": "HEAD", "OPTIONS": "OPTIONS", "POST": "POST", "PUT": "PUT", "TRACE": "TRACE"},
        )

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_method.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_scheme = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_scheme
        request_scheme.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_scheme.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_scheme.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"HTTP": "HTTP", "HTTPS": "HTTPS"},
        )

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_scheme.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        request_uri = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_uri
        request_uri.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_uri.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_uri.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.request_uri.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        server_port = cls._args_schema.delivery_policy.rules.Element.conditions.Element.server_port
        server_port.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.server_port.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.server_port.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.server_port.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        socket_addr = cls._args_schema.delivery_policy.rules.Element.conditions.Element.socket_addr
        socket_addr.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.socket_addr.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "IPMatch": "IPMatch"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.socket_addr.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.socket_addr.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        ssl_protocol = cls._args_schema.delivery_policy.rules.Element.conditions.Element.ssl_protocol
        ssl_protocol.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.ssl_protocol.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Equal": "Equal"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.ssl_protocol.parameters.match_values
        match_values.Element = AAZStrArg(
            enum={"TLSv1": "TLSv1", "TLSv1.1": "TLSv1.1", "TLSv1.2": "TLSv1.2"},
        )

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.ssl_protocol.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        url_file_extension = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_extension
        url_file_extension.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_extension.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_extension.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_extension.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        url_file_name = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_name
        url_file_name.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_name.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_name.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_file_name.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        url_path = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_path
        url_path.parameters = AAZObjectArg(
            options=["parameters"],
            help="Defines the parameters for the condition.",
            required=True,
        )

        parameters = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_path.parameters
        parameters.match_values = AAZListArg(
            options=["match-values"],
            help="The match value for the condition of the delivery rule",
        )
        parameters.negate_condition = AAZBoolArg(
            options=["negate-condition"],
            help="Describes if this is negate condition or not",
            default=False,
        )
        parameters.operator = AAZStrArg(
            options=["operator"],
            help="Describes operator to be matched",
            required=True,
            enum={"Any": "Any", "BeginsWith": "BeginsWith", "Contains": "Contains", "EndsWith": "EndsWith", "Equal": "Equal", "GreaterThan": "GreaterThan", "GreaterThanOrEqual": "GreaterThanOrEqual", "LessThan": "LessThan", "LessThanOrEqual": "LessThanOrEqual", "RegEx": "RegEx", "Wildcard": "Wildcard"},
        )
        parameters.transforms = AAZListArg(
            options=["transforms"],
            help="List of transforms",
        )

        match_values = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_path.parameters.match_values
        match_values.Element = AAZStrArg()

        transforms = cls._args_schema.delivery_policy.rules.Element.conditions.Element.url_path.parameters.transforms
        transforms.Element = AAZStrArg(
            enum={"Lowercase": "Lowercase", "RemoveNulls": "RemoveNulls", "Trim": "Trim", "Uppercase": "Uppercase", "UrlDecode": "UrlDecode", "UrlEncode": "UrlEncode"},
        )

        geo_filters = cls._args_schema.geo_filters
        geo_filters.Element = AAZObjectArg()

        _element = cls._args_schema.geo_filters.Element
        _element.action = AAZStrArg(
            options=["action"],
            help="Action of the geo filter, i.e. allow or block access.",
            required=True,
            enum={"Allow": "Allow", "Block": "Block"},
        )
        _element.country_codes = AAZListArg(
            options=["country-codes"],
            help="Two letter country or region codes defining user country or region access in a geo filter, e.g. AU, MX, US.",
            required=True,
        )
        _element.relative_path = AAZStrArg(
            options=["relative-path"],
            help="Relative path applicable to geo filter. (e.g. '/mypictures', '/mypicture/kitty.jpg', and etc.)",
            required=True,
        )

        country_codes = cls._args_schema.geo_filters.Element.country_codes
        country_codes.Element = AAZStrArg()

        origin_groups = cls._args_schema.origin_groups
        origin_groups.Element = AAZObjectArg()

        _element = cls._args_schema.origin_groups.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="Origin group name which must be unique within the endpoint.",
            required=True,
        )
        _element.health_probe_settings = AAZObjectArg(
            options=["health-probe-settings"],
            help="Health probe settings to the origin that is used to determine the health of the origin.",
        )
        _element.origins = AAZListArg(
            options=["origins"],
            help="The source of the content being delivered via CDN within given origin group.",
        )
        _element.response_based_origin_error_detection_settings = AAZObjectArg(
            options=["response-based-origin-error-detection-settings"],
            help="The JSON object that contains the properties to determine origin health using real requests/responses.This property is currently not supported.",
        )
        _element.traffic_restoration_time_to_healed_or_new_endpoints_in_minutes = AAZIntArg(
            options=["traffic-restoration-time-to-healed-or-new-endpoints-in-minutes"],
            help="Time in minutes to shift the traffic to the endpoint gradually when an unhealthy endpoint comes healthy or a new endpoint is added. Default is 10 mins. This property is currently not supported.",
            fmt=AAZIntArgFormat(
                maximum=50,
                minimum=0,
            ),
        )

        health_probe_settings = cls._args_schema.origin_groups.Element.health_probe_settings
        health_probe_settings.probe_interval_in_seconds = AAZIntArg(
            options=["probe-interval-in-seconds"],
            help="The number of seconds between health probes.Default is 240sec.",
            fmt=AAZIntArgFormat(
                maximum=255,
                minimum=1,
            ),
        )
        health_probe_settings.probe_path = AAZStrArg(
            options=["probe-path"],
            help="The path relative to the origin that is used to determine the health of the origin.",
        )
        health_probe_settings.probe_protocol = AAZStrArg(
            options=["probe-protocol"],
            help="Protocol to use for health probe.",
            enum={"Http": "Http", "Https": "Https", "NotSet": "NotSet"},
        )
        health_probe_settings.probe_request_type = AAZStrArg(
            options=["probe-request-type"],
            help="The type of health probe request that is made.",
            enum={"GET": "GET", "HEAD": "HEAD", "NotSet": "NotSet"},
        )

        origins = cls._args_schema.origin_groups.Element.origins
        origins.Element = AAZObjectArg()
        cls._build_args_resource_reference_create(origins.Element)

        response_based_origin_error_detection_settings = cls._args_schema.origin_groups.Element.response_based_origin_error_detection_settings
        response_based_origin_error_detection_settings.http_error_ranges = AAZListArg(
            options=["http-error-ranges"],
            help="The list of Http status code ranges that are considered as server errors for origin and it is marked as unhealthy.",
        )
        response_based_origin_error_detection_settings.response_based_detected_error_types = AAZStrArg(
            options=["response-based-detected-error-types"],
            help="Type of response errors for real user requests for which origin will be deemed unhealthy",
            enum={"None": "None", "TcpAndHttpErrors": "TcpAndHttpErrors", "TcpErrorsOnly": "TcpErrorsOnly"},
        )
        response_based_origin_error_detection_settings.response_based_failover_threshold_percentage = AAZIntArg(
            options=["response-based-failover-threshold-percentage"],
            help="The percentage of failed requests in the sample where failover should trigger.",
            fmt=AAZIntArgFormat(
                maximum=100,
                minimum=0,
            ),
        )

        http_error_ranges = cls._args_schema.origin_groups.Element.response_based_origin_error_detection_settings.http_error_ranges
        http_error_ranges.Element = AAZObjectArg()

        _element = cls._args_schema.origin_groups.Element.response_based_origin_error_detection_settings.http_error_ranges.Element
        _element.begin = AAZIntArg(
            options=["begin"],
            help="The inclusive start of the http status code range.",
            fmt=AAZIntArgFormat(
                maximum=999,
                minimum=100,
            ),
        )
        _element.end = AAZIntArg(
            options=["end"],
            help="The inclusive end of the http status code range.",
            fmt=AAZIntArgFormat(
                maximum=999,
                minimum=100,
            ),
        )

        origins = cls._args_schema.origins
        origins.Element = AAZObjectArg()

        _element = cls._args_schema.origins.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="Origin name which must be unique within the endpoint. ",
            required=True,
        )
        _element.enabled = AAZBoolArg(
            options=["enabled"],
            help="Origin is enabled for load balancing or not. By default, origin is always enabled.",
        )
        _element.host_name = AAZStrArg(
            options=["host-name"],
            help="The address of the origin. It can be a domain name, IPv4 address, or IPv6 address. This should be unique across all origins in an endpoint.",
        )
        _element.http_port = AAZIntArg(
            options=["http-port"],
            help="The value of the HTTP port. Must be between 1 and 65535.",
            fmt=AAZIntArgFormat(
                maximum=65535,
                minimum=1,
            ),
        )
        _element.https_port = AAZIntArg(
            options=["https-port"],
            help="The value of the HTTPS port. Must be between 1 and 65535.",
            fmt=AAZIntArgFormat(
                maximum=65535,
                minimum=1,
            ),
        )
        _element.origin_host_header = AAZStrArg(
            options=["origin-host-header"],
            help="The host header value sent to the origin with each request. If you leave this blank, the request hostname determines this value. Azure CDN origins, such as Web Apps, Blob Storage, and Cloud Services require this host header value to match the origin hostname by default.",
        )
        _element.priority = AAZIntArg(
            options=["priority"],
            help="Priority of origin in given origin group for load balancing. Higher priorities will not be used for load balancing if any lower priority origin is healthy.Must be between 1 and 5.",
            fmt=AAZIntArgFormat(
                maximum=5,
                minimum=1,
            ),
        )
        _element.private_link_alias = AAZStrArg(
            options=["private-link-alias"],
            help="The Alias of the Private Link resource. Populating this optional field indicates that this origin is 'Private'",
        )
        _element.private_link_approval_message = AAZStrArg(
            options=["private-link-approval-message"],
            help="A custom message to be included in the approval request to connect to the Private Link.",
        )
        _element.private_link_location = AAZStrArg(
            options=["private-link-location"],
            help="The location of the Private Link resource. Required only if 'privateLinkResourceId' is populated",
        )
        _element.private_link_resource_id = AAZStrArg(
            options=["private-link-resource-id"],
            help="The Resource Id of the Private Link resource. Populating this optional field indicates that this backend is 'Private'",
        )
        _element.weight = AAZIntArg(
            options=["weight"],
            help="Weight of the origin in given origin group for load balancing. Must be between 1 and 1000",
            fmt=AAZIntArgFormat(
                maximum=1000,
                minimum=1,
            ),
        )

        url_signing_keys = cls._args_schema.url_signing_keys
        url_signing_keys.Element = AAZObjectArg()

        _element = cls._args_schema.url_signing_keys.Element
        _element.key_id = AAZStrArg(
            options=["key-id"],
            help="Defines the customer defined key Id. This id will exist in the incoming request to indicate the key used to form the hash.",
            required=True,
        )
        _element.key_source_parameters = AAZObjectArg(
            options=["key-source-parameters"],
            help="Defines the parameters for using customer key vault for Url Signing Key.",
            required=True,
        )

        key_source_parameters = cls._args_schema.url_signing_keys.Element.key_source_parameters
        key_source_parameters.resource_group_name = AAZStrArg(
            options=["resource-group-name"],
            help="Resource group of the user's Key Vault containing the secret",
            required=True,
        )
        key_source_parameters.secret_name = AAZStrArg(
            options=["secret-name"],
            help="The name of secret in Key Vault.",
            required=True,
        )
        key_source_parameters.secret_version = AAZStrArg(
            options=["secret-version"],
            help="The version(GUID) of secret in Key Vault.",
            required=True,
        )
        key_source_parameters.subscription_id = AAZStrArg(
            options=["subscription-id"],
            help="Subscription Id of the user's Key Vault containing the secret",
            required=True,
        )
        key_source_parameters.type_name = AAZStrArg(
            options=["type-name"],
            required=True,
            enum={"KeyVaultSigningKeyParameters": "KeyVaultSigningKeyParameters"},
        )
        key_source_parameters.vault_name = AAZStrArg(
            options=["vault-name"],
            help="The name of the user's Key Vault containing the secret",
            required=True,
        )

        web_application_firewall_policy_link = cls._args_schema.web_application_firewall_policy_link
        web_application_firewall_policy_link.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )
        return cls._args_schema

    _args_header_action_parameters_create = None

    @classmethod
    def _build_args_header_action_parameters_create(cls, _schema):
        if cls._args_header_action_parameters_create is not None:
            _schema.header_action = cls._args_header_action_parameters_create.header_action
            _schema.header_name = cls._args_header_action_parameters_create.header_name
            _schema.value = cls._args_header_action_parameters_create.value
            return

        cls._args_header_action_parameters_create = AAZObjectArg()

        header_action_parameters_create = cls._args_header_action_parameters_create
        header_action_parameters_create.header_action = AAZStrArg(
            options=["header-action"],
            help="Action to perform",
            required=True,
            enum={"Append": "Append", "Delete": "Delete", "Overwrite": "Overwrite"},
        )
        header_action_parameters_create.header_name = AAZStrArg(
            options=["header-name"],
            help="Name of the header to modify",
            required=True,
        )
        header_action_parameters_create.value = AAZStrArg(
            options=["value"],
            help="Value for the specified action",
        )

        _schema.header_action = cls._args_header_action_parameters_create.header_action
        _schema.header_name = cls._args_header_action_parameters_create.header_name
        _schema.value = cls._args_header_action_parameters_create.value

    _args_resource_reference_create = None

    @classmethod
    def _build_args_resource_reference_create(cls, _schema):
        if cls._args_resource_reference_create is not None:
            _schema.id = cls._args_resource_reference_create.id
            return

        cls._args_resource_reference_create = AAZObjectArg()

        resource_reference_create = cls._args_resource_reference_create
        resource_reference_create.id = AAZStrArg(
            options=["id"],
            help="Resource ID.",
        )

        _schema.id = cls._args_resource_reference_create.id

    def _execute_operations(self):
        self.pre_operations()
        yield self.EndpointsCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class EndpointsCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}/endpoints/{endpointName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "endpointName", self.ctx.args.endpoint_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-06-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("contentTypesToCompress", AAZListType, ".content_types_to_compress")
                properties.set_prop("defaultOriginGroup", AAZObjectType)
                properties.set_prop("deliveryPolicy", AAZObjectType, ".delivery_policy")
                properties.set_prop("geoFilters", AAZListType, ".geo_filters")
                properties.set_prop("isCompressionEnabled", AAZBoolType, ".is_compression_enabled")
                properties.set_prop("isHttpAllowed", AAZBoolType, ".is_http_allowed")
                properties.set_prop("isHttpsAllowed", AAZBoolType, ".is_https_allowed")
                properties.set_prop("optimizationType", AAZStrType, ".optimization_type")
                properties.set_prop("originGroups", AAZListType, ".origin_groups")
                properties.set_prop("originHostHeader", AAZStrType, ".origin_host_header")
                properties.set_prop("originPath", AAZStrType, ".origin_path")
                properties.set_prop("origins", AAZListType, ".origins", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("probePath", AAZStrType, ".probe_path")
                properties.set_prop("queryStringCachingBehavior", AAZStrType, ".query_string_caching_behavior")
                properties.set_prop("urlSigningKeys", AAZListType, ".url_signing_keys")
                properties.set_prop("webApplicationFirewallPolicyLink", AAZObjectType, ".web_application_firewall_policy_link")

            content_types_to_compress = _builder.get(".properties.contentTypesToCompress")
            if content_types_to_compress is not None:
                content_types_to_compress.set_elements(AAZStrType, ".")

            default_origin_group = _builder.get(".properties.defaultOriginGroup")
            if default_origin_group is not None:
                default_origin_group.set_prop("id", AAZStrType, ".default_origin_group")

            delivery_policy = _builder.get(".properties.deliveryPolicy")
            if delivery_policy is not None:
                delivery_policy.set_prop("description", AAZStrType, ".description")
                delivery_policy.set_prop("rules", AAZListType, ".rules", typ_kwargs={"flags": {"required": True}})

            rules = _builder.get(".properties.deliveryPolicy.rules")
            if rules is not None:
                rules.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.deliveryPolicy.rules[]")
            if _elements is not None:
                _elements.set_prop("actions", AAZListType, ".actions", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("conditions", AAZListType, ".conditions")
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("order", AAZIntType, ".order", typ_kwargs={"flags": {"required": True}})

            actions = _builder.get(".properties.deliveryPolicy.rules[].actions")
            if actions is not None:
                actions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.deliveryPolicy.rules[].actions[]")
            if _elements is not None:
                _elements.set_const("name", "CacheExpiration", AAZStrType, ".cache_expiration", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "CacheKeyQueryString", AAZStrType, ".cache_key_query_string", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "ModifyRequestHeader", AAZStrType, ".modify_request_header", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "ModifyResponseHeader", AAZStrType, ".modify_response_header", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "OriginGroupOverride", AAZStrType, ".origin_group_override", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RouteConfigurationOverride", AAZStrType, ".route_configuration_override", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlRedirect", AAZStrType, ".url_redirect", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlRewrite", AAZStrType, ".url_rewrite", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlSigning", AAZStrType, ".url_signing", typ_kwargs={"flags": {"required": True}})
                _elements.discriminate_by("name", "CacheExpiration")
                _elements.discriminate_by("name", "CacheKeyQueryString")
                _elements.discriminate_by("name", "ModifyRequestHeader")
                _elements.discriminate_by("name", "ModifyResponseHeader")
                _elements.discriminate_by("name", "OriginGroupOverride")
                _elements.discriminate_by("name", "RouteConfigurationOverride")
                _elements.discriminate_by("name", "UrlRedirect")
                _elements.discriminate_by("name", "UrlRewrite")
                _elements.discriminate_by("name", "UrlSigning")

            disc_cache_expiration = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:CacheExpiration}")
            if disc_cache_expiration is not None:
                disc_cache_expiration.set_prop("parameters", AAZObjectType, ".cache_expiration.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:CacheExpiration}.parameters")
            if parameters is not None:
                parameters.set_prop("cacheBehavior", AAZStrType, ".cache_behavior", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("cacheDuration", AAZStrType, ".cache_duration", typ_kwargs={"nullable": True})
                parameters.set_prop("cacheType", AAZStrType, ".cache_type", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleCacheExpirationActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_cache_key_query_string = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:CacheKeyQueryString}")
            if disc_cache_key_query_string is not None:
                disc_cache_key_query_string.set_prop("parameters", AAZObjectType, ".cache_key_query_string.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:CacheKeyQueryString}.parameters")
            if parameters is not None:
                parameters.set_prop("queryParameters", AAZStrType, ".query_parameters", typ_kwargs={"nullable": True})
                parameters.set_prop("queryStringBehavior", AAZStrType, ".query_string_behavior", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleCacheKeyQueryStringBehaviorActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_modify_request_header = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:ModifyRequestHeader}")
            if disc_modify_request_header is not None:
                _CreateHelper._build_schema_header_action_parameters_create(disc_modify_request_header.set_prop("parameters", AAZObjectType, ".modify_request_header.parameters", typ_kwargs={"flags": {"required": True}}))

            disc_modify_response_header = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:ModifyResponseHeader}")
            if disc_modify_response_header is not None:
                _CreateHelper._build_schema_header_action_parameters_create(disc_modify_response_header.set_prop("parameters", AAZObjectType, ".modify_response_header.parameters", typ_kwargs={"flags": {"required": True}}))

            disc_origin_group_override = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:OriginGroupOverride}")
            if disc_origin_group_override is not None:
                disc_origin_group_override.set_prop("parameters", AAZObjectType, ".origin_group_override.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:OriginGroupOverride}.parameters")
            if parameters is not None:
                _CreateHelper._build_schema_resource_reference_create(parameters.set_prop("originGroup", AAZObjectType, ".origin_group", typ_kwargs={"flags": {"required": True}}))
                parameters.set_const("typeName", "DeliveryRuleOriginGroupOverrideActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_route_configuration_override = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:RouteConfigurationOverride}")
            if disc_route_configuration_override is not None:
                disc_route_configuration_override.set_prop("parameters", AAZObjectType, ".route_configuration_override.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:RouteConfigurationOverride}.parameters")
            if parameters is not None:
                parameters.set_prop("cacheConfiguration", AAZObjectType, ".cache_configuration")
                parameters.set_prop("originGroupOverride", AAZObjectType, ".origin_group_override")
                parameters.set_const("typeName", "DeliveryRuleRouteConfigurationOverrideActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            cache_configuration = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:RouteConfigurationOverride}.parameters.cacheConfiguration")
            if cache_configuration is not None:
                cache_configuration.set_prop("cacheBehavior", AAZStrType, ".cache_behavior")
                cache_configuration.set_prop("cacheDuration", AAZStrType, ".cache_duration")
                cache_configuration.set_prop("isCompressionEnabled", AAZStrType, ".is_compression_enabled")
                cache_configuration.set_prop("queryParameters", AAZStrType, ".query_parameters")
                cache_configuration.set_prop("queryStringCachingBehavior", AAZStrType, ".query_string_caching_behavior")

            origin_group_override = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:RouteConfigurationOverride}.parameters.originGroupOverride")
            if origin_group_override is not None:
                origin_group_override.set_prop("forwardingProtocol", AAZStrType, ".forwarding_protocol")
                _CreateHelper._build_schema_resource_reference_create(origin_group_override.set_prop("originGroup", AAZObjectType, ".origin_group"))

            disc_url_redirect = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlRedirect}")
            if disc_url_redirect is not None:
                disc_url_redirect.set_prop("parameters", AAZObjectType, ".url_redirect.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlRedirect}.parameters")
            if parameters is not None:
                parameters.set_prop("customFragment", AAZStrType, ".custom_fragment")
                parameters.set_prop("customHostname", AAZStrType, ".custom_hostname")
                parameters.set_prop("customPath", AAZStrType, ".custom_path")
                parameters.set_prop("customQueryString", AAZStrType, ".custom_query_string")
                parameters.set_prop("destinationProtocol", AAZStrType, ".destination_protocol")
                parameters.set_prop("redirectType", AAZStrType, ".redirect_type", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleUrlRedirectActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_url_rewrite = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlRewrite}")
            if disc_url_rewrite is not None:
                disc_url_rewrite.set_prop("parameters", AAZObjectType, ".url_rewrite.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlRewrite}.parameters")
            if parameters is not None:
                parameters.set_prop("destination", AAZStrType, ".destination", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("preserveUnmatchedPath", AAZBoolType, ".preserve_unmatched_path")
                parameters.set_prop("sourcePattern", AAZStrType, ".source_pattern", typ_kwargs={"flags": {"required": True}})
                parameters.set_const("typeName", "DeliveryRuleUrlRewriteActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            disc_url_signing = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlSigning}")
            if disc_url_signing is not None:
                disc_url_signing.set_prop("parameters", AAZObjectType, ".url_signing.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlSigning}.parameters")
            if parameters is not None:
                parameters.set_prop("algorithm", AAZStrType, ".algorithm")
                parameters.set_prop("parameterNameOverride", AAZListType, ".parameter_name_override")
                parameters.set_const("typeName", "DeliveryRuleUrlSigningActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            parameter_name_override = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlSigning}.parameters.parameterNameOverride")
            if parameter_name_override is not None:
                parameter_name_override.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.deliveryPolicy.rules[].actions[]{name:UrlSigning}.parameters.parameterNameOverride[]")
            if _elements is not None:
                _elements.set_prop("paramIndicator", AAZStrType, ".param_indicator", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("paramName", AAZStrType, ".param_name", typ_kwargs={"flags": {"required": True}})

            conditions = _builder.get(".properties.deliveryPolicy.rules[].conditions")
            if conditions is not None:
                conditions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.deliveryPolicy.rules[].conditions[]")
            if _elements is not None:
                _elements.set_const("name", "ClientPort", AAZStrType, ".client_port", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "Cookies", AAZStrType, ".cookies", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "HostName", AAZStrType, ".host_name", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "HttpVersion", AAZStrType, ".http_version", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "IsDevice", AAZStrType, ".is_device", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "PostArgs", AAZStrType, ".post_args", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "QueryString", AAZStrType, ".query_string", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RemoteAddress", AAZStrType, ".remote_address", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestBody", AAZStrType, ".request_body", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestHeader", AAZStrType, ".request_header", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestMethod", AAZStrType, ".request_method", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestScheme", AAZStrType, ".request_scheme", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "RequestUri", AAZStrType, ".request_uri", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "ServerPort", AAZStrType, ".server_port", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "SocketAddr", AAZStrType, ".socket_addr", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "SslProtocol", AAZStrType, ".ssl_protocol", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlFileExtension", AAZStrType, ".url_file_extension", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlFileName", AAZStrType, ".url_file_name", typ_kwargs={"flags": {"required": True}})
                _elements.set_const("name", "UrlPath", AAZStrType, ".url_path", typ_kwargs={"flags": {"required": True}})
                _elements.discriminate_by("name", "ClientPort")
                _elements.discriminate_by("name", "Cookies")
                _elements.discriminate_by("name", "HostName")
                _elements.discriminate_by("name", "HttpVersion")
                _elements.discriminate_by("name", "IsDevice")
                _elements.discriminate_by("name", "PostArgs")
                _elements.discriminate_by("name", "QueryString")
                _elements.discriminate_by("name", "RemoteAddress")
                _elements.discriminate_by("name", "RequestBody")
                _elements.discriminate_by("name", "RequestHeader")
                _elements.discriminate_by("name", "RequestMethod")
                _elements.discriminate_by("name", "RequestScheme")
                _elements.discriminate_by("name", "RequestUri")
                _elements.discriminate_by("name", "ServerPort")
                _elements.discriminate_by("name", "SocketAddr")
                _elements.discriminate_by("name", "SslProtocol")
                _elements.discriminate_by("name", "UrlFileExtension")
                _elements.discriminate_by("name", "UrlFileName")
                _elements.discriminate_by("name", "UrlPath")

            disc_client_port = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ClientPort}")
            if disc_client_port is not None:
                disc_client_port.set_prop("parameters", AAZObjectType, ".client_port.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ClientPort}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleClientPortConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ClientPort}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ClientPort}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_cookies = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:Cookies}")
            if disc_cookies is not None:
                disc_cookies.set_prop("parameters", AAZObjectType, ".cookies.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:Cookies}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("selector", AAZStrType, ".selector")
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleCookiesConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:Cookies}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:Cookies}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_host_name = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HostName}")
            if disc_host_name is not None:
                disc_host_name.set_prop("parameters", AAZObjectType, ".host_name.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HostName}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleHostNameConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HostName}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HostName}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_http_version = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HttpVersion}")
            if disc_http_version is not None:
                disc_http_version.set_prop("parameters", AAZObjectType, ".http_version.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HttpVersion}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleHttpVersionConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HttpVersion}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:HttpVersion}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_is_device = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:IsDevice}")
            if disc_is_device is not None:
                disc_is_device.set_prop("parameters", AAZObjectType, ".is_device.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:IsDevice}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleIsDeviceConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:IsDevice}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:IsDevice}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_post_args = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:PostArgs}")
            if disc_post_args is not None:
                disc_post_args.set_prop("parameters", AAZObjectType, ".post_args.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:PostArgs}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("selector", AAZStrType, ".selector")
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRulePostArgsConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:PostArgs}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:PostArgs}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_query_string = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:QueryString}")
            if disc_query_string is not None:
                disc_query_string.set_prop("parameters", AAZObjectType, ".query_string.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:QueryString}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleQueryStringConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:QueryString}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:QueryString}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_remote_address = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RemoteAddress}")
            if disc_remote_address is not None:
                disc_remote_address.set_prop("parameters", AAZObjectType, ".remote_address.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RemoteAddress}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRemoteAddressConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RemoteAddress}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RemoteAddress}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_body = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestBody}")
            if disc_request_body is not None:
                disc_request_body.set_prop("parameters", AAZObjectType, ".request_body.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestBody}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestBodyConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestBody}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestBody}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_header = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestHeader}")
            if disc_request_header is not None:
                disc_request_header.set_prop("parameters", AAZObjectType, ".request_header.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestHeader}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("selector", AAZStrType, ".selector")
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestHeaderConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestHeader}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestHeader}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_method = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestMethod}")
            if disc_request_method is not None:
                disc_request_method.set_prop("parameters", AAZObjectType, ".request_method.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestMethod}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestMethodConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestMethod}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestMethod}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_scheme = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestScheme}")
            if disc_request_scheme is not None:
                disc_request_scheme.set_prop("parameters", AAZObjectType, ".request_scheme.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestScheme}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestSchemeConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestScheme}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestScheme}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_request_uri = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestUri}")
            if disc_request_uri is not None:
                disc_request_uri.set_prop("parameters", AAZObjectType, ".request_uri.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestUri}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleRequestUriConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestUri}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:RequestUri}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_server_port = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ServerPort}")
            if disc_server_port is not None:
                disc_server_port.set_prop("parameters", AAZObjectType, ".server_port.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ServerPort}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleServerPortConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ServerPort}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:ServerPort}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_socket_addr = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SocketAddr}")
            if disc_socket_addr is not None:
                disc_socket_addr.set_prop("parameters", AAZObjectType, ".socket_addr.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SocketAddr}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleSocketAddrConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SocketAddr}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SocketAddr}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_ssl_protocol = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SslProtocol}")
            if disc_ssl_protocol is not None:
                disc_ssl_protocol.set_prop("parameters", AAZObjectType, ".ssl_protocol.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SslProtocol}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleSslProtocolConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SslProtocol}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:SslProtocol}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_url_file_extension = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileExtension}")
            if disc_url_file_extension is not None:
                disc_url_file_extension.set_prop("parameters", AAZObjectType, ".url_file_extension.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileExtension}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleUrlFileExtensionMatchConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileExtension}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileExtension}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_url_file_name = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileName}")
            if disc_url_file_name is not None:
                disc_url_file_name.set_prop("parameters", AAZObjectType, ".url_file_name.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileName}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleUrlFilenameConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileName}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlFileName}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            disc_url_path = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlPath}")
            if disc_url_path is not None:
                disc_url_path.set_prop("parameters", AAZObjectType, ".url_path.parameters", typ_kwargs={"flags": {"required": True}})

            parameters = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlPath}.parameters")
            if parameters is not None:
                parameters.set_prop("matchValues", AAZListType, ".match_values")
                parameters.set_prop("negateCondition", AAZBoolType, ".negate_condition")
                parameters.set_prop("operator", AAZStrType, ".operator", typ_kwargs={"flags": {"required": True}})
                parameters.set_prop("transforms", AAZListType, ".transforms")
                parameters.set_const("typeName", "DeliveryRuleUrlPathMatchConditionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})

            match_values = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlPath}.parameters.matchValues")
            if match_values is not None:
                match_values.set_elements(AAZStrType, ".")

            transforms = _builder.get(".properties.deliveryPolicy.rules[].conditions[]{name:UrlPath}.parameters.transforms")
            if transforms is not None:
                transforms.set_elements(AAZStrType, ".")

            geo_filters = _builder.get(".properties.geoFilters")
            if geo_filters is not None:
                geo_filters.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.geoFilters[]")
            if _elements is not None:
                _elements.set_prop("action", AAZStrType, ".action", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("countryCodes", AAZListType, ".country_codes", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("relativePath", AAZStrType, ".relative_path", typ_kwargs={"flags": {"required": True}})

            country_codes = _builder.get(".properties.geoFilters[].countryCodes")
            if country_codes is not None:
                country_codes.set_elements(AAZStrType, ".")

            origin_groups = _builder.get(".properties.originGroups")
            if origin_groups is not None:
                origin_groups.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.originGroups[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.originGroups[].properties")
            if properties is not None:
                properties.set_prop("healthProbeSettings", AAZObjectType, ".health_probe_settings")
                properties.set_prop("origins", AAZListType, ".origins", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("responseBasedOriginErrorDetectionSettings", AAZObjectType, ".response_based_origin_error_detection_settings")
                properties.set_prop("trafficRestorationTimeToHealedOrNewEndpointsInMinutes", AAZIntType, ".traffic_restoration_time_to_healed_or_new_endpoints_in_minutes")

            health_probe_settings = _builder.get(".properties.originGroups[].properties.healthProbeSettings")
            if health_probe_settings is not None:
                health_probe_settings.set_prop("probeIntervalInSeconds", AAZIntType, ".probe_interval_in_seconds")
                health_probe_settings.set_prop("probePath", AAZStrType, ".probe_path")
                health_probe_settings.set_prop("probeProtocol", AAZStrType, ".probe_protocol")
                health_probe_settings.set_prop("probeRequestType", AAZStrType, ".probe_request_type")

            origins = _builder.get(".properties.originGroups[].properties.origins")
            if origins is not None:
                _CreateHelper._build_schema_resource_reference_create(origins.set_elements(AAZObjectType, "."))

            response_based_origin_error_detection_settings = _builder.get(".properties.originGroups[].properties.responseBasedOriginErrorDetectionSettings")
            if response_based_origin_error_detection_settings is not None:
                response_based_origin_error_detection_settings.set_prop("httpErrorRanges", AAZListType, ".http_error_ranges")
                response_based_origin_error_detection_settings.set_prop("responseBasedDetectedErrorTypes", AAZStrType, ".response_based_detected_error_types")
                response_based_origin_error_detection_settings.set_prop("responseBasedFailoverThresholdPercentage", AAZIntType, ".response_based_failover_threshold_percentage")

            http_error_ranges = _builder.get(".properties.originGroups[].properties.responseBasedOriginErrorDetectionSettings.httpErrorRanges")
            if http_error_ranges is not None:
                http_error_ranges.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.originGroups[].properties.responseBasedOriginErrorDetectionSettings.httpErrorRanges[]")
            if _elements is not None:
                _elements.set_prop("begin", AAZIntType, ".begin")
                _elements.set_prop("end", AAZIntType, ".end")

            origins = _builder.get(".properties.origins")
            if origins is not None:
                origins.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.origins[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties.origins[].properties")
            if properties is not None:
                properties.set_prop("enabled", AAZBoolType, ".enabled")
                properties.set_prop("hostName", AAZStrType, ".host_name", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("httpPort", AAZIntType, ".http_port")
                properties.set_prop("httpsPort", AAZIntType, ".https_port")
                properties.set_prop("originHostHeader", AAZStrType, ".origin_host_header")
                properties.set_prop("priority", AAZIntType, ".priority")
                properties.set_prop("privateLinkAlias", AAZStrType, ".private_link_alias")
                properties.set_prop("privateLinkApprovalMessage", AAZStrType, ".private_link_approval_message")
                properties.set_prop("privateLinkLocation", AAZStrType, ".private_link_location")
                properties.set_prop("privateLinkResourceId", AAZStrType, ".private_link_resource_id")
                properties.set_prop("weight", AAZIntType, ".weight")

            url_signing_keys = _builder.get(".properties.urlSigningKeys")
            if url_signing_keys is not None:
                url_signing_keys.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.urlSigningKeys[]")
            if _elements is not None:
                _elements.set_prop("keyId", AAZStrType, ".key_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("keySourceParameters", AAZObjectType, ".key_source_parameters", typ_kwargs={"flags": {"required": True}})

            key_source_parameters = _builder.get(".properties.urlSigningKeys[].keySourceParameters")
            if key_source_parameters is not None:
                key_source_parameters.set_prop("resourceGroupName", AAZStrType, ".resource_group_name", typ_kwargs={"flags": {"required": True}})
                key_source_parameters.set_prop("secretName", AAZStrType, ".secret_name", typ_kwargs={"flags": {"required": True}})
                key_source_parameters.set_prop("secretVersion", AAZStrType, ".secret_version", typ_kwargs={"flags": {"required": True}})
                key_source_parameters.set_prop("subscriptionId", AAZStrType, ".subscription_id", typ_kwargs={"flags": {"required": True}})
                key_source_parameters.set_prop("typeName", AAZStrType, ".type_name", typ_kwargs={"flags": {"required": True}})
                key_source_parameters.set_prop("vaultName", AAZStrType, ".vault_name", typ_kwargs={"flags": {"required": True}})

            web_application_firewall_policy_link = _builder.get(".properties.webApplicationFirewallPolicyLink")
            if web_application_firewall_policy_link is not None:
                web_application_firewall_policy_link.set_prop("id", AAZStrType, ".id")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _CreateHelper._build_schema_endpoint_read(cls._schema_on_200_201)

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""

    @classmethod
    def _build_schema_header_action_parameters_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("headerAction", AAZStrType, ".header_action", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("headerName", AAZStrType, ".header_name", typ_kwargs={"flags": {"required": True}})
        _builder.set_const("typeName", "DeliveryRuleHeaderActionParameters", AAZStrType, ".", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("value", AAZStrType, ".value")

    @classmethod
    def _build_schema_resource_reference_create(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("id", AAZStrType, ".id")

    _schema_endpoint_read = None

    @classmethod
    def _build_schema_endpoint_read(cls, _schema):
        if cls._schema_endpoint_read is not None:
            _schema.id = cls._schema_endpoint_read.id
            _schema.location = cls._schema_endpoint_read.location
            _schema.name = cls._schema_endpoint_read.name
            _schema.properties = cls._schema_endpoint_read.properties
            _schema.system_data = cls._schema_endpoint_read.system_data
            _schema.tags = cls._schema_endpoint_read.tags
            _schema.type = cls._schema_endpoint_read.type
            return

        cls._schema_endpoint_read = _schema_endpoint_read = AAZObjectType()

        endpoint_read = _schema_endpoint_read
        endpoint_read.id = AAZStrType(
            flags={"read_only": True},
        )
        endpoint_read.location = AAZStrType(
            flags={"required": True},
        )
        endpoint_read.name = AAZStrType(
            flags={"read_only": True},
        )
        endpoint_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        endpoint_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        endpoint_read.tags = AAZDictType()
        endpoint_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_endpoint_read.properties
        properties.content_types_to_compress = AAZListType(
            serialized_name="contentTypesToCompress",
        )
        properties.custom_domains = AAZListType(
            serialized_name="customDomains",
            flags={"read_only": True},
        )
        properties.default_origin_group = AAZObjectType(
            serialized_name="defaultOriginGroup",
        )
        cls._build_schema_resource_reference_read(properties.default_origin_group)
        properties.delivery_policy = AAZObjectType(
            serialized_name="deliveryPolicy",
        )
        properties.geo_filters = AAZListType(
            serialized_name="geoFilters",
        )
        properties.host_name = AAZStrType(
            serialized_name="hostName",
            flags={"read_only": True},
        )
        properties.is_compression_enabled = AAZBoolType(
            serialized_name="isCompressionEnabled",
        )
        properties.is_http_allowed = AAZBoolType(
            serialized_name="isHttpAllowed",
        )
        properties.is_https_allowed = AAZBoolType(
            serialized_name="isHttpsAllowed",
        )
        properties.optimization_type = AAZStrType(
            serialized_name="optimizationType",
        )
        properties.origin_groups = AAZListType(
            serialized_name="originGroups",
        )
        properties.origin_host_header = AAZStrType(
            serialized_name="originHostHeader",
        )
        properties.origin_path = AAZStrType(
            serialized_name="originPath",
        )
        properties.origins = AAZListType(
            flags={"required": True},
        )
        properties.probe_path = AAZStrType(
            serialized_name="probePath",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.query_string_caching_behavior = AAZStrType(
            serialized_name="queryStringCachingBehavior",
        )
        properties.resource_state = AAZStrType(
            serialized_name="resourceState",
            flags={"read_only": True},
        )
        properties.url_signing_keys = AAZListType(
            serialized_name="urlSigningKeys",
        )
        properties.web_application_firewall_policy_link = AAZObjectType(
            serialized_name="webApplicationFirewallPolicyLink",
        )

        content_types_to_compress = _schema_endpoint_read.properties.content_types_to_compress
        content_types_to_compress.Element = AAZStrType()

        custom_domains = _schema_endpoint_read.properties.custom_domains
        custom_domains.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.custom_domains.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_endpoint_read.properties.custom_domains.Element.properties
        properties.host_name = AAZStrType(
            serialized_name="hostName",
            flags={"required": True},
        )
        properties.validation_data = AAZStrType(
            serialized_name="validationData",
        )

        delivery_policy = _schema_endpoint_read.properties.delivery_policy
        delivery_policy.description = AAZStrType()
        delivery_policy.rules = AAZListType(
            flags={"required": True},
        )

        rules = _schema_endpoint_read.properties.delivery_policy.rules
        rules.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element
        _element.actions = AAZListType(
            flags={"required": True},
        )
        _element.conditions = AAZListType()
        _element.name = AAZStrType()
        _element.order = AAZIntType(
            flags={"required": True},
        )

        actions = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions
        actions.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )

        disc_cache_expiration = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheExpiration")
        disc_cache_expiration.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheExpiration").parameters
        parameters.cache_behavior = AAZStrType(
            serialized_name="cacheBehavior",
            flags={"required": True},
        )
        parameters.cache_duration = AAZStrType(
            serialized_name="cacheDuration",
            nullable=True,
        )
        parameters.cache_type = AAZStrType(
            serialized_name="cacheType",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_cache_key_query_string = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheKeyQueryString")
        disc_cache_key_query_string.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "CacheKeyQueryString").parameters
        parameters.query_parameters = AAZStrType(
            serialized_name="queryParameters",
            nullable=True,
        )
        parameters.query_string_behavior = AAZStrType(
            serialized_name="queryStringBehavior",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_modify_request_header = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "ModifyRequestHeader")
        disc_modify_request_header.parameters = AAZObjectType(
            flags={"required": True},
        )
        cls._build_schema_header_action_parameters_read(disc_modify_request_header.parameters)

        disc_modify_response_header = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "ModifyResponseHeader")
        disc_modify_response_header.parameters = AAZObjectType(
            flags={"required": True},
        )
        cls._build_schema_header_action_parameters_read(disc_modify_response_header.parameters)

        disc_origin_group_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "OriginGroupOverride")
        disc_origin_group_override.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "OriginGroupOverride").parameters
        parameters.origin_group = AAZObjectType(
            serialized_name="originGroup",
            flags={"required": True},
        )
        cls._build_schema_resource_reference_read(parameters.origin_group)
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_route_configuration_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride")
        disc_route_configuration_override.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters
        parameters.cache_configuration = AAZObjectType(
            serialized_name="cacheConfiguration",
        )
        parameters.origin_group_override = AAZObjectType(
            serialized_name="originGroupOverride",
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        cache_configuration = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.cache_configuration
        cache_configuration.cache_behavior = AAZStrType(
            serialized_name="cacheBehavior",
        )
        cache_configuration.cache_duration = AAZStrType(
            serialized_name="cacheDuration",
        )
        cache_configuration.is_compression_enabled = AAZStrType(
            serialized_name="isCompressionEnabled",
        )
        cache_configuration.query_parameters = AAZStrType(
            serialized_name="queryParameters",
        )
        cache_configuration.query_string_caching_behavior = AAZStrType(
            serialized_name="queryStringCachingBehavior",
        )

        origin_group_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "RouteConfigurationOverride").parameters.origin_group_override
        origin_group_override.forwarding_protocol = AAZStrType(
            serialized_name="forwardingProtocol",
        )
        origin_group_override.origin_group = AAZObjectType(
            serialized_name="originGroup",
        )
        cls._build_schema_resource_reference_read(origin_group_override.origin_group)

        disc_url_redirect = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRedirect")
        disc_url_redirect.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRedirect").parameters
        parameters.custom_fragment = AAZStrType(
            serialized_name="customFragment",
        )
        parameters.custom_hostname = AAZStrType(
            serialized_name="customHostname",
        )
        parameters.custom_path = AAZStrType(
            serialized_name="customPath",
        )
        parameters.custom_query_string = AAZStrType(
            serialized_name="customQueryString",
        )
        parameters.destination_protocol = AAZStrType(
            serialized_name="destinationProtocol",
        )
        parameters.redirect_type = AAZStrType(
            serialized_name="redirectType",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_url_rewrite = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRewrite")
        disc_url_rewrite.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlRewrite").parameters
        parameters.destination = AAZStrType(
            flags={"required": True},
        )
        parameters.preserve_unmatched_path = AAZBoolType(
            serialized_name="preserveUnmatchedPath",
        )
        parameters.source_pattern = AAZStrType(
            serialized_name="sourcePattern",
            flags={"required": True},
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        disc_url_signing = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning")
        disc_url_signing.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning").parameters
        parameters.algorithm = AAZStrType()
        parameters.parameter_name_override = AAZListType(
            serialized_name="parameterNameOverride",
        )
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        parameter_name_override = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override
        parameter_name_override.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element.actions.Element.discriminate_by("name", "UrlSigning").parameters.parameter_name_override.Element
        _element.param_indicator = AAZStrType(
            serialized_name="paramIndicator",
            flags={"required": True},
        )
        _element.param_name = AAZStrType(
            serialized_name="paramName",
            flags={"required": True},
        )

        conditions = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions
        conditions.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )

        disc_client_port = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort")
        disc_client_port.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ClientPort").parameters.transforms
        transforms.Element = AAZStrType()

        disc_cookies = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies")
        disc_cookies.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "Cookies").parameters.transforms
        transforms.Element = AAZStrType()

        disc_host_name = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName")
        disc_host_name.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HostName").parameters.transforms
        transforms.Element = AAZStrType()

        disc_http_version = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion")
        disc_http_version.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "HttpVersion").parameters.transforms
        transforms.Element = AAZStrType()

        disc_is_device = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice")
        disc_is_device.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "IsDevice").parameters.transforms
        transforms.Element = AAZStrType()

        disc_post_args = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs")
        disc_post_args.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "PostArgs").parameters.transforms
        transforms.Element = AAZStrType()

        disc_query_string = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString")
        disc_query_string.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "QueryString").parameters.transforms
        transforms.Element = AAZStrType()

        disc_remote_address = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress")
        disc_remote_address.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RemoteAddress").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_body = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody")
        disc_request_body.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestBody").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_header = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader")
        disc_request_header.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.selector = AAZStrType()
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestHeader").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_method = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod")
        disc_request_method.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestMethod").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_scheme = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme")
        disc_request_scheme.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestScheme").parameters.transforms
        transforms.Element = AAZStrType()

        disc_request_uri = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri")
        disc_request_uri.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "RequestUri").parameters.transforms
        transforms.Element = AAZStrType()

        disc_server_port = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort")
        disc_server_port.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "ServerPort").parameters.transforms
        transforms.Element = AAZStrType()

        disc_socket_addr = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr")
        disc_socket_addr.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SocketAddr").parameters.transforms
        transforms.Element = AAZStrType()

        disc_ssl_protocol = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol")
        disc_ssl_protocol.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "SslProtocol").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_file_extension = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension")
        disc_url_file_extension.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileExtension").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_file_name = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName")
        disc_url_file_name.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlFileName").parameters.transforms
        transforms.Element = AAZStrType()

        disc_url_path = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath")
        disc_url_path.parameters = AAZObjectType(
            flags={"required": True},
        )

        parameters = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath").parameters
        parameters.match_values = AAZListType(
            serialized_name="matchValues",
        )
        parameters.negate_condition = AAZBoolType(
            serialized_name="negateCondition",
        )
        parameters.operator = AAZStrType(
            flags={"required": True},
        )
        parameters.transforms = AAZListType()
        parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )

        match_values = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath").parameters.match_values
        match_values.Element = AAZStrType()

        transforms = _schema_endpoint_read.properties.delivery_policy.rules.Element.conditions.Element.discriminate_by("name", "UrlPath").parameters.transforms
        transforms.Element = AAZStrType()

        geo_filters = _schema_endpoint_read.properties.geo_filters
        geo_filters.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.geo_filters.Element
        _element.action = AAZStrType(
            flags={"required": True},
        )
        _element.country_codes = AAZListType(
            serialized_name="countryCodes",
            flags={"required": True},
        )
        _element.relative_path = AAZStrType(
            serialized_name="relativePath",
            flags={"required": True},
        )

        country_codes = _schema_endpoint_read.properties.geo_filters.Element.country_codes
        country_codes.Element = AAZStrType()

        origin_groups = _schema_endpoint_read.properties.origin_groups
        origin_groups.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.origin_groups.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_endpoint_read.properties.origin_groups.Element.properties
        properties.health_probe_settings = AAZObjectType(
            serialized_name="healthProbeSettings",
        )
        properties.origins = AAZListType(
            flags={"required": True},
        )
        properties.response_based_origin_error_detection_settings = AAZObjectType(
            serialized_name="responseBasedOriginErrorDetectionSettings",
        )
        properties.traffic_restoration_time_to_healed_or_new_endpoints_in_minutes = AAZIntType(
            serialized_name="trafficRestorationTimeToHealedOrNewEndpointsInMinutes",
        )

        health_probe_settings = _schema_endpoint_read.properties.origin_groups.Element.properties.health_probe_settings
        health_probe_settings.probe_interval_in_seconds = AAZIntType(
            serialized_name="probeIntervalInSeconds",
        )
        health_probe_settings.probe_path = AAZStrType(
            serialized_name="probePath",
        )
        health_probe_settings.probe_protocol = AAZStrType(
            serialized_name="probeProtocol",
        )
        health_probe_settings.probe_request_type = AAZStrType(
            serialized_name="probeRequestType",
        )

        origins = _schema_endpoint_read.properties.origin_groups.Element.properties.origins
        origins.Element = AAZObjectType()
        cls._build_schema_resource_reference_read(origins.Element)

        response_based_origin_error_detection_settings = _schema_endpoint_read.properties.origin_groups.Element.properties.response_based_origin_error_detection_settings
        response_based_origin_error_detection_settings.http_error_ranges = AAZListType(
            serialized_name="httpErrorRanges",
        )
        response_based_origin_error_detection_settings.response_based_detected_error_types = AAZStrType(
            serialized_name="responseBasedDetectedErrorTypes",
        )
        response_based_origin_error_detection_settings.response_based_failover_threshold_percentage = AAZIntType(
            serialized_name="responseBasedFailoverThresholdPercentage",
        )

        http_error_ranges = _schema_endpoint_read.properties.origin_groups.Element.properties.response_based_origin_error_detection_settings.http_error_ranges
        http_error_ranges.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.origin_groups.Element.properties.response_based_origin_error_detection_settings.http_error_ranges.Element
        _element.begin = AAZIntType()
        _element.end = AAZIntType()

        origins = _schema_endpoint_read.properties.origins
        origins.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.origins.Element
        _element.name = AAZStrType(
            flags={"required": True},
        )
        _element.properties = AAZObjectType(
            flags={"client_flatten": True},
        )

        properties = _schema_endpoint_read.properties.origins.Element.properties
        properties.enabled = AAZBoolType()
        properties.host_name = AAZStrType(
            serialized_name="hostName",
            flags={"required": True},
        )
        properties.http_port = AAZIntType(
            serialized_name="httpPort",
        )
        properties.https_port = AAZIntType(
            serialized_name="httpsPort",
        )
        properties.origin_host_header = AAZStrType(
            serialized_name="originHostHeader",
        )
        properties.priority = AAZIntType()
        properties.private_endpoint_status = AAZStrType(
            serialized_name="privateEndpointStatus",
            flags={"read_only": True},
        )
        properties.private_link_alias = AAZStrType(
            serialized_name="privateLinkAlias",
        )
        properties.private_link_approval_message = AAZStrType(
            serialized_name="privateLinkApprovalMessage",
        )
        properties.private_link_location = AAZStrType(
            serialized_name="privateLinkLocation",
        )
        properties.private_link_resource_id = AAZStrType(
            serialized_name="privateLinkResourceId",
        )
        properties.weight = AAZIntType()

        url_signing_keys = _schema_endpoint_read.properties.url_signing_keys
        url_signing_keys.Element = AAZObjectType()

        _element = _schema_endpoint_read.properties.url_signing_keys.Element
        _element.key_id = AAZStrType(
            serialized_name="keyId",
            flags={"required": True},
        )
        _element.key_source_parameters = AAZObjectType(
            serialized_name="keySourceParameters",
            flags={"required": True},
        )

        key_source_parameters = _schema_endpoint_read.properties.url_signing_keys.Element.key_source_parameters
        key_source_parameters.resource_group_name = AAZStrType(
            serialized_name="resourceGroupName",
            flags={"required": True},
        )
        key_source_parameters.secret_name = AAZStrType(
            serialized_name="secretName",
            flags={"required": True},
        )
        key_source_parameters.secret_version = AAZStrType(
            serialized_name="secretVersion",
            flags={"required": True},
        )
        key_source_parameters.subscription_id = AAZStrType(
            serialized_name="subscriptionId",
            flags={"required": True},
        )
        key_source_parameters.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )
        key_source_parameters.vault_name = AAZStrType(
            serialized_name="vaultName",
            flags={"required": True},
        )

        web_application_firewall_policy_link = _schema_endpoint_read.properties.web_application_firewall_policy_link
        web_application_firewall_policy_link.id = AAZStrType()

        system_data = _schema_endpoint_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_endpoint_read.tags
        tags.Element = AAZStrType()

        _schema.id = cls._schema_endpoint_read.id
        _schema.location = cls._schema_endpoint_read.location
        _schema.name = cls._schema_endpoint_read.name
        _schema.properties = cls._schema_endpoint_read.properties
        _schema.system_data = cls._schema_endpoint_read.system_data
        _schema.tags = cls._schema_endpoint_read.tags
        _schema.type = cls._schema_endpoint_read.type

    _schema_header_action_parameters_read = None

    @classmethod
    def _build_schema_header_action_parameters_read(cls, _schema):
        if cls._schema_header_action_parameters_read is not None:
            _schema.header_action = cls._schema_header_action_parameters_read.header_action
            _schema.header_name = cls._schema_header_action_parameters_read.header_name
            _schema.type_name = cls._schema_header_action_parameters_read.type_name
            _schema.value = cls._schema_header_action_parameters_read.value
            return

        cls._schema_header_action_parameters_read = _schema_header_action_parameters_read = AAZObjectType()

        header_action_parameters_read = _schema_header_action_parameters_read
        header_action_parameters_read.header_action = AAZStrType(
            serialized_name="headerAction",
            flags={"required": True},
        )
        header_action_parameters_read.header_name = AAZStrType(
            serialized_name="headerName",
            flags={"required": True},
        )
        header_action_parameters_read.type_name = AAZStrType(
            serialized_name="typeName",
            flags={"required": True},
        )
        header_action_parameters_read.value = AAZStrType()

        _schema.header_action = cls._schema_header_action_parameters_read.header_action
        _schema.header_name = cls._schema_header_action_parameters_read.header_name
        _schema.type_name = cls._schema_header_action_parameters_read.type_name
        _schema.value = cls._schema_header_action_parameters_read.value

    _schema_resource_reference_read = None

    @classmethod
    def _build_schema_resource_reference_read(cls, _schema):
        if cls._schema_resource_reference_read is not None:
            _schema.id = cls._schema_resource_reference_read.id
            return

        cls._schema_resource_reference_read = _schema_resource_reference_read = AAZObjectType()

        resource_reference_read = _schema_resource_reference_read
        resource_reference_read.id = AAZStrType()

        _schema.id = cls._schema_resource_reference_read.id


__all__ = ["Create"]
