# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "afd route wait",
)
class Wait(AAZWaitCommand):
    """Place the CLI in a waiting state until a condition is met.
    """

    _aaz_info = {
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.cdn/profiles/{}/afdendpoints/{}/routes/{}", "2025-06-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.endpoint_name = AAZStrArg(
            options=["--endpoint-name"],
            help="Name of the endpoint under the profile which is unique globally.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.profile_name = AAZStrArg(
            options=["--profile-name"],
            help="Name of the Azure Front Door Standard or Azure Front Door Premium profile which is unique within the resource group.",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9]+(-*[a-zA-Z0-9])*$",
                max_length=260,
                min_length=1,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.route_name = AAZStrArg(
            options=["-n", "--name", "--route-name"],
            help="Name of the routing rule.",
            required=True,
            id_part="child_name_2",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.RoutesGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=False)
        return result

    class RoutesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Cdn/profiles/{profileName}/afdEndpoints/{endpointName}/routes/{routeName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "endpointName", self.ctx.args.endpoint_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "profileName", self.ctx.args.profile_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "routeName", self.ctx.args.route_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-06-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.cache_configuration = AAZObjectType(
                serialized_name="cacheConfiguration",
            )
            properties.custom_domains = AAZListType(
                serialized_name="customDomains",
            )
            properties.deployment_status = AAZStrType(
                serialized_name="deploymentStatus",
                flags={"read_only": True},
            )
            properties.enabled_state = AAZStrType(
                serialized_name="enabledState",
            )
            properties.endpoint_name = AAZStrType(
                serialized_name="endpointName",
                flags={"read_only": True},
            )
            properties.forwarding_protocol = AAZStrType(
                serialized_name="forwardingProtocol",
            )
            properties.https_redirect = AAZStrType(
                serialized_name="httpsRedirect",
            )
            properties.link_to_default_domain = AAZStrType(
                serialized_name="linkToDefaultDomain",
            )
            properties.origin_group = AAZObjectType(
                serialized_name="originGroup",
                flags={"required": True},
            )
            _WaitHelper._build_schema_resource_reference_read(properties.origin_group)
            properties.origin_path = AAZStrType(
                serialized_name="originPath",
            )
            properties.patterns_to_match = AAZListType(
                serialized_name="patternsToMatch",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.rule_sets = AAZListType(
                serialized_name="ruleSets",
            )
            properties.supported_protocols = AAZListType(
                serialized_name="supportedProtocols",
            )

            cache_configuration = cls._schema_on_200.properties.cache_configuration
            cache_configuration.compression_settings = AAZObjectType(
                serialized_name="compressionSettings",
            )
            cache_configuration.query_parameters = AAZStrType(
                serialized_name="queryParameters",
            )
            cache_configuration.query_string_caching_behavior = AAZStrType(
                serialized_name="queryStringCachingBehavior",
            )

            compression_settings = cls._schema_on_200.properties.cache_configuration.compression_settings
            compression_settings.content_types_to_compress = AAZListType(
                serialized_name="contentTypesToCompress",
            )
            compression_settings.is_compression_enabled = AAZBoolType(
                serialized_name="isCompressionEnabled",
            )

            content_types_to_compress = cls._schema_on_200.properties.cache_configuration.compression_settings.content_types_to_compress
            content_types_to_compress.Element = AAZStrType()

            custom_domains = cls._schema_on_200.properties.custom_domains
            custom_domains.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.custom_domains.Element
            _element.id = AAZStrType()
            _element.is_active = AAZBoolType(
                serialized_name="isActive",
                flags={"read_only": True},
            )

            patterns_to_match = cls._schema_on_200.properties.patterns_to_match
            patterns_to_match.Element = AAZStrType()

            rule_sets = cls._schema_on_200.properties.rule_sets
            rule_sets.Element = AAZObjectType()
            _WaitHelper._build_schema_resource_reference_read(rule_sets.Element)

            supported_protocols = cls._schema_on_200.properties.supported_protocols
            supported_protocols.Element = AAZStrType()

            system_data = cls._schema_on_200.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200


class _WaitHelper:
    """Helper class for Wait"""

    _schema_resource_reference_read = None

    @classmethod
    def _build_schema_resource_reference_read(cls, _schema):
        if cls._schema_resource_reference_read is not None:
            _schema.id = cls._schema_resource_reference_read.id
            return

        cls._schema_resource_reference_read = _schema_resource_reference_read = AAZObjectType()

        resource_reference_read = _schema_resource_reference_read
        resource_reference_read.id = AAZStrType()

        _schema.id = cls._schema_resource_reference_read.id


__all__ = ["Wait"]
