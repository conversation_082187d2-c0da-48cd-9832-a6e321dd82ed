# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "policy assignment non-compliance-message",
)
class __CMDGroup(AAZCommandGroup):
    """Non-compliance message used by the policy assignment.

    Customized message used by the enclosing policy assignment to report non-compliance.
    """
    pass


__all__ = ["__CMDGroup"]
