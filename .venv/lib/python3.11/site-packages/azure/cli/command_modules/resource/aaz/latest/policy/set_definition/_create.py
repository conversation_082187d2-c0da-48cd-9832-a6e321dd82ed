# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "policy set-definition create",
)
class Create(AAZCommand):
    """Create a policy set definition.

    Create a policy set definition in the given subscription or management group with the given name and other properties.

    :example: Create a policy set definition
        az policy set-definition create -n readOnlyStorage --definitions '[ { 'policyDefinitionId': '/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/policyDefinitions/{policyDefinitionName}' } ]'

    :example: Create a policy set definition with parameters
        az policy set-definition create -n readOnlyStorage --definitions "[ { 'policyDefinitionId': '/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/policyDefinitions/{policyDefinitionName}', 'parameters': { 'storageSku': { 'value': '[parameters(\\'requiredSku\\')]' } } }]" --params "{ 'requiredSku': { 'type': 'String' } }"

    :example: Create a policy set definition in a subscription
        az policy set-definition create -n readOnlyStorage --subscription {subscriptionName} --definitions "[ { 'policyDefinitionId': '/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/policyDefinitions/{policyDefinitionName}' } ]"

    :example: Create a policy set definition with policy definition groups
        az policy set-definition create -n computeRequirements --definitions "[ { 'policyDefinitionId ': '/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/policyDefinitions/storagePolicy', 'groupNames': [ 'CostSaving', 'Organizational' ] }, { 'policyDefinitionId': '/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/policyDefinitions/tagPolicy', 'groupNames': [ 'Organizational' ] } ]" --definition-groups "[{ 'name': 'CostSaving' }, { 'name': 'Organizational' } ]"
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/providers/microsoft.management/managementgroups/{}/providers/microsoft.authorization/policysetdefinitions/{}", "2024-05-01"],
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.authorization/policysetdefinitions/{}", "2024-05-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.management_group = AAZStrArg(
            options=["--management-group"],
            help={"short-summary": "The management group.", "long-summary": "The management group with the given name is where the policy definition will reside. It can be assigned only at scopes at or below this management group."},
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The name of the policy set definition.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[^<>*%&:\\?.+/]*[^<>*%&:\\?.+/ ]+$",
            ),
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.description = AAZStrArg(
            options=["--description"],
            arg_group="Properties",
            help={"short-summary": "Policy set definition description.", "long-summary": "Full description of the policy set definition."},
        )
        _args_schema.display_name = AAZStrArg(
            options=["--display-name"],
            arg_group="Properties",
            help={"short-summary": "The display name of the policy set definition.", "long-summary": "The display name of the policy set definition is not part of its ID, allowing for longer and more flexible naming."},
        )
        _args_schema.metadata = AAZDictArg(
            options=["--metadata"],
            arg_group="Properties",
            help={"short-summary": "The policy set definition metadata.", "long-summary": "The policy set definition metadata. Metadata is an open-ended object and is typically a collection of key value pairs."},
        )
        _args_schema.params = AAZDictArg(
            options=["-p", "--params"],
            arg_group="Properties",
            help={"short-summary": "The policy set definition parameter definitions.", "long-summary": "The definitions for parameters used in the policy rule. The keys are the parameter names."},
        )
        _args_schema.definition_groups = AAZListArg(
            options=["--definition-groups"],
            arg_group="Properties",
            help="The metadata describing groups of policy definition references within the policy set definition.",
        )
        _args_schema.definitions = AAZListArg(
            options=["--definitions"],
            arg_group="Properties",
            help="An array of policy definition references.",
        )
        _args_schema.version = AAZStrArg(
            options=["--version"],
            arg_group="Properties",
            help={"short-summary": "The policy set definition version.", "long-summary": "The policy set definition version in #.#.# format."},
        )

        metadata = cls._args_schema.metadata
        metadata.Element = AAZAnyTypeArg()

        params = cls._args_schema.params
        params.Element = AAZObjectArg()

        _element = cls._args_schema.params.Element
        _element.allowed_values = AAZListArg(
            options=["allowed-values"],
            help="The allowed values for the parameter.",
        )
        _element.default_value = AAZAnyTypeArg(
            options=["default-value"],
            help="The default value for the parameter if no value is provided.",
        )
        _element.metadata = AAZFreeFormDictArg(
            options=["metadata"],
            help="General metadata for the parameter.",
        )
        _element.schema = AAZDictArg(
            options=["schema"],
            help="Provides validation of parameter inputs during assignment using a self-defined JSON schema. This property is only supported for object-type parameters and follows the Json.NET Schema 2019-09 implementation. You can learn more about using schemas at https://json-schema.org/ and test draft schemas at https://www.jsonschemavalidator.net/.",
        )
        _element.type = AAZStrArg(
            options=["type"],
            help="The data type of the parameter.",
            enum={"Array": "Array", "Boolean": "Boolean", "DateTime": "DateTime", "Float": "Float", "Integer": "Integer", "Object": "Object", "String": "String"},
        )

        allowed_values = cls._args_schema.params.Element.allowed_values
        allowed_values.Element = AAZAnyTypeArg()

        schema = cls._args_schema.params.Element.schema
        schema.Element = AAZAnyTypeArg()

        definition_groups = cls._args_schema.definition_groups
        definition_groups.Element = AAZObjectArg()

        _element = cls._args_schema.definition_groups.Element
        _element.additional_metadata_id = AAZStrArg(
            options=["additional-metadata-id"],
            help="A resource ID of a resource that contains additional metadata about the group.",
        )
        _element.category = AAZStrArg(
            options=["category"],
            help="The group's category.",
        )
        _element.description = AAZStrArg(
            options=["description"],
            help="The group's description.",
        )
        _element.display_name = AAZStrArg(
            options=["display-name"],
            help="The group's display name.",
        )
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the group.",
            required=True,
        )

        definitions = cls._args_schema.definitions
        definitions.Element = AAZObjectArg()

        _element = cls._args_schema.definitions.Element
        _element.definition_version = AAZStrArg(
            options=["definition-version"],
            help="The version of the policy definition to use.",
        )
        _element.group_names = AAZListArg(
            options=["group-names"],
            help="The name of the groups that this policy definition reference belongs to.",
        )
        _element.parameters = AAZDictArg(
            options=["parameters"],
            help="The parameter values for the referenced policy rule. The keys are the parameter names.",
        )
        _element.policy_definition_id = AAZStrArg(
            options=["policy-definition-id"],
            help="The ID of the policy definition or policy set definition.",
            required=True,
        )
        _element.policy_definition_reference_id = AAZStrArg(
            options=["policy-definition-reference-id"],
            help="A unique id (within the policy set definition) for this policy definition reference.",
        )

        group_names = cls._args_schema.definitions.Element.group_names
        group_names.Element = AAZStrArg()

        parameters = cls._args_schema.definitions.Element.parameters
        parameters.Element = AAZObjectArg()

        _element = cls._args_schema.definitions.Element.parameters.Element
        _element.value = AAZAnyTypeArg(
            options=["value"],
            help="The value of the parameter.",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        condition_0 = has_value(self.ctx.args.management_group) and has_value(self.ctx.args.name)
        condition_1 = has_value(self.ctx.args.name) and has_value(self.ctx.subscription_id)
        if condition_0:
            self.PolicySetDefinitionsCreateOrUpdateAtManagementGroup(ctx=self.ctx)()
        if condition_1:
            self.PolicySetDefinitionsCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class PolicySetDefinitionsCreateOrUpdateAtManagementGroup(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200, 201]:
                return self.on_200_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/providers/Microsoft.Management/managementGroups/{managementGroupId}/providers/Microsoft.Authorization/policySetDefinitions/{policySetDefinitionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "managementGroupId", self.ctx.args.management_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "policySetDefinitionName", self.ctx.args.name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("displayName", AAZStrType, ".display_name")
                properties.set_prop("metadata", AAZDictType, ".metadata")
                properties.set_prop("parameters", AAZDictType, ".params")
                properties.set_prop("policyDefinitionGroups", AAZListType, ".definition_groups")
                properties.set_prop("policyDefinitions", AAZListType, ".definitions", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("version", AAZStrType, ".version")

            metadata = _builder.get(".properties.metadata")
            if metadata is not None:
                metadata.set_elements(AAZAnyType, ".")

            parameters = _builder.get(".properties.parameters")
            if parameters is not None:
                parameters.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.parameters{}")
            if _elements is not None:
                _elements.set_prop("allowedValues", AAZListType, ".allowed_values")
                _elements.set_prop("defaultValue", AAZAnyType, ".default_value")
                _elements.set_prop("metadata", AAZFreeFormDictType, ".metadata")
                _elements.set_prop("schema", AAZDictType, ".schema")
                _elements.set_prop("type", AAZStrType, ".type")

            allowed_values = _builder.get(".properties.parameters{}.allowedValues")
            if allowed_values is not None:
                allowed_values.set_elements(AAZAnyType, ".")

            metadata = _builder.get(".properties.parameters{}.metadata")
            if metadata is not None:
                metadata.set_anytype_elements(".")

            schema = _builder.get(".properties.parameters{}.schema")
            if schema is not None:
                schema.set_elements(AAZAnyType, ".")

            policy_definition_groups = _builder.get(".properties.policyDefinitionGroups")
            if policy_definition_groups is not None:
                policy_definition_groups.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.policyDefinitionGroups[]")
            if _elements is not None:
                _elements.set_prop("additionalMetadataId", AAZStrType, ".additional_metadata_id")
                _elements.set_prop("category", AAZStrType, ".category")
                _elements.set_prop("description", AAZStrType, ".description")
                _elements.set_prop("displayName", AAZStrType, ".display_name")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})

            policy_definitions = _builder.get(".properties.policyDefinitions")
            if policy_definitions is not None:
                policy_definitions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.policyDefinitions[]")
            if _elements is not None:
                _elements.set_prop("definitionVersion", AAZStrType, ".definition_version")
                _elements.set_prop("groupNames", AAZListType, ".group_names")
                _elements.set_prop("parameters", AAZDictType, ".parameters")
                _elements.set_prop("policyDefinitionId", AAZStrType, ".policy_definition_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("policyDefinitionReferenceId", AAZStrType, ".policy_definition_reference_id")

            group_names = _builder.get(".properties.policyDefinitions[].groupNames")
            if group_names is not None:
                group_names.set_elements(AAZStrType, ".")

            parameters = _builder.get(".properties.policyDefinitions[].parameters")
            if parameters is not None:
                parameters.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.policyDefinitions[].parameters{}")
            if _elements is not None:
                _elements.set_prop("value", AAZAnyType, ".value")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200_201.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200_201.properties
            properties.description = AAZStrType()
            properties.display_name = AAZStrType(
                serialized_name="displayName",
            )
            properties.metadata = AAZDictType()
            properties.parameters = AAZDictType()
            properties.policy_definition_groups = AAZListType(
                serialized_name="policyDefinitionGroups",
            )
            properties.policy_definitions = AAZListType(
                serialized_name="policyDefinitions",
                flags={"required": True},
            )
            properties.policy_type = AAZStrType(
                serialized_name="policyType",
            )
            properties.version = AAZStrType()
            properties.versions = AAZListType()

            metadata = cls._schema_on_200_201.properties.metadata
            metadata.Element = AAZAnyType()

            parameters = cls._schema_on_200_201.properties.parameters
            parameters.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.parameters.Element
            _element.allowed_values = AAZListType(
                serialized_name="allowedValues",
            )
            _element.default_value = AAZAnyType(
                serialized_name="defaultValue",
            )
            _element.metadata = AAZFreeFormDictType()
            _element.schema = AAZDictType()
            _element.type = AAZStrType()

            allowed_values = cls._schema_on_200_201.properties.parameters.Element.allowed_values
            allowed_values.Element = AAZAnyType()

            schema = cls._schema_on_200_201.properties.parameters.Element.schema
            schema.Element = AAZAnyType()

            policy_definition_groups = cls._schema_on_200_201.properties.policy_definition_groups
            policy_definition_groups.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.policy_definition_groups.Element
            _element.additional_metadata_id = AAZStrType(
                serialized_name="additionalMetadataId",
            )
            _element.category = AAZStrType()
            _element.description = AAZStrType()
            _element.display_name = AAZStrType(
                serialized_name="displayName",
            )
            _element.name = AAZStrType(
                flags={"required": True},
            )

            policy_definitions = cls._schema_on_200_201.properties.policy_definitions
            policy_definitions.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.policy_definitions.Element
            _element.definition_version = AAZStrType(
                serialized_name="definitionVersion",
            )
            _element.effective_definition_version = AAZStrType(
                serialized_name="effectiveDefinitionVersion",
                flags={"read_only": True},
            )
            _element.group_names = AAZListType(
                serialized_name="groupNames",
            )
            _element.latest_definition_version = AAZStrType(
                serialized_name="latestDefinitionVersion",
                flags={"read_only": True},
            )
            _element.parameters = AAZDictType()
            _element.policy_definition_id = AAZStrType(
                serialized_name="policyDefinitionId",
                flags={"required": True},
            )
            _element.policy_definition_reference_id = AAZStrType(
                serialized_name="policyDefinitionReferenceId",
            )

            group_names = cls._schema_on_200_201.properties.policy_definitions.Element.group_names
            group_names.Element = AAZStrType()

            parameters = cls._schema_on_200_201.properties.policy_definitions.Element.parameters
            parameters.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.policy_definitions.Element.parameters.Element
            _element.value = AAZAnyType()

            versions = cls._schema_on_200_201.properties.versions
            versions.Element = AAZStrType()

            system_data = cls._schema_on_200_201.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200_201

    class PolicySetDefinitionsCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200, 201]:
                return self.on_200_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/policySetDefinitions/{policySetDefinitionName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "policySetDefinitionName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("displayName", AAZStrType, ".display_name")
                properties.set_prop("metadata", AAZDictType, ".metadata")
                properties.set_prop("parameters", AAZDictType, ".params")
                properties.set_prop("policyDefinitionGroups", AAZListType, ".definition_groups")
                properties.set_prop("policyDefinitions", AAZListType, ".definitions", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("version", AAZStrType, ".version")

            metadata = _builder.get(".properties.metadata")
            if metadata is not None:
                metadata.set_elements(AAZAnyType, ".")

            parameters = _builder.get(".properties.parameters")
            if parameters is not None:
                parameters.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.parameters{}")
            if _elements is not None:
                _elements.set_prop("allowedValues", AAZListType, ".allowed_values")
                _elements.set_prop("defaultValue", AAZAnyType, ".default_value")
                _elements.set_prop("metadata", AAZFreeFormDictType, ".metadata")
                _elements.set_prop("schema", AAZDictType, ".schema")
                _elements.set_prop("type", AAZStrType, ".type")

            allowed_values = _builder.get(".properties.parameters{}.allowedValues")
            if allowed_values is not None:
                allowed_values.set_elements(AAZAnyType, ".")

            metadata = _builder.get(".properties.parameters{}.metadata")
            if metadata is not None:
                metadata.set_anytype_elements(".")

            schema = _builder.get(".properties.parameters{}.schema")
            if schema is not None:
                schema.set_elements(AAZAnyType, ".")

            policy_definition_groups = _builder.get(".properties.policyDefinitionGroups")
            if policy_definition_groups is not None:
                policy_definition_groups.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.policyDefinitionGroups[]")
            if _elements is not None:
                _elements.set_prop("additionalMetadataId", AAZStrType, ".additional_metadata_id")
                _elements.set_prop("category", AAZStrType, ".category")
                _elements.set_prop("description", AAZStrType, ".description")
                _elements.set_prop("displayName", AAZStrType, ".display_name")
                _elements.set_prop("name", AAZStrType, ".name", typ_kwargs={"flags": {"required": True}})

            policy_definitions = _builder.get(".properties.policyDefinitions")
            if policy_definitions is not None:
                policy_definitions.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.policyDefinitions[]")
            if _elements is not None:
                _elements.set_prop("definitionVersion", AAZStrType, ".definition_version")
                _elements.set_prop("groupNames", AAZListType, ".group_names")
                _elements.set_prop("parameters", AAZDictType, ".parameters")
                _elements.set_prop("policyDefinitionId", AAZStrType, ".policy_definition_id", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("policyDefinitionReferenceId", AAZStrType, ".policy_definition_reference_id")

            group_names = _builder.get(".properties.policyDefinitions[].groupNames")
            if group_names is not None:
                group_names.set_elements(AAZStrType, ".")

            parameters = _builder.get(".properties.policyDefinitions[].parameters")
            if parameters is not None:
                parameters.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.policyDefinitions[].parameters{}")
            if _elements is not None:
                _elements.set_prop("value", AAZAnyType, ".value")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200_201.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200_201.properties
            properties.description = AAZStrType()
            properties.display_name = AAZStrType(
                serialized_name="displayName",
            )
            properties.metadata = AAZDictType()
            properties.parameters = AAZDictType()
            properties.policy_definition_groups = AAZListType(
                serialized_name="policyDefinitionGroups",
            )
            properties.policy_definitions = AAZListType(
                serialized_name="policyDefinitions",
                flags={"required": True},
            )
            properties.policy_type = AAZStrType(
                serialized_name="policyType",
            )
            properties.version = AAZStrType()
            properties.versions = AAZListType()

            metadata = cls._schema_on_200_201.properties.metadata
            metadata.Element = AAZAnyType()

            parameters = cls._schema_on_200_201.properties.parameters
            parameters.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.parameters.Element
            _element.allowed_values = AAZListType(
                serialized_name="allowedValues",
            )
            _element.default_value = AAZAnyType(
                serialized_name="defaultValue",
            )
            _element.metadata = AAZFreeFormDictType()
            _element.schema = AAZDictType()
            _element.type = AAZStrType()

            allowed_values = cls._schema_on_200_201.properties.parameters.Element.allowed_values
            allowed_values.Element = AAZAnyType()

            schema = cls._schema_on_200_201.properties.parameters.Element.schema
            schema.Element = AAZAnyType()

            policy_definition_groups = cls._schema_on_200_201.properties.policy_definition_groups
            policy_definition_groups.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.policy_definition_groups.Element
            _element.additional_metadata_id = AAZStrType(
                serialized_name="additionalMetadataId",
            )
            _element.category = AAZStrType()
            _element.description = AAZStrType()
            _element.display_name = AAZStrType(
                serialized_name="displayName",
            )
            _element.name = AAZStrType(
                flags={"required": True},
            )

            policy_definitions = cls._schema_on_200_201.properties.policy_definitions
            policy_definitions.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.policy_definitions.Element
            _element.definition_version = AAZStrType(
                serialized_name="definitionVersion",
            )
            _element.effective_definition_version = AAZStrType(
                serialized_name="effectiveDefinitionVersion",
                flags={"read_only": True},
            )
            _element.group_names = AAZListType(
                serialized_name="groupNames",
            )
            _element.latest_definition_version = AAZStrType(
                serialized_name="latestDefinitionVersion",
                flags={"read_only": True},
            )
            _element.parameters = AAZDictType()
            _element.policy_definition_id = AAZStrType(
                serialized_name="policyDefinitionId",
                flags={"required": True},
            )
            _element.policy_definition_reference_id = AAZStrType(
                serialized_name="policyDefinitionReferenceId",
            )

            group_names = cls._schema_on_200_201.properties.policy_definitions.Element.group_names
            group_names.Element = AAZStrType()

            parameters = cls._schema_on_200_201.properties.policy_definitions.Element.parameters
            parameters.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.policy_definitions.Element.parameters.Element
            _element.value = AAZAnyType()

            versions = cls._schema_on_200_201.properties.versions
            versions.Element = AAZStrType()

            system_data = cls._schema_on_200_201.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""


__all__ = ["Create"]
