# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "policy assignment identity",
)
class __CMDGroup(AAZCommandGroup):
    """Managed identity of the policy assignment.

    The system or user assigned managed identity used by the enclosing policy assignment for remediation tasks.
    """
    pass


__all__ = ["__CMDGroup"]
