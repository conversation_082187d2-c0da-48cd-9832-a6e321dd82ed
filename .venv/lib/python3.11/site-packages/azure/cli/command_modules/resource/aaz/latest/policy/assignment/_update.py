# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "policy assignment update",
)
class Update(AAZCommand):
    """Update a policy assignment.

    Update the policy assignment with the given name and scope by applying the given property values.

    :example: Update a resource policy assignment's description
        az policy assignment update --name myPolicy --description 'My policy description'
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/{scope}/providers/microsoft.authorization/policyassignments/{}", "2024-05-01"],
        ]
    }

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help={"short-summary": "The name of the policy assignment.", "long-summary": "The name of the policy assignment is the name segment of its resource ID."},
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[^<>*%&:\\?.+/]*[^<>*%&:\\?.+/ ]+$",
            ),
        )
        _args_schema.scope = AAZStrArg(
            options=["--scope"],
            help={"short-summary": "The scope of the policy assignment.", "long-summary": "Valid scopes are: management group (format: '/providers/Microsoft.Management/managementGroups/{managementGroup}'), subscription (format: '/subscriptions/{subscriptionId}'), resource group (format: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'. The scope of an assignment is always the part of its ID preceding '/providers/Microsoft.Authorization/policyAssignments/{policyAssignmentName}'. If scope is not provided, the scope will be the implied or specified subscription."},
            required=True,
        )

        # define Arg Group "Identity"

        # define Arg Group "Parameters"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Parameters",
            help={"short-summary": "The location of the policy assignment.", "long-summary": "The location of the policy assignment is only required when utilizing managed identity."},
            nullable=True,
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.definition_version = AAZStrArg(
            options=["--definition-version"],
            arg_group="Properties",
            help={"short-summary": "The policy version to assign.", "long-summary": "The version of the policy definition or policy set definition to assign."},
            nullable=True,
        )
        _args_schema.description = AAZStrArg(
            options=["--description"],
            arg_group="Properties",
            help={"short-summary": "Policy assignment description.", "long-summary": "Full description of the policy assignment."},
            nullable=True,
        )
        _args_schema.display_name = AAZStrArg(
            options=["--display-name"],
            arg_group="Properties",
            help={"short-summary": "The display name of the policy assignment.", "long-summary": "The display name of the policy assignment is not part of its ID, allowing for longer and more flexible naming."},
            nullable=True,
        )
        _args_schema.enforcement_mode = AAZStrArg(
            options=["-e", "--enforcement-mode"],
            arg_group="Properties",
            help={"short-summary": "The policy assignment enforcement mode.", "long-summary": "The policy assignment enforcement mode. Possible values are Default and DoNotEnforce."},
            nullable=True,
            enum={"Default": "Default", "DoNotEnforce": "DoNotEnforce"},
        )
        _args_schema.metadata = AAZDictArg(
            options=["--metadata"],
            arg_group="Properties",
            help={"short-summary": "The policy assignment metadata.", "long-summary": "The policy assignment metadata. Metadata is an open-ended object and is typically a collection of key value pairs."},
            nullable=True,
        )
        _args_schema.not_scopes = AAZListArg(
            options=["--not-scopes"],
            arg_group="Properties",
            help={"short-summary": "The policy assignment excluded scopes.", "long-summary": "The collection of scopes within the policy assignment scope that it does not apply to."},
            nullable=True,
        )
        _args_schema.overrides = AAZListArg(
            options=["--overrides"],
            arg_group="Properties",
            help="The policy property value override.",
            nullable=True,
        )
        _args_schema.params = AAZDictArg(
            options=["-p", "--params"],
            arg_group="Properties",
            help={"short-summary": "The parameter values for the assigned policy rule.", "long-summary": "Object or file path containing the policy rule parameter values to be used when assigning the policy definition. The object keys are the parameter names."},
            nullable=True,
        )
        _args_schema.policy_set_definition = AAZStrArg(
            options=["-d", "--policy-set-definition"],
            arg_group="Properties",
            help={"short-summary": "The policy definition or policy set definition to assign.", "long-summary": "The resource ID of the policy definition (format: /{scope}/providers/Microsoft.Authorization/policyDefinitions/{name}) or policy set definition (format: /{scope}/providers/Microsoft.Authorization/policySetDefinitions/{name}) to be assigned."},
            nullable=True,
        )
        _args_schema.resource_selectors = AAZListArg(
            options=["--resource-selectors"],
            arg_group="Properties",
            help={"short-summary": "The resource selectors list to filter policies by resource properties.", "long-summary": "The collection of resource selector expressions used to filter policy assignment applicability by certain resource property values."},
            nullable=True,
        )

        metadata = cls._args_schema.metadata
        metadata.Element = AAZAnyTypeArg(
            nullable=True,
        )

        not_scopes = cls._args_schema.not_scopes
        not_scopes.Element = AAZStrArg(
            nullable=True,
        )

        overrides = cls._args_schema.overrides
        overrides.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.overrides.Element
        _element.kind = AAZStrArg(
            options=["kind"],
            help="The override kind.",
            nullable=True,
            enum={"definitionVersion": "definitionVersion", "policyEffect": "policyEffect"},
        )
        _element.selectors = AAZListArg(
            options=["selectors"],
            help="The list of the selector expressions.",
            nullable=True,
        )
        _element.value = AAZStrArg(
            options=["value"],
            help="The value to override the policy property.",
            nullable=True,
        )

        selectors = cls._args_schema.overrides.Element.selectors
        selectors.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_selector_update(selectors.Element)

        params = cls._args_schema.params
        params.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.params.Element
        _element.value = AAZAnyTypeArg(
            options=["value"],
            help="The value of the parameter.",
            nullable=True,
        )

        resource_selectors = cls._args_schema.resource_selectors
        resource_selectors.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.resource_selectors.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="The name of the resource selector.",
            nullable=True,
        )
        _element.selectors = AAZListArg(
            options=["selectors"],
            help="The list of the selector expressions.",
            nullable=True,
        )

        selectors = cls._args_schema.resource_selectors.Element.selectors
        selectors.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_selector_update(selectors.Element)

        # define Arg Group "non-compliance-message"

        _args_schema = cls._args_schema
        _args_schema.non_compliance_messages = AAZListArg(
            options=["-m", "--non-compliance-messages"],
            arg_group="non-compliance-message",
            help="The messages that describe why a resource is non-compliant with the policy.",
            nullable=True,
        )

        non_compliance_messages = cls._args_schema.non_compliance_messages
        non_compliance_messages.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.non_compliance_messages.Element
        _element.message = AAZStrArg(
            options=["m", "message"],
            help={"short-summary": "A custom non-compliance message.", "long-summary": "A message that describes the reasons a resource was determined to be non-compliant. This will be shown in 'deny' error messages and resource non-compliance results."},
        )
        _element.policy_definition_reference_id = AAZStrArg(
            options=["r", "policy-definition-reference-id"],
            help={"short-summary": "The policy definition reference ID.", "long-summary": "The policy definition reference ID within a policy set definition the message is intended for. This is only applicable if the policy assignment assigns a policy set definition. If not provided, the message applies to all policy definitions assigned by this policy assignment."},
            nullable=True,
        )
        return cls._args_schema

    _args_selector_update = None

    @classmethod
    def _build_args_selector_update(cls, _schema):
        if cls._args_selector_update is not None:
            _schema.in_ = cls._args_selector_update.in_
            _schema.kind = cls._args_selector_update.kind
            _schema.not_in = cls._args_selector_update.not_in
            return

        cls._args_selector_update = AAZObjectArg(
            nullable=True,
        )

        selector_update = cls._args_selector_update
        selector_update.in_ = AAZListArg(
            options=["in"],
            help="The list of values to filter in.",
            nullable=True,
        )
        selector_update.kind = AAZStrArg(
            options=["kind"],
            help="The selector kind.",
            nullable=True,
            enum={"policyDefinitionReferenceId": "policyDefinitionReferenceId", "resourceLocation": "resourceLocation", "resourceType": "resourceType", "resourceWithoutLocation": "resourceWithoutLocation"},
        )
        selector_update.not_in = AAZListArg(
            options=["not-in"],
            help="The list of values to filter out.",
            nullable=True,
        )

        in_ = cls._args_selector_update.in_
        in_.Element = AAZStrArg(
            nullable=True,
        )

        not_in = cls._args_selector_update.not_in
        not_in.Element = AAZStrArg(
            nullable=True,
        )

        _schema.in_ = cls._args_selector_update.in_
        _schema.kind = cls._args_selector_update.kind
        _schema.not_in = cls._args_selector_update.not_in

    def _execute_operations(self):
        self.pre_operations()
        self.PolicyAssignmentsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        self.PolicyAssignmentsCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class PolicyAssignmentsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/{scope}/providers/Microsoft.Authorization/policyAssignments/{policyAssignmentName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "policyAssignmentName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "scope", self.ctx.args.scope,
                    skip_quote=True,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_policy_assignment_read(cls._schema_on_200)

            return cls._schema_on_200

    class PolicyAssignmentsCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [201]:
                return self.on_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/{scope}/providers/Microsoft.Authorization/policyAssignments/{policyAssignmentName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "policyAssignmentName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "scope", self.ctx.args.scope,
                    skip_quote=True,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_201
            )

        _schema_on_201 = None

        @classmethod
        def _build_schema_on_201(cls):
            if cls._schema_on_201 is not None:
                return cls._schema_on_201

            cls._schema_on_201 = AAZObjectType()
            _UpdateHelper._build_schema_policy_assignment_read(cls._schema_on_201)

            return cls._schema_on_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("identity", AAZIdentityObjectType)
            _builder.set_prop("location", AAZStrType, ".location")
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("definitionVersion", AAZStrType, ".definition_version")
                properties.set_prop("description", AAZStrType, ".description")
                properties.set_prop("displayName", AAZStrType, ".display_name")
                properties.set_prop("enforcementMode", AAZStrType, ".enforcement_mode")
                properties.set_prop("metadata", AAZDictType, ".metadata")
                properties.set_prop("nonComplianceMessages", AAZListType, ".non_compliance_messages")
                properties.set_prop("notScopes", AAZListType, ".not_scopes")
                properties.set_prop("overrides", AAZListType, ".overrides")
                properties.set_prop("parameters", AAZDictType, ".params")
                properties.set_prop("policyDefinitionId", AAZStrType, ".policy_set_definition")
                properties.set_prop("resourceSelectors", AAZListType, ".resource_selectors")

            metadata = _builder.get(".properties.metadata")
            if metadata is not None:
                metadata.set_elements(AAZAnyType, ".")

            non_compliance_messages = _builder.get(".properties.nonComplianceMessages")
            if non_compliance_messages is not None:
                non_compliance_messages.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.nonComplianceMessages[]")
            if _elements is not None:
                _elements.set_prop("message", AAZStrType, ".message", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("policyDefinitionReferenceId", AAZStrType, ".policy_definition_reference_id")

            not_scopes = _builder.get(".properties.notScopes")
            if not_scopes is not None:
                not_scopes.set_elements(AAZStrType, ".")

            overrides = _builder.get(".properties.overrides")
            if overrides is not None:
                overrides.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.overrides[]")
            if _elements is not None:
                _elements.set_prop("kind", AAZStrType, ".kind")
                _elements.set_prop("selectors", AAZListType, ".selectors")
                _elements.set_prop("value", AAZStrType, ".value")

            selectors = _builder.get(".properties.overrides[].selectors")
            if selectors is not None:
                _UpdateHelper._build_schema_selector_update(selectors.set_elements(AAZObjectType, "."))

            parameters = _builder.get(".properties.parameters")
            if parameters is not None:
                parameters.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.parameters{}")
            if _elements is not None:
                _elements.set_prop("value", AAZAnyType, ".value")

            resource_selectors = _builder.get(".properties.resourceSelectors")
            if resource_selectors is not None:
                resource_selectors.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.resourceSelectors[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("selectors", AAZListType, ".selectors")

            selectors = _builder.get(".properties.resourceSelectors[].selectors")
            if selectors is not None:
                _UpdateHelper._build_schema_selector_update(selectors.set_elements(AAZObjectType, "."))

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    @classmethod
    def _build_schema_selector_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("in", AAZListType, ".in_")
        _builder.set_prop("kind", AAZStrType, ".kind")
        _builder.set_prop("notIn", AAZListType, ".not_in")

        in_ = _builder.get(".in")
        if in_ is not None:
            in_.set_elements(AAZStrType, ".")

        not_in = _builder.get(".notIn")
        if not_in is not None:
            not_in.set_elements(AAZStrType, ".")

    _schema_policy_assignment_read = None

    @classmethod
    def _build_schema_policy_assignment_read(cls, _schema):
        if cls._schema_policy_assignment_read is not None:
            _schema.id = cls._schema_policy_assignment_read.id
            _schema.identity = cls._schema_policy_assignment_read.identity
            _schema.location = cls._schema_policy_assignment_read.location
            _schema.name = cls._schema_policy_assignment_read.name
            _schema.properties = cls._schema_policy_assignment_read.properties
            _schema.system_data = cls._schema_policy_assignment_read.system_data
            _schema.type = cls._schema_policy_assignment_read.type
            return

        cls._schema_policy_assignment_read = _schema_policy_assignment_read = AAZObjectType()

        policy_assignment_read = _schema_policy_assignment_read
        policy_assignment_read.id = AAZStrType(
            flags={"read_only": True},
        )
        policy_assignment_read.identity = AAZIdentityObjectType()
        policy_assignment_read.location = AAZStrType()
        policy_assignment_read.name = AAZStrType(
            flags={"read_only": True},
        )
        policy_assignment_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        policy_assignment_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        policy_assignment_read.type = AAZStrType(
            flags={"read_only": True},
        )

        identity = _schema_policy_assignment_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType()
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_policy_assignment_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_policy_assignment_read.properties
        properties.assignment_type = AAZStrType(
            serialized_name="assignmentType",
        )
        properties.definition_version = AAZStrType(
            serialized_name="definitionVersion",
        )
        properties.description = AAZStrType()
        properties.display_name = AAZStrType(
            serialized_name="displayName",
        )
        properties.effective_definition_version = AAZStrType(
            serialized_name="effectiveDefinitionVersion",
            flags={"read_only": True},
        )
        properties.enforcement_mode = AAZStrType(
            serialized_name="enforcementMode",
        )
        properties.latest_definition_version = AAZStrType(
            serialized_name="latestDefinitionVersion",
            flags={"read_only": True},
        )
        properties.metadata = AAZDictType()
        properties.non_compliance_messages = AAZListType(
            serialized_name="nonComplianceMessages",
        )
        properties.not_scopes = AAZListType(
            serialized_name="notScopes",
        )
        properties.overrides = AAZListType()
        properties.parameters = AAZDictType()
        properties.policy_definition_id = AAZStrType(
            serialized_name="policyDefinitionId",
        )
        properties.resource_selectors = AAZListType(
            serialized_name="resourceSelectors",
        )
        properties.scope = AAZStrType(
            flags={"read_only": True},
        )

        metadata = _schema_policy_assignment_read.properties.metadata
        metadata.Element = AAZAnyType()

        non_compliance_messages = _schema_policy_assignment_read.properties.non_compliance_messages
        non_compliance_messages.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.non_compliance_messages.Element
        _element.message = AAZStrType(
            flags={"required": True},
        )
        _element.policy_definition_reference_id = AAZStrType(
            serialized_name="policyDefinitionReferenceId",
        )

        not_scopes = _schema_policy_assignment_read.properties.not_scopes
        not_scopes.Element = AAZStrType()

        overrides = _schema_policy_assignment_read.properties.overrides
        overrides.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.overrides.Element
        _element.kind = AAZStrType()
        _element.selectors = AAZListType()
        _element.value = AAZStrType()

        selectors = _schema_policy_assignment_read.properties.overrides.Element.selectors
        selectors.Element = AAZObjectType()
        cls._build_schema_selector_read(selectors.Element)

        parameters = _schema_policy_assignment_read.properties.parameters
        parameters.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.parameters.Element
        _element.value = AAZAnyType()

        resource_selectors = _schema_policy_assignment_read.properties.resource_selectors
        resource_selectors.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.resource_selectors.Element
        _element.name = AAZStrType()
        _element.selectors = AAZListType()

        selectors = _schema_policy_assignment_read.properties.resource_selectors.Element.selectors
        selectors.Element = AAZObjectType()
        cls._build_schema_selector_read(selectors.Element)

        system_data = _schema_policy_assignment_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        _schema.id = cls._schema_policy_assignment_read.id
        _schema.identity = cls._schema_policy_assignment_read.identity
        _schema.location = cls._schema_policy_assignment_read.location
        _schema.name = cls._schema_policy_assignment_read.name
        _schema.properties = cls._schema_policy_assignment_read.properties
        _schema.system_data = cls._schema_policy_assignment_read.system_data
        _schema.type = cls._schema_policy_assignment_read.type

    _schema_selector_read = None

    @classmethod
    def _build_schema_selector_read(cls, _schema):
        if cls._schema_selector_read is not None:
            _schema["in"] = cls._schema_selector_read["in"]
            _schema.kind = cls._schema_selector_read.kind
            _schema.not_in = cls._schema_selector_read.not_in
            return

        cls._schema_selector_read = _schema_selector_read = AAZObjectType()

        selector_read = _schema_selector_read
        selector_read["in"] = AAZListType()
        selector_read.kind = AAZStrType()
        selector_read.not_in = AAZListType(
            serialized_name="notIn",
        )

        in_ = _schema_selector_read["in"]
        in_.Element = AAZStrType()

        not_in = _schema_selector_read.not_in
        not_in.Element = AAZStrType()

        _schema["in"] = cls._schema_selector_read["in"]
        _schema.kind = cls._schema_selector_read.kind
        _schema.not_in = cls._schema_selector_read.not_in


__all__ = ["Update"]
