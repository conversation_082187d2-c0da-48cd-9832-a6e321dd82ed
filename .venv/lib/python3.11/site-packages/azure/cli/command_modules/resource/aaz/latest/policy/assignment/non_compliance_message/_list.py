# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "policy assignment non-compliance-message list",
)
class List(AAZCommand):
    """Retrieve non-compliance messages.

    Retrieve all non-compliance messages from the policy assignment matching the given name and scope.

    :example: List the non-compliance messages for a policy assignment
        az policy assignment non-compliance-message list -g MyResourceGroup -n MyPolicyAssignment
    """

    _aaz_info = {
        "version": "2024-05-01",
        "resources": [
            ["mgmt-plane", "/{scope}/providers/microsoft.authorization/policyassignments/{}", "2024-05-01", "properties.nonComplianceMessages"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self.SubresourceSelector(ctx=self.ctx, name="subresource")
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help={"short-summary": "The name of the policy assignment.", "long-summary": "The name of the policy assignment is the name segment of its resource ID."},
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[^<>*%&:\\?.+/]*[^<>*%&:\\?.+/ ]+$",
            ),
        )
        _args_schema.scope = AAZStrArg(
            options=["--scope"],
            help={"short-summary": "The scope of the policy assignment.", "long-summary": "Valid scopes are: management group (format: '/providers/Microsoft.Management/managementGroups/{managementGroup}'), subscription (format: '/subscriptions/{subscriptionId}'), resource group (format: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'. The scope of an assignment is always the part of its ID preceding '/providers/Microsoft.Authorization/policyAssignments/{policyAssignmentName}'. If scope is not provided, the scope will be the implied or specified subscription."},
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.PolicyAssignmentsGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.selectors.subresource.required(), client_flatten=True)
        return result

    class SubresourceSelector(AAZJsonSelector):

        def _get(self):
            result = self.ctx.vars.instance
            return result.properties.nonComplianceMessages

        def _set(self, value):
            result = self.ctx.vars.instance
            result.properties.nonComplianceMessages = value
            return

    class PolicyAssignmentsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/{scope}/providers/Microsoft.Authorization/policyAssignments/{policyAssignmentName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "policyAssignmentName", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "scope", self.ctx.args.scope,
                    skip_quote=True,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-05-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _ListHelper._build_schema_policy_assignment_read(cls._schema_on_200)

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""

    _schema_policy_assignment_read = None

    @classmethod
    def _build_schema_policy_assignment_read(cls, _schema):
        if cls._schema_policy_assignment_read is not None:
            _schema.id = cls._schema_policy_assignment_read.id
            _schema.identity = cls._schema_policy_assignment_read.identity
            _schema.location = cls._schema_policy_assignment_read.location
            _schema.name = cls._schema_policy_assignment_read.name
            _schema.properties = cls._schema_policy_assignment_read.properties
            _schema.system_data = cls._schema_policy_assignment_read.system_data
            _schema.type = cls._schema_policy_assignment_read.type
            return

        cls._schema_policy_assignment_read = _schema_policy_assignment_read = AAZObjectType()

        policy_assignment_read = _schema_policy_assignment_read
        policy_assignment_read.id = AAZStrType(
            flags={"read_only": True},
        )
        policy_assignment_read.identity = AAZIdentityObjectType()
        policy_assignment_read.location = AAZStrType()
        policy_assignment_read.name = AAZStrType(
            flags={"read_only": True},
        )
        policy_assignment_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        policy_assignment_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        policy_assignment_read.type = AAZStrType(
            flags={"read_only": True},
        )

        identity = _schema_policy_assignment_read.identity
        identity.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )
        identity.tenant_id = AAZStrType(
            serialized_name="tenantId",
            flags={"read_only": True},
        )
        identity.type = AAZStrType()
        identity.user_assigned_identities = AAZDictType(
            serialized_name="userAssignedIdentities",
        )

        user_assigned_identities = _schema_policy_assignment_read.identity.user_assigned_identities
        user_assigned_identities.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.identity.user_assigned_identities.Element
        _element.client_id = AAZStrType(
            serialized_name="clientId",
            flags={"read_only": True},
        )
        _element.principal_id = AAZStrType(
            serialized_name="principalId",
            flags={"read_only": True},
        )

        properties = _schema_policy_assignment_read.properties
        properties.assignment_type = AAZStrType(
            serialized_name="assignmentType",
        )
        properties.definition_version = AAZStrType(
            serialized_name="definitionVersion",
        )
        properties.description = AAZStrType()
        properties.display_name = AAZStrType(
            serialized_name="displayName",
        )
        properties.effective_definition_version = AAZStrType(
            serialized_name="effectiveDefinitionVersion",
            flags={"read_only": True},
        )
        properties.enforcement_mode = AAZStrType(
            serialized_name="enforcementMode",
        )
        properties.latest_definition_version = AAZStrType(
            serialized_name="latestDefinitionVersion",
            flags={"read_only": True},
        )
        properties.metadata = AAZDictType()
        properties.non_compliance_messages = AAZListType(
            serialized_name="nonComplianceMessages",
        )
        properties.not_scopes = AAZListType(
            serialized_name="notScopes",
        )
        properties.overrides = AAZListType()
        properties.parameters = AAZDictType()
        properties.policy_definition_id = AAZStrType(
            serialized_name="policyDefinitionId",
        )
        properties.resource_selectors = AAZListType(
            serialized_name="resourceSelectors",
        )
        properties.scope = AAZStrType(
            flags={"read_only": True},
        )

        metadata = _schema_policy_assignment_read.properties.metadata
        metadata.Element = AAZAnyType()

        non_compliance_messages = _schema_policy_assignment_read.properties.non_compliance_messages
        non_compliance_messages.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.non_compliance_messages.Element
        _element.message = AAZStrType(
            flags={"required": True},
        )
        _element.policy_definition_reference_id = AAZStrType(
            serialized_name="policyDefinitionReferenceId",
        )

        not_scopes = _schema_policy_assignment_read.properties.not_scopes
        not_scopes.Element = AAZStrType()

        overrides = _schema_policy_assignment_read.properties.overrides
        overrides.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.overrides.Element
        _element.kind = AAZStrType()
        _element.selectors = AAZListType()
        _element.value = AAZStrType()

        selectors = _schema_policy_assignment_read.properties.overrides.Element.selectors
        selectors.Element = AAZObjectType()
        cls._build_schema_selector_read(selectors.Element)

        parameters = _schema_policy_assignment_read.properties.parameters
        parameters.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.parameters.Element
        _element.value = AAZAnyType()

        resource_selectors = _schema_policy_assignment_read.properties.resource_selectors
        resource_selectors.Element = AAZObjectType()

        _element = _schema_policy_assignment_read.properties.resource_selectors.Element
        _element.name = AAZStrType()
        _element.selectors = AAZListType()

        selectors = _schema_policy_assignment_read.properties.resource_selectors.Element.selectors
        selectors.Element = AAZObjectType()
        cls._build_schema_selector_read(selectors.Element)

        system_data = _schema_policy_assignment_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        _schema.id = cls._schema_policy_assignment_read.id
        _schema.identity = cls._schema_policy_assignment_read.identity
        _schema.location = cls._schema_policy_assignment_read.location
        _schema.name = cls._schema_policy_assignment_read.name
        _schema.properties = cls._schema_policy_assignment_read.properties
        _schema.system_data = cls._schema_policy_assignment_read.system_data
        _schema.type = cls._schema_policy_assignment_read.type

    _schema_selector_read = None

    @classmethod
    def _build_schema_selector_read(cls, _schema):
        if cls._schema_selector_read is not None:
            _schema["in"] = cls._schema_selector_read["in"]
            _schema.kind = cls._schema_selector_read.kind
            _schema.not_in = cls._schema_selector_read.not_in
            return

        cls._schema_selector_read = _schema_selector_read = AAZObjectType()

        selector_read = _schema_selector_read
        selector_read["in"] = AAZListType()
        selector_read.kind = AAZStrType()
        selector_read.not_in = AAZListType(
            serialized_name="notIn",
        )

        in_ = _schema_selector_read["in"]
        in_.Element = AAZStrType()

        not_in = _schema_selector_read.not_in
        not_in.Element = AAZStrType()

        _schema["in"] = cls._schema_selector_read["in"]
        _schema.kind = cls._schema_selector_read.kind
        _schema.not_in = cls._schema_selector_read.not_in


__all__ = ["List"]
