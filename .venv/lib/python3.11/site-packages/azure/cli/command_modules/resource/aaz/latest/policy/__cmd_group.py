# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "policy",
)
class __CMDGroup(AAZCommandGroup):
    """Manage resources defined and used by the Azure Policy service.

    Azure Policy is an Azure service that offers APIs to manage Azure resources in a rule-based declarative way. The policy command group provides create, update, show and list commands for managing policy definitions, policy set definitions (also called policy initiatives), policy assignments, and policy exemptions.
    """
    pass


__all__ = ["__CMDGroup"]
