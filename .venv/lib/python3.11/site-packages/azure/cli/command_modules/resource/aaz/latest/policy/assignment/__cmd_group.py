# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "policy assignment",
)
class __CMDGroup(AAZCommandGroup):
    """Manage policy assignments.

    Policy assignments are used to apply a policy definition or policy set definition to a given resource scope.
    """
    pass


__all__ = ["__CMDGroup"]
