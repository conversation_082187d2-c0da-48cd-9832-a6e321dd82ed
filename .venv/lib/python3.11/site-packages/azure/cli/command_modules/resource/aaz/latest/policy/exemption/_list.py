# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "policy exemption list",
)
class List(AAZCommand):
    """Retrieve all applicable policy exemptions.

    Retrieve the list of all policy assignments applicable to the given subscription or management group.

    :example: List policy exemptions that apply to a management group
        az policy exemption list --management-group DevOrg --filter atScopeAndBelow()

    :example: List policy exemptions that apply to a resource group
        az policy exemption list --resource-group TestResourceGroup

    :example: List policy exemptions that apply to a subscription
        az policy exemption list
    """

    _aaz_info = {
        "version": "2022-07-01-preview",
        "resources": [
            ["mgmt-plane", "/providers/microsoft.management/managementgroups/{}/providers/microsoft.authorization/policyexemptions", "2022-07-01-preview"],
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.authorization/policyexemptions", "2022-07-01-preview"],
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.authorization/policyexemptions", "2022-07-01-preview"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.management_group = AAZStrArg(
            options=["--management-group"],
            help={"short-summary": "The management group.", "long-summary": "Indicates that policy exemptions whose scope covers the management group with the given name should be listed."},
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            help={"short-summary": "The resource group.", "long-summary": "Indicates that policy exemptions whose scope covers the resource group with the given name are to be listed."},
        )
        _args_schema.filter = AAZStrArg(
            options=["--filter"],
            help={"short-summary": "Filter list results.", "long-summary": "The filter to limit list results. Valid values are: 'atScope()', 'atExactScope()', 'atScopeAndBelow()' or 'policyDefinitionId eq '{value}''. If filter is not provided, no filtering is performed. If filter atScope() is provided, the returned list includes all policy assignments that apply to the given scope, which is everything in the unfiltered list except those applied to sub scopes contained within the given scope. If filter atExactScope() is provided, the returned list includes all policy assignments at the given scope.  If filter atScopeAndBelow() is provided, the returned list includes all policy assignments at the given scope and those in sub scopes contained within the given scope. If filter policyDefinitionId eq '{value}' is provided, the returned list includes all policy assignments of the policy definition whose id is {value}."},
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        condition_0 = has_value(self.ctx.args.management_group)
        condition_1 = has_value(self.ctx.subscription_id) and has_value(self.ctx.args.resource_group) is not True
        condition_2 = has_value(self.ctx.args.resource_group) and has_value(self.ctx.subscription_id)
        if condition_0:
            self.PolicyExemptionsListForManagementGroup(ctx=self.ctx)()
        if condition_1:
            self.PolicyExemptionsList(ctx=self.ctx)()
        if condition_2:
            self.PolicyExemptionsListForResourceGroup(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class PolicyExemptionsListForManagementGroup(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/providers/Microsoft.Management/managementGroups/{managementGroupId}/providers/Microsoft.Authorization/policyExemptions",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "managementGroupId", self.ctx.args.management_group,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$filter", self.ctx.args.filter,
                ),
                **self.serialize_query_param(
                    "api-version", "2022-07-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType()

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.assignment_scope_validation = AAZStrType(
                serialized_name="assignmentScopeValidation",
            )
            properties.description = AAZStrType()
            properties.display_name = AAZStrType(
                serialized_name="displayName",
            )
            properties.exemption_category = AAZStrType(
                serialized_name="exemptionCategory",
                flags={"required": True},
            )
            properties.expires_on = AAZStrType(
                serialized_name="expiresOn",
            )
            properties.metadata = AAZDictType()
            properties.policy_assignment_id = AAZStrType(
                serialized_name="policyAssignmentId",
                flags={"required": True},
            )
            properties.policy_definition_reference_ids = AAZListType(
                serialized_name="policyDefinitionReferenceIds",
            )
            properties.resource_selectors = AAZListType(
                serialized_name="resourceSelectors",
            )

            metadata = cls._schema_on_200.value.Element.properties.metadata
            metadata.Element = AAZAnyType()

            policy_definition_reference_ids = cls._schema_on_200.value.Element.properties.policy_definition_reference_ids
            policy_definition_reference_ids.Element = AAZStrType()

            resource_selectors = cls._schema_on_200.value.Element.properties.resource_selectors
            resource_selectors.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.resource_selectors.Element
            _element.name = AAZStrType()
            _element.selectors = AAZListType()

            selectors = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors
            selectors.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element
            _element["in"] = AAZListType()
            _element.kind = AAZStrType()
            _element.not_in = AAZListType(
                serialized_name="notIn",
            )

            in_ = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element["in"]
            in_.Element = AAZStrType()

            not_in = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element.not_in
            not_in.Element = AAZStrType()

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200

    class PolicyExemptionsList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/policyExemptions",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$filter", self.ctx.args.filter,
                ),
                **self.serialize_query_param(
                    "api-version", "2022-07-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType()

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.assignment_scope_validation = AAZStrType(
                serialized_name="assignmentScopeValidation",
            )
            properties.description = AAZStrType()
            properties.display_name = AAZStrType(
                serialized_name="displayName",
            )
            properties.exemption_category = AAZStrType(
                serialized_name="exemptionCategory",
                flags={"required": True},
            )
            properties.expires_on = AAZStrType(
                serialized_name="expiresOn",
            )
            properties.metadata = AAZDictType()
            properties.policy_assignment_id = AAZStrType(
                serialized_name="policyAssignmentId",
                flags={"required": True},
            )
            properties.policy_definition_reference_ids = AAZListType(
                serialized_name="policyDefinitionReferenceIds",
            )
            properties.resource_selectors = AAZListType(
                serialized_name="resourceSelectors",
            )

            metadata = cls._schema_on_200.value.Element.properties.metadata
            metadata.Element = AAZAnyType()

            policy_definition_reference_ids = cls._schema_on_200.value.Element.properties.policy_definition_reference_ids
            policy_definition_reference_ids.Element = AAZStrType()

            resource_selectors = cls._schema_on_200.value.Element.properties.resource_selectors
            resource_selectors.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.resource_selectors.Element
            _element.name = AAZStrType()
            _element.selectors = AAZListType()

            selectors = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors
            selectors.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element
            _element["in"] = AAZListType()
            _element.kind = AAZStrType()
            _element.not_in = AAZListType(
                serialized_name="notIn",
            )

            in_ = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element["in"]
            in_.Element = AAZStrType()

            not_in = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element.not_in
            not_in.Element = AAZStrType()

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200

    class PolicyExemptionsListForResourceGroup(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Authorization/policyExemptions",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$filter", self.ctx.args.filter,
                ),
                **self.serialize_query_param(
                    "api-version", "2022-07-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType()

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.assignment_scope_validation = AAZStrType(
                serialized_name="assignmentScopeValidation",
            )
            properties.description = AAZStrType()
            properties.display_name = AAZStrType(
                serialized_name="displayName",
            )
            properties.exemption_category = AAZStrType(
                serialized_name="exemptionCategory",
                flags={"required": True},
            )
            properties.expires_on = AAZStrType(
                serialized_name="expiresOn",
            )
            properties.metadata = AAZDictType()
            properties.policy_assignment_id = AAZStrType(
                serialized_name="policyAssignmentId",
                flags={"required": True},
            )
            properties.policy_definition_reference_ids = AAZListType(
                serialized_name="policyDefinitionReferenceIds",
            )
            properties.resource_selectors = AAZListType(
                serialized_name="resourceSelectors",
            )

            metadata = cls._schema_on_200.value.Element.properties.metadata
            metadata.Element = AAZAnyType()

            policy_definition_reference_ids = cls._schema_on_200.value.Element.properties.policy_definition_reference_ids
            policy_definition_reference_ids.Element = AAZStrType()

            resource_selectors = cls._schema_on_200.value.Element.properties.resource_selectors
            resource_selectors.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.resource_selectors.Element
            _element.name = AAZStrType()
            _element.selectors = AAZListType()

            selectors = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors
            selectors.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element
            _element["in"] = AAZListType()
            _element.kind = AAZStrType()
            _element.not_in = AAZListType(
                serialized_name="notIn",
            )

            in_ = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element["in"]
            in_.Element = AAZStrType()

            not_in = cls._schema_on_200.value.Element.properties.resource_selectors.Element.selectors.Element.not_in
            not_in.Element = AAZStrType()

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""


__all__ = ["List"]
