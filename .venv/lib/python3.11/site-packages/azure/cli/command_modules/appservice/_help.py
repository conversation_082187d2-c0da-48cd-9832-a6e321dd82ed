# coding=utf-8
# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# --------------------------------------------------------------------------------------------

from knack.help_files import helps  # pylint: disable=unused-import
# pylint: disable=line-too-long, too-many-lines

helps['appservice'] = """
type: group
short-summary: Manage App Service plans.
"""

helps['appservice hybrid-connection'] = """
type: group
short-summary: a method that sets the key a hybrid-connection uses
"""

helps['appservice hybrid-connection set-key'] = """
type: command
short-summary: set the key that all apps in an appservice plan use to connect to the hybrid-connections in that appservice plan
examples:
  - name: set the key that all apps in an appservice plan use to connect to the hybrid-connections in that appservice plan
    text: az appservice hybrid-connection set-key -g MyResourceGroup --plan MyAppServicePlan --namespace [HybridConectionNamespace] --hybrid-connection [HybridConnectionName] --key-type ["primary"/"secondary"]
"""

helps['appservice list-locations'] = """
type: command
short-summary: List regions where a plan sku is available.
examples:
  - name: List regions where a plan sku is available. (autogenerated)
    text: az appservice list-locations --sku F1
    crafted: true
"""

helps['appservice plan'] = """
type: group
short-summary: Manage app service plans.
"""

helps['appservice plan create'] = """
type: command
short-summary: Create an app service plan.
examples:
  - name: Create a basic app service plan.
    text: >
        az appservice plan create -g MyResourceGroup -n MyPlan
  - name: Create a standard app service plan with four Linux workers.
    text: >
        az appservice plan create -g MyResourceGroup -n MyPlan --is-linux --number-of-workers 4 --sku S1
  - name: Create a Windows container app service plan.
    text: >
        az appservice plan create -g MyResourceGroup -n MyPlan --hyper-v --sku P1V3
  - name: Create an app service plan for App Service Environment.
    text: >
        az appservice plan create -g MyResourceGroup -n MyPlan --app-service-environment MyAppServiceEnvironment --sku I1v2
  - name: Create an app service plan for App Service Environment in different subscription.
    text: >
        az appservice plan create -g MyResourceGroup -n MyPlan --app-service-environment '/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/test-rg/providers/Microsoft.Web/hostingEnvironments/test-ase' --sku I1V2
  - name: Create an app service plan for App Service Environment in different subscription and the resource group in different region than App Service Environment.
    text: >
        az appservice plan create -g MyResourceGroup -n MyPlan --app-service-environment '/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/test-rg/providers/Microsoft.Web/hostingEnvironments/test-ase' --sku I1V2 --location ase-region
"""

helps['appservice plan delete'] = """
type: command
short-summary: Delete an app service plan.
examples:
  - name: Delete an app service plan. (autogenerated)
    text: az appservice plan delete --name MyAppServicePlan --resource-group MyResourceGroup
    crafted: true
"""

helps['appservice plan list'] = """
type: command
short-summary: List app service plans.
examples:
  - name: List all free tier App Service plans.
    text: >
        az appservice plan list --query "[?sku.tier=='Free']"
  - name: List all App Service plans for an App Service environment.
    text: >
        az appservice plan list --query "[?hostingEnvironmentProfile.name=='<ase-name>']"
"""

helps['appservice plan show'] = """
type: command
short-summary: Get the app service plans for a resource group or a set of resource groups.
examples:
  - name: Get the app service plans for a resource group or a set of resource groups. (autogenerated)
    text: az appservice plan show --name MyAppServicePlan --resource-group MyResourceGroup
    crafted: true
"""

helps['appservice plan update'] = """
type: command
short-summary: Update an app service plan.
long-summary: See https:///go.microsoft.com/fwlink/?linkid=2133856 to learn more.
examples:
  - name: Update an app service plan. (autogenerated)
    text: az appservice plan update --name MyAppServicePlan --resource-group MyResourceGroup --sku F1
    crafted: true
"""

helps['appservice vnet-integration'] = """
type: group
short-summary: a method that lists the virtual network integrations used in an appservice plan
"""

helps['appservice vnet-integration list'] = """
type: command
short-summary: list the virtual network integrations used in an appservice plan
examples:
  - name: list the virtual network integrations used in an appservice plan
    text: az appservice vnet-integration list -g MyResourceGroup --plan MyAppServicePlan
"""

helps['functionapp'] = """
type: group
short-summary: Manage function apps. To install the Azure Functions Core tools see https://github.com/Azure/azure-functions-core-tools
"""

helps['functionapp config'] = """
type: group
short-summary: Configure a function app.
"""

helps['functionapp config access-restriction'] = """
type: group
short-summary: Methods that show, set, add, and remove access restrictions on a functionapp
"""

helps['functionapp config access-restriction add'] = """
type: command
short-summary: Adds an Access Restriction to the function app
examples:
  - name: Add Access Restriction opening (Allow) named developers for IPv4 address ***********/27 with priority 200 to main site.
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --rule-name developers --action Allow --ip-address ***********/27 --priority 200
  - name: Add Access Restriction opening (Allow) named build_server for IPv4 address ***********/27 with priority 250 to scm site.
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --rule-name build_server --action Allow --ip-address ***********/27 --priority 250 --scm-site true
  - name: Add Access Restriction opening (Allow) named app_gateway for Subnet app_gw in vNet core_weu with priority 300 to main site.
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --rule-name app_gateway --action Allow --vnet-name core_weu --subnet app_gateway --priority 300
  - name: Add Access Restriction opening (Allow) named internal_agents for Subnet build_agents in vNet corp01 with priority 500 to scm site; and ignore service endpoint registration on the Subnet.
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --rule-name internal_agents --action Allow --vnet-name corp01 --subnet build_agents --priority 500 --scm-site true --ignore-missing-endpoint true
  - name: Add Access Restriction opening (Allow) named remote_agents in vNet 'corp01' in rg 'vnets' with subnet 'agents'
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --rule-name remote_agents --action Allow --vnet-name corp01 --subnet agents --priority 500 --vnet-resource-group vnets
  - name: Add Access Restriction opening (Allow) named agents in vNet 'corp01' in rg 'vnets' with subnet 'agents' (using subnet resource id)
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --rule-name remote_agents --action Allow --priority 800 --subnet '/subscriptions/<subscription-id>/resourceGroups/vnets/providers/Microsoft.Network/virtualNetworks/corp01/subnets/agents'
  - name: Add Access Restriction opening (Allow) with no rule name for service tag AzureCloud
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --priority 400 --service-tag AzureCloud
  - name: Add Access Restriction opening (Allow) with no rule name for service tag AzureFrontDoor.Backend and http-header X-Azure-FDID with value '12345678-abcd-1234-abcd-12345678910a'
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --priority 400 --service-tag AzureFrontDoor.Backend --http-header x-azure-fdid=12345678-abcd-1234-abcd-12345678910a
  - name: Add Access Restriction opening (Allow) with multiple http-header values for the same header 'X-Azure-FDID'
    text: az functionapp config access-restriction add -g ResourceGroup -n AppName --priority 400 --service-tag AzureFrontDoor.Backend --http-header x-azure-fdid=12345678-abcd-1234-abcd-12345678910a x-azure-fdid=11111111-abcd-1234-abcd-222222222222
"""

helps['functionapp config access-restriction remove'] = """
type: command
short-summary: Removes an Access Restriction from the functionapp.
examples:
  - name: Remove Access Restriction named developers from the main site.
    text: az functionapp config access-restriction remove -g ResourceGroup -n AppName --rule-name developers
  - name: Remove Access Restriction named internal_agents from the scm site.
    text: az functionapp config access-restriction remove -g ResourceGroup -n AppName --rule-name internal_agents --scm-site true
  - name: Remove Access Restriction with service tag AzureFrontDoor.Backend from the main site.
    text: az functionapp config access-restriction remove -g ResourceGroup -n AppName --service-tag AzureFrontDoor.Backend
"""

helps['functionapp config access-restriction set'] = """
type: command
short-summary: Sets if SCM site is using the same restrictions as the main site.
examples:
  - name: Enable SCM site to use same access restrictions as main site.
    text: az functionapp config access-restriction set -g ResourceGroup -n AppName --use-same-restrictions-for-scm-site true
  - name: Set default action to Allow for main site.
    text: az functionapp config access-restriction set -g ResourceGroup -n AppName --default-action Allow
  - name: Set default action to Deny for scm site.
    text: az functionapp config access-restriction set -g ResourceGroup -n AppName --scm-default-action Deny
"""

helps['functionapp config access-restriction show'] = """
type: command
short-summary: Show Access Restriction settings for functionapp.
examples:
  - name: Get Access Restriction settings for a functionapp.
    text: az functionapp config access-restriction show -g ResourceGroup -n AppName
"""

helps['functionapp config appsettings'] = """
type: group
short-summary: Configure function app settings.
"""

helps['functionapp config appsettings delete'] = """
type: command
short-summary: Delete a function app's settings.
long-summary: Note that setting values are now redacted in the result. Please use the `az functionapp config appsettings list` command to view the settings.
examples:
  - name: Delete a function app's settings. (autogenerated)
    text: az functionapp config appsettings delete --name MyFunctionApp --resource-group MyResourceGroup --setting-names {setting-names}
    crafted: true
"""

helps['functionapp config appsettings list'] = """
type: command
short-summary: Show settings for a function app.
examples:
  - name: Show settings for a function app. (autogenerated)
    text: az functionapp config appsettings list --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config appsettings set'] = """
type: command
short-summary: Update a function app's settings.
long-summary: Note that setting values are now redacted in the result. Please use the `az functionapp config appsettings list` command to view the settings.
parameters:
  - name: --settings
    short-summary: Space-separated appsettings in KEY=VALUE format. Use @{file} to load from a file.
  - name: --slot-settings
    short-summary: Space-separated appsettings in KEY=VALUE format. Use @{file} to load from a file. Given setting are added to the configuration and marked as Deployment slot setting by default.
examples:
  - name: Update a function app's settings.
    text: |
        az functionapp config appsettings set --name MyFunctionApp --resource-group MyResourceGroup --settings foo=bar AzureWebJobsStorage=$storageConnectionString
  - name: Set using both key-value pair and a json file with more settings.
    text: >
        az functionapp config appsettings set -g MyResourceGroup -n MyUniqueApp --settings mySetting=value @moreSettings.json
"""

helps['functionapp config container'] = """
type: group
short-summary: Manage an existing function app's container settings.
"""

helps['functionapp config container delete'] = """
type: command
short-summary: Delete an existing function app's container settings.
"""

helps['functionapp config container set'] = """
type: command
short-summary: Set an existing function app's container settings.
examples:
  - name: Set a function app container's settings. (autogenerated)
    text: az functionapp config container set --docker-custom-image-name MyDockerCustomImage --docker-registry-server-password StrongPassword --docker-registry-server-url https://{azure-container-registry-name}.azurecr.io --docker-registry-server-user DockerUserId --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config container show'] = """
type: command
short-summary: Get details of a function app's container settings.
examples:
  - name: Get details of a function app container's settings. (autogenerated)
    text: az functionapp config container show --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config hostname'] = """
type: group
short-summary: Configure hostnames for a function app.
"""

helps['functionapp config hostname add'] = """
type: command
short-summary: Bind a hostname to a function app.
examples:
  - name: Bind a hostname to a function app. (autogenerated)
    text: az functionapp config hostname add --hostname www.yourdomain.com --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config hostname delete'] = """
type: command
short-summary: Unbind a hostname from a function app.
"""

helps['functionapp config hostname get-external-ip'] = """
type: command
short-summary: Get the external-facing IP address for a function app.
examples:
  - name: Get the external-facing IP address for a function app. (autogenerated)
    text: az functionapp config hostname get-external-ip --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config hostname list'] = """
type: command
short-summary: List all hostname bindings for a function app.
examples:
  - name: List all hostname bindings for a function app. (autogenerated)
    text: az functionapp config hostname list --resource-group MyResourceGroup --webapp-name MyWebapp
    crafted: true
"""

helps['functionapp config set'] = """
type: command
short-summary: Set an existing function app's configuration.
examples:
  - name: Set the function app's configuration. (autogenerated)
    text: az functionapp config set --always-on true --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
  - name: set configuration through a JSON file called params.json
    text: >
        az functionapp config set -g MyResourceGroup -n MyFunctionApp --generic-configurations "@.\\params.json"
"""

helps['functionapp config show'] = """
type: command
short-summary: Get the details of an existing function app's configuration.
examples:
  - name: Get the details of a web app's configuration. (autogenerated)
    text: az functionapp config show --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config ssl'] = """
type: group
short-summary: Configure SSL certificates.
"""

helps['functionapp config ssl bind'] = """
type: command
short-summary: Bind an SSL certificate to a function app.
examples:
  - name: Bind an SSL certificate to a function app. (autogenerated)
    text: az functionapp config ssl bind --certificate-thumbprint {certificate-thumbprint} --name MyFunctionApp --resource-group MyResourceGroup --ssl-type SNI
    crafted: true
"""

helps['functionapp config ssl delete'] = """
type: command
short-summary: Delete an SSL certificate from a function app.
"""

helps['functionapp config ssl list'] = """
type: command
short-summary: List SSL certificates for a function app.
examples:
  - name: List SSL certificates for a function app. (autogenerated)
    text: az functionapp config ssl list --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config ssl show'] = """
type: command
short-summary: Show the details of an SSL certificate for a function app.
examples:
  - name: Show the details of an SSL certificate for a function app. (autogenerated)
    text: az functionapp config ssl show --resource-group MyResourceGroup --certificate-name cname.mycustomdomain.com
    crafted: true
"""

helps['functionapp config ssl unbind'] = """
type: command
short-summary: Unbind an SSL certificate from a function app.
"""

helps['functionapp config ssl upload'] = """
type: command
short-summary: Upload an SSL certificate to a function app.
examples:
  - name: Upload an SSL certificate to a function app. (autogenerated)
    text: az functionapp config ssl upload --certificate-file {certificate-file} --certificate-password {certificate-password} --name MyFunctionApp     --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp config ssl import'] = """
type: command
short-summary: Import an SSL certificate to a function app from Key Vault.
examples:
  - name: Import an SSL certificate to a function app from Key Vault.
    text: az functionapp config ssl import --resource-group MyResourceGroup --name MyFunctionApp --key-vault MyKeyVault --key-vault-certificate-name MyCertificateName
  - name: Import an SSL certificate to a function app from Key Vault using resource id (typically if Key Vault is in another subscription).
    text: az functionapp config ssl import --resource-group MyResourceGroup --name MyFunctionApp --key-vault '/subscriptions/[sub id]/resourceGroups/[rg]/providers/Microsoft.KeyVault/vaults/[vault name]' --key-vault-certificate-name MyCertificateName
"""

helps['functionapp config ssl create'] = """
type: command
short-summary: Create a Managed Certificate for a hostname in a function app.
examples:
  - name: Create a Managed Certificate for cname.mycustomdomain.com.
    text: az functionapp config ssl create --resource-group MyResourceGroup --name MyWebapp --hostname cname.mycustomdomain.com
"""

helps['functionapp deployment config'] = """
type: group
short-summary: Manage a function app's deployment configuration.
"""

helps['functionapp deployment config set'] = """
type: command
short-summary: Update an existing function app's deployment configuration.
examples:
  - name: Set the function app's deployment storage.
    text: az functionapp deployment config set --name MyFunctionApp --resource-group MyResourceGroup --deployment-storage-name MyStorageAccount --deployment-storage-container-name MyStorageContainer
  - name: Set the function app's deployment storage authentication method.
    text: az functionapp deployment config set --name MyFunctionApp --resource-group MyResourceGroup --deployment-storage-auth-type userAssignedIdentity --deployment-storage-auth-value myAssignedId
"""

helps['functionapp deployment config show'] = """
type: command
short-summary: Get the details of a function app's deployment configuration.
examples:
  - name: Get the details of a function app's deployment configuration.
    text: az functionapp deployment config show --name MyFunctionApp --resource-group MyResourceGroup
"""

helps['functionapp runtime'] = """
type: group
short-summary: Manage a function app's runtime.
"""

helps['functionapp runtime config'] = """
type: group
short-summary: Manage a function app's runtime configuration.
"""

helps['functionapp runtime config set'] = """
type: command
short-summary: Update an existing function app's runtime configuration.
examples:
  - name: Set the function app's runtime version.
    text: az functionapp runtime config set --name MyFunctionApp --resource-group MyResourceGroup --runtime-version 3.11
"""

helps['functionapp runtime config show'] = """
type: command
short-summary: Get the details of a function app's runtime configuration.
examples:
  - name: Get the details of a function app's runtime configuration.
    text: az functionapp runtime config show --name MyFunctionApp --resource-group MyResourceGroup
"""

helps['functionapp scale'] = """
type: group
short-summary: Manage a function app's scale.
"""

helps['functionapp scale config'] = """
type: group
short-summary: Manage a function app's scale configuration.
"""

helps['functionapp scale config set'] = """
type: command
short-summary: Update an existing function app's scale configuration.
examples:
  - name: Set the function app's instance memory configuration.
    text: az functionapp scale config set --name MyFunctionApp --resource-group MyResourceGroup --instance-memory 2048
  - name: Set the function app's maximum instance count configuration.
    text: az functionapp scale config set --name MyFunctionApp --resource-group MyResourceGroup --maximum-instance-count 5
  - name: Set the function app's trigger configuration.
    text: az functionapp scale config set --name MyFunctionApp --resource-group MyResourceGroup --trigger-type http --trigger-settings perInstanceConcurrency=1
"""

helps['functionapp scale config show'] = """
type: command
short-summary: Get the details of a function app's scale configuration.
examples:
  - name: Get the details of a function app's scale configuration.
    text: az functionapp scale config show --name MyFunctionApp --resource-group MyResourceGroup
"""

helps['functionapp scale config always-ready'] = """
type: group
short-summary: Manage the always-ready settings in the scale configuration.
"""

helps['functionapp scale config always-ready delete'] = """
type: command
short-summary: Delete always-ready settings in the scale configuration.
examples:
  - name: Delete always-ready setings in the scale configuration.
    text: az functionapp scale config always-ready delete --name MyFunctionApp --resource-group MyResourceGroup --setting-names key1 key2
"""

helps['functionapp scale config always-ready set'] = """
type: command
short-summary: Add or update existing always-ready settings in the scale configuration.
examples:
  - name: Add or update existing always-ready settings in the scale configuration.
    text: az functionapp scale config always-ready set --name MyFunctionApp --resource-group MyResourceGroup --settings key1=value1 key2=value2
"""

helps['functionapp cors'] = """
type: group
short-summary: Manage Cross-Origin Resource Sharing (CORS)
"""

helps['functionapp cors add'] = """
type: command
short-summary: Add allowed origins
examples:
  - name: add a new allowed origin
    text: >
        az functionapp cors add -g {myRG} -n {myAppName} --allowed-origins https://myapps.com
"""

helps['functionapp cors remove'] = """
type: command
short-summary: Remove allowed origins
examples:
  - name: remove an allowed origin
    text: >
        az functionapp cors remove -g {myRG} -n {myAppName} --allowed-origins https://myapps.com
  - name: remove all allowed origins
    text: >
        az functionapp cors remove -g {myRG} -n {myAppName} --allowed-origins
"""

helps['functionapp cors show'] = """
type: command
short-summary: show allowed origins
examples:
  - name: show allowed origins (autogenerated)
    text: az functionapp cors show --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp cors credentials'] = """
type: command
short-summary: Enable or disable access-control-allow-credentials.
examples:
  - name: Enable CORS access-control-allow-credentials.
    text: az functionapp cors credentials --name MyFunctionApp --resource-group MyResourceGroup --enable true
    crafted: true
  - name: Disable CORS access-control-allow-credentials.
    text: az functionapp cors credentials --name MyFunctionApp --resource-group MyResourceGroup --enable false
    crafted: false
"""

helps['functionapp create'] = """
type: command
short-summary: Create a function app.
long-summary: The function app's name must be able to produce a unique FQDN as AppName.azurewebsites.net.
examples:
  - name: Create a basic function app.
    text: >
        az functionapp create -g MyResourceGroup  -p MyPlan -n MyUniqueAppName -s MyStorageAccount
  - name: Create a function app. (autogenerated)
    text: az functionapp create --consumption-plan-location westus --name MyUniqueAppName --os-type Windows --resource-group MyResourceGroup --runtime dotnet-isolated --storage-account MyStorageAccount
    crafted: true
  - name: Create a function app using a private ACR image.
    text: >
        az functionapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName --runtime node --storage-account MyStorageAccount --deployment-container-image-name myacr.azurecr.io/myimage:tag --docker-registry-server-password passw0rd --docker-registry-server-user MyUser
  - name: Create a flex consumption function app. See https://aka.ms/flex-http-concurrency for more information on default http concurrency values.
    text: >
        az functionapp create -g MyResourceGroup --name MyUniqueAppName -s MyStorageAccount --flexconsumption-location northeurope --runtime java --instance-memory 2048
"""

helps['functionapp delete'] = """
type: command
short-summary: Delete a function app.
examples:
  - name: Delete a function app. (autogenerated)
    text: az functionapp delete --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment'] = """
type: group
short-summary: Manage function app deployments.
"""

helps['functionapp deployment user show'] = """
type: command
short-summary: Gets publishing user.
"""

helps['functionapp deployment container'] = """
type: group
short-summary: Manage container-based continuous deployment.
"""

helps['functionapp deployment container config'] = """
type: command
short-summary: Configure continuous deployment via containers.
examples:
  - name: Configure continuous deployment via containers (autogenerated)
    text: az functionapp deployment container config --enable-cd true --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment container show-cd-url'] = """
type: command
short-summary: Get the URL which can be used to configure webhooks for continuous deployment.
examples:
  - name: Get the URL which can be used to configure webhooks for continuous deployment. (autogenerated)
    text: az functionapp deployment container show-cd-url --ids {ids}
    crafted: true
  - name: Get the URL which can be used to configure webhooks for continuous deployment. (autogenerated)
    text: az functionapp deployment container show-cd-url --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment list-publishing-credentials'] = """
type: command
short-summary: Get the details for available function app publishing credentials.
examples:
  - name: Get the details for available function app deployment publishing credentials.
    text: az functionapp deployment list-publishing-credentials --name MyFunctionApp   --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment list-publishing-profiles'] = """
type: command
short-summary: Get the details for available function app deployment profiles.
examples:
  - name: Get the details for available function app deployment profiles. (autogenerated)
    text: az functionapp deployment list-publishing-profiles --name MyFunctionApp   --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment slot'] = """
type: group
short-summary: Manage function app deployment slots.
"""

helps['functionapp deployment slot auto-swap'] = """
type: command
short-summary: Configure deployment slot auto swap.
"""

helps['functionapp deployment slot create'] = """
type: command
short-summary: Create a deployment slot.
examples:
  - name: Create a deployment slot. (autogenerated)
    text: az functionapp deployment slot create --name MyFunctionapp --resource-group MyResourceGroup --slot staging
    crafted: true
"""

helps['functionapp deployment slot delete'] = """
type: command
short-summary: Delete a deployment slot.
examples:
  - name: Delete a deployment slot. (autogenerated)
    text: az functionapp deployment slot delete --name MyFunctionapp --resource-group MyResourceGroup --slot staging
    crafted: true
"""

helps['functionapp deployment slot list'] = """
type: command
short-summary: List all deployment slots.
examples:
  - name: List all deployment slots. (autogenerated)
    text: az functionapp deployment slot list --name MyFunctionapp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment slot swap'] = """
type: command
short-summary: Swap deployment slots for a function app.
examples:
  - name: Swap a staging slot into production for the MyUniqueApp function app.
    text: >
        az functionapp deployment slot swap  -g MyResourceGroup -n MyUniqueApp --slot staging \\
            --target-slot production
"""

helps['functionapp deployment source'] = """
type: group
short-summary: Manage function app deployment via source control.
"""

helps['functionapp deployment source config'] = """
type: command
short-summary: Manage deployment from git or Mercurial repositories.
long-summary: Note that the GitHub action password is now redacted in the result. Please use the `az functionapp deployment source show` command to view the GitHub action password.
examples:
  - name: Manage deployment from git or Mercurial repositories. (autogenerated)
    text: az functionapp deployment source config --branch master --manual-integration --name MyFunctionApp --repo-url https://github.com/Azure-Samples/function-image-upload-resize --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment source config-local-git'] = """
type: command
short-summary: Get a URL for a git repository endpoint to clone and push to for function app deployment.
examples:
  - name: Get an endpoint and add it as a git remote.
    text: >
        az functionapp deployment source config-local-git \\
            -g MyResourceGroup -n MyUniqueApp

        git remote add azure \\
            https://{deploy_user_name}@MyUniqueApp.scm.azurewebsites.net/MyUniqueApp.git
"""

helps['functionapp deployment source config-zip'] = """
type: command
short-summary: Perform deployment using the kudu zip push deployment for a function app.
long-summary: >
    By default Kudu assumes that zip deployments do not require any build-related actions like
    npm install or dotnet publish. This can be overridden by including an .deployment file in your
    zip file with the following content '[config] SCM_DO_BUILD_DURING_DEPLOYMENT = true',
    to enable Kudu detection logic and build script generation process.
    See https://github.com/projectkudu/kudu/wiki/Configurable-settings#enabledisable-build-actions-preview.
    Alternately the setting can be enabled using the az functionapp config appsettings set command.
examples:
  - name: Perform deployment by using zip file content.
    text: >
        az functionapp deployment source config-zip \\
            -g {myRG} -n {myAppName} \\
            --src {zipFilePathLocation}
"""

helps['functionapp deployment source delete'] = """
type: command
short-summary: Delete a source control deployment configuration.
examples:
  - name: Delete a source control deployment configuration. (autogenerated)
    text: az functionapp deployment source delete --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment source show'] = """
type: command
short-summary: Get the details of a source control deployment configuration.
examples:
  - name: Get the details of a source control deployment configuration. (autogenerated)
    text: az functionapp deployment source show --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment source sync'] = """
type: command
short-summary: Synchronize from the repository. Only needed under manual integration mode.
examples:
  - name: Synchronize from the repository. Only needed under manual integration mode. (autogenerated)
    text: az functionapp deployment source sync --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp deployment user'] = """
type: group
short-summary: Manage user credentials for deployment.
"""

helps['functionapp deployment user set'] = """
type: command
short-summary: Update deployment credentials.
long-summary: All function and web apps in the subscription will be impacted since they share the same deployment credentials.
examples:
  - name: Set FTP and git deployment credentials for all apps.
    text: >
        az functionapp deployment user set --user-name MyUserName
"""

helps['functionapp deployment github-actions'] = """
type: group
short-summary: Configure GitHub Actions for a functionapp
"""

helps['functionapp deployment github-actions add'] = """
type: command
short-summary: Add a GitHub Actions workflow file to the specified repository. The workflow will build and deploy your app to the specified functionapp.
examples:
  - name: Add GitHub Actions to a specified repository, providing personal access token
    text: >
        az functionapp deployment github-actions add --repo "githubUser/githubRepo" -g MyResourceGroup -n MyFunctionapp --token MyPersonalAccessToken
  - name: Add GitHub Actions to a specified repository, using interactive method of retrieving personal access token
    text: >
        az functionapp deployment github-actions add --repo "githubUser/githubRepo" -g MyResourceGroup -n MyFunctionapp --login-with-github
"""

helps['functionapp deployment github-actions remove'] = """
type: command
short-summary: Remove and disconnect the GitHub Actions workflow file from the specified repository.
examples:
  - name: Remove GitHub Actions from a specified repository, providing personal access token
    text: >
        az functionapp deployment github-actions remove --repo "githubUser/githubRepo" -g MyResourceGroup -n MyFunctionapp --token MyPersonalAccessToken
  - name: Remove GitHub Actions from a specified repository, using interactive method of retrieving personal access token
    text: >
        az functionapp deployment github-actions remove --repo "githubUser/githubRepo" -g MyResourceGroup -n MyFunctionapp --login-with-github
"""

helps['functionapp function'] = """
type: group
short-summary: Manage function app functions.
"""

helps['functionapp function show'] = """
type: command
short-summary: Get the details of a function.
examples:
  - name: Show function details.
    text: >
        az functionapp function show -g MyResourceGroup -n MyFunctionAppName --function-name MyFunctionName
    crafted: true
"""

helps['functionapp function delete'] = """
type: command
short-summary: Delete a function.
examples:
  - name: Delete a function.
    text: >
        az functionapp function delete -g MyResourceGroup -n MyFunctionAppName --function-name MyFunctionName
    crafted: true
"""

helps['functionapp function list'] = """
type: command
short-summary: List functions in a function app.
examples:
  - name: List functions.
    text: >
        az functionapp function list -g MyResourceGroup -n MyFunctionAppName
"""

helps['functionapp function keys'] = """
type: group
short-summary: Manage function keys.
"""

helps['functionapp function keys set'] = """
type: command
short-summary: Create or update a function key.
long-summary: Note that key values are now redacted in the result. Please use the `az functionapp function keys list` command to view the key values.
examples:
  - name: Create a function key.
    text: >
        az functionapp function keys set -g MyResourceGroup -n MyFunctionAppName --function-name MyFunctionName --key-name MyKeyName --key-value MyKeyValue
    crafted: true
"""

helps['functionapp function keys list'] = """
type: command
short-summary: List all function keys.
examples:
  - name: List all function keys.
    text: >
        az functionapp function keys list -g MyResourceGroup -n MyFunctionAppName --function-name MyFunctionName
    crafted: true
"""

helps['functionapp function keys delete'] = """
type: command
short-summary: Delete a function key.
examples:
  - name: Delete a function key.
    text: >
        az functionapp function keys delete -g MyResourceGroup -n MyFunctionAppName --function-name MyFunctionName --key-name MyKeyName
    crafted: true
"""

helps['functionapp hybrid-connection'] = """
type: group
short-summary: methods that list, add and remove hybrid-connections from functionapp
"""

helps['functionapp hybrid-connection add'] = """
type: command
short-summary: add an existing hybrid-connection to a functionapp
examples:
  - name: add a hybrid-connection to a functionapp
    text: az functionapp hybrid-connection add -g MyResourceGroup -n MyWebapp --namespace [HybridConnectionNamespace] --hybrid-connection [HybridConnectionName] -s [slot]
"""

helps['functionapp hybrid-connection list'] = """
type: command
short-summary: list the hybrid-connections on a functionapp
examples:
  - name: list the hybrid-connections on a functionapp
    text: az functionapp hybrid-connection list -g MyResourceGroup -n MyWebapp -s [slot]
"""

helps['functionapp hybrid-connection remove'] = """
type: command
short-summary: remove a hybrid-connection from a functionapp
examples:
  - name: remove a hybrid-connection from a functionapp
    text: az functionapp hybrid-connection remove -g MyResourceGroup -n MyWebapp --namespace [HybridConnectionNamespace] --hybrid-connection [HybridConnectionName] -s [slot]
"""

helps['functionapp identity'] = """
type: group
short-summary: manage web app's managed identity
"""

helps['functionapp identity assign'] = """
type: command
short-summary: assign managed identity to the web app
examples:
  - name: assign local identity and assign a reader role to the current resource group.
    text: >
        az functionapp identity assign -g MyResourceGroup -n MyUniqueApp --role reader --scope /subscriptions/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx/resourcegroups/MyResourceGroup
  - name: enable identity for the web app.
    text: >
        az functionapp identity assign -g MyResourceGroup -n MyUniqueApp
  - name: assign local identity and a user assigned identity to a function app.
    text: >
        az functionapp identity assign -g MyResourceGroup -n MyUniqueApp --identities [system] myAssignedId
"""

helps['functionapp identity remove'] = """
type: command
short-summary: Disable web app's managed identity
examples:
  - name: Disable web app's system managed identity
    text: az functionapp identity remove --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
  - name: Disable web app's system managed identity and a user managed identity
    text: az functionapp identity remove --name MyFunctionApp --resource-group MyResourceGroup --identities [system] myAssignedId
"""

helps['functionapp identity show'] = """
type: command
short-summary: display web app's managed identity
examples:
  - name: display functionapp's managed identity (autogenerated)
    text: az functionapp identity show --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp keys'] = """
type: group
short-summary: Manage function app keys.
"""

helps['functionapp keys set'] = """
type: command
short-summary: Create or update a function app key.
long-summary: Note that key values are now redacted in the result. Please use the `az functionapp keys list` command to view the key values.
examples:
  - name: Create a function key for an Azure Function app.
    text: >
        az functionapp keys set -g MyResourceGroup -n MyFunctionAppName --key-type functionKeys --key-name MyKeyName --key-value MyKeyValue
    crafted: true
"""

helps['functionapp keys list'] = """
type: command
short-summary: List all function app keys.
examples:
  - name: List all keys for an Azure Function app.
    text: >
        az functionapp keys list -g MyResourceGroup -n MyFunctionAppName
    crafted: true
"""

helps['functionapp keys delete'] = """
type: command
short-summary: Delete a function app key.
examples:
  - name: Delete a master key for an Azure Function app.
    text: >
        az functionapp keys delete -g MyResourceGroup -n MyFunctionAppName --key-type masterKey --key-name MyKeyName
    crafted: true
"""

helps['functionapp list'] = """
type: command
short-summary: List function apps.
examples:
  - name: List all function apps in MyResourceGroup.
    text: >
        az functionapp list --resource-group MyResourceGroup
  - name: List default host name and state for all function apps.
    text: >
        az functionapp list --query "[].{hostName: defaultHostName, state: state}"
  - name: List all running function apps.
    text: >
        az functionapp list --query "[?state=='Running']"
"""

helps['functionapp list-consumption-locations'] = """
type: command
short-summary: List available locations for running function apps.
"""

helps['functionapp list-flexconsumption-locations'] = """
type: command
short-summary: List available locations for running function apps on the Flex Consumption plan.
"""

helps['functionapp list-flexconsumption-runtimes'] = """
type: command
short-summary: List available built-in stacks which can be used for function apps on the Flex Consumption plan.
"""

helps['functionapp flex-migration'] = """
type: group
short-summary: Manage migration of Linux Consumption function apps to the Flex Consumption plan.
"""

helps['functionapp flex-migration start'] = """
type: command
short-summary: Create a Flex Consumption app with the same settings as the provided Linux Consumption function app.
examples:
  - name: Migrate a Linux Consumption function app to the Flex Consumption plan.
    text: >
        az functionapp flex-migration start --source-name MyLinuxConsumptionApp --source-resource-group MyLinuxConsumptionResourceGroup --name MyFunctionApp --resource-group MyResourceGroup --storage-account MyStorageAccount

  - name: Migrate a Linux Consumption function app to the Flex Consumption plan without migrating managed identity configurations.
    text: >
        az functionapp flex-migration start --source-name MyLinuxConsumptionApp --source-resource-group MyLinuxConsumptionResourceGroup --name MyFunctionApp --resource-group MyResourceGroup --storage-account MyStorageAccount --skip-managed-identities
"""

helps['functionapp flex-migration list'] = """
type: command
short-summary: List all Linux Consumption function apps that are eligible for migration to the Flex Consumption plan.
examples:
  - name: List all Linux Consumption function apps that are eligible for migration to the Flex Consumption plan.
    text: >
        az functionapp flex-migration list
"""

helps['functionapp plan'] = """
type: group
short-summary: Manage App Service Plans for an Azure Function
"""

helps['functionapp plan create'] = """
type: command
short-summary: Create an App Service Plan for an Azure Function.
examples:
  - name: Create an elastic premium app service plan with burst out capability up to 10 instances.
    text: >
        az functionapp plan create -g MyResourceGroup -n MyPlan --min-instances 1 --max-burst 10 --sku EP1
  - name: Create a basic app service plan.
    text: >
        az functionapp plan create -g MyResourceGroup -n MyPlan --sku B1
  - name: Create an App Service Plan for an Azure Function. (autogenerated)
    text: az functionapp plan create --location westus2 --name MyPlan --number-of-workers 1 --resource-group MyResourceGroup --sku B1
    crafted: true
"""

helps['functionapp plan delete'] = """
type: command
short-summary: Delete an App Service Plan.
"""

helps['functionapp plan list'] = """
type: command
short-summary: List App Service Plans.
examples:
  - name: List all Elastic Premium 1 tier App Service plans.
    text: >
        az functionapp plan list --query "[?sku.tier=='EP1']"
"""

helps['functionapp plan show'] = """
type: command
short-summary: Get the App Service Plans for a resource group or a set of resource groups.
examples:
  - name: Get the app service plans for a resource group or a set of resource groups. (autogenerated)
    text: az functionapp plan show --name MyAppServicePlan --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp plan update'] = """
type: command
short-summary: Update an App Service plan for an Azure Function.
examples:
  - name: Update an app service plan to EP2 sku with twenty maximum workers.
    text: >
        az functionapp plan update -g MyResourceGroup -n MyPlan --max-burst 20 --sku EP2
"""

helps['functionapp restart'] = """
type: command
short-summary: Restart a function app.
examples:
  - name: Restart a function app. (autogenerated)
    text: az functionapp restart --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp show'] = """
type: command
short-summary: Get the details of a function app.
examples:
  - name: Get the details of a function app. (autogenerated)
    text: az functionapp show --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp start'] = """
type: command
short-summary: Start a function app.
examples:
  - name: Start a function app. (autogenerated)
    text: az functionapp start --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp stop'] = """
type: command
short-summary: Stop a function app.
examples:
  - name: Stop a function app. (autogenerated)
    text: az functionapp stop --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp update'] = """
type: command
short-summary: Update a function app.
examples:
  - name: Update a function app. (autogenerated)
    text: az functionapp update --name MyFunctionApp --resource-group MyResourceGroup
    crafted: true
"""

helps['functionapp vnet-integration'] = """
type: group
short-summary: methods that list, add, and remove virtual networks integrations from a functionapp
"""

helps['functionapp vnet-integration add'] = """
type: command
short-summary: Add a regional virtual network integration to a functionapp
long-summary: >
    If there are multiple vnets of the same name across different resource groups, use vnet resource id to specify
    which vnet to use. If vnet name is used, by default, the vnet in the same resource group as the functionapp will be used.
examples:
  - name: Add a regional virtual network integration to a functionapp
    text: az functionapp vnet-integration add -g MyResourceGroup -n MyFunctionapp --vnet MyVnetName --subnet MySubnetName -s [slot]
  - name: Add a regional virtual network integration to a functionapp using vnet resource id
    text: az functionapp vnet-integration add -g MyResourceGroup -n MyFunctionapp --vnet '/subscriptions/[sub id]/resourceGroups/[MyResourceGroup]/providers/Microsoft.Network/virtualNetworks/[MyVnetName]' --subnet MySubnetName -s [slot]
  - name: Add a regional virtual network integration to a functionapp using subnet resource id
    text: az functionapp vnet-integration add -g MyResourceGroup -n MyFunctionapp --vnet MyVnetName --subnet '/subscriptions/[sub id]/resourceGroups/[MyResourceGroup]/providers/Microsoft.Network/virtualNetworks/[MyVnetName]/subnets/MySubnetName' -s [slot]
"""

helps['functionapp vnet-integration list'] = """
type: command
short-summary: list the virtual network integrations on a functionapp
examples:
  - name: list the virtual networks integrations on a functionapp
    text: az functionapp vnet-integration list -g MyResourceGroup -n MyFunctionapp -s [slot]
"""

helps['functionapp vnet-integration remove'] = """
type: command
short-summary: remove a regional virtual network integration from functionapp
examples:
  - name: remove a regional virtual network integration from functionapp
    text: az functionapp vnet-integration remove -g MyResourceGroup -n MyFunctionapp -s [slot]
"""

helps['functionapp deploy'] = """
    type: command
    short-summary: Deploys a provided artifact to Azure functionapp.
    examples:
    - name: Deploy a war file asynchronously.
      text: az functionapp deploy --resource-group ResourceGroup --name AppName --src-path SourcePath --type war --async true
    - name: Deploy a static text file to wwwroot/staticfiles/test.txt
      text: az functionapp deploy --resource-group ResourceGroup --name AppName --src-path SourcePath --type static --target-path staticfiles/test.txt
"""

helps['webapp'] = """
type: group
short-summary: Manage web apps.
"""

helps['webapp auth'] = """
type: group
short-summary: Manage webapp authentication and authorization. To use v2 auth commands, run "az extension add --name authV2" to add the authV2 CLI extension.
"""

helps['webapp auth show'] = """
type: command
short-summary: Show the authentification settings for the webapp.
examples:
  - name: Show the authentification settings for the webapp. (autogenerated)
    text: az webapp auth show --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp auth update'] = """
type: command
short-summary: Update the authentication settings for the webapp.
examples:
  - name: Enable AAD by enabling authentication and setting AAD-associated parameters. Default provider is set to AAD. Must have created a AAD service principal beforehand.
    text: >
        az webapp auth update  -g myResourceGroup -n myUniqueApp --enabled true \\
          --action LoginWithAzureActiveDirectory \\
          --aad-allowed-token-audiences https://webapp_name.azurewebsites.net/.auth/login/aad/callback \\
          --aad-client-id ecbacb08-df8b-450d-82b3-3fced03f2b27 --aad-client-secret very_secret_password \\
          --aad-token-issuer-url https://sts.windows.net/54826b22-38d6-4fb2-bad9-b7983a3e9c5a/
  - name: Allow Facebook authentication by setting FB-associated parameters and turning on public-profile and email scopes; allow anonymous users
    text: >
        az webapp auth update -g myResourceGroup -n myUniqueApp --action AllowAnonymous \\
          --facebook-app-id my_fb_id --facebook-app-secret my_fb_secret \\
          --facebook-oauth-scopes public_profile email
"""

helps['webapp browse'] = """
type: command
short-summary: Open a web app in a browser. This is not supported in Azure Cloud Shell.
examples:
  - name: Open a web app in a browser. (autogenerated)
    text: az webapp browse --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config'] = """
type: group
short-summary: Configure a web app.
"""

helps['webapp config access-restriction'] = """
type: group
short-summary: Methods that show, set, add, and remove access restrictions on a webapp
"""

helps['webapp config access-restriction add'] = """
type: command
short-summary: Adds an Access Restriction to the webapp.
examples:
  - name: Add Access Restriction opening (Allow) named developers for IPv4 address ***********/27 with priority 200 to main site.
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --rule-name developers --action Allow --ip-address ***********/27 --priority 200
  - name: Add Access Restriction opening (Allow) named build_server for IPv4 address ***********/27 with priority 250 to scm site.
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --rule-name build_server --action Allow --ip-address ***********/27 --priority 250 --scm-site true
  - name: Add Access Restriction opening (Allow) named app_gateway for Subnet app_gw in vNet core_weu with priority 300 to main site.
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --rule-name app_gateway --action Allow --vnet-name core_weu --subnet app_gateway --priority 300
  - name: Add Access Restriction opening (Allow) named internal_agents for Subnet build_agents in vNet corp01 with priority 500 to scm site; and ignore service endpoint registration on the Subnet.
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --rule-name internal_agents --action Allow --vnet-name corp01 --subnet build_agents --priority 500 --scm-site true --ignore-missing-endpoint true
  - name: Add Access Restriction opening (Allow) named remote_agents in vNet 'corp01' in rg 'vnets' with subnet 'agents'
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --rule-name remote_agents --action Allow --vnet-name corp01 --subnet agents --priority 500 --vnet-resource-group vnets
  - name: Add Access Restriction opening (Allow) named agents in vNet 'corp01' in rg 'vnets' with subnet 'agents' (using subnet resource id)
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --rule-name remote_agents --action Allow --priority 800 --subnet '/subscriptions/<subscription-id>/resourceGroups/vnets/providers/Microsoft.Network/virtualNetworks/corp01/subnets/agents'
  - name: Add Access Restriction opening (Allow) with no rule name for service tag AzureCloud
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --priority 400 --service-tag AzureCloud
  - name: Add Access Restriction opening (Allow) with no rule name for service tag AzureFrontDoor.Backend and http-header X-Azure-FDID with value '12345678-abcd-1234-abcd-12345678910a'
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --priority 400 --service-tag AzureFrontDoor.Backend --http-header x-azure-fdid=12345678-abcd-1234-abcd-12345678910a
  - name: Add Access Restriction opening (Allow) with multiple http-header values for the same header 'X-Azure-FDID'
    text: az webapp config access-restriction add -g ResourceGroup -n AppName --priority 400 --service-tag AzureFrontDoor.Backend --http-header x-azure-fdid=12345678-abcd-1234-abcd-12345678910a x-azure-fdid=11111111-abcd-1234-abcd-222222222222
"""

helps['webapp config access-restriction remove'] = """
type: command
short-summary: Removes an Access Restriction from the webapp.
examples:
  - name: Remove Access Restriction named developers from the main site.
    text: az webapp config access-restriction remove -g ResourceGroup -n AppName --rule-name developers
  - name: Remove Access Restriction named internal_agents from the scm site.
    text: az webapp config access-restriction remove -g ResourceGroup -n AppName --rule-name internal_agents --scm-site true
  - name: Remove Access Restriction with service tag AzureFrontDoor.Backend from the main site.
    text: az webapp config access-restriction remove -g ResourceGroup -n AppName --service-tag AzureFrontDoor.Backend
"""

helps['webapp config access-restriction set'] = """
type: command
short-summary: Sets if SCM site is using the same restrictions as the main site and default actions.
examples:
  - name: Enable SCM site to use same access restrictions as main site.
    text: az webapp config access-restriction set -g ResourceGroup -n AppName --use-same-restrictions-for-scm-site true
  - name: Set default action to Allow for main site.
    text: az webapp config access-restriction set -g ResourceGroup -n AppName --default-action Allow
  - name: Set default action to Deny for scm site.
    text: az webapp config access-restriction set -g ResourceGroup -n AppName --scm-default-action Deny
"""

helps['webapp config access-restriction show'] = """
type: command
short-summary: Show Access Restriction settings for webapp.
examples:
  - name: Get Access Restriction settings for a webapp.
    text: az webapp config access-restriction show -g ResourceGroup -n AppName
"""

helps['webapp config appsettings'] = """
type: group
short-summary: Configure web app settings. Updating or removing application settings will cause an app recycle.
"""

helps['webapp config appsettings delete'] = """
type: command
short-summary: Delete web app settings.
long-summary: Note that setting values are now redacted in the result. Please use the `az webapp config appsettings list` command to view the settings.
examples:
  - name: Delete web app settings. (autogenerated)
    text: az webapp config appsettings delete --name MyWebApp --resource-group MyResourceGroup --setting-names {setting-names}
    crafted: true
"""

helps['webapp config appsettings list'] = """
type: command
short-summary: Get the details of a web app's settings.
examples:
  - name: Get the details of a web app's settings. (autogenerated)
    text: az webapp config appsettings list --name MyWebapp --resource-group MyResourceGroup --subscription MySubscription
    crafted: true
"""

helps['webapp config appsettings set'] = """
type: command
short-summary: Set a web app's settings.
long-summary: Note that setting values are now redacted in the result. Please use the `az webapp config appsettings list` command to view the settings.
parameters:
  - name: --settings
    short-summary: Space-separated appsettings in KEY=VALUE format. Use @{file} to load from a file. See https://go.microsoft.com/fwlink/?linkid=2219923 for more information on file format and editing app settings in bulk.
  - name: --slot-settings
    short-summary: Space-separated appsettings in KEY=VALUE format. Use @{file} to load from a file. Given setting are added to the configuration and marked as Deployment slot setting by default.
examples:
  - name: Set the default NodeJS version to 6.9.1 for a web app.
    text: >
        az webapp config appsettings set -g MyResourceGroup -n MyUniqueApp --settings WEBSITE_NODE_DEFAULT_VERSION=6.9.1
  - name: Set using both key-value pair and a json file with more settings.
    text: >
        az webapp config appsettings set -g MyResourceGroup -n MyUniqueApp --settings mySetting=value @moreSettings.json
"""

helps['webapp config backup'] = """
type: group
short-summary: Manage backups for web apps.
"""

helps['webapp config backup create'] = """
type: command
short-summary: Create a backup of a web app.
examples:
  - name: Create a backup of a web app. (autogenerated)
    text: az webapp config backup create --container-url {container-url} --resource-group MyResourceGroup --webapp-name MyWebapp
    crafted: true
"""

helps['webapp config backup list'] = """
type: command
short-summary: List backups of a web app.
examples:
  - name: List backups of a web app. (autogenerated)
    text: az webapp config backup list --resource-group MyResourceGroup --webapp-name MyWebapp
    crafted: true
"""

helps['webapp config backup restore'] = """
type: command
short-summary: Restore a web app from a backup.
"""

helps['webapp config backup delete'] = """
type: command
short-summary: Delete a web app backup.
"""

helps['webapp config backup show'] = """
type: command
short-summary: Show the backup schedule for a web app.
examples:
  - name: Show the backup schedule for a web app. (autogenerated)
    text: az webapp config backup show --resource-group MyResourceGroup --webapp-name MyWebapp
    crafted: true
"""

helps['webapp config backup update'] = """
type: command
short-summary: Configure a new backup schedule for a web app.
"""

helps['webapp config connection-string'] = """
type: group
short-summary: Manage a web app's connection strings.
"""

helps['webapp config connection-string delete'] = """
type: command
short-summary: Delete a web app's connection strings.
long-summary: Note that connection string values are now redacted in the result. Please use the `az webapp config connection-string list` command to view the values.
examples:
  - name: Delete a web app's connection strings. (autogenerated)
    text: az webapp config connection-string delete --name MyWebApp --resource-group MyResourceGroup --setting-names {setting-names}
    crafted: true
"""

helps['webapp config connection-string list'] = """
type: command
short-summary: Get a web app's connection strings.
examples:
  - name: Get a web app's connection strings. (autogenerated)
    text: az webapp config connection-string list --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config connection-string set'] = """
type: command
short-summary: Update a web app's connection strings.
long-summary: Note that connection string values are now redacted in the result. Please use the `az webapp config connection-string list` command to view the values.
examples:
  - name: Add a mysql connection string.
    text: >
        az webapp config connection-string set -g MyResourceGroup -n MyUniqueApp -t mysql \\
            --settings mysql1='Server=myServer;Database=myDB;Uid=myUser;Pwd=*****;'
"""

helps['webapp config container'] = """
type: group
short-summary: Manage an existing web app's container settings.
"""

helps['webapp config container delete'] = """
type: command
short-summary: Delete an existing web app's container settings.
examples:
  - name: Delete a web app container's settings. (autogenerated)
    text: az webapp config container delete --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config container set'] = """
type: command
short-summary: Set an existing web app's container settings.
examples:
  - name: Set a web app container's settings. (autogenerated)
    text: az webapp config container set --docker-custom-image-name MyDockerCustomImage --docker-registry-server-password StrongPassword --docker-registry-server-url https://{azure-container-registry-name}.azurecr.io --docker-registry-server-user DockerUserId --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config container show'] = """
type: command
short-summary: Get details of a web app's container settings.
examples:
  - name: Get details of a web app container's settings. (autogenerated)
    text: az webapp config container show --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config hostname'] = """
type: group
short-summary: Configure hostnames for a web app.
"""

helps['webapp config hostname add'] = """
type: command
short-summary: Bind a hostname to a web app.
examples:
  - name: Bind a hostname to a web app. (autogenerated)
    text: az webapp config hostname add --hostname cname.mycustomdomain.com --resource-group MyResourceGroup --webapp-name MyWebapp
    crafted: true
"""

helps['webapp config hostname delete'] = """
type: command
short-summary: Unbind a hostname from a web app.
"""

helps['webapp config hostname get-external-ip'] = """
type: command
short-summary: Get the external-facing IP address for a web app.
examples:
  - name: Get the external-facing IP address for a web app. (autogenerated)
    text: az webapp config hostname get-external-ip --resource-group MyResourceGroup --webapp-name MyWebapp
    crafted: true
"""

helps['webapp config hostname list'] = """
type: command
short-summary: List all hostname bindings for a web app.
examples:
  - name: List all hostname bindings for a web app. (autogenerated)
    text: az webapp config hostname list --resource-group MyResourceGroup --webapp-name MyWebapp
    crafted: true
"""

helps['webapp config set'] = """
type: command
short-summary: Set a web app's configuration.
examples:
  - name: turn on "alwaysOn"
    text: >
        az webapp config set -g MyResourceGroup -n MyUniqueApp --always-on true
  - name: set configuration through a JSON file called params.json
    text: >
        az webapp config set -g MyResourceGroup -n MyUniqueApp --generic-configurations "@.\\params.json"

"""

helps['webapp config show'] = """
type: command
short-summary: Get the details of a web app's configuration.
examples:
  - name: Get the details of a web app's configuration. (autogenerated)
    text: az webapp config show --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config snapshot'] = """
type: group
short-summary: Manage web app snapshots.
"""

helps['webapp config snapshot list'] = """
type: command
short-summary: List the restorable snapshots for a web app.
examples:
  - name: List the restorable snapshots for a web app. (autogenerated)
    text: az webapp config snapshot list --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config snapshot restore'] = """
type: command
short-summary: Restore a web app snapshot.
examples:
  - name: Restore web app files from a snapshot. Overwrites the web app's current files and settings.
    text: >
        az webapp config snapshot restore -g MyResourceGroup -n MySite --time 2018-12-11T23:34:16.8388367
  - name: Restore a snapshot of web app SourceApp to web app TargetApp. Use --restore-content-only to not restore app settings. Overwrites TargetApp's files.
    text: >
        az webapp config snapshot restore -g TargetResourceGroup -n TargetApp --source-name SourceApp --source-resource-group OriginalResourceGroup --time 2018-12-11T23:34:16.8388367 --restore-content-only
"""

helps['webapp config ssl'] = """
type: group
short-summary: Configure SSL certificates for web apps.
"""

helps['webapp config ssl bind'] = """
type: command
short-summary: Bind an SSL certificate to a web app.
examples:
  - name: Bind an SSL certificate to a web app. (autogenerated)
    text: az webapp config ssl bind --certificate-thumbprint {certificate-thumbprint} --name MyWebapp --resource-group MyResourceGroup --ssl-type SNI
    crafted: true
"""

helps['webapp config ssl delete'] = """
type: command
short-summary: Delete an SSL certificate from a web app.
examples:
  - name: Delete an SSL certificate from a web app. (autogenerated)
    text: az webapp config ssl delete --certificate-thumbprint {certificate-thumbprint} --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config ssl list'] = """
type: command
short-summary: List SSL certificates for a web app.
examples:
  - name: List SSL certificates for a web app. (autogenerated)
    text: az webapp config ssl list --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config ssl show'] = """
type: command
short-summary: Show the details of an SSL certificate for a web app.
examples:
  - name: Show the details of an SSL certificate for a web app. (autogenerated)
    text: az webapp config ssl show --resource-group MyResourceGroup --certificate-name cname.mycustomdomain.com
    crafted: true
"""

helps['webapp config ssl unbind'] = """
type: command
short-summary: Unbind an SSL certificate from a web app.
"""

helps['webapp config ssl upload'] = """
type: command
short-summary: Upload an SSL certificate to a web app.
examples:
  - name: Upload an SSL certificate to a web app. (autogenerated)
    text: az webapp config ssl upload --certificate-file {certificate-file} --certificate-password {certificate-password} --name MyWebapp     --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config ssl import'] = """
type: command
short-summary: Import an SSL or App Service Certificate to a web app from Key Vault.
examples:
  - name: Import an SSL or App Service Certificate certificate to a web app from Key Vault. Note that all webapps in the webspace will also be able to use the certificate.
    text: az webapp config ssl import --resource-group MyResourceGroup --name MyWebapp --key-vault MyKeyVault --key-vault-certificate-name MyCertificateName
  - name: Import an SSL or App Service Certificate to a web app from Key Vault using resource id (typically if Key Vault is in another subscription). Note that all webapps in the webspace will also be able to use the certificate.
    text: az webapp config ssl import --resource-group MyResourceGroup --name MyWebapp --key-vault '/subscriptions/[sub id]/resourceGroups/[rg]/providers/Microsoft.KeyVault/vaults/[vault name]' --key-vault-certificate-name MyCertificateName
  - name: Import an SSL or App Service Certificate certificate to a webspace from Key Vault. Note that all webapps in the webspace will also be able to use the certificate.
    text: az webapp config ssl import --resource-group MyResourceGroup --key-vault MyKeyVault --key-vault-certificate-name MyCertificateName
"""

helps['webapp config ssl create'] = """
type: command
short-summary: Create a Managed Certificate for a hostname in a webapp app.
examples:
  - name: Create a Managed Certificate for cname.mycustomdomain.com.
    text: az webapp config ssl create --resource-group MyResourceGroup --name MyWebapp --hostname cname.mycustomdomain.com
"""

helps['webapp config storage-account'] = """
type: group
short-summary: Manage a web app's Azure storage account configurations.
"""

helps['webapp config storage-account add'] = """
type: command
short-summary: Add an Azure storage account configuration to a web app.
long-summary: Note that storage account access keys are now redacted in the result. Please use the `az webapp config storage-account list` command to view the keys.
examples:
  - name: Add a connection to the Azure Files file share called MyShare in the storage account named MyStorageAccount.
    text: >
        az webapp config storage-account add -g MyResourceGroup -n MyUniqueApp \\
          --custom-id CustomId \\
          --storage-type AzureFiles \\
          --account-name MyStorageAccount \\
          --share-name MyShare \\
          --access-key MyAccessKey \\
          --mount-path /path/to/mount
"""

helps['webapp config storage-account delete'] = """
type: command
short-summary: Delete a web app's Azure storage account configuration.
long-summary: Note that storage account access keys are now redacted in the result. Please use the `az webapp config storage-account list` command to view the keys.
examples:
  - name: Delete a web app's Azure storage account configuration.
    text: az webapp config storage-account delete --custom-id CustomId --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config storage-account list'] = """
type: command
short-summary: Get a web app's Azure storage account configurations.
examples:
  - name: Get a web app's Azure storage account configurations.
    text: az webapp config storage-account list --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp config storage-account update'] = """
type: command
short-summary: Update an existing Azure storage account configuration on a web app.
long-summary: Note that storage account access keys are now redacted in the result. Please use the `az webapp config storage-account list` command to view the keys.
examples:
  - name: Update the mount path for a connection to the Azure Files file share with the ID MyId.
    text: >
        az webapp config storage-account update -g MyResourceGroup -n MyUniqueApp \\
          --custom-id CustomId \\
          --mount-path /path/to/new/mount
  - name: Update an existing Azure storage account configuration on a web app.
    text: az webapp config storage-account update --access-key MyAccessKey --account-name MyAccount --custom-id CustomId --mount-path /path/to/new/mount --name MyUniqueApp --resource-group MyResourceGroup --share-name MyShare --storage-type AzureFiles
    crafted: true
"""

helps['webapp cors'] = """
type: group
short-summary: Manage Cross-Origin Resource Sharing (CORS)
"""

helps['webapp cors add'] = """
type: command
short-summary: Add allowed origins
examples:
  - name: add a new allowed origin
    text: >
        az webapp cors add -g {myRG} -n {myAppName} --allowed-origins https://myapps.com
  - name: Add allowed origins (autogenerated)
    text: az webapp cors add --allowed-origins https://myapps.com --name MyWebApp --resource-group MyResourceGroup --subscription MySubscription
    crafted: true
"""

helps['webapp cors remove'] = """
type: command
short-summary: Remove allowed origins
examples:
  - name: remove an allowed origin
    text: >
        az webapp cors remove -g {myRG} -n {myAppName} --allowed-origins https://myapps.com
  - name: remove all allowed origins
    text: >
        az webapp cors remove -g {myRG} -n {myAppName} --allowed-origins
"""

helps['webapp cors show'] = """
type: command
short-summary: show allowed origins
examples:
  - name: show allowed origins (autogenerated)
    text: az webapp cors show --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp create'] = """
type: command
short-summary: Create a web app.
long-summary: The web app's name must be able to produce a unique FQDN as AppName.azurewebsites.net.
examples:
  - name: Create a web app with the default configuration.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName
  - name: Create a web app with a Java 21 runtime.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName --runtime "JAVA:21-java21"
  - name: Create a web app with a NodeJS 20 runtime and deployed from a local git repository.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName --runtime "node:20LTS" --deployment-local-git
  - name: Create a web app with both SCM and FTP Basic Auth Publishing Credentials disabled.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName --basic-auth Disabled
  - name: Create a web app which supports sitecontainers.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName --sitecontainers-app
  - name: Create a web app with an image from DockerHub.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName -i nginx
  - name: Create a web app with an image from a private DockerHub registry.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName -i MyImageName -s username -w password
  - name: Create a web app with an image from a private Azure Container Registry.
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName -i myregistry.azurecr.io/docker-image:tag
  - name: create a WebApp using shared App Service Plan that is in a different resource group.
    text: >
        AppServicePlanID=$(az appservice plan show -n SharedAppServicePlan -g MyASPRG --query "id" --out tsv)
        az webapp create -g MyResourceGroup -p "$AppServicePlanID" -n MyUniqueAppName
  - name: Create a container webapp with an image pulled from a private Azure Container Registry using a User Assigned Managed Identity
    text: >
        az webapp create -g MyResourceGroup -p MyPlan -n MyUniqueAppName --container-image-name myregistry.azurecr.io/docker-image:tag --assign-identity MyAssignIdentities --acr-use-identity --acr-identity MyUserAssignedIdentityResourceId
"""

helps['webapp create-remote-connection'] = """
type: command
short-summary: Creates a remote connection using a tcp tunnel to your web app
examples:
  - name: Create a remote connection using a tcp tunnel to your web app
    text: az webapp create-remote-connection --name MyWebApp --resource-group MyResourceGroup
"""

helps['webapp delete'] = """
type: command
short-summary: Delete a web app.
examples:
  - name: Delete a web app. (autogenerated)
    text: az webapp delete --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp deleted'] = """
type: group
short-summary: Manage deleted web apps.
"""

helps['webapp deleted list'] = """
type: command
short-summary: List web apps that have been deleted.
"""

helps['webapp deleted restore'] = """
type: command
short-summary: Restore a deleted web app.
long-summary: Restores the files and settings of a deleted web app to the specified web app.
examples:
  - name: Restore a deleted app to the Staging slot of MySite.
    text: >
        az webapp deleted restore -g MyResourceGroup -n MySite -s Staging --deleted-id /subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Web/locations/location/deletedSites/1234
  - name: Restore a deleted app to the app MySite. Do not restore the deleted app's settings.
    text: >
        az webapp deleted restore -g MyResourceGroup -n MySite --deleted-id /subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Web/locations/location/deletedSites/1234 --restore-content-only
  - name: Restore a deleted web app. (autogenerated)
    text: az webapp deleted restore --deleted-id /subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Web/deletedSites/1234 --name MySite --resource-group MyResourceGroup --subscription MySubscription
    crafted: true
"""

helps['webapp deployment'] = """
type: group
short-summary: Manage web app deployments.
"""

helps['webapp deployment container'] = """
type: group
short-summary: Manage container-based continuous deployment.
"""

helps['webapp deployment container config'] = """
type: command
short-summary: Configure continuous deployment via containers.
examples:
  - name: Configure continuous deployment via containers. (autogenerated)
    text: az webapp deployment container config --enable-cd true --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp deployment container show-cd-url'] = """
type: command
short-summary: Get the URL which can be used to configure webhooks for continuous deployment.
examples:
  - name: Get the URL which can be used to configure webhooks for continuous deployment (autogenerated)
    text: az webapp deployment container show-cd-url --name MyWebApp --resource-group MyResourceGroup --slot staging
    crafted: true
"""

helps['webapp deployment list-publishing-credentials'] = """
type: command
short-summary: Get the details for available web app publishing credentials
examples:
  - name: Get the details for available web app publishing credentials (autogenerated)
    text: az webapp deployment list-publishing-credentials --name MyWebapp --resource-group MyResourceGroup --subscription MySubscription
    crafted: true
"""

helps['webapp deployment list-publishing-profiles'] = """
type: command
short-summary: Get the details for available web app deployment profiles.
examples:
  - name: Get the details for available web app deployment profiles. (autogenerated)
    text: az webapp deployment list-publishing-profiles --name MyWebapp --resource-group MyResourceGroup --subscription MySubscription
    crafted: true
"""

helps['webapp deployment slot'] = """
type: group
short-summary: Manage web app deployment slots.
"""

helps['webapp deployment slot auto-swap'] = """
type: command
short-summary: Configure deployment slot auto swap.
examples:
  - name: Configure deployment slot auto swap. (autogenerated)
    text: az webapp deployment slot auto-swap --name MyWebapp --resource-group MyResourceGroup --slot staging
    crafted: true
"""

helps['webapp deployment slot create'] = """
type: command
short-summary: Create a deployment slot.
examples:
  - name: Create a deployment slot. (autogenerated)
    text: az webapp deployment slot create --name MyWebapp --resource-group MyResourceGroup --slot staging
    crafted: true
"""

helps['webapp deployment slot delete'] = """
type: command
short-summary: Delete a deployment slot.
examples:
  - name: Delete a deployment slot. (autogenerated)
    text: az webapp deployment slot delete --name MyWebapp --resource-group MyResourceGroup --slot staging
    crafted: true
"""

helps['webapp deployment slot list'] = """
type: command
short-summary: List all deployment slots.
examples:
  - name: List all deployment slots. (autogenerated)
    text: az webapp deployment slot list --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp deployment slot swap'] = """
type: command
short-summary: Swap deployment slots for a web app.
examples:
  - name: Swap a staging slot into production for the MyUniqueApp web app.
    text: >
        az webapp deployment slot swap  -g MyResourceGroup -n MyUniqueApp --slot staging \\
            --target-slot production
"""

helps['webapp deployment source'] = """
type: group
short-summary: Manage web app deployment via source control.
"""

helps['webapp deployment source config'] = """
type: command
short-summary: Manage deployment from git or Mercurial repositories.
long-summary: Note that the GitHub action password is now redacted in the result. Please use the `az webapp deployment source show` command to view the GitHub action password.
examples:
  - name: Manage deployment from git or Mercurial repositories. (autogenerated)
    text: az webapp deployment source config --branch master --manual-integration --name MyWebApp --repo-url https://github.com/Azure-Samples/function-image-upload-resize --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp deployment source config-local-git'] = """
type: command
short-summary: Get a URL for a git repository endpoint to clone and push to for web app deployment.
examples:
  - name: Get an endpoint and add it as a git remote.
    text: >
        az webapp deployment source config-local-git \\
            -g MyResourceGroup -n MyUniqueApp

        git remote add azure \\
            https://{deploy_user_name}@MyUniqueApp.scm.azurewebsites.net/MyUniqueApp.git
"""

helps['webapp deployment source config-zip'] = """
type: command
short-summary: Perform deployment using the kudu zip push deployment for a web app.
long-summary: >
    By default Kudu assumes that zip deployments do not require any build-related actions like
    npm install or dotnet publish. This can be overridden by including a .deployment file in your
    zip file with the following content '[config] SCM_DO_BUILD_DURING_DEPLOYMENT = true',
    to enable Kudu detection logic and build script generation process.
    See https://github.com/projectkudu/kudu/wiki/Configurable-settings#enabledisable-build-actions-preview.
    Alternately the setting can be enabled using the az webapp config appsettings set command.
examples:
  - name: Perform deployment by using zip file content.
    text: >
        az webapp deployment source config-zip \\
            -g {myRG} -n {myAppName} \\
            --src {zipFilePathLocation}
"""

helps['webapp deployment source delete'] = """
type: command
short-summary: Delete a source control deployment configuration.
examples:
  - name: Delete a source control deployment configuration. (autogenerated)
    text: az webapp deployment source delete --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp deployment source show'] = """
type: command
short-summary: Get the details of a source control deployment configuration.
examples:
  - name: Get the details of a source control deployment configuration. (autogenerated)
    text: az webapp deployment source show --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp deployment source sync'] = """
type: command
short-summary: Synchronize from the repository. Only needed under manual integration mode.
examples:
  - name: Synchronize from the repository. Only needed under manual integration mode. (autogenerated)
    text: az webapp deployment source sync --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp deployment user'] = """
type: group
short-summary: Manage user credentials for deployment.
"""

helps['webapp deployment user set'] = """
type: command
short-summary: Update deployment credentials.
long-summary: All function and web apps in the subscription will be impacted since they share the same deployment credentials.
examples:
  - name: Set FTP and git deployment credentials for all apps.
    text: >
        az webapp deployment user set --user-name MyUserName
"""

helps['webapp deployment user show'] = """
type: command
short-summary: Get deployment publishing user.
examples:
  - name: Get publishing user information.
    text: >
        az webapp deployment user show
"""

helps['webapp deployment github-actions'] = """
type: group
short-summary: Configure GitHub Actions for a webapp
"""

helps['webapp deployment github-actions add'] = """
type: command
short-summary: Add a GitHub Actions workflow file to the specified repository. The workflow will build and deploy your app to the specified webapp.
examples:
  - name: Add GitHub Actions to a specified repository, providing personal access token
    text: >
        az webapp deployment github-actions add --repo "githubUser/githubRepo" -g MyResourceGroup -n MyWebapp --token MyPersonalAccessToken
  - name: Add GitHub Actions to a specified repository, using interactive method of retrieving personal access token
    text: >
        az webapp deployment github-actions add --repo "githubUser/githubRepo" -g MyResourceGroup -n MyWebapp --login-with-github
"""

helps['webapp deployment github-actions remove'] = """
type: command
short-summary: Remove and disconnect the GitHub Actions workflow file from the specified repository.
examples:
  - name: Remove GitHub Actions from a specified repository, providing personal access token
    text: >
        az webapp deployment github-actions remove --repo "githubUser/githubRepo" -g MyResourceGroup -n MyWebapp --token MyPersonalAccessToken
  - name: Remove GitHub Actions from a specified repository, using interactive method of retrieving personal access token
    text: >
        az webapp deployment github-actions remove --repo "githubUser/githubRepo" -g MyResourceGroup -n MyWebapp --login-with-github
"""

helps['webapp hybrid-connection'] = """
type: group
short-summary: methods that list, add and remove hybrid-connections from webapps
"""

helps['webapp hybrid-connection add'] = """
type: command
short-summary: add an existing hybrid-connection to a webapp
examples:
  - name: add a hybrid-connection to a webapp
    text: az webapp hybrid-connection add -g MyResourceGroup -n MyWebapp --namespace [HybridConnectionNamespace] --hybrid-connection [HybridConnectionName] -s [slot]
"""

helps['webapp hybrid-connection list'] = """
type: command
short-summary: list the hybrid-connections on a webapp
examples:
  - name: list the hybrid-connections on a webapp
    text: az webapp hybrid-connection list -g MyResourceGroup -n MyWebapp -s [slot]
"""

helps['webapp hybrid-connection remove'] = """
type: command
short-summary: remove a hybrid-connection from a webapp
examples:
  - name: remove a hybrid-connection from a webapp
    text: az webapp hybrid-connection remove  -g MyResourceGroup -n MyWebapp --namespace [HybridConnectionNamespace] --hybrid-connection [HybridConnectionName] -s [slot]
"""

helps['webapp identity'] = """
type: group
short-summary: manage web app's managed identity
"""

helps['webapp identity assign'] = """
type: command
short-summary: assign managed identity to the web app
examples:
  - name: assign local identity and assign a reader role to the current resource group.
    text: >
        az webapp identity assign -g MyResourceGroup -n MyUniqueApp --role reader --scope /subscriptions/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx/resourcegroups/MyResourceGroup
  - name: enable identity for the web app.
    text: >
        az webapp identity assign -g MyResourceGroup -n MyUniqueApp
  - name: assign local identity and a user assigned identity to a webapp.
    text: >
        az webapp identity assign -g MyResourceGroup -n MyUniqueApp --identities [system] myAssignedId
"""

helps['webapp identity remove'] = """
type: command
short-summary: Disable web app's managed identity
examples:
  - name: Disable web app's system managed identity
    text: az webapp identity remove --name MyWebApp --resource-group MyResourceGroup
    crafted: true
  - name: Disable web app's system managed identity and a user managed identity
    text: az webapp identity remove --name MyWebApp --resource-group MyResourceGroup --identities [system] myAssignedId
"""

helps['webapp identity show'] = """
type: command
short-summary: display web app's managed identity
examples:
  - name: display webapp's managed identity (autogenerated)
    text: az webapp identity show --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp list'] = """
type: command
short-summary: List web apps.
examples:
  - name: List all web apps in MyResourceGroup.
    text: >
        az webapp list --resource-group MyResourceGroup
  - name: List default host name and state for all web apps.
    text: >
        az webapp list --query "[].{hostName: defaultHostName, state: state}"
  - name: List all running web apps.
    text: >
        az webapp list --query "[?state=='Running']"
"""

helps['webapp list-instances'] = """
type: command
short-summary: List all scaled out instances of a web app or web app slot.
"""

helps['webapp list-runtimes'] = """
type: command
short-summary: List available built-in stacks which can be used for web apps.
"""

helps['functionapp list-runtimes'] = """
type: command
short-summary: List available built-in stacks which can be used for function apps.
"""

helps['webapp log'] = """
type: group
short-summary: Manage web app logs.
"""

helps['webapp log config'] = """
type: command
short-summary: Configure logging for a web app.
examples:
  - name: Configure logging for a web app. (autogenerated)
    text: az webapp log config --name MyWebapp --resource-group MyResourceGroup --web-server-logging off
    crafted: true
  - name: Configure logging for a web app. (autogenerated)
    text: az webapp log config --docker-container-logging off --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp log download'] = """
type: command
short-summary: Download a web app's log history as a zip file.
long-summary: This command may not work with web apps running on Linux.
examples:
  - name: Download a web app's log history as a zip file. (autogenerated)
    text: az webapp log download --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp log show'] = """
type: command
short-summary: Get the details of a web app's logging configuration.
examples:
  - name: Get the details of a web app's logging configuration. (autogenerated)
    text: az webapp log show --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp log tail'] = """
type: command
short-summary: Start live log tracing for a web app.
"""

helps['webapp log deployment'] = """
type: group
short-summary: Manage web app deployment logs.
"""

helps['webapp log deployment show'] = """
type: command
short-summary: Show deployment logs of the latest deployment, or a specific deployment if deployment-id is specified.
examples:
  - name: Show the deployment logs of the latest deployment
    text: az webapp log deployment show --name MyWebApp --resource-group MyResourceGroup
  - name: Show the deployment logs of a particular deployment
    text: az webapp log deployment show --name MyWebApp --resource-group MyResourceGroup --deployment-id MyDeploymentId
"""

helps['webapp log deployment list'] = """
type: command
short-summary: List deployments associated with web app
examples:
  - name: List the deployment logs
    text: az webapp log deployment list --name MyWebApp --resource-group MyResourceGroup
"""

helps['functionapp log'] = """
type: group
short-summary: Manage function app logs.
"""

helps['functionapp log deployment'] = """
type: group
short-summary: Manage function app deployment logs.
"""

helps['functionapp log deployment show'] = """
type: command
short-summary: Show deployment logs of the latest deployment, or a specific deployment if deployment-id is specified.
examples:
  - name: Show the deployment logs of the latest deployment
    text: az functionapp log deployment show --name MyFunctionApp --resource-group MyResourceGroup
  - name: Show the deployment logs of a particular deployment
    text: az functionapp log deployment show --name MyFunctionApp --resource-group MyResourceGroup --deployment-id MyDeploymentId
"""

helps['functionapp log deployment list'] = """
type: command
short-summary: List deployment logs of the deployments associated with function app
examples:
  - name: List the deployment logs
    text: az functionapp log deployment list --name MyFunctionApp --resource-group MyResourceGroup
"""

helps['webapp restart'] = """
type: command
short-summary: Restart a web app.
examples:
  - name: Restart a web app. (autogenerated)
    text: az webapp restart --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp show'] = """
type: command
short-summary: Get the details of a web app.
examples:
  - name: Get the details of a web app. (autogenerated)
    text: az webapp show --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp ssh'] = """
type: command
short-summary: SSH command establishes a ssh session to the web container and developer would get a shell terminal remotely.
examples:
  - name: ssh into a web app
    text: >
        az webapp ssh -n MyUniqueAppName -g MyResourceGroup
"""

helps['webapp start'] = """
type: command
short-summary: Start a web app.
examples:
  - name: Start a web app. (autogenerated)
    text: az webapp start --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp stop'] = """
type: command
short-summary: Stop a web app.
examples:
  - name: Stop a web app. (autogenerated)
    text: az webapp stop --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp traffic-routing'] = """
type: group
short-summary: Manage traffic routing for web apps.
"""

helps['webapp traffic-routing clear'] = """
type: command
short-summary: Clear the routing rules and send all traffic to production.
examples:
  - name: Clear the routing rules and send all traffic to production. (autogenerated)
    text: az webapp traffic-routing clear --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp traffic-routing set'] = """
type: command
short-summary: Configure routing traffic to deployment slots.
examples:
  - name: Configure routing traffic to deployment slots. (autogenerated)
    text: az webapp traffic-routing set --distribution staging=50 --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp traffic-routing show'] = """
type: command
short-summary: Display the current distribution of traffic across slots.
examples:
  - name: Display the current distribution of traffic across slots. (autogenerated)
    text: az webapp traffic-routing show --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp sitecontainers'] = """
type: group
short-summary: Manage linux web apps sitecontainers.
"""

helps['webapp sitecontainers create'] = """
type: command
short-summary: Create sitecontainers for a linux webapp
long-summary: |
    Multiple sitecontainers can be added or updated at once by passing arg --sitecontainers-spec-file, which is the path to a json file containing an array of sitecontainer specs.
    Example json file:
    [
    {
        "name": "firstcontainer",
        "properties": {
            "image": "myregistry.io/firstimage:latest",
            "targetPort": "80",
            "isMain": true,
            "environmentVariables": [
                {
                    "name": "VARIABLE_1",
                    "value": "APPSETTING_NAME1"
                }
            ],
            "volumeMounts": [
                {
                    "containerMountPath": "mountPath",
                    "readOnly": true,
                    "volumeSubPath": "subPath"
                }
            ]
        }
    },
    {
        "name": "secondcontainer",
        "properties": {
            "image": "myregistry.io/secondimage:latest",
            "targetPort": "3000",
            "isMain": false,
            "authType": "SystemIdentity",
            "startUpCommand": "MyStartupCmd"
        }
    },
    {
        "name": "thirdcontainer",
        "properties": {
            "image": "myregistry.io/thirdimage:latest",
            "targetPort": "3001",
            "isMain": false,
            "authType": "UserAssigned",
            "userManagedIdentityClientId": "ClientID"
        }
    },
    {
        "name": "fourthcontainer",
        "properties": {
            "image": "myregistry.io/fourthimage:latest",
            "targetPort": "3002",
            "isMain": false,
            "authType": "UserCredentials",
            "userName": "Username",
            "passwordSecret": "Password"
        }
    }
    ]
examples:
  - name: Create a main sitecontainer for a linux webapp
    text: az webapp sitecontainers create --name MyWebApp --resource-group MyResourceGroup --container-name MyContainer --image MyImageRegistry.io/MyImage:latest --target-port 80 --is-main
  - name : Create or update multiple sitecontainers for a linux webapp using a json sitecontainer-spec file
    text: az webapp sitecontainers create --name MyWebApp --resource-group MyResourceGroup --sitecontainers-spec-file ./sitecontainersspec.json
"""


helps['webapp sitecontainers update'] = """
type: command
short-summary: Update an existing sitecontainer for a linux webapp
examples:
  - name: Update a sitecontainer for a linux webapp
    text: az webapp sitecontainers update --name MyWebApp --resource-group MyResourceGroup --container-name MyContainer --image MyImageRegistry.io/MyImage:latest --target-port 3000 --is-main false
"""


helps['webapp sitecontainers delete'] = """
type: command
short-summary: Delete a sitecontainer for a linux webapp
examples:
  - name: Delete a sitecontainer for a linux webapp
    text: az webapp sitecontainers delete --name MyWebApp --resource-group MyResourceGroup --container-name MyContainer
"""


helps['webapp sitecontainers show'] = """
type: command
short-summary: List the details of a sitecontainer for a linux webapp
examples:
  - name: List the details of a sitecontainer for a linux webapp
    text: az webapp sitecontainers show --name MyWebApp --resource-group MyResourceGroup --container-name MyContainer
"""


helps['webapp sitecontainers list'] = """
type: command
short-summary: List all the sitecontainers for a linux webapp
examples:
  - name: List all the sitecontainers for a linux webapp
    text: az webapp sitecontainers list --name MyWebApp --resource-group MyResourceGroup
"""


helps['webapp sitecontainers status'] = """
type: command
short-summary: Get the status of a sitecontainer for a linux webapp
examples:
  - name: Get the status of a sitecontainer for a linux webapp
    text: az webapp sitecontainers status --name MyWebApp --resource-group MyResourceGroup --container-name MyContainer
"""

helps['webapp sitecontainers log'] = """
type: command
short-summary: Get the logs of a sitecontainer for a linux webapp
examples:
  - name: Get the logs of a sitecontainer for a linux webapp
    text: az webapp sitecontainers log --name MyWebApp --resource-group MyResourceGroup --container-name MyContainer
"""

helps['webapp sitecontainers convert'] = """
type: command
short-summary: Convert a webapp from sitecontainers to a classic custom container and vice versa.
examples:
  - name: Convert a webapp to classic custom container (docker) from sitecontainers
    text: az webapp sitecontainers convert --mode docker --name MyWebApp --resource-group MyResourceGroup
  - name: Convert a webapp to sitecontainers from classic custom container (docker)
    text: az webapp sitecontainers convert --mode sitecontainers --name MyWebApp --resource-group MyResourceGroup
"""


helps['webapp up'] = """
type: command
short-summary: >
    Create a webapp and deploy code from a local workspace to the app. The command is required to run from the folder
    where the code is present. Current support includes Node, Python, .NET Core and ASP.NET. Node,
    Python apps are created as Linux apps. .Net Core, ASP.NET, and static HTML apps are created as Windows apps.
    Append the html flag to deploy as a static HTML app.
    Each time the command is successfully run, default argument values for resource group, sku, location, plan, and name are saved for the current directory.
    These defaults are then used for any arguments not provided on subsequent runs of the command in the same directory.  Use 'az configure' to manage defaults.
    Run this command with the --debug parameter to see the API calls and parameters values being used.

examples:
  - name: View the details of the app that will be created, without actually running the operation
    text: >
        az webapp up --dryrun
  - name: Create a web app with the default configuration, by running the command from the folder where the code to be deployed exists.
    text: >
        az webapp up
  - name: Create a web app with a specified name
    text: >
        az webapp up -n MyUniqueAppName
  - name: Create a web app with a specified name and a Java 11 runtime
    text: >
        az webapp up -n MyUniqueAppName --runtime "java:11:Java SE:11"
  - name: Create a web app in a specific region, by running the command from the folder where the code to be deployed exists.
    text: >
        az webapp up -l locationName
  - name: Create a web app and enable log streaming after the deployment operation is complete. This will enable the default configuration required to enable log streaming.
    text: >
        az webapp up --logs
  - name: Create a web app and deploy as a static HTML app.
    text: >
        az webapp up --html
"""

helps['webapp update'] = """
type: command
short-summary: Update an existing web app.
examples:
  - name: Update the tags of a web app.
    text: >
        az webapp update -g MyResourceGroup -n MyAppName --set tags.tagName=tagValue
  - name: Update a web app. (autogenerated)
    text: az webapp update --https-only true --name MyAppName --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp vnet-integration'] = """
type: group
short-summary: methods that list, add, and remove virtual network integrations from a webapp
"""

helps['webapp vnet-integration add'] = """
type: command
short-summary: Add a regional virtual network integration to a webapp
long-summary: >
    If there are multiple vnets of the same name across different resource groups, use vnet resource id to specify
    which vnet to use. If vnet name is used, by default, the vnet in the same resource group as the webapp will be used.
examples:
  - name: Add a regional virtual network integration to a webapp
    text: az webapp vnet-integration add -g MyResourceGroup -n MyWebapp --vnet MyVnetName --subnet MySubnetName -s [slot]
  - name: Add a regional virtual network integration to a webapp using vnet resource id
    text: az webapp vnet-integration add -g MyResourceGroup -n MyWebapp --vnet /subscriptions/[SubscriptionId]/resourceGroups/[MyResourceGroup]/providers/Microsoft.Network/virtualNetworks/[MyVnetName] --subnet MySubnetName -s [slot]
  - name: Add a regional virtual network integration to a webapp using subnet resource id
    text: az webapp vnet-integration add -g MyResourceGroup -n MyWebapp --vnet MyVnetName --subnet /subscriptions/[SubscriptionId]/resourceGroups/[MyResourceGroup]/providers/Microsoft.Network/virtualNetworks/[MyVnetName]/subnets/MySubnetName -s [slot]
"""

helps['webapp vnet-integration list'] = """
type: command
short-summary: list the virtual network integrations on a webapp
examples:
  - name: list the virtual network integrations on a webapp
    text: az webapp vnet-integration list -g MyResourceGroup -n MyWebapp -s [slot]
"""

helps['webapp vnet-integration remove'] = """
type: command
short-summary: remove a regional virtual network integration from webapp
examples:
  - name: remove a regional virtual network integration from webapp
    text: az webapp vnet-integration remove -g MyResourceGroup -n MyWebapp -s [slot]
"""

helps['webapp webjob'] = """
type: group
short-summary: Allows management operations for webjobs on a web app.
"""

helps['webapp webjob continuous'] = """
type: group
short-summary: Allows management operations of continuous webjobs on a web app.
"""

helps['webapp webjob continuous list'] = """
type: command
short-summary: List all continuous webjobs on a selected web app.
examples:
  - name: List all continuous webjobs on a selected webapp. (autogenerated)
    text: az webapp webjob continuous list --name MyWebapp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp webjob continuous remove'] = """
type: command
short-summary: Delete a specific continuous webjob.
examples:
  - name: Delete a specific continuous webjob. (autogenerated)
    text: az webapp webjob continuous remove --name MyWebApp --resource-group MyResourceGroup --webjob-name MyWebjob
    crafted: true
"""

helps['webapp webjob continuous start'] = """
type: command
short-summary: Start a specific continuous webjob on a selected web app.
examples:
  - name: Start a specific continuous webjob on a selected web app. (autogenerated)
    text: az webapp webjob continuous start --name MyWebApp --resource-group MyResourceGroup --webjob-name MyWebjob
    crafted: true
"""

helps['webapp webjob continuous stop'] = """
type: command
short-summary: Stop a specific continuous webjob.
examples:
  - name: Stop a specific continuous webjob. (autogenerated)
    text: az webapp webjob continuous stop --name MyWebApp --resource-group MyResourceGroup --webjob-name MyWebjob
    crafted: true
"""

helps['webapp webjob triggered'] = """
type: group
short-summary: Allows management operations of triggered webjobs on a web app.
"""

helps['webapp webjob triggered list'] = """
type: command
short-summary: List all triggered webjobs hosted on a web app.
examples:
  - name: List all triggered webjobs hosted on a web app. (autogenerated)
    text: az webapp webjob triggered list --name MyWebApp --resource-group MyResourceGroup
    crafted: true
"""

helps['webapp webjob triggered log'] = """
type: command
short-summary: Get history of a specific triggered webjob hosted on a web app.
examples:
  - name: Get history of a specific triggered webjob hosted on a web app. (autogenerated)
    text: az webapp webjob triggered log --name MyWebApp --resource-group MyResourceGroup --subscription MySubscription --webjob-name MyWebjob
    crafted: true
"""

helps['webapp webjob triggered remove'] = """
type: command
short-summary: Delete a specific triggered webjob hosted on a web app.
examples:
  - name: Delete a specific triggered webjob hosted on a web app. (autogenerated)
    text: az webapp webjob triggered remove --name MyWebApp --resource-group MyResourceGroup --webjob-name MyWebjob
    crafted: true
"""

helps['webapp webjob triggered run'] = """
type: command
short-summary: Run a specific triggered webjob hosted on a web app.
examples:
  - name: Run a specific triggered webjob hosted on a web app. (autogenerated)
    text: az webapp webjob triggered run --name MyWebApp --resource-group MyResourceGroup --webjob-name MyWebjob
    crafted: true
"""

helps['appservice ase'] = """
type: group
short-summary: Manage App Service Environments
"""

helps['appservice ase list'] = """
    type: command
    short-summary: List App Service Environments.
    examples:
    - name: List all App Service Environments in subscription.
      text: az appservice ase list
    - name: List all App Service Environment in resource group.
      text: az appservice ase list --resource-group MyResourceGroup
"""

helps['appservice ase show'] = """
    type: command
    short-summary: Show details of an App Service Environment.
    examples:
    - name: Show App Service Environment.
      text: az appservice ase show --name MyAseName
"""

helps['appservice ase list-addresses'] = """
    type: command
    short-summary: List IP addresses associated with an App Service Environment.
    examples:
    - name: List IPs for an App Service Environment.
      text: az appservice ase list-addresses --name MyAseName
"""

helps['appservice ase list-plans'] = """
    type: command
    short-summary: List app service plans associated with an App Service Environment.
    examples:
    - name: List app service plans for an App Service Environments.
      text: az appservice ase list-plans --name MyAseName
"""

helps['appservice ase create'] = """
    type: command
    short-summary: Create App Service Environment.
    examples:
    - name: Create resource group, Virtual Network and App Service Environment with default values.
      text: |
          az group create -g MyResourceGroup --location westeurope

          az network vnet create -g MyResourceGroup -n MyVirtualNetwork \\
            --address-prefixes 10.0.0.0/16 --subnet-name MyAseSubnet --subnet-prefixes 10.0.0.0/24

          az appservice ase create -n MyAseName -g MyResourceGroup --vnet-name MyVirtualNetwork \\
            --subnet MyAseSubnet
    - name: Create external App Service Environments in existing resource group and Virtual Network.
      text: |
          az appservice ase create -n MyAseName -g MyResourceGroup --vnet-name MyVirtualNetwork \\
            --subnet MyAseSubnet --virtual-ip-type External
    - name: Create Virtual Network and App Service Environment in a smaller than recommended subnet in existing resource group.
      text: |
          az network vnet create -g MyResourceGroup -n MyVirtualNetwork \\
            --address-prefixes 10.0.0.0/16 --subnet-name MyAseSubnet --subnet-prefixes 10.0.0.0/26

          az appservice ase create -n MyAseName -g MyResourceGroup --vnet-name MyVirtualNetwork \\
            --subnet MyAseSubnet --ignore-subnet-size-validation
    - name: Create external zone redundant App Service Environment with default values.
      text: |
          az appservice ase create -n MyASEName -g ASEResourceGroup \\
            --vnet-name MyASEVirtualNetwork --subnet MyASESubnet \\
            --zone-redundant --virtual-ip-type External
"""

helps['appservice ase create-inbound-services'] = """
    type: command
    short-summary: Private DNS Zone for Internal (ILB) App Service Environments.
    examples:
    - name: Create Private DNS Zone and A records.
      text: |
          az appservice ase create-inbound-services -n MyASEName -g ASEResourceGroup \\
            --vnet-name MyASEVirtualNetwork --subnet MyAseSubnet
"""

helps['appservice ase upgrade'] = """
    type: command
    short-summary: Upgrade App Service Environment.
    examples:
    - name: Upgrade App Service Environment.
      text: |
          az appservice ase upgrade -n MyAseName -g MyResourceGroup
"""

helps['appservice ase send-test-notification'] = """
    type: command
    short-summary: Send a test upgrade notification in App Service Environment.
    examples:
    - name: Send a test upgrade notification in App Service Environment.
      text: |
          az appservice ase send-test-notification -n MyAseName -g MyResourceGroup
"""

helps['appservice ase update'] = """
    type: command
    short-summary: Update App Service Environment.
    examples:
    - name: Update App Service Environment to allow new private endpoint connections.
      text: |
          az appservice ase update -n MyAseName -g MyResourceGroup --allow-new-private-endpoint-connections
    - name: Update App Service Environment to allow incoming ftp connections.
      text: |
          az appservice ase update -n MyAseName -g MyResourceGroup --allow-incoming-ftp-connections
    - name: Update App Service Environment to allow remote debugging.
      text: |
          az appservice ase update -n MyAseName -g MyResourceGroup --allow-remote-debugging
"""

helps['appservice ase delete'] = """
    type: command
    short-summary: Delete App Service Environment.
    examples:
    - name: Delete App Service Environment.
      text: az appservice ase delete -n MyAseName
"""

helps['appservice domain'] = """
    type: group
    short-summary: Manage custom domains.
"""

helps['appservice domain create'] = """
    type: command
    short-summary: Create and purchase a custom domain.
    examples:
    - name: Accept the legal terms for purchasing and creating MyCustomDomain.com, then purchase and create domain.
      text: az appservice domain create -g MyResourceGroup --hostname MyCustomDomain.com --contact-info=@'C:/path_to_contact_info.json' --accept-terms
    - name: View the details of the domain that will be purchased and created, without actually running the operation
      text: az appservice domain create -g MyResourceGroup --hostname MyCustomDomain.com --contact-info=@'C:/path_to_contact_info.json' --dryrun
"""

helps['appservice domain show-terms'] = """
    type: command
    short-summary: Show the legal terms for purchasing and creating a custom domain.
    examples:
    - name: Show the legal terms for purchasing and creating MyCustomDomain.com
      text: az appservice domain show-terms --hostname MyCustomDomain.com
"""

helps['staticwebapp'] = """
    type: group
    short-summary: Manage static apps.
"""

helps['staticwebapp list'] = """
    type: command
    short-summary: List all static app resources in a subscription, or in resource group if provided
    examples:
    - name: List static apps in a subscription.
      text: az staticwebapp list
"""

helps['staticwebapp show'] = """
    type: command
    short-summary: Show details of a static app.
    examples:
    - name: Show static app in a subscription.
      text: az staticwebapp show -n MyStaticAppName
"""

helps['staticwebapp create'] = """
    type: command
    short-summary: Create a static app. To provide content to the static web app and integrate with a Github repo, provide the Github repository URL (--source) and a branch (--branch). If the repo is under a Github organization, please ensure that the Azure CLI Github App has access to the organization. Access can be requested in the browser when using the "--login-with-github" argument. Access must be granted by the organization's admin.
    examples:
    - name: Create static app in a subscription.
      text: az staticwebapp create -n MyStaticAppName -g MyExistingRg
       -s https://github.com/JohnDoe/my-first-static-web-app -l WestUs2 -b master -t MyAccessToken
    - name: Create static app in a subscription, retrieving token interactively
      text: az staticwebapp create -n MyStaticAppName -g MyExistingRg
       -s https://github.com/JohnDoe/my-first-static-web-app -l WestUs2 -b master --login-with-github
    - name: Create a static web app without any content and without a github integration
      text: az staticwebapp create -n MyStaticAppName -g MyExistingRg
"""

helps['staticwebapp update'] = """
    type: command
    short-summary: Update a static app. Return the app updated.
    examples:
    - name: Update static app to standard sku.
      text: az staticwebapp update -n MyStaticAppName --sku Standard
"""

helps['staticwebapp disconnect'] = """
    type: command
    short-summary: Disconnect source control to enable connecting to a different repo.
    examples:
    - name: Disconnect static app.
      text: az staticwebapp disconnect -n MyStaticAppName
"""

helps['staticwebapp reconnect'] = """
    type: command
    short-summary: Connect to a repo and branch following a disconnect command.
    examples:
    - name: Connect a repo and branch to static app.
      text: az staticwebapp reconnect -n MyStaticAppName --source MyGitHubRepo -b master --token MyAccessToken
    - name: Connect a repo and branch to static app, retrieving token interactively
      text: az staticwebapp reconnect -n MyStaticAppName --source MyGitHubRepo -b master --login-with-github
"""

helps['staticwebapp delete'] = """
    type: command
    short-summary: Delete a static app.
    examples:
    - name: Delete a static app.
      text: az staticwebapp delete -n MyStaticAppName -g MyRg
"""

helps['staticwebapp environment'] = """
    type: group
    short-summary: Manage environment of the static app.
"""

helps['staticwebapp environment list'] = """
    type: command
    short-summary: List all environment of the static app including production.
    examples:
    - name: List static app environment.
      text: az staticwebapp environment list -n MyStaticAppName
"""

helps['staticwebapp environment show'] = """
    type: command
    short-summary: Show information about the production environment or the specified environment.
    examples:
    - name: Show a static app environment.
      text: az staticwebapp environment show -n MyStaticAppName
"""

helps['staticwebapp environment delete'] = """
    type: command
    short-summary: Delete the static app production environment or the specified environment.
    examples:
    - name: Delete a static app environment.
      text: az staticwebapp environment delete -n MyStaticAppName
"""

helps['staticwebapp environment functions'] = """
    type: command
    short-summary: Show information about functions.
    examples:
    - name: Show static app functions.
      text: az staticwebapp environment functions -n MyStaticAppName
"""

helps['staticwebapp hostname'] = """
    type: group
    short-summary: Manage custom hostnames of Functions of the static app.
"""

helps['staticwebapp hostname list'] = """
    type: command
    short-summary: List custom hostnames of the static app.
    examples:
    - name: List custom hostnames of the static app.
      text: az staticwebapp hostname list -n MyStaticAppName
"""

helps['staticwebapp hostname set'] = """
    type: command
    short-summary: Set given sub-domain hostname to the static app. Please configure CNAME/TXT/ALIAS record with your DNS provider. Use --no-wait to not wait for validation.
    examples:
    - name: Set a hostname for a static app using CNAME validation (default)
      text: az staticwebapp hostname set -n MyStaticAppName --hostname www.example.com
    - name: Set a root domain for a webapp using TXT validation
      text: az staticwebapp hostname set -n MyStaticAppName --hostname example.com --validation-method "dns-txt-token"
"""

helps['staticwebapp hostname show'] = """
    type: command
    short-summary: Get details for a staticwebapp custom domain. Can be used to fetch validation token for TXT domain validation (see example).
    examples:
    - name: Fetch the validation token (if generated) for TXT validation
      text: az staticwebapp hostname show -n MyStaticAppName -g MyResourceGroup --hostname example.com --query "validationToken"
    - name: Show all custom domain details for a particular hostname
      text: az staticwebapp hostname show -n MyStaticAppName -g MyResourceGroup --hostname example.com
"""

helps['staticwebapp hostname delete'] = """
    type: command
    short-summary: Delete given hostname of the static app.
    examples:
    - name: Delete given hostname of the static app.
      text: az staticwebapp hostname delete -n MyStaticAppName --hostname HostnameToDelete
"""

helps['staticwebapp appsettings'] = """
    type: group
    short-summary: Manage app settings the static app.
"""

helps['staticwebapp appsettings list'] = """
    type: command
    short-summary: List app settings of the static app.
    examples:
    - name: List app settings of the static app.
      text: az staticwebapp appsettings list -n MyStaticAppName
    - name: List app settings of the static app environment.
      text: az staticwebapp appsettings list -n MyStaticAppName --environment-name MyEnvironmentName
"""

helps['staticwebapp appsettings set'] = """
    type: command
    short-summary: Add to or change the app settings of the static app.
    examples:
    - name: Add to or change the app settings of the static app.
      text: az staticwebapp appsettings set -n MyStaticAppName --setting-names key1=val1 key2=val2
    - name: Add to or change the app settings of the static app environment.
      text: az staticwebapp appsettings set -n MyStaticAppName --setting-names key1=val1 key2=val2 --environment-name MyEnvironmentName
"""

helps['staticwebapp appsettings delete'] = """
    type: command
    short-summary: Delete app settings with given keys of the static app.
    examples:
    - name: Delete given app settings of the static app.
      text: az staticwebapp appsettings delete -n MyStaticAppName --setting-names key1 key2
    - name: Delete given app settings of the static app.
      text: az staticwebapp appsettings delete -n MyStaticAppName --setting-names key1 key2 --environment-name MyEnvironmentName
"""

helps['staticwebapp identity'] = """
type: group
short-summary: Manage a static web app's managed identity
"""

helps['staticwebapp identity assign'] = """
type: command
short-summary: assign managed identity to the static web app
examples:
  - name: assign local identity and assign a reader role to the current resource group.
    text: >
        az staticwebapp identity assign -g MyResourceGroup -n MyUniqueApp --role reader --scope /subscriptions/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx/resourcegroups/MyResourceGroup
  - name: enable identity for the web app.
    text: >
        az staticwebapp identity assign -g MyResourceGroup -n MyUniqueApp
  - name: assign local identity and a user assigned identity to a static web app.
    text: >
        az staticwebapp identity assign -g MyResourceGroup -n MyUniqueApp --identities [system] myAssignedId
"""

helps['staticwebapp identity remove'] = """
type: command
short-summary: Disable static web app's managed identity
examples:
  - name: Disable static web app's system managed identity
    text: az staticwebapp identity remove --name MyApp --resource-group MyResourceGroup
    crafted: true
  - name: Disable static web app's system managed identity and a user managed identity
    text: az staticwebapp identity remove --name MyApp --resource-group MyResourceGroup --identities [system] myAssignedId
"""

helps['staticwebapp identity show'] = """
type: command
short-summary: display static web app's managed identity
examples:
  - name: display static web app's managed identity (autogenerated)
    text: az staticwebapp identity show --name MyApp --resource-group MyResourceGroup
    crafted: true
"""

helps['staticwebapp users'] = """
    type: group
    short-summary: Manage users of the static app.
"""

helps['staticwebapp users list'] = """
    type: command
    short-summary: Lists users and assigned roles, limited to users who accepted their invites.
    examples:
    - name: Lists users and assigned roles.
      text: az staticwebapp users list -n MyStaticAppName
"""

helps['staticwebapp users invite'] = """
    type: command
    short-summary: Create invitation link for specified user to the static app.
    examples:
    - name: Create invitation link for specified user to the static app.
      text: az staticwebapp users invite -n MyStaticAppName --authentication-provider GitHub --user-details JohnDoe
       --role Contributor --domain static-app-001.azurestaticapps.net --invitation-expiration-in-hours 1

"""

helps['staticwebapp users update'] = """
    type: command
    short-summary: Updates a user entry with the listed roles. Either user details or user id is required.
    examples:
    - name: Updates a user entry with the listed roles.
      text: az staticwebapp users update -n MyStaticAppName --user-details JohnDoe --role Contributor
"""

helps['staticwebapp secrets'] = """
    type: group
    short-summary: Manage deployment token for the static app
"""

helps['staticwebapp secrets list'] = """
    type: command
    short-summary: List the deployment token for the static app.
    examples:
    - name: List deployment token
      text: az staticwebapp secrets list --name MyStaticAppName
"""

helps['staticwebapp secrets reset-api-key'] = """
    type: command
    short-summary: Reset the deployment token for the static app.
    examples:
    - name: Reset deployment token
      text: az staticwebapp secrets reset-api-key --name MyStaticAppName
"""

helps['staticwebapp functions'] = """
type: group
short-summary: Link or unlink a prexisting functionapp with a static webapp. Also known as "Bring your own Functions."
"""

helps['staticwebapp functions link'] = """
    type: command
    short-summary: Link an Azure Function to a static webapp. Also known as "Bring your own Functions." Only one Azure Functions app is available to a single static web app. Static webapp SKU must be "Standard" or "Dedicated"
    examples:
    - name: Link a function to a static webapp
      text: az staticwebapp functions link -n MyStaticAppName -g MyResourceGroup --function-resource-id "/subscriptions/<subscription-id>/resourceGroups/<resource-group>/providers/Microsoft.Web/sites/<function-name>"
"""

helps['staticwebapp functions unlink'] = """
    type: command
    short-summary: Unlink an Azure Function from a static webapp
    examples:
    - name: Show static app functions.
      text: az staticwebapp functions unlink -n MyStaticAppName -g MyResourceGroup
"""

helps['staticwebapp functions show'] = """
    type: command
    short-summary: Show details on the Azure Function linked to a static webapp
    examples:
    - name: Show static app functions.
      text: az staticwebapp functions show -n MyStaticAppName -g MyResourceGroup
"""

helps['staticwebapp backends'] = """
type: group
short-summary: Link or unlink a prexisting backend with a static web app. Also known as "Bring your own API."
"""

helps['staticwebapp backends validate'] = """
    type: command
    short-summary: Validate a backend for a static web app
    long-summary: >
      Only one backend is available to a single static web app.
      If a backend was previously linked to another static Web App, the auth configuration must first be removed from the backend before linking to a different Static Web App.
      Static web app SKU must be "Standard" or "Dedicated".
      Supported backend types are Azure Functions, Azure API Management, Azure App Service, Azure Container Apps.
      Backend region must be provided for backends of type Azure Functions and Azure App Service.
      See https://learn.microsoft.com/azure/static-web-apps/apis-overview to learn more.
    examples:
    - name: Validate a backend for a static web app
      text: az staticwebapp backends validate -n MyStaticAppName -g MyResourceGroup --backend-resource-id "/subscriptions/<subscription-id>/resourceGroups/<resource-group>/providers/<resource-provider>/<resource-type>/<backend-name>" --backend-region MyBackendRegion
    - name: Validate a backend for a static web app environment
      text: az staticwebapp backends validate -n MyStaticAppName -g MyResourceGroup --environment-name MyEnvironmentName --backend-resource-id "/subscriptions/<subscription-id>/resourceGroups/<resource-group>/providers/<resource-provider>/<resource-type>/<backend-name>" --backend-region MyBackendRegion
"""

helps['staticwebapp backends link'] = """
    type: command
    short-summary: Link a backend to a static web app. Also known as "Bring your own API."
    long-summary: >
      Only one backend is available to a single static web app.
      If a backend was previously linked to another static Web App, the auth configuration must first be removed from the backend before linking to a different Static Web App.
      Static web app SKU must be "Standard" or "Dedicated".
      Supported backend types are Azure Functions, Azure API Management, Azure App Service, Azure Container Apps.
      Backend region must be provided for backends of type Azure Functions and Azure App Service.
      See https://learn.microsoft.com/azure/static-web-apps/apis-overview to learn more.
    examples:
    - name: Link a backend to a static web app
      text: az staticwebapp backends link -n MyStaticAppName -g MyResourceGroup --backend-resource-id "/subscriptions/<subscription-id>/resourceGroups/<resource-group>/providers/<resource-provider>/<resource-type>/<backend-name>" --backend-region MyBackendRegion
    - name: Link a backend to a static web app environment
      text: az staticwebapp backends link -n MyStaticAppName -g MyResourceGroup --environment-name MyEnvironmentName --backend-resource-id "/subscriptions/<subscription-id>/resourceGroups/<resource-group>/providers/<resource-provider>/<resource-type>/<backend-name>" --backend-region MyBackendRegion
"""

helps['staticwebapp backends unlink'] = """
    type: command
    short-summary: Unlink backend from a static web app
    examples:
    - name: Unlink static app backends.
      text: az staticwebapp backends unlink -n MyStaticAppName -g MyResourceGroup
    - name: Unlink backend from static web app environment and remove auth config from backend.
      text: az staticwebapp backends unlink -n MyStaticAppName -g MyResourceGroup --environment-name MyEnvironmentName --remove-backend-auth
"""

helps['staticwebapp backends show'] = """
    type: command
    short-summary: Show details on the backend linked to a static web app
    examples:
    - name: Show static web app backends.
      text: az staticwebapp backends show -n MyStaticAppName -g MyResourceGroup
    - name: Show static web app backends for environment.
      text: az staticwebapp backends show -n MyStaticAppName -g MyResourceGroup --environment-name MyEnvironmentName
"""

helps['staticwebapp enterprise-edge'] = """
    type: group
    short-summary: Manage the Azure Front Door CDN for static webapps. For optimal experience and availability please check our documentation https://aka.ms/swaedge
"""

helps['staticwebapp enterprise-edge enable'] = """
    type: command
    short-summary: Enable the Azure Front Door CDN for a static webapp. Enabling enterprise-grade edge requires re-registration for the Azure Front Door Microsoft.CDN resource provider. For optimal experience and availability please check our documentation https://aka.ms/swaedge
"""

helps['staticwebapp enterprise-edge disable'] = """
    type: command
    short-summary: Disable the Azure Front Door CDN for a static webapp. For optimal experience and availability please check our documentation https://aka.ms/swaedge
"""

helps['staticwebapp enterprise-edge show'] = """
    type: command
    short-summary: Show the status (Enabled, Disabled, Enabling, Disabling) of the Azure Front Door CDN for a webapp. For optimal experience and availability please check our documentation https://aka.ms/swaedge
"""

helps['webapp deploy'] = """
    type: command
    short-summary: Deploys a provided artifact to Azure Web Apps.
    examples:
    - name: Deploy a war file asynchronously.
      text: az webapp deploy --resource-group ResourceGroup --name AppName --src-path SourcePath --type war --async true
    - name: Deploy a static text file to wwwroot/staticfiles/test.txt
      text: az webapp deploy --resource-group ResourceGroup --name AppName --src-path SourcePath --type static --target-path staticfiles/test.txt
"""
