# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "netappfiles account backup-vault backup restore-file",
)
class RestoreFile(AAZCommand):
    """Restore the specified files from the specified backup to the active filesystem
    """

    _aaz_info = {
        "version": "2025-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.netapp/netappaccounts/{}/backupvaults/{}/backups/{}/restorefiles", "2025-01-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, None)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.account_name = AAZStrArg(
            options=["-a", "--account-name"],
            help="The name of the NetApp account",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,127}$",
            ),
        )
        _args_schema.backup_name = AAZStrArg(
            options=["-b", "--backup-name"],
            help="The name of the backup",
            required=True,
            id_part="child_name_2",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_.]{0,255}$",
            ),
        )
        _args_schema.backup_vault_name = AAZStrArg(
            options=["-v", "--backup-vault-name"],
            help="The name of the Backup Vault",
            required=True,
            id_part="child_name_1",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,63}$",
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "Body"

        _args_schema = cls._args_schema
        _args_schema.destination_volume_id = AAZStrArg(
            options=["--destination-volume-id"],
            arg_group="Body",
            help="Resource Id of the destination volume on which the files need to be restored",
            required=True,
        )
        _args_schema.file_list = AAZListArg(
            options=["--file-list"],
            arg_group="Body",
            help="List of files to be restored",
            required=True,
        )
        _args_schema.restore_file_path = AAZStrArg(
            options=["--restore-file-path"],
            arg_group="Body",
            help="Destination folder where the files will be restored. The path name should start with a forward slash. If it is omitted from request then restore is done at the root folder of the destination volume by default",
            fmt=AAZStrArgFormat(
                pattern="^\\/.*$",
            ),
        )

        file_list = cls._args_schema.file_list
        file_list.Element = AAZStrArg(
            fmt=AAZStrArgFormat(
                max_length=1024,
                min_length=1,
            ),
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.BackupsUnderBackupVaultRestoreFiles(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    class BackupsUnderBackupVaultRestoreFiles(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    None,
                    self.on_error,
                    lro_options={"final-state-via": "location"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.NetApp/netAppAccounts/{accountName}/backupVaults/{backupVaultName}/backups/{backupName}/restoreFiles",
                **self.url_parameters
            )

        @property
        def method(self):
            return "POST"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "backupName", self.ctx.args.backup_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "backupVaultName", self.ctx.args.backup_vault_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("destinationVolumeId", AAZStrType, ".destination_volume_id", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("fileList", AAZListType, ".file_list", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("restoreFilePath", AAZStrType, ".restore_file_path")

            file_list = _builder.get(".fileList")
            if file_list is not None:
                file_list.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)


class _RestoreFileHelper:
    """Helper class for RestoreFile"""


__all__ = ["RestoreFile"]
