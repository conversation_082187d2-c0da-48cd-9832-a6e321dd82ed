# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "netappfiles volume-group update",
)
class Update(AAZCommand):
    """Update a volume group along with specified volumes
    """

    _aaz_info = {
        "version": "2025-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.netapp/netappaccounts/{}/volumegroups/{}", "2025-01-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.account_name = AAZStrArg(
            options=["-a", "--account-name"],
            help="The name of the NetApp account",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,127}$",
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.volume_group_name = AAZStrArg(
            options=["-n", "--name", "--group-name", "--volume-group-name"],
            help="The name of the volumeGroup",
            required=True,
            id_part="child_name_1",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,63}$",
                max_length=64,
                min_length=1,
            ),
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.group_meta_data = AAZObjectArg(
            options=["--group-meta-data"],
            arg_group="Properties",
            help="Volume group details",
            nullable=True,
        )
        _args_schema.volumes = AAZListArg(
            options=["--volumes"],
            arg_group="Properties",
            help="List of volumes from group",
            nullable=True,
        )

        group_meta_data = cls._args_schema.group_meta_data
        group_meta_data.application_identifier = AAZStrArg(
            options=["application-identifier"],
            help="Application specific identifier",
            nullable=True,
        )
        group_meta_data.application_type = AAZStrArg(
            options=["application-type"],
            help="Application Type",
            default="SAP-HANA",
            nullable=True,
            enum={"ORACLE": "ORACLE", "SAP-HANA": "SAP-HANA"},
        )
        group_meta_data.global_placement_rules = AAZListArg(
            options=["global-placement-rules"],
            help="Application specific placement rules for the volume group",
            nullable=True,
        )
        group_meta_data.group_description = AAZStrArg(
            options=["group-description"],
            help="Group Description",
            nullable=True,
        )

        global_placement_rules = cls._args_schema.group_meta_data.global_placement_rules
        global_placement_rules.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_placement_key_value_pairs_update(global_placement_rules.Element)

        volumes = cls._args_schema.volumes
        volumes.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.volumes.Element
        _element.name = AAZStrArg(
            options=["name"],
            help="Resource name",
            nullable=True,
        )
        _element.capacity_pool_resource_id = AAZStrArg(
            options=["capacity-pool-resource-id"],
            help="Pool Resource Id used in case of creating a volume through volume group",
            nullable=True,
        )
        _element.cool_access = AAZBoolArg(
            options=["cool-access"],
            help="Specifies whether Cool Access(tiering) is enabled for the volume.",
            nullable=True,
        )
        _element.cool_access_retrieval_policy = AAZStrArg(
            options=["cool-access-retrieval-policy"],
            help="coolAccessRetrievalPolicy determines the data retrieval behavior from the cool tier to standard storage based on the read pattern for cool access enabled volumes. The possible values for this field are:   Default - Data will be pulled from cool tier to standard storage on random reads. This policy is the default.  OnRead - All client-driven data read is pulled from cool tier to standard storage on both sequential and random reads.  Never - No client-driven data is pulled from cool tier to standard storage.",
            nullable=True,
            enum={"Default": "Default", "Never": "Never", "OnRead": "OnRead"},
        )
        _element.cool_access_tiering_policy = AAZStrArg(
            options=["cool-access-tiering-policy"],
            help="coolAccessTieringPolicy determines which cold data blocks are moved to cool tier. The possible values for this field are: Auto - Moves cold user data blocks in both the Snapshot copies and the active file system to the cool tier tier. This policy is the default. SnapshotOnly - Moves user data blocks of the Volume Snapshot copies that are not associated with the active file system to the cool tier.",
            nullable=True,
            enum={"Auto": "Auto", "SnapshotOnly": "SnapshotOnly"},
        )
        _element.coolness_period = AAZIntArg(
            options=["coolness-period"],
            help="Specifies the number of days after which data that is not accessed by clients will be tiered.",
            nullable=True,
            fmt=AAZIntArgFormat(
                maximum=183,
                minimum=2,
            ),
        )
        _element.data_protection = AAZObjectArg(
            options=["data-protection"],
            help="DataProtection type volumes include an object containing details of the replication",
            nullable=True,
        )
        _element.default_group_quota_in_ki_bs = AAZIntArg(
            options=["default-group-quota-in-ki-bs"],
            help="Default group quota for volume in KiBs. If isDefaultQuotaEnabled is set, the minimum value of 4 KiBs applies.",
            nullable=True,
        )
        _element.default_user_quota_in_ki_bs = AAZIntArg(
            options=["default-user-quota-in-ki-bs"],
            help="Default user quota for volume in KiBs. If isDefaultQuotaEnabled is set, the minimum value of 4 KiBs applies .",
            nullable=True,
        )
        _element.delete_base_snapshot = AAZBoolArg(
            options=["delete-base-snapshot"],
            help="If enabled (true) the snapshot the volume was created from will be automatically deleted after the volume create operation has finished.  Defaults to false",
            nullable=True,
        )
        _element.enable_subvolumes = AAZStrArg(
            options=["enable-subvolumes"],
            help="Flag indicating whether subvolume operations are enabled on the volume",
            nullable=True,
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _element.encryption_key_source = AAZStrArg(
            options=["encryption-key-source"],
            help="Source of key used to encrypt data in volume. Applicable if NetApp account has encryption.keySource = 'Microsoft.KeyVault'. Possible values (case-insensitive) are: 'Microsoft.NetApp, Microsoft.KeyVault'",
            nullable=True,
            enum={"Microsoft.KeyVault": "Microsoft.KeyVault", "Microsoft.NetApp": "Microsoft.NetApp"},
        )
        _element.export_policy = AAZObjectArg(
            options=["export-policy"],
            help="Set of export policy rules",
            nullable=True,
        )
        _element.is_default_quota_enabled = AAZBoolArg(
            options=["is-default-quota-enabled"],
            help="Specifies if default quota is enabled for the volume.",
            nullable=True,
        )
        _element.key_vault_private_endpoint_resource_id = AAZStrArg(
            options=["key-vault-private-endpoint-resource-id"],
            help="The resource ID of private endpoint for KeyVault. It must reside in the same VNET as the volume. Only applicable if encryptionKeySource = 'Microsoft.KeyVault'.",
            nullable=True,
        )
        _element.placement_rules = AAZListArg(
            options=["placement-rules"],
            help="Application specific placement rules for the particular volume",
            nullable=True,
        )
        _element.protocol_types = AAZListArg(
            options=["protocol-types"],
            help="Set of protocol types, default NFSv3, CIFS for SMB protocol",
            nullable=True,
        )
        _element.proximity_placement_group = AAZStrArg(
            options=["proximity-placement-group"],
            help="Proximity placement group associated with the volume",
            nullable=True,
        )
        _element.service_level = AAZStrArg(
            options=["service-level"],
            help="serviceLevel",
            nullable=True,
            enum={"Premium": "Premium", "Standard": "Standard", "StandardZRS": "StandardZRS", "Ultra": "Ultra"},
        )
        _element.smb_access_based_enumeration = AAZStrArg(
            options=["smb-access-based-enumeration"],
            help="Enables access-based enumeration share property for SMB Shares. Only applicable for SMB/DualProtocol volume",
            nullable=True,
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _element.smb_continuously_available = AAZBoolArg(
            options=["smb-continuously-available"],
            help="Enables continuously available share property for smb volume. Only applicable for SMB volume",
            nullable=True,
        )
        _element.smb_encryption = AAZBoolArg(
            options=["smb-encryption"],
            help="Enables encryption for in-flight smb3 data. Only applicable for SMB/DualProtocol volume. To be used with swagger version 2020-08-01 or later",
            nullable=True,
        )
        _element.smb_non_browsable = AAZStrArg(
            options=["smb-non-browsable"],
            help="Enables non-browsable property for SMB Shares. Only applicable for SMB/DualProtocol volume",
            nullable=True,
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _element.snapshot_directory_visible = AAZBoolArg(
            options=["snapshot-directory-visible"],
            help="If enabled (true) the volume will contain a read-only snapshot directory which provides access to each of the volume's snapshots (defaults to true).",
            nullable=True,
        )
        _element.subnet_id = AAZStrArg(
            options=["subnet-id"],
            help="The Azure Resource URI for a delegated subnet. Must have the delegation Microsoft.NetApp/volumes",
        )
        _element.throughput_mibps = AAZFloatArg(
            options=["throughput-mibps"],
            help="Maximum throughput in MiB/s that can be achieved by this volume and this will be accepted as input only for manual qosType volume",
            nullable=True,
        )
        _element.unix_permissions = AAZStrArg(
            options=["unix-permissions"],
            help="UNIX permissions for NFS volume accepted in octal 4 digit format. First digit selects the set user ID(4), set group ID (2) and sticky (1) attributes. Second digit selects permission for the owner of the file: read (4), write (2) and execute (1). Third selects permissions for other users in the same group. the fourth for other users not in the group. 0755 - gives read/write/execute permissions to owner and read/execute to group and other users.",
            nullable=True,
            fmt=AAZStrArgFormat(
                max_length=4,
                min_length=4,
            ),
        )
        _element.usage_threshold = AAZIntArg(
            options=["usage-threshold"],
            help="Maximum storage quota allowed for a file system in bytes. This is a soft quota used for alerting only. Minimum size is 100 GiB. Upper limit is 100TiB, 500Tib for LargeVolume or 2400Tib for LargeVolume on exceptional basis. Specified in bytes.",
            fmt=AAZIntArgFormat(
                maximum=2638827906662400,
                minimum=53687091200,
            ),
        )
        _element.volume_spec_name = AAZStrArg(
            options=["volume-spec-name"],
            help="Volume spec name is the application specific designation or identifier for the particular volume in a volume group for e.g. data, log",
            nullable=True,
        )
        _element.tags = AAZDictArg(
            options=["tags"],
            help="Resource tags",
            nullable=True,
        )

        data_protection = cls._args_schema.volumes.Element.data_protection
        data_protection.backup = AAZObjectArg(
            options=["backup"],
            help="Backup Properties",
            nullable=True,
        )
        data_protection.replication = AAZObjectArg(
            options=["replication"],
            help="Replication properties",
            nullable=True,
        )
        data_protection.snapshot = AAZObjectArg(
            options=["snapshot"],
            help="Snapshot properties.",
            nullable=True,
        )
        data_protection.volume_relocation = AAZObjectArg(
            options=["volume-relocation"],
            help="VolumeRelocation properties",
            nullable=True,
        )

        backup = cls._args_schema.volumes.Element.data_protection.backup
        backup.backup_policy_id = AAZResourceIdArg(
            options=["backup-policy-id"],
            help="Backup Policy Resource ID",
            nullable=True,
        )
        backup.backup_vault_id = AAZResourceIdArg(
            options=["backup-vault-id"],
            help="Backup Vault Resource ID",
            nullable=True,
        )
        backup.policy_enforced = AAZBoolArg(
            options=["policy-enforced"],
            help="Policy Enforced",
            nullable=True,
        )

        replication = cls._args_schema.volumes.Element.data_protection.replication
        replication.endpoint_type = AAZStrArg(
            options=["endpoint-type"],
            help="Indicates whether the local volume is the source or destination for the Volume Replication",
            nullable=True,
            enum={"dst": "dst", "src": "src"},
        )
        replication.remote_volume_region = AAZStrArg(
            options=["remote-volume-region"],
            help="The remote region for the other end of the Volume Replication.",
            nullable=True,
        )
        replication.remote_volume_resource_id = AAZStrArg(
            options=["remote-volume-resource-id"],
            help="The resource ID of the remote volume.",
            nullable=True,
        )
        replication.replication_schedule = AAZStrArg(
            options=["replication-schedule"],
            help="Schedule",
            nullable=True,
            enum={"_10minutely": "_10minutely", "daily": "daily", "hourly": "hourly"},
        )

        snapshot = cls._args_schema.volumes.Element.data_protection.snapshot
        snapshot.snapshot_policy_id = AAZStrArg(
            options=["snapshot-policy-id"],
            help="Snapshot Policy ResourceId",
            nullable=True,
        )

        volume_relocation = cls._args_schema.volumes.Element.data_protection.volume_relocation
        volume_relocation.relocation_requested = AAZBoolArg(
            options=["relocation-requested"],
            help="Has relocation been requested for this volume",
            nullable=True,
        )

        export_policy = cls._args_schema.volumes.Element.export_policy
        export_policy.rules = AAZListArg(
            options=["rules"],
            help="Export policy rule",
            nullable=True,
        )

        rules = cls._args_schema.volumes.Element.export_policy.rules
        rules.Element = AAZObjectArg(
            nullable=True,
        )

        _element = cls._args_schema.volumes.Element.export_policy.rules.Element
        _element.allowed_clients = AAZStrArg(
            options=["allowed-clients"],
            help="Client ingress specification as comma separated string with IPv4 CIDRs, IPv4 host addresses and host names",
            nullable=True,
        )
        _element.chown_mode = AAZStrArg(
            options=["chown-mode"],
            help="This parameter specifies who is authorized to change the ownership of a file. restricted - Only root user can change the ownership of the file. unrestricted - Non-root users can change ownership of files that they own.",
            nullable=True,
            enum={"Restricted": "Restricted", "Unrestricted": "Unrestricted"},
        )
        _element.cifs = AAZBoolArg(
            options=["cifs"],
            help="Allows CIFS protocol",
            nullable=True,
        )
        _element.has_root_access = AAZBoolArg(
            options=["has-root-access"],
            help="Has root access to volume",
            nullable=True,
        )
        _element.kerberos5_read_only = AAZBoolArg(
            options=["kerberos5-read-only"],
            help="Kerberos5 Read only access. To be use with swagger version 2020-05-01 or later",
            nullable=True,
        )
        _element.kerberos5_read_write = AAZBoolArg(
            options=["kerberos5-read-write"],
            help="Kerberos5 Read and write access. To be use with swagger version 2020-05-01 or later",
            nullable=True,
        )
        _element.kerberos5i_read_only = AAZBoolArg(
            options=["kerberos5i-read-only"],
            help="Kerberos5i Read only access. To be use with swagger version 2020-05-01 or later",
            nullable=True,
        )
        _element.kerberos5i_read_write = AAZBoolArg(
            options=["kerberos5i-read-write"],
            help="Kerberos5i Read and write access. To be use with swagger version 2020-05-01 or later",
            nullable=True,
        )
        _element.kerberos5p_read_only = AAZBoolArg(
            options=["kerberos5p-read-only"],
            help="Kerberos5p Read only access. To be use with swagger version 2020-05-01 or later",
            nullable=True,
        )
        _element.kerberos5p_read_write = AAZBoolArg(
            options=["kerberos5p-read-write"],
            help="Kerberos5p Read and write access. To be use with swagger version 2020-05-01 or later",
            nullable=True,
        )
        _element.nfsv3 = AAZBoolArg(
            options=["nfsv3"],
            help="Allows NFSv3 protocol. Enable only for NFSv3 type volumes",
            nullable=True,
        )
        _element.nfsv41 = AAZBoolArg(
            options=["nfsv41"],
            help="Allows NFSv4.1 protocol. Enable only for NFSv4.1 type volumes",
            nullable=True,
        )
        _element.rule_index = AAZIntArg(
            options=["rule-index"],
            help="Order index",
            nullable=True,
        )
        _element.unix_read_only = AAZBoolArg(
            options=["unix-read-only"],
            help="Read only access",
            nullable=True,
        )
        _element.unix_read_write = AAZBoolArg(
            options=["unix-read-write"],
            help="Read and write access",
            nullable=True,
        )

        placement_rules = cls._args_schema.volumes.Element.placement_rules
        placement_rules.Element = AAZObjectArg(
            nullable=True,
        )
        cls._build_args_placement_key_value_pairs_update(placement_rules.Element)

        protocol_types = cls._args_schema.volumes.Element.protocol_types
        protocol_types.Element = AAZStrArg(
            nullable=True,
        )

        tags = cls._args_schema.volumes.Element.tags
        tags.Element = AAZStrArg(
            nullable=True,
        )
        return cls._args_schema

    _args_placement_key_value_pairs_update = None

    @classmethod
    def _build_args_placement_key_value_pairs_update(cls, _schema):
        if cls._args_placement_key_value_pairs_update is not None:
            _schema.key = cls._args_placement_key_value_pairs_update.key
            _schema.value = cls._args_placement_key_value_pairs_update.value
            return

        cls._args_placement_key_value_pairs_update = AAZObjectArg(
            nullable=True,
        )

        placement_key_value_pairs_update = cls._args_placement_key_value_pairs_update
        placement_key_value_pairs_update.key = AAZStrArg(
            options=["key"],
            help="Key for an application specific parameter for the placement of volumes in the volume group",
        )
        placement_key_value_pairs_update.value = AAZStrArg(
            options=["value"],
            help="Value for an application specific parameter for the placement of volumes in the volume group",
        )

        _schema.key = cls._args_placement_key_value_pairs_update.key
        _schema.value = cls._args_placement_key_value_pairs_update.value

    def _execute_operations(self):
        self.pre_operations()
        self.VolumeGroupsGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.VolumeGroupsCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VolumeGroupsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.NetApp/netAppAccounts/{accountName}/volumeGroups/{volumeGroupName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "volumeGroupName", self.ctx.args.volume_group_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_volume_group_details_read(cls._schema_on_200)

            return cls._schema_on_200

    class VolumeGroupsCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.NetApp/netAppAccounts/{accountName}/volumeGroups/{volumeGroupName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "volumeGroupName", self.ctx.args.volume_group_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_201
            )

        _schema_on_201 = None

        @classmethod
        def _build_schema_on_201(cls):
            if cls._schema_on_201 is not None:
                return cls._schema_on_201

            cls._schema_on_201 = AAZObjectType()
            _UpdateHelper._build_schema_volume_group_details_read(cls._schema_on_201)

            return cls._schema_on_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("groupMetaData", AAZObjectType, ".group_meta_data")
                properties.set_prop("volumes", AAZListType, ".volumes")

            group_meta_data = _builder.get(".properties.groupMetaData")
            if group_meta_data is not None:
                group_meta_data.set_prop("applicationIdentifier", AAZStrType, ".application_identifier")
                group_meta_data.set_prop("applicationType", AAZStrType, ".application_type")
                group_meta_data.set_prop("globalPlacementRules", AAZListType, ".global_placement_rules")
                group_meta_data.set_prop("groupDescription", AAZStrType, ".group_description")

            global_placement_rules = _builder.get(".properties.groupMetaData.globalPlacementRules")
            if global_placement_rules is not None:
                _UpdateHelper._build_schema_placement_key_value_pairs_update(global_placement_rules.set_elements(AAZObjectType, "."))

            volumes = _builder.get(".properties.volumes")
            if volumes is not None:
                volumes.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.volumes[]")
            if _elements is not None:
                _elements.set_prop("name", AAZStrType, ".name")
                _elements.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})
                _elements.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties.volumes[].properties")
            if properties is not None:
                properties.set_prop("capacityPoolResourceId", AAZStrType, ".capacity_pool_resource_id")
                properties.set_prop("coolAccess", AAZBoolType, ".cool_access")
                properties.set_prop("coolAccessRetrievalPolicy", AAZStrType, ".cool_access_retrieval_policy")
                properties.set_prop("coolAccessTieringPolicy", AAZStrType, ".cool_access_tiering_policy")
                properties.set_prop("coolnessPeriod", AAZIntType, ".coolness_period")
                properties.set_prop("dataProtection", AAZObjectType, ".data_protection")
                properties.set_prop("defaultGroupQuotaInKiBs", AAZIntType, ".default_group_quota_in_ki_bs")
                properties.set_prop("defaultUserQuotaInKiBs", AAZIntType, ".default_user_quota_in_ki_bs")
                properties.set_prop("deleteBaseSnapshot", AAZBoolType, ".delete_base_snapshot")
                properties.set_prop("enableSubvolumes", AAZStrType, ".enable_subvolumes")
                properties.set_prop("encryptionKeySource", AAZStrType, ".encryption_key_source")
                properties.set_prop("exportPolicy", AAZObjectType, ".export_policy")
                properties.set_prop("isDefaultQuotaEnabled", AAZBoolType, ".is_default_quota_enabled")
                properties.set_prop("keyVaultPrivateEndpointResourceId", AAZStrType, ".key_vault_private_endpoint_resource_id")
                properties.set_prop("placementRules", AAZListType, ".placement_rules")
                properties.set_prop("protocolTypes", AAZListType, ".protocol_types")
                properties.set_prop("proximityPlacementGroup", AAZStrType, ".proximity_placement_group")
                properties.set_prop("serviceLevel", AAZStrType, ".service_level")
                properties.set_prop("smbAccessBasedEnumeration", AAZStrType, ".smb_access_based_enumeration", typ_kwargs={"nullable": True})
                properties.set_prop("smbContinuouslyAvailable", AAZBoolType, ".smb_continuously_available")
                properties.set_prop("smbEncryption", AAZBoolType, ".smb_encryption")
                properties.set_prop("smbNonBrowsable", AAZStrType, ".smb_non_browsable")
                properties.set_prop("snapshotDirectoryVisible", AAZBoolType, ".snapshot_directory_visible")
                properties.set_prop("subnetId", AAZStrType, ".subnet_id", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("throughputMibps", AAZFloatType, ".throughput_mibps", typ_kwargs={"nullable": True})
                properties.set_prop("unixPermissions", AAZStrType, ".unix_permissions", typ_kwargs={"nullable": True})
                properties.set_prop("usageThreshold", AAZIntType, ".usage_threshold", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("volumeSpecName", AAZStrType, ".volume_spec_name")

            data_protection = _builder.get(".properties.volumes[].properties.dataProtection")
            if data_protection is not None:
                data_protection.set_prop("backup", AAZObjectType, ".backup")
                data_protection.set_prop("replication", AAZObjectType, ".replication")
                data_protection.set_prop("snapshot", AAZObjectType, ".snapshot")
                data_protection.set_prop("volumeRelocation", AAZObjectType, ".volume_relocation")

            backup = _builder.get(".properties.volumes[].properties.dataProtection.backup")
            if backup is not None:
                backup.set_prop("backupPolicyId", AAZStrType, ".backup_policy_id")
                backup.set_prop("backupVaultId", AAZStrType, ".backup_vault_id")
                backup.set_prop("policyEnforced", AAZBoolType, ".policy_enforced")

            replication = _builder.get(".properties.volumes[].properties.dataProtection.replication")
            if replication is not None:
                replication.set_prop("endpointType", AAZStrType, ".endpoint_type")
                replication.set_prop("remoteVolumeRegion", AAZStrType, ".remote_volume_region")
                replication.set_prop("remoteVolumeResourceId", AAZStrType, ".remote_volume_resource_id")
                replication.set_prop("replicationSchedule", AAZStrType, ".replication_schedule")

            snapshot = _builder.get(".properties.volumes[].properties.dataProtection.snapshot")
            if snapshot is not None:
                snapshot.set_prop("snapshotPolicyId", AAZStrType, ".snapshot_policy_id")

            volume_relocation = _builder.get(".properties.volumes[].properties.dataProtection.volumeRelocation")
            if volume_relocation is not None:
                volume_relocation.set_prop("relocationRequested", AAZBoolType, ".relocation_requested")

            export_policy = _builder.get(".properties.volumes[].properties.exportPolicy")
            if export_policy is not None:
                export_policy.set_prop("rules", AAZListType, ".rules")

            rules = _builder.get(".properties.volumes[].properties.exportPolicy.rules")
            if rules is not None:
                rules.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.volumes[].properties.exportPolicy.rules[]")
            if _elements is not None:
                _elements.set_prop("allowedClients", AAZStrType, ".allowed_clients")
                _elements.set_prop("chownMode", AAZStrType, ".chown_mode")
                _elements.set_prop("cifs", AAZBoolType, ".cifs")
                _elements.set_prop("hasRootAccess", AAZBoolType, ".has_root_access")
                _elements.set_prop("kerberos5ReadOnly", AAZBoolType, ".kerberos5_read_only")
                _elements.set_prop("kerberos5ReadWrite", AAZBoolType, ".kerberos5_read_write")
                _elements.set_prop("kerberos5iReadOnly", AAZBoolType, ".kerberos5i_read_only")
                _elements.set_prop("kerberos5iReadWrite", AAZBoolType, ".kerberos5i_read_write")
                _elements.set_prop("kerberos5pReadOnly", AAZBoolType, ".kerberos5p_read_only")
                _elements.set_prop("kerberos5pReadWrite", AAZBoolType, ".kerberos5p_read_write")
                _elements.set_prop("nfsv3", AAZBoolType, ".nfsv3")
                _elements.set_prop("nfsv41", AAZBoolType, ".nfsv41")
                _elements.set_prop("ruleIndex", AAZIntType, ".rule_index")
                _elements.set_prop("unixReadOnly", AAZBoolType, ".unix_read_only")
                _elements.set_prop("unixReadWrite", AAZBoolType, ".unix_read_write")

            placement_rules = _builder.get(".properties.volumes[].properties.placementRules")
            if placement_rules is not None:
                _UpdateHelper._build_schema_placement_key_value_pairs_update(placement_rules.set_elements(AAZObjectType, "."))

            protocol_types = _builder.get(".properties.volumes[].properties.protocolTypes")
            if protocol_types is not None:
                protocol_types.set_elements(AAZStrType, ".")

            tags = _builder.get(".properties.volumes[].tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    @classmethod
    def _build_schema_placement_key_value_pairs_update(cls, _builder):
        if _builder is None:
            return
        _builder.set_prop("key", AAZStrType, ".key", typ_kwargs={"flags": {"required": True}})
        _builder.set_prop("value", AAZStrType, ".value", typ_kwargs={"flags": {"required": True}})

    _schema_placement_key_value_pairs_read = None

    @classmethod
    def _build_schema_placement_key_value_pairs_read(cls, _schema):
        if cls._schema_placement_key_value_pairs_read is not None:
            _schema.key = cls._schema_placement_key_value_pairs_read.key
            _schema.value = cls._schema_placement_key_value_pairs_read.value
            return

        cls._schema_placement_key_value_pairs_read = _schema_placement_key_value_pairs_read = AAZObjectType()

        placement_key_value_pairs_read = _schema_placement_key_value_pairs_read
        placement_key_value_pairs_read.key = AAZStrType(
            flags={"required": True},
        )
        placement_key_value_pairs_read.value = AAZStrType(
            flags={"required": True},
        )

        _schema.key = cls._schema_placement_key_value_pairs_read.key
        _schema.value = cls._schema_placement_key_value_pairs_read.value

    _schema_volume_group_details_read = None

    @classmethod
    def _build_schema_volume_group_details_read(cls, _schema):
        if cls._schema_volume_group_details_read is not None:
            _schema.id = cls._schema_volume_group_details_read.id
            _schema.location = cls._schema_volume_group_details_read.location
            _schema.name = cls._schema_volume_group_details_read.name
            _schema.properties = cls._schema_volume_group_details_read.properties
            _schema.type = cls._schema_volume_group_details_read.type
            return

        cls._schema_volume_group_details_read = _schema_volume_group_details_read = AAZObjectType()

        volume_group_details_read = _schema_volume_group_details_read
        volume_group_details_read.id = AAZStrType(
            flags={"read_only": True},
        )
        volume_group_details_read.location = AAZStrType()
        volume_group_details_read.name = AAZStrType(
            flags={"read_only": True},
        )
        volume_group_details_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        volume_group_details_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_volume_group_details_read.properties
        properties.group_meta_data = AAZObjectType(
            serialized_name="groupMetaData",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.volumes = AAZListType()

        group_meta_data = _schema_volume_group_details_read.properties.group_meta_data
        group_meta_data.application_identifier = AAZStrType(
            serialized_name="applicationIdentifier",
        )
        group_meta_data.application_type = AAZStrType(
            serialized_name="applicationType",
        )
        group_meta_data.global_placement_rules = AAZListType(
            serialized_name="globalPlacementRules",
        )
        group_meta_data.group_description = AAZStrType(
            serialized_name="groupDescription",
        )
        group_meta_data.volumes_count = AAZIntType(
            serialized_name="volumesCount",
            flags={"read_only": True},
        )

        global_placement_rules = _schema_volume_group_details_read.properties.group_meta_data.global_placement_rules
        global_placement_rules.Element = AAZObjectType()
        cls._build_schema_placement_key_value_pairs_read(global_placement_rules.Element)

        volumes = _schema_volume_group_details_read.properties.volumes
        volumes.Element = AAZObjectType()

        _element = _schema_volume_group_details_read.properties.volumes.Element
        _element.id = AAZStrType(
            flags={"read_only": True},
        )
        _element.name = AAZStrType()
        _element.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )
        _element.tags = AAZDictType()
        _element.type = AAZStrType(
            flags={"read_only": True},
        )
        _element.zones = AAZListType()

        properties = _schema_volume_group_details_read.properties.volumes.Element.properties
        properties.actual_throughput_mibps = AAZFloatType(
            serialized_name="actualThroughputMibps",
            flags={"read_only": True},
        )
        properties.avs_data_store = AAZStrType(
            serialized_name="avsDataStore",
        )
        properties.backup_id = AAZStrType(
            serialized_name="backupId",
            nullable=True,
        )
        properties.baremetal_tenant_id = AAZStrType(
            serialized_name="baremetalTenantId",
            flags={"read_only": True},
        )
        properties.capacity_pool_resource_id = AAZStrType(
            serialized_name="capacityPoolResourceId",
        )
        properties.clone_progress = AAZIntType(
            serialized_name="cloneProgress",
            nullable=True,
            flags={"read_only": True},
        )
        properties.cool_access = AAZBoolType(
            serialized_name="coolAccess",
        )
        properties.cool_access_retrieval_policy = AAZStrType(
            serialized_name="coolAccessRetrievalPolicy",
        )
        properties.cool_access_tiering_policy = AAZStrType(
            serialized_name="coolAccessTieringPolicy",
        )
        properties.coolness_period = AAZIntType(
            serialized_name="coolnessPeriod",
        )
        properties.creation_token = AAZStrType(
            serialized_name="creationToken",
            flags={"required": True},
        )
        properties.data_protection = AAZObjectType(
            serialized_name="dataProtection",
        )
        properties.data_store_resource_id = AAZListType(
            serialized_name="dataStoreResourceId",
            flags={"read_only": True},
        )
        properties.default_group_quota_in_ki_bs = AAZIntType(
            serialized_name="defaultGroupQuotaInKiBs",
        )
        properties.default_user_quota_in_ki_bs = AAZIntType(
            serialized_name="defaultUserQuotaInKiBs",
        )
        properties.delete_base_snapshot = AAZBoolType(
            serialized_name="deleteBaseSnapshot",
        )
        properties.effective_network_features = AAZStrType(
            serialized_name="effectiveNetworkFeatures",
        )
        properties.enable_subvolumes = AAZStrType(
            serialized_name="enableSubvolumes",
        )
        properties.encrypted = AAZBoolType(
            flags={"read_only": True},
        )
        properties.encryption_key_source = AAZStrType(
            serialized_name="encryptionKeySource",
        )
        properties.export_policy = AAZObjectType(
            serialized_name="exportPolicy",
        )
        properties.file_access_logs = AAZStrType(
            serialized_name="fileAccessLogs",
            flags={"read_only": True},
        )
        properties.file_system_id = AAZStrType(
            serialized_name="fileSystemId",
            flags={"read_only": True},
        )
        properties.is_default_quota_enabled = AAZBoolType(
            serialized_name="isDefaultQuotaEnabled",
        )
        properties.is_large_volume = AAZBoolType(
            serialized_name="isLargeVolume",
        )
        properties.is_restoring = AAZBoolType(
            serialized_name="isRestoring",
            flags={"read_only": True},
        )
        properties.kerberos_enabled = AAZBoolType(
            serialized_name="kerberosEnabled",
        )
        properties.key_vault_private_endpoint_resource_id = AAZStrType(
            serialized_name="keyVaultPrivateEndpointResourceId",
        )
        properties.ldap_enabled = AAZBoolType(
            serialized_name="ldapEnabled",
        )
        properties.maximum_number_of_files = AAZIntType(
            serialized_name="maximumNumberOfFiles",
            flags={"read_only": True},
        )
        properties.mount_targets = AAZListType(
            serialized_name="mountTargets",
            flags={"read_only": True},
        )
        properties.network_features = AAZStrType(
            serialized_name="networkFeatures",
        )
        properties.network_sibling_set_id = AAZStrType(
            serialized_name="networkSiblingSetId",
            flags={"read_only": True},
        )
        properties.originating_resource_id = AAZStrType(
            serialized_name="originatingResourceId",
            nullable=True,
            flags={"read_only": True},
        )
        properties.placement_rules = AAZListType(
            serialized_name="placementRules",
        )
        properties.protocol_types = AAZListType(
            serialized_name="protocolTypes",
        )
        properties.provisioned_availability_zone = AAZStrType(
            serialized_name="provisionedAvailabilityZone",
            nullable=True,
            flags={"read_only": True},
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.proximity_placement_group = AAZStrType(
            serialized_name="proximityPlacementGroup",
        )
        properties.security_style = AAZStrType(
            serialized_name="securityStyle",
        )
        properties.service_level = AAZStrType(
            serialized_name="serviceLevel",
        )
        properties.smb_access_based_enumeration = AAZStrType(
            serialized_name="smbAccessBasedEnumeration",
            nullable=True,
        )
        properties.smb_continuously_available = AAZBoolType(
            serialized_name="smbContinuouslyAvailable",
        )
        properties.smb_encryption = AAZBoolType(
            serialized_name="smbEncryption",
        )
        properties.smb_non_browsable = AAZStrType(
            serialized_name="smbNonBrowsable",
        )
        properties.snapshot_directory_visible = AAZBoolType(
            serialized_name="snapshotDirectoryVisible",
        )
        properties.snapshot_id = AAZStrType(
            serialized_name="snapshotId",
            nullable=True,
        )
        properties.storage_to_network_proximity = AAZStrType(
            serialized_name="storageToNetworkProximity",
            flags={"read_only": True},
        )
        properties.subnet_id = AAZStrType(
            serialized_name="subnetId",
            flags={"required": True},
        )
        properties.t2_network = AAZStrType(
            serialized_name="t2Network",
            flags={"read_only": True},
        )
        properties.throughput_mibps = AAZFloatType(
            serialized_name="throughputMibps",
            nullable=True,
        )
        properties.unix_permissions = AAZStrType(
            serialized_name="unixPermissions",
            nullable=True,
        )
        properties.usage_threshold = AAZIntType(
            serialized_name="usageThreshold",
            flags={"required": True},
        )
        properties.volume_group_name = AAZStrType(
            serialized_name="volumeGroupName",
            flags={"read_only": True},
        )
        properties.volume_spec_name = AAZStrType(
            serialized_name="volumeSpecName",
        )
        properties.volume_type = AAZStrType(
            serialized_name="volumeType",
        )

        data_protection = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection
        data_protection.backup = AAZObjectType()
        data_protection.replication = AAZObjectType()
        data_protection.snapshot = AAZObjectType()
        data_protection.volume_relocation = AAZObjectType(
            serialized_name="volumeRelocation",
        )

        backup = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection.backup
        backup.backup_policy_id = AAZStrType(
            serialized_name="backupPolicyId",
        )
        backup.backup_vault_id = AAZStrType(
            serialized_name="backupVaultId",
        )
        backup.policy_enforced = AAZBoolType(
            serialized_name="policyEnforced",
        )

        replication = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection.replication
        replication.destination_replications = AAZListType(
            serialized_name="destinationReplications",
            flags={"read_only": True},
        )
        replication.endpoint_type = AAZStrType(
            serialized_name="endpointType",
        )
        replication.remote_path = AAZObjectType(
            serialized_name="remotePath",
        )
        replication.remote_volume_region = AAZStrType(
            serialized_name="remoteVolumeRegion",
        )
        replication.remote_volume_resource_id = AAZStrType(
            serialized_name="remoteVolumeResourceId",
        )
        replication.replication_id = AAZStrType(
            serialized_name="replicationId",
            flags={"read_only": True},
        )
        replication.replication_schedule = AAZStrType(
            serialized_name="replicationSchedule",
        )

        destination_replications = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection.replication.destination_replications
        destination_replications.Element = AAZObjectType()

        _element = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection.replication.destination_replications.Element
        _element.region = AAZStrType()
        _element.replication_type = AAZStrType(
            serialized_name="replicationType",
        )
        _element.resource_id = AAZStrType(
            serialized_name="resourceId",
        )
        _element.zone = AAZStrType()

        remote_path = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection.replication.remote_path
        remote_path.external_host_name = AAZStrType(
            serialized_name="externalHostName",
            flags={"required": True},
        )
        remote_path.server_name = AAZStrType(
            serialized_name="serverName",
            flags={"required": True},
        )
        remote_path.volume_name = AAZStrType(
            serialized_name="volumeName",
            flags={"required": True},
        )

        snapshot = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection.snapshot
        snapshot.snapshot_policy_id = AAZStrType(
            serialized_name="snapshotPolicyId",
        )

        volume_relocation = _schema_volume_group_details_read.properties.volumes.Element.properties.data_protection.volume_relocation
        volume_relocation.ready_to_be_finalized = AAZBoolType(
            serialized_name="readyToBeFinalized",
            flags={"read_only": True},
        )
        volume_relocation.relocation_requested = AAZBoolType(
            serialized_name="relocationRequested",
        )

        data_store_resource_id = _schema_volume_group_details_read.properties.volumes.Element.properties.data_store_resource_id
        data_store_resource_id.Element = AAZStrType()

        export_policy = _schema_volume_group_details_read.properties.volumes.Element.properties.export_policy
        export_policy.rules = AAZListType()

        rules = _schema_volume_group_details_read.properties.volumes.Element.properties.export_policy.rules
        rules.Element = AAZObjectType()

        _element = _schema_volume_group_details_read.properties.volumes.Element.properties.export_policy.rules.Element
        _element.allowed_clients = AAZStrType(
            serialized_name="allowedClients",
        )
        _element.chown_mode = AAZStrType(
            serialized_name="chownMode",
        )
        _element.cifs = AAZBoolType()
        _element.has_root_access = AAZBoolType(
            serialized_name="hasRootAccess",
        )
        _element.kerberos5_read_only = AAZBoolType(
            serialized_name="kerberos5ReadOnly",
        )
        _element.kerberos5_read_write = AAZBoolType(
            serialized_name="kerberos5ReadWrite",
        )
        _element.kerberos5i_read_only = AAZBoolType(
            serialized_name="kerberos5iReadOnly",
        )
        _element.kerberos5i_read_write = AAZBoolType(
            serialized_name="kerberos5iReadWrite",
        )
        _element.kerberos5p_read_only = AAZBoolType(
            serialized_name="kerberos5pReadOnly",
        )
        _element.kerberos5p_read_write = AAZBoolType(
            serialized_name="kerberos5pReadWrite",
        )
        _element.nfsv3 = AAZBoolType()
        _element.nfsv41 = AAZBoolType()
        _element.rule_index = AAZIntType(
            serialized_name="ruleIndex",
        )
        _element.unix_read_only = AAZBoolType(
            serialized_name="unixReadOnly",
        )
        _element.unix_read_write = AAZBoolType(
            serialized_name="unixReadWrite",
        )

        mount_targets = _schema_volume_group_details_read.properties.volumes.Element.properties.mount_targets
        mount_targets.Element = AAZObjectType()

        _element = _schema_volume_group_details_read.properties.volumes.Element.properties.mount_targets.Element
        _element.file_system_id = AAZStrType(
            serialized_name="fileSystemId",
            flags={"required": True},
        )
        _element.ip_address = AAZStrType(
            serialized_name="ipAddress",
            flags={"read_only": True},
        )
        _element.mount_target_id = AAZStrType(
            serialized_name="mountTargetId",
            flags={"read_only": True},
        )
        _element.smb_server_fqdn = AAZStrType(
            serialized_name="smbServerFqdn",
        )

        placement_rules = _schema_volume_group_details_read.properties.volumes.Element.properties.placement_rules
        placement_rules.Element = AAZObjectType()
        cls._build_schema_placement_key_value_pairs_read(placement_rules.Element)

        protocol_types = _schema_volume_group_details_read.properties.volumes.Element.properties.protocol_types
        protocol_types.Element = AAZStrType()

        tags = _schema_volume_group_details_read.properties.volumes.Element.tags
        tags.Element = AAZStrType()

        zones = _schema_volume_group_details_read.properties.volumes.Element.zones
        zones.Element = AAZStrType()

        _schema.id = cls._schema_volume_group_details_read.id
        _schema.location = cls._schema_volume_group_details_read.location
        _schema.name = cls._schema_volume_group_details_read.name
        _schema.properties = cls._schema_volume_group_details_read.properties
        _schema.type = cls._schema_volume_group_details_read.type


__all__ = ["Update"]
