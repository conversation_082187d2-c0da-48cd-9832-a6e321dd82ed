# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "netappfiles volume create",
)
class Create(AAZCommand):
    """Create or Update a volume

    Create the specified volume within the capacity pool

    :example: Create an ANF volume
        az netappfiles volume create -g group --account-name aname --pool-name pname --volume-name vname -l location --service-level "Premium" --usage-threshold 100 --creation-token "unique-token" --protocol-types NFSv3 --vnet myvnet --subnet-id "/subscriptions/mysubsid/resourceGroups/myrg/providers/Microsoft.Network/virtualNetworks/myvnet/subnets/default" --rules '[{"allowed_clients":"0.0.0.0/0","rule_index":"1","unix_read_only":"true","unix_read_write":"false","cifs":"false","nfsv3":"true","nfsv41":"false"}]'

    :example: Create an ANF volume with zones (Availability Zone) specified
        az netappfiles volume create -g mygroup --account-name myaccname --pool-name mypoolname --name myvolname -l westus2 --service-level premium --usage-threshold 100 --file-path "unique-file-path" --vnet myvnet --subnet mysubnet --protocol-types NFSv3 --zones zone1

    :example: Create an ANF volume with CMK Encryption
        az netappfiles volume create -g mygroup --account-name myaccname --pool-name mypoolname --name myvolname -l westus2 --service-level premium --usage-threshold 100 --file-path "unique-file-path" --vnet myvnet --subnet mysubnet --protocol-types NFSv3 --network-features Standard --protocol-types NFSv4.1 --rule-index 1 --allowed-clients '********/24' --kerberos-enabled false --encryption-key-source  Microsoft.KeyVault --kv-private-endpoint-id myPrivateEndpointId
    """

    _aaz_info = {
        "version": "2025-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.netapp/netappaccounts/{}/capacitypools/{}/volumes/{}", "2025-01-01"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.account_name = AAZStrArg(
            options=["-a", "--account-name"],
            help="The name of the NetApp account",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,127}$",
            ),
        )
        _args_schema.pool_name = AAZStrArg(
            options=["-p", "--pool-name"],
            help="The name of the capacity pool",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,63}$",
                max_length=64,
                min_length=1,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.volume_name = AAZStrArg(
            options=["-n", "-v", "--name", "--volume-name"],
            help="The name of the volume",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z][a-zA-Z0-9\\-_]{0,63}$",
                max_length=64,
                min_length=1,
            ),
        )

        # define Arg Group "Backup"

        _args_schema = cls._args_schema
        _args_schema.backup_policy_id = AAZResourceIdArg(
            options=["--backup-policy-id"],
            arg_group="Backup",
            help="Backup Policy Resource ID",
        )
        _args_schema.backup_vault_id = AAZResourceIdArg(
            options=["--backup-vault-id"],
            arg_group="Backup",
            help="Backup Vault Resource ID",
        )
        _args_schema.policy_enforced = AAZBoolArg(
            options=["--policy-enforced"],
            arg_group="Backup",
            help="Policy Enforced",
        )

        # define Arg Group "Body"

        _args_schema = cls._args_schema
        _args_schema.location = AAZResourceLocationArg(
            arg_group="Body",
            help="The geo-location where the resource lives",
            required=True,
            fmt=AAZResourceLocationArgFormat(
                resource_group_arg="resource_group",
            ),
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Body",
            help="Resource tags.",
        )
        _args_schema.zones = AAZListArg(
            options=["--zones"],
            arg_group="Body",
            help="Availability Zone",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        zones = cls._args_schema.zones
        zones.Element = AAZStrArg(
            fmt=AAZStrArgFormat(
                max_length=255,
                min_length=1,
            ),
        )

        # define Arg Group "CMK Encryption"

        _args_schema = cls._args_schema
        _args_schema.encryption_key_source = AAZStrArg(
            options=["--encryption-key-source"],
            arg_group="CMK Encryption",
            help="Source of key used to encrypt data in volume. Applicable if NetApp account has encryption.keySource = 'Microsoft.KeyVault'. Possible values (case-insensitive) are: 'Microsoft.NetApp, Microsoft.KeyVault'",
            default="Microsoft.NetApp",
            enum={"Microsoft.KeyVault": "Microsoft.KeyVault", "Microsoft.NetApp": "Microsoft.NetApp"},
        )
        _args_schema.key_vault_private_endpoint_resource_id = AAZStrArg(
            options=["--kv-private-endpoint-id", "--key-vault-private-endpoint-resource-id"],
            arg_group="CMK Encryption",
            help="The resource ID of private endpoint for KeyVault. It must reside in the same VNET as the volume. Only applicable if encryptionKeySource = 'Microsoft.KeyVault'.",
        )

        # define Arg Group "ExportPolicy"

        _args_schema = cls._args_schema
        _args_schema.export_policy_rules = AAZListArg(
            options=["--rules", "--export-policy-rules"],
            arg_group="ExportPolicy",
            help="Export policy rule",
        )

        export_policy_rules = cls._args_schema.export_policy_rules
        export_policy_rules.Element = AAZObjectArg()

        _element = cls._args_schema.export_policy_rules.Element
        _element.allowed_clients = AAZStrArg(
            options=["allowed-clients"],
            help="Client ingress specification as comma separated string with IPv4 CIDRs, IPv4 host addresses and host names",
        )
        _element.chown_mode = AAZStrArg(
            options=["chown-mode"],
            help="This parameter specifies who is authorized to change the ownership of a file. restricted - Only root user can change the ownership of the file. unrestricted - Non-root users can change ownership of files that they own.",
            default="Restricted",
            enum={"Restricted": "Restricted", "Unrestricted": "Unrestricted"},
        )
        _element.cifs = AAZBoolArg(
            options=["cifs"],
            help="Allows CIFS protocol",
        )
        _element.has_root_access = AAZBoolArg(
            options=["has-root-access"],
            help="Has root access to volume",
            default=True,
        )
        _element.kerberos5_read_only = AAZBoolArg(
            options=["kerberos5-read-only"],
            help="Kerberos5 Read only access. To be use with swagger version 2020-05-01 or later",
            default=False,
        )
        _element.kerberos5_read_write = AAZBoolArg(
            options=["kerberos5-read-write"],
            help="Kerberos5 Read and write access. To be use with swagger version 2020-05-01 or later",
            default=False,
        )
        _element.kerberos5i_read_only = AAZBoolArg(
            options=["kerberos5i-read-only"],
            help="Kerberos5i Read only access. To be use with swagger version 2020-05-01 or later",
            default=False,
        )
        _element.kerberos5i_read_write = AAZBoolArg(
            options=["kerberos5i-read-write"],
            help="Kerberos5i Read and write access. To be use with swagger version 2020-05-01 or later",
            default=False,
        )
        _element.kerberos5p_read_only = AAZBoolArg(
            options=["kerberos5p-read-only"],
            help="Kerberos5p Read only access. To be use with swagger version 2020-05-01 or later",
            default=False,
        )
        _element.kerberos5p_read_write = AAZBoolArg(
            options=["kerberos5p-read-write"],
            help="Kerberos5p Read and write access. To be use with swagger version 2020-05-01 or later",
            default=False,
        )
        _element.nfsv3 = AAZBoolArg(
            options=["nfsv3"],
            help="Allows NFSv3 protocol. Enable only for NFSv3 type volumes",
        )
        _element.nfsv41 = AAZBoolArg(
            options=["nfsv41"],
            help="Allows NFSv4.1 protocol. Enable only for NFSv4.1 type volumes",
        )
        _element.rule_index = AAZIntArg(
            options=["rule-index"],
            help="Order index",
        )
        _element.unix_read_only = AAZBoolArg(
            options=["unix-read-only"],
            help="Read only access",
        )
        _element.unix_read_write = AAZBoolArg(
            options=["unix-read-write"],
            help="Read and write access",
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.avs_data_store = AAZStrArg(
            options=["--avs-data-store"],
            arg_group="Properties",
            help="Specifies whether the volume is enabled for Azure VMware Solution (AVS) datastore purpose",
            default="Disabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _args_schema.backup_id = AAZResourceIdArg(
            options=["--backup-id"],
            arg_group="Properties",
            help="Resource identifier used to identify the Backup.",
            nullable=True,
        )
        _args_schema.capacity_pool_resource_id = AAZStrArg(
            options=["--pool-resource-id", "--capacity-pool-resource-id"],
            arg_group="Properties",
            help="Pool Resource Id used in case of creating a volume through volume group",
        )
        _args_schema.cool_access = AAZBoolArg(
            options=["--cool-access"],
            arg_group="Properties",
            help="Specifies whether Cool Access(tiering) is enabled for the volume.",
            default=False,
        )
        _args_schema.cool_access_retrieval_policy = AAZStrArg(
            options=["--ca-retrieval-policy", "--cool-access-retrieval-policy"],
            arg_group="Properties",
            help="coolAccessRetrievalPolicy determines the data retrieval behavior from the cool tier to standard storage based on the read pattern for cool access enabled volumes. The possible values for this field are:   Default - Data will be pulled from cool tier to standard storage on random reads. This policy is the default.  OnRead - All client-driven data read is pulled from cool tier to standard storage on both sequential and random reads.  Never - No client-driven data is pulled from cool tier to standard storage.",
            enum={"Default": "Default", "Never": "Never", "OnRead": "OnRead"},
        )
        _args_schema.cool_access_tiering_policy = AAZStrArg(
            options=["--ca-tiering-policy", "--cool-access-tiering-policy"],
            arg_group="Properties",
            help="coolAccessTieringPolicy determines which cold data blocks are moved to cool tier. The possible values for this field are: Auto - Moves cold user data blocks in both the Snapshot copies and the active file system to the cool tier tier. This policy is the default. SnapshotOnly - Moves user data blocks of the Volume Snapshot copies that are not associated with the active file system to the cool tier.",
            enum={"Auto": "Auto", "SnapshotOnly": "SnapshotOnly"},
        )
        _args_schema.coolness_period = AAZIntArg(
            options=["--coolness-period"],
            arg_group="Properties",
            help="Specifies the number of days after which data that is not accessed by clients will be tiered.",
            fmt=AAZIntArgFormat(
                maximum=183,
                minimum=2,
            ),
        )
        _args_schema.creation_token = AAZStrArg(
            options=["--file-path", "--creation-token"],
            arg_group="Properties",
            help="A unique file path for the volume. Used when creating mount targets",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z][a-zA-Z0-9\\-]{0,79}$",
                max_length=80,
                min_length=1,
            ),
        )
        _args_schema.default_group_quota_in_ki_bs = AAZIntArg(
            options=["--default-group-quota", "--default-group-quota-in-ki-bs"],
            arg_group="Properties",
            help="Default group quota for volume in KiBs. If isDefaultQuotaEnabled is set, the minimum value of 4 KiBs applies.",
            default=0,
        )
        _args_schema.default_user_quota_in_ki_bs = AAZIntArg(
            options=["--default-user-quota", "--default-user-quota-in-ki-bs"],
            arg_group="Properties",
            help="Default user quota for volume in KiBs. If isDefaultQuotaEnabled is set, the minimum value of 4 KiBs applies .",
            default=0,
        )
        _args_schema.delete_base_snapshot = AAZBoolArg(
            options=["--delete-base-snapshot"],
            arg_group="Properties",
            help="If enabled (true) the snapshot the volume was created from will be automatically deleted after the volume create operation has finished.  Defaults to false",
        )
        _args_schema.enable_subvolumes = AAZStrArg(
            options=["--enable-subvolumes"],
            arg_group="Properties",
            help="Flag indicating whether subvolume operations are enabled on the volume",
            default="Disabled",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _args_schema.is_default_quota_enabled = AAZBoolArg(
            options=["--is-def-quota-enabled", "--default-quota-enabled", "--is-default-quota-enabled"],
            arg_group="Properties",
            help="Specifies if default quota is enabled for the volume.",
            default=False,
        )
        _args_schema.is_large_volume = AAZBoolArg(
            options=["--is-large-volume"],
            arg_group="Properties",
            help="Specifies whether volume is a Large Volume or Regular Volume.",
            default=False,
        )
        _args_schema.kerberos_enabled = AAZBoolArg(
            options=["--kerberos-enabled"],
            arg_group="Properties",
            help="Describe if a volume is KerberosEnabled. To be use with swagger version 2020-05-01 or later",
            default=False,
        )
        _args_schema.ldap_enabled = AAZBoolArg(
            options=["--ldap-enabled"],
            arg_group="Properties",
            help="Specifies whether LDAP is enabled or not for a given NFS volume.",
            default=False,
        )
        _args_schema.network_features = AAZStrArg(
            options=["--network-features"],
            arg_group="Properties",
            help="Basic network, or Standard features available to the volume.",
            default="Basic",
            enum={"Basic": "Basic", "Basic_Standard": "Basic_Standard", "Standard": "Standard", "Standard_Basic": "Standard_Basic"},
        )
        _args_schema.placement_rules = AAZListArg(
            options=["--placement-rules"],
            arg_group="Properties",
            help="Application specific placement rules for the particular volume",
        )
        _args_schema.protocol_types = AAZListArg(
            options=["--protocol-types"],
            arg_group="Properties",
            help="Set of protocol types, default NFSv3, CIFS for SMB protocol",
        )
        _args_schema.proximity_placement_group = AAZStrArg(
            options=["--ppg", "--proximity-placement-group"],
            arg_group="Properties",
            help="Proximity placement group associated with the volume",
        )
        _args_schema.security_style = AAZStrArg(
            options=["--security-style"],
            arg_group="Properties",
            help="The security style of volume, default unix, defaults to ntfs for dual protocol or CIFS protocol",
            default="unix",
            enum={"ntfs": "ntfs", "unix": "unix"},
        )
        _args_schema.service_level = AAZStrArg(
            options=["--service-level"],
            arg_group="Properties",
            help="serviceLevel",
            default="Premium",
            enum={"Premium": "Premium", "Standard": "Standard", "StandardZRS": "StandardZRS", "Ultra": "Ultra"},
        )
        _args_schema.smb_access_based_enumeration = AAZStrArg(
            options=["--smb-access-enumeration", "--smb-access-based-enumeration"],
            arg_group="Properties",
            help="Enables access based enumeration share property for SMB Shares. Only applicable for SMB/DualProtocol volume",
            nullable=True,
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _args_schema.smb_continuously_available = AAZBoolArg(
            options=["--smb-ca", "--smb-continuously-avl", "--smb-continuously-available"],
            arg_group="Properties",
            help="Enables continuously available share property for smb volume. Only applicable for SMB volume",
            default=False,
        )
        _args_schema.smb_encryption = AAZBoolArg(
            options=["--smb-encryption"],
            arg_group="Properties",
            help="Enables encryption for in-flight smb3 data. Only applicable for SMB/DualProtocol volume. To be used with swagger version 2020-08-01 or later",
            default=False,
        )
        _args_schema.smb_non_browsable = AAZStrArg(
            options=["--smb-non-browsable"],
            arg_group="Properties",
            help="Enables non browsable property for SMB Shares. Only applicable for SMB/DualProtocol volume",
            enum={"Disabled": "Disabled", "Enabled": "Enabled"},
        )
        _args_schema.snapshot_directory_visible = AAZBoolArg(
            options=["--snapshot-dir-visible", "--snapshot-directory-visible"],
            arg_group="Properties",
            help="If enabled (true) the volume will contain a read-only snapshot directory which provides access to each of the volume's snapshots (defaults to true).",
            default=True,
        )
        _args_schema.snapshot_id = AAZResourceIdArg(
            options=["--snapshot-id"],
            arg_group="Properties",
            help="Resource identifier used to identify the Snapshot.",
            nullable=True,
        )
        _args_schema.subnet_id = AAZStrArg(
            options=["--subnet", "--subnet-id"],
            arg_group="Properties",
            help="The Azure Resource URI for a delegated subnet. Must have the delegation Microsoft.NetApp/volumes",
            required=True,
            default="default",
        )
        _args_schema.throughput_mibps = AAZFloatArg(
            options=["--throughput-mibps"],
            arg_group="Properties",
            help="Maximum throughput in MiB/s that can be achieved by this volume and this will be accepted as input only for manual qosType volume",
            nullable=True,
        )
        _args_schema.unix_permissions = AAZStrArg(
            options=["--unix-permissions"],
            arg_group="Properties",
            help="UNIX permissions for NFS volume accepted in octal 4 digit format. First digit selects the set user ID(4), set group ID (2) and sticky (1) attributes. Second digit selects permission for the owner of the file: read (4), write (2) and execute (1). Third selects permissions for other users in the same group. the fourth for other users not in the group. 0755 - gives read/write/execute permissions to owner and read/execute to group and other users.",
            nullable=True,
            fmt=AAZStrArgFormat(
                max_length=4,
                min_length=4,
            ),
        )
        _args_schema.usage_threshold = AAZIntArg(
            options=["--usage-threshold"],
            arg_group="Properties",
            help={"short-summary": "Maximum storage quota allowed for a file system in GiB.", "long-summary": "Maximum storage quota allowed for a file system in bytes. This is a soft quota used for alerting only. For regular volumes, valid values are in the range 50GiB to 100TiB. For large volumes, valid values are in the range 100TiB to 500TiB, and on an exceptional basis, to 2400TiB."},
            required=True,
            default=100,
            fmt=AAZIntArgFormat(
                maximum=2638827906662400,
                minimum=53687091200,
            ),
        )
        _args_schema.volume_spec_name = AAZStrArg(
            options=["--volume-spec-name"],
            arg_group="Properties",
            help="Volume spec name is the application specific designation or identifier for the particular volume in a volume group for e.g. data, log",
        )
        _args_schema.volume_type = AAZStrArg(
            options=["--volume-type"],
            arg_group="Properties",
            help="What type of volume is this. For destination volumes in Cross Region Replication, set type to DataProtection",
        )

        placement_rules = cls._args_schema.placement_rules
        placement_rules.Element = AAZObjectArg()

        _element = cls._args_schema.placement_rules.Element
        _element.key = AAZStrArg(
            options=["key"],
            help="Key for an application specific parameter for the placement of volumes in the volume group",
            required=True,
        )
        _element.value = AAZStrArg(
            options=["value"],
            help="Value for an application specific parameter for the placement of volumes in the volume group",
            required=True,
        )

        protocol_types = cls._args_schema.protocol_types
        protocol_types.Element = AAZStrArg()

        # define Arg Group "RemotePath"

        _args_schema = cls._args_schema
        _args_schema.external_host_name = AAZStrArg(
            options=["--external-host-name"],
            arg_group="RemotePath",
            help="The Path to a ONTAP Host",
        )
        _args_schema.external_server_name = AAZStrArg(
            options=["--external-server-name"],
            arg_group="RemotePath",
            help="The name of a server on the ONTAP Host",
        )
        _args_schema.external_volume_name = AAZStrArg(
            options=["--external-volume-name"],
            arg_group="RemotePath",
            help="The name of a volume on the server",
        )

        # define Arg Group "Replication"

        _args_schema = cls._args_schema
        _args_schema.endpoint_type = AAZStrArg(
            options=["--endpoint-type"],
            arg_group="Replication",
            help="Indicates whether the local volume is the source or destination for the Volume Replication",
            enum={"dst": "dst", "src": "src"},
        )
        _args_schema.remote_volume_region = AAZStrArg(
            options=["--remote-volume-region"],
            arg_group="Replication",
            help="The remote region for the other end of the Volume Replication.",
        )
        _args_schema.remote_volume_resource_id = AAZStrArg(
            options=["--remote-volume-id", "--remote-volume-resource-id"],
            arg_group="Replication",
            help="The resource ID of the remote volume.",
        )
        _args_schema.replication_schedule = AAZStrArg(
            options=["--replication-schedule"],
            arg_group="Replication",
            help="Schedule",
            enum={"_10minutely": "_10minutely", "daily": "daily", "hourly": "hourly"},
        )

        # define Arg Group "Snapshot"

        _args_schema = cls._args_schema
        _args_schema.snapshot_policy_id = AAZStrArg(
            options=["--snapshot-policy-id"],
            arg_group="Snapshot",
            help="Snapshot Policy ResourceId",
        )

        # define Arg Group "VolumeRelocation"

        _args_schema = cls._args_schema
        _args_schema.relocation_requested = AAZBoolArg(
            options=["--relocation-requested"],
            arg_group="VolumeRelocation",
            help="Has relocation been requested for this volume",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        yield self.VolumesCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class VolumesCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200, 201]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200_201,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.NetApp/netAppAccounts/{accountName}/capacityPools/{poolName}/volumes/{volumeName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "poolName", self.ctx.args.pool_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
                **self.serialize_url_param(
                    "volumeName", self.ctx.args.volume_name,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("location", AAZStrType, ".location", typ_kwargs={"flags": {"required": True}})
            _builder.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")
            _builder.set_prop("zones", AAZListType, ".zones")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("avsDataStore", AAZStrType, ".avs_data_store")
                properties.set_prop("backupId", AAZStrType, ".backup_id", typ_kwargs={"nullable": True})
                properties.set_prop("capacityPoolResourceId", AAZStrType, ".capacity_pool_resource_id")
                properties.set_prop("coolAccess", AAZBoolType, ".cool_access")
                properties.set_prop("coolAccessRetrievalPolicy", AAZStrType, ".cool_access_retrieval_policy")
                properties.set_prop("coolAccessTieringPolicy", AAZStrType, ".cool_access_tiering_policy")
                properties.set_prop("coolnessPeriod", AAZIntType, ".coolness_period")
                properties.set_prop("creationToken", AAZStrType, ".creation_token", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("dataProtection", AAZObjectType)
                properties.set_prop("defaultGroupQuotaInKiBs", AAZIntType, ".default_group_quota_in_ki_bs")
                properties.set_prop("defaultUserQuotaInKiBs", AAZIntType, ".default_user_quota_in_ki_bs")
                properties.set_prop("deleteBaseSnapshot", AAZBoolType, ".delete_base_snapshot")
                properties.set_prop("enableSubvolumes", AAZStrType, ".enable_subvolumes")
                properties.set_prop("encryptionKeySource", AAZStrType, ".encryption_key_source")
                properties.set_prop("exportPolicy", AAZObjectType)
                properties.set_prop("isDefaultQuotaEnabled", AAZBoolType, ".is_default_quota_enabled")
                properties.set_prop("isLargeVolume", AAZBoolType, ".is_large_volume")
                properties.set_prop("kerberosEnabled", AAZBoolType, ".kerberos_enabled")
                properties.set_prop("keyVaultPrivateEndpointResourceId", AAZStrType, ".key_vault_private_endpoint_resource_id")
                properties.set_prop("ldapEnabled", AAZBoolType, ".ldap_enabled")
                properties.set_prop("networkFeatures", AAZStrType, ".network_features")
                properties.set_prop("placementRules", AAZListType, ".placement_rules")
                properties.set_prop("protocolTypes", AAZListType, ".protocol_types")
                properties.set_prop("proximityPlacementGroup", AAZStrType, ".proximity_placement_group")
                properties.set_prop("securityStyle", AAZStrType, ".security_style")
                properties.set_prop("serviceLevel", AAZStrType, ".service_level")
                properties.set_prop("smbAccessBasedEnumeration", AAZStrType, ".smb_access_based_enumeration", typ_kwargs={"nullable": True})
                properties.set_prop("smbContinuouslyAvailable", AAZBoolType, ".smb_continuously_available")
                properties.set_prop("smbEncryption", AAZBoolType, ".smb_encryption")
                properties.set_prop("smbNonBrowsable", AAZStrType, ".smb_non_browsable")
                properties.set_prop("snapshotDirectoryVisible", AAZBoolType, ".snapshot_directory_visible")
                properties.set_prop("snapshotId", AAZStrType, ".snapshot_id", typ_kwargs={"nullable": True})
                properties.set_prop("subnetId", AAZStrType, ".subnet_id", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("throughputMibps", AAZFloatType, ".throughput_mibps", typ_kwargs={"nullable": True})
                properties.set_prop("unixPermissions", AAZStrType, ".unix_permissions", typ_kwargs={"nullable": True})
                properties.set_prop("usageThreshold", AAZIntType, ".usage_threshold", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("volumeSpecName", AAZStrType, ".volume_spec_name")
                properties.set_prop("volumeType", AAZStrType, ".volume_type")

            data_protection = _builder.get(".properties.dataProtection")
            if data_protection is not None:
                data_protection.set_prop("backup", AAZObjectType)
                data_protection.set_prop("replication", AAZObjectType)
                data_protection.set_prop("snapshot", AAZObjectType)
                data_protection.set_prop("volumeRelocation", AAZObjectType)

            backup = _builder.get(".properties.dataProtection.backup")
            if backup is not None:
                backup.set_prop("backupPolicyId", AAZStrType, ".backup_policy_id")
                backup.set_prop("backupVaultId", AAZStrType, ".backup_vault_id")
                backup.set_prop("policyEnforced", AAZBoolType, ".policy_enforced")

            replication = _builder.get(".properties.dataProtection.replication")
            if replication is not None:
                replication.set_prop("endpointType", AAZStrType, ".endpoint_type")
                replication.set_prop("remotePath", AAZObjectType)
                replication.set_prop("remoteVolumeRegion", AAZStrType, ".remote_volume_region")
                replication.set_prop("remoteVolumeResourceId", AAZStrType, ".remote_volume_resource_id")
                replication.set_prop("replicationSchedule", AAZStrType, ".replication_schedule")

            remote_path = _builder.get(".properties.dataProtection.replication.remotePath")
            if remote_path is not None:
                remote_path.set_prop("externalHostName", AAZStrType, ".external_host_name", typ_kwargs={"flags": {"required": True}})
                remote_path.set_prop("serverName", AAZStrType, ".external_server_name", typ_kwargs={"flags": {"required": True}})
                remote_path.set_prop("volumeName", AAZStrType, ".external_volume_name", typ_kwargs={"flags": {"required": True}})

            snapshot = _builder.get(".properties.dataProtection.snapshot")
            if snapshot is not None:
                snapshot.set_prop("snapshotPolicyId", AAZStrType, ".snapshot_policy_id")

            volume_relocation = _builder.get(".properties.dataProtection.volumeRelocation")
            if volume_relocation is not None:
                volume_relocation.set_prop("relocationRequested", AAZBoolType, ".relocation_requested")

            export_policy = _builder.get(".properties.exportPolicy")
            if export_policy is not None:
                export_policy.set_prop("rules", AAZListType, ".export_policy_rules")

            rules = _builder.get(".properties.exportPolicy.rules")
            if rules is not None:
                rules.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.exportPolicy.rules[]")
            if _elements is not None:
                _elements.set_prop("allowedClients", AAZStrType, ".allowed_clients")
                _elements.set_prop("chownMode", AAZStrType, ".chown_mode")
                _elements.set_prop("cifs", AAZBoolType, ".cifs")
                _elements.set_prop("hasRootAccess", AAZBoolType, ".has_root_access")
                _elements.set_prop("kerberos5ReadOnly", AAZBoolType, ".kerberos5_read_only")
                _elements.set_prop("kerberos5ReadWrite", AAZBoolType, ".kerberos5_read_write")
                _elements.set_prop("kerberos5iReadOnly", AAZBoolType, ".kerberos5i_read_only")
                _elements.set_prop("kerberos5iReadWrite", AAZBoolType, ".kerberos5i_read_write")
                _elements.set_prop("kerberos5pReadOnly", AAZBoolType, ".kerberos5p_read_only")
                _elements.set_prop("kerberos5pReadWrite", AAZBoolType, ".kerberos5p_read_write")
                _elements.set_prop("nfsv3", AAZBoolType, ".nfsv3")
                _elements.set_prop("nfsv41", AAZBoolType, ".nfsv41")
                _elements.set_prop("ruleIndex", AAZIntType, ".rule_index")
                _elements.set_prop("unixReadOnly", AAZBoolType, ".unix_read_only")
                _elements.set_prop("unixReadWrite", AAZBoolType, ".unix_read_write")

            placement_rules = _builder.get(".properties.placementRules")
            if placement_rules is not None:
                placement_rules.set_elements(AAZObjectType, ".")

            _elements = _builder.get(".properties.placementRules[]")
            if _elements is not None:
                _elements.set_prop("key", AAZStrType, ".key", typ_kwargs={"flags": {"required": True}})
                _elements.set_prop("value", AAZStrType, ".value", typ_kwargs={"flags": {"required": True}})

            protocol_types = _builder.get(".properties.protocolTypes")
            if protocol_types is not None:
                protocol_types.set_elements(AAZStrType, ".")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            zones = _builder.get(".zones")
            if zones is not None:
                zones.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()

            _schema_on_200_201 = cls._schema_on_200_201
            _schema_on_200_201.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200_201.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _schema_on_200_201.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _schema_on_200_201.tags = AAZDictType()
            _schema_on_200_201.type = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200_201.zones = AAZListType()

            properties = cls._schema_on_200_201.properties
            properties.actual_throughput_mibps = AAZFloatType(
                serialized_name="actualThroughputMibps",
                flags={"read_only": True},
            )
            properties.avs_data_store = AAZStrType(
                serialized_name="avsDataStore",
            )
            properties.backup_id = AAZStrType(
                serialized_name="backupId",
                nullable=True,
            )
            properties.baremetal_tenant_id = AAZStrType(
                serialized_name="baremetalTenantId",
                flags={"read_only": True},
            )
            properties.capacity_pool_resource_id = AAZStrType(
                serialized_name="capacityPoolResourceId",
            )
            properties.clone_progress = AAZIntType(
                serialized_name="cloneProgress",
                nullable=True,
                flags={"read_only": True},
            )
            properties.cool_access = AAZBoolType(
                serialized_name="coolAccess",
            )
            properties.cool_access_retrieval_policy = AAZStrType(
                serialized_name="coolAccessRetrievalPolicy",
            )
            properties.cool_access_tiering_policy = AAZStrType(
                serialized_name="coolAccessTieringPolicy",
            )
            properties.coolness_period = AAZIntType(
                serialized_name="coolnessPeriod",
            )
            properties.creation_token = AAZStrType(
                serialized_name="creationToken",
                flags={"required": True},
            )
            properties.data_protection = AAZObjectType(
                serialized_name="dataProtection",
            )
            properties.data_store_resource_id = AAZListType(
                serialized_name="dataStoreResourceId",
                flags={"read_only": True},
            )
            properties.default_group_quota_in_ki_bs = AAZIntType(
                serialized_name="defaultGroupQuotaInKiBs",
            )
            properties.default_user_quota_in_ki_bs = AAZIntType(
                serialized_name="defaultUserQuotaInKiBs",
            )
            properties.delete_base_snapshot = AAZBoolType(
                serialized_name="deleteBaseSnapshot",
            )
            properties.effective_network_features = AAZStrType(
                serialized_name="effectiveNetworkFeatures",
            )
            properties.enable_subvolumes = AAZStrType(
                serialized_name="enableSubvolumes",
            )
            properties.encrypted = AAZBoolType(
                flags={"read_only": True},
            )
            properties.encryption_key_source = AAZStrType(
                serialized_name="encryptionKeySource",
            )
            properties.export_policy = AAZObjectType(
                serialized_name="exportPolicy",
            )
            properties.file_access_logs = AAZStrType(
                serialized_name="fileAccessLogs",
                flags={"read_only": True},
            )
            properties.file_system_id = AAZStrType(
                serialized_name="fileSystemId",
                flags={"read_only": True},
            )
            properties.is_default_quota_enabled = AAZBoolType(
                serialized_name="isDefaultQuotaEnabled",
            )
            properties.is_large_volume = AAZBoolType(
                serialized_name="isLargeVolume",
            )
            properties.is_restoring = AAZBoolType(
                serialized_name="isRestoring",
                flags={"read_only": True},
            )
            properties.kerberos_enabled = AAZBoolType(
                serialized_name="kerberosEnabled",
            )
            properties.key_vault_private_endpoint_resource_id = AAZStrType(
                serialized_name="keyVaultPrivateEndpointResourceId",
            )
            properties.ldap_enabled = AAZBoolType(
                serialized_name="ldapEnabled",
            )
            properties.maximum_number_of_files = AAZIntType(
                serialized_name="maximumNumberOfFiles",
                flags={"read_only": True},
            )
            properties.mount_targets = AAZListType(
                serialized_name="mountTargets",
                flags={"read_only": True},
            )
            properties.network_features = AAZStrType(
                serialized_name="networkFeatures",
            )
            properties.network_sibling_set_id = AAZStrType(
                serialized_name="networkSiblingSetId",
                flags={"read_only": True},
            )
            properties.originating_resource_id = AAZStrType(
                serialized_name="originatingResourceId",
                nullable=True,
                flags={"read_only": True},
            )
            properties.placement_rules = AAZListType(
                serialized_name="placementRules",
            )
            properties.protocol_types = AAZListType(
                serialized_name="protocolTypes",
            )
            properties.provisioned_availability_zone = AAZStrType(
                serialized_name="provisionedAvailabilityZone",
                nullable=True,
                flags={"read_only": True},
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.proximity_placement_group = AAZStrType(
                serialized_name="proximityPlacementGroup",
            )
            properties.security_style = AAZStrType(
                serialized_name="securityStyle",
            )
            properties.service_level = AAZStrType(
                serialized_name="serviceLevel",
            )
            properties.smb_access_based_enumeration = AAZStrType(
                serialized_name="smbAccessBasedEnumeration",
                nullable=True,
            )
            properties.smb_continuously_available = AAZBoolType(
                serialized_name="smbContinuouslyAvailable",
            )
            properties.smb_encryption = AAZBoolType(
                serialized_name="smbEncryption",
            )
            properties.smb_non_browsable = AAZStrType(
                serialized_name="smbNonBrowsable",
            )
            properties.snapshot_directory_visible = AAZBoolType(
                serialized_name="snapshotDirectoryVisible",
            )
            properties.snapshot_id = AAZStrType(
                serialized_name="snapshotId",
                nullable=True,
            )
            properties.storage_to_network_proximity = AAZStrType(
                serialized_name="storageToNetworkProximity",
                flags={"read_only": True},
            )
            properties.subnet_id = AAZStrType(
                serialized_name="subnetId",
                flags={"required": True},
            )
            properties.t2_network = AAZStrType(
                serialized_name="t2Network",
                flags={"read_only": True},
            )
            properties.throughput_mibps = AAZFloatType(
                serialized_name="throughputMibps",
                nullable=True,
            )
            properties.unix_permissions = AAZStrType(
                serialized_name="unixPermissions",
                nullable=True,
            )
            properties.usage_threshold = AAZIntType(
                serialized_name="usageThreshold",
                flags={"required": True},
            )
            properties.volume_group_name = AAZStrType(
                serialized_name="volumeGroupName",
                flags={"read_only": True},
            )
            properties.volume_spec_name = AAZStrType(
                serialized_name="volumeSpecName",
            )
            properties.volume_type = AAZStrType(
                serialized_name="volumeType",
            )

            data_protection = cls._schema_on_200_201.properties.data_protection
            data_protection.backup = AAZObjectType()
            data_protection.replication = AAZObjectType()
            data_protection.snapshot = AAZObjectType()
            data_protection.volume_relocation = AAZObjectType(
                serialized_name="volumeRelocation",
            )

            backup = cls._schema_on_200_201.properties.data_protection.backup
            backup.backup_policy_id = AAZStrType(
                serialized_name="backupPolicyId",
            )
            backup.backup_vault_id = AAZStrType(
                serialized_name="backupVaultId",
            )
            backup.policy_enforced = AAZBoolType(
                serialized_name="policyEnforced",
            )

            replication = cls._schema_on_200_201.properties.data_protection.replication
            replication.destination_replications = AAZListType(
                serialized_name="destinationReplications",
                flags={"read_only": True},
            )
            replication.endpoint_type = AAZStrType(
                serialized_name="endpointType",
            )
            replication.remote_path = AAZObjectType(
                serialized_name="remotePath",
            )
            replication.remote_volume_region = AAZStrType(
                serialized_name="remoteVolumeRegion",
            )
            replication.remote_volume_resource_id = AAZStrType(
                serialized_name="remoteVolumeResourceId",
            )
            replication.replication_id = AAZStrType(
                serialized_name="replicationId",
                flags={"read_only": True},
            )
            replication.replication_schedule = AAZStrType(
                serialized_name="replicationSchedule",
            )

            destination_replications = cls._schema_on_200_201.properties.data_protection.replication.destination_replications
            destination_replications.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.data_protection.replication.destination_replications.Element
            _element.region = AAZStrType()
            _element.replication_type = AAZStrType(
                serialized_name="replicationType",
            )
            _element.resource_id = AAZStrType(
                serialized_name="resourceId",
            )
            _element.zone = AAZStrType()

            remote_path = cls._schema_on_200_201.properties.data_protection.replication.remote_path
            remote_path.external_host_name = AAZStrType(
                serialized_name="externalHostName",
                flags={"required": True},
            )
            remote_path.server_name = AAZStrType(
                serialized_name="serverName",
                flags={"required": True},
            )
            remote_path.volume_name = AAZStrType(
                serialized_name="volumeName",
                flags={"required": True},
            )

            snapshot = cls._schema_on_200_201.properties.data_protection.snapshot
            snapshot.snapshot_policy_id = AAZStrType(
                serialized_name="snapshotPolicyId",
            )

            volume_relocation = cls._schema_on_200_201.properties.data_protection.volume_relocation
            volume_relocation.ready_to_be_finalized = AAZBoolType(
                serialized_name="readyToBeFinalized",
                flags={"read_only": True},
            )
            volume_relocation.relocation_requested = AAZBoolType(
                serialized_name="relocationRequested",
            )

            data_store_resource_id = cls._schema_on_200_201.properties.data_store_resource_id
            data_store_resource_id.Element = AAZStrType()

            export_policy = cls._schema_on_200_201.properties.export_policy
            export_policy.rules = AAZListType()

            rules = cls._schema_on_200_201.properties.export_policy.rules
            rules.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.export_policy.rules.Element
            _element.allowed_clients = AAZStrType(
                serialized_name="allowedClients",
            )
            _element.chown_mode = AAZStrType(
                serialized_name="chownMode",
            )
            _element.cifs = AAZBoolType()
            _element.has_root_access = AAZBoolType(
                serialized_name="hasRootAccess",
            )
            _element.kerberos5_read_only = AAZBoolType(
                serialized_name="kerberos5ReadOnly",
            )
            _element.kerberos5_read_write = AAZBoolType(
                serialized_name="kerberos5ReadWrite",
            )
            _element.kerberos5i_read_only = AAZBoolType(
                serialized_name="kerberos5iReadOnly",
            )
            _element.kerberos5i_read_write = AAZBoolType(
                serialized_name="kerberos5iReadWrite",
            )
            _element.kerberos5p_read_only = AAZBoolType(
                serialized_name="kerberos5pReadOnly",
            )
            _element.kerberos5p_read_write = AAZBoolType(
                serialized_name="kerberos5pReadWrite",
            )
            _element.nfsv3 = AAZBoolType()
            _element.nfsv41 = AAZBoolType()
            _element.rule_index = AAZIntType(
                serialized_name="ruleIndex",
            )
            _element.unix_read_only = AAZBoolType(
                serialized_name="unixReadOnly",
            )
            _element.unix_read_write = AAZBoolType(
                serialized_name="unixReadWrite",
            )

            mount_targets = cls._schema_on_200_201.properties.mount_targets
            mount_targets.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.mount_targets.Element
            _element.file_system_id = AAZStrType(
                serialized_name="fileSystemId",
                flags={"required": True},
            )
            _element.ip_address = AAZStrType(
                serialized_name="ipAddress",
                flags={"read_only": True},
            )
            _element.mount_target_id = AAZStrType(
                serialized_name="mountTargetId",
                flags={"read_only": True},
            )
            _element.smb_server_fqdn = AAZStrType(
                serialized_name="smbServerFqdn",
            )

            placement_rules = cls._schema_on_200_201.properties.placement_rules
            placement_rules.Element = AAZObjectType()

            _element = cls._schema_on_200_201.properties.placement_rules.Element
            _element.key = AAZStrType(
                flags={"required": True},
            )
            _element.value = AAZStrType(
                flags={"required": True},
            )

            protocol_types = cls._schema_on_200_201.properties.protocol_types
            protocol_types.Element = AAZStrType()

            system_data = cls._schema_on_200_201.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            tags = cls._schema_on_200_201.tags
            tags.Element = AAZStrType()

            zones = cls._schema_on_200_201.zones
            zones.Element = AAZStrType()

            return cls._schema_on_200_201


class _CreateHelper:
    """Helper class for Create"""


__all__ = ["Create"]
