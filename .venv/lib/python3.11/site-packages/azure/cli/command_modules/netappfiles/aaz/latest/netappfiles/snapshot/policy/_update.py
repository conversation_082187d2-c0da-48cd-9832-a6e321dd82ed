# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "netappfiles snapshot policy update",
)
class Update(AAZCommand):
    """Update a snapshot policy

    :example: Update specific values for an ANF snapshot policy
        az netappfiles snapshot policy update -g mygroup --account-name myaccountname --snapshot-policy-name mysnapshotpolicyname --daily-snapshots 1 --enabled false
    """

    _aaz_info = {
        "version": "2025-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.netapp/netappaccounts/{}/snapshotpolicies/{}", "2025-01-01"],
        ]
    }

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.account_name = AAZStrArg(
            options=["-a", "--account-name"],
            help="The name of the NetApp account",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,127}$",
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.snapshot_policy_name = AAZStrArg(
            options=["-n", "--name", "--snapshot-policy-name"],
            help="The name of the snapshot policy",
            required=True,
            id_part="child_name_1",
        )

        # define Arg Group "Body"

        _args_schema = cls._args_schema
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            arg_group="Body",
            help="Resource tags.",
            nullable=True,
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg(
            nullable=True,
        )

        # define Arg Group "DailySchedule"

        _args_schema = cls._args_schema
        _args_schema.daily_hour = AAZIntArg(
            options=["--daily-hour"],
            arg_group="DailySchedule",
            help="Indicates which hour in UTC timezone a snapshot should be taken",
            nullable=True,
        )
        _args_schema.daily_minute = AAZIntArg(
            options=["--daily-minute"],
            arg_group="DailySchedule",
            help="Indicates which minute snapshot should be taken",
            nullable=True,
        )
        _args_schema.snapshots_to_keep = AAZIntArg(
            options=["-d", "--daily-snapshots", "--snapshots-to-keep"],
            arg_group="DailySchedule",
            help="Daily snapshot count to keep",
            nullable=True,
        )

        # define Arg Group "HourlySchedule"

        _args_schema = cls._args_schema
        _args_schema.hourly_minute = AAZIntArg(
            options=["--minute", "--hourly-minute"],
            arg_group="HourlySchedule",
            help="Indicates which minute snapshot should be taken",
            nullable=True,
        )
        _args_schema.hourly_snapshots = AAZIntArg(
            options=["-u", "--hourly-snapshots"],
            arg_group="HourlySchedule",
            help="Hourly snapshot count to keep",
            nullable=True,
        )

        # define Arg Group "MonthlySchedule"

        _args_schema = cls._args_schema
        _args_schema.days_of_month = AAZStrArg(
            options=["--monthly-days", "--days-of-month"],
            arg_group="MonthlySchedule",
            help="Indicates which days of the month snapshot should be taken. A comma delimited string.",
            nullable=True,
        )
        _args_schema.monthly_hour = AAZIntArg(
            options=["--hour", "--monthly-hour"],
            arg_group="MonthlySchedule",
            help="Indicates which hour in UTC timezone a snapshot should be taken",
            nullable=True,
        )
        _args_schema.monthly_minute = AAZIntArg(
            options=["--monthly-minute"],
            arg_group="MonthlySchedule",
            help="Indicates which minute snapshot should be taken",
            nullable=True,
        )
        _args_schema.monthly_snapshots = AAZIntArg(
            options=["-m", "--monthly-snapshots"],
            arg_group="MonthlySchedule",
            help="Monthly snapshot count to keep",
            nullable=True,
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.enabled = AAZBoolArg(
            options=["-e", "--enabled"],
            arg_group="Properties",
            help="The property to decide policy is enabled or not",
            nullable=True,
        )

        # define Arg Group "WeeklySchedule"

        _args_schema = cls._args_schema
        _args_schema.weekly_day = AAZStrArg(
            options=["--weekly-day"],
            arg_group="WeeklySchedule",
            help="Indicates which weekdays snapshot should be taken, accepts a comma separated list of week day names in english",
            nullable=True,
        )
        _args_schema.weekly_hour = AAZIntArg(
            options=["--weekly-hour"],
            arg_group="WeeklySchedule",
            help="Indicates which hour in UTC timezone a snapshot should be taken",
            nullable=True,
        )
        _args_schema.weekly_minute = AAZIntArg(
            options=["--weekly-minute"],
            arg_group="WeeklySchedule",
            help="Indicates which minute snapshot should be taken",
            nullable=True,
        )
        _args_schema.weekly_snapshots = AAZIntArg(
            options=["-w", "--weekly-snapshots"],
            arg_group="WeeklySchedule",
            help="Weekly snapshot count to keep",
            nullable=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.SnapshotPoliciesGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        self.SnapshotPoliciesCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class SnapshotPoliciesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.NetApp/netAppAccounts/{accountName}/snapshotPolicies/{snapshotPolicyName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "snapshotPolicyName", self.ctx.args.snapshot_policy_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_snapshot_policy_read(cls._schema_on_200)

            return cls._schema_on_200

    class SnapshotPoliciesCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200, 201]:
                return self.on_200_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.NetApp/netAppAccounts/{accountName}/snapshotPolicies/{snapshotPolicyName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "snapshotPolicyName", self.ctx.args.snapshot_policy_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_snapshot_policy_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("dailySchedule", AAZObjectType)
                properties.set_prop("enabled", AAZBoolType, ".enabled")
                properties.set_prop("hourlySchedule", AAZObjectType)
                properties.set_prop("monthlySchedule", AAZObjectType)
                properties.set_prop("weeklySchedule", AAZObjectType)

            daily_schedule = _builder.get(".properties.dailySchedule")
            if daily_schedule is not None:
                daily_schedule.set_prop("hour", AAZIntType, ".daily_hour")
                daily_schedule.set_prop("minute", AAZIntType, ".daily_minute")
                daily_schedule.set_prop("snapshotsToKeep", AAZIntType, ".snapshots_to_keep")

            hourly_schedule = _builder.get(".properties.hourlySchedule")
            if hourly_schedule is not None:
                hourly_schedule.set_prop("minute", AAZIntType, ".hourly_minute")
                hourly_schedule.set_prop("snapshotsToKeep", AAZIntType, ".hourly_snapshots")

            monthly_schedule = _builder.get(".properties.monthlySchedule")
            if monthly_schedule is not None:
                monthly_schedule.set_prop("daysOfMonth", AAZStrType, ".days_of_month")
                monthly_schedule.set_prop("hour", AAZIntType, ".monthly_hour")
                monthly_schedule.set_prop("minute", AAZIntType, ".monthly_minute")
                monthly_schedule.set_prop("snapshotsToKeep", AAZIntType, ".monthly_snapshots")

            weekly_schedule = _builder.get(".properties.weeklySchedule")
            if weekly_schedule is not None:
                weekly_schedule.set_prop("day", AAZStrType, ".weekly_day")
                weekly_schedule.set_prop("hour", AAZIntType, ".weekly_hour")
                weekly_schedule.set_prop("minute", AAZIntType, ".weekly_minute")
                weekly_schedule.set_prop("snapshotsToKeep", AAZIntType, ".weekly_snapshots")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_snapshot_policy_read = None

    @classmethod
    def _build_schema_snapshot_policy_read(cls, _schema):
        if cls._schema_snapshot_policy_read is not None:
            _schema.etag = cls._schema_snapshot_policy_read.etag
            _schema.id = cls._schema_snapshot_policy_read.id
            _schema.location = cls._schema_snapshot_policy_read.location
            _schema.name = cls._schema_snapshot_policy_read.name
            _schema.properties = cls._schema_snapshot_policy_read.properties
            _schema.system_data = cls._schema_snapshot_policy_read.system_data
            _schema.tags = cls._schema_snapshot_policy_read.tags
            _schema.type = cls._schema_snapshot_policy_read.type
            return

        cls._schema_snapshot_policy_read = _schema_snapshot_policy_read = AAZObjectType()

        snapshot_policy_read = _schema_snapshot_policy_read
        snapshot_policy_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        snapshot_policy_read.id = AAZStrType(
            flags={"read_only": True},
        )
        snapshot_policy_read.location = AAZStrType(
            flags={"required": True},
        )
        snapshot_policy_read.name = AAZStrType(
            flags={"read_only": True},
        )
        snapshot_policy_read.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )
        snapshot_policy_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        snapshot_policy_read.tags = AAZDictType()
        snapshot_policy_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_snapshot_policy_read.properties
        properties.daily_schedule = AAZObjectType(
            serialized_name="dailySchedule",
        )
        properties.enabled = AAZBoolType()
        properties.hourly_schedule = AAZObjectType(
            serialized_name="hourlySchedule",
        )
        properties.monthly_schedule = AAZObjectType(
            serialized_name="monthlySchedule",
        )
        properties.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        properties.weekly_schedule = AAZObjectType(
            serialized_name="weeklySchedule",
        )

        daily_schedule = _schema_snapshot_policy_read.properties.daily_schedule
        daily_schedule.hour = AAZIntType()
        daily_schedule.minute = AAZIntType()
        daily_schedule.snapshots_to_keep = AAZIntType(
            serialized_name="snapshotsToKeep",
        )
        daily_schedule.used_bytes = AAZIntType(
            serialized_name="usedBytes",
        )

        hourly_schedule = _schema_snapshot_policy_read.properties.hourly_schedule
        hourly_schedule.minute = AAZIntType()
        hourly_schedule.snapshots_to_keep = AAZIntType(
            serialized_name="snapshotsToKeep",
        )
        hourly_schedule.used_bytes = AAZIntType(
            serialized_name="usedBytes",
        )

        monthly_schedule = _schema_snapshot_policy_read.properties.monthly_schedule
        monthly_schedule.days_of_month = AAZStrType(
            serialized_name="daysOfMonth",
        )
        monthly_schedule.hour = AAZIntType()
        monthly_schedule.minute = AAZIntType()
        monthly_schedule.snapshots_to_keep = AAZIntType(
            serialized_name="snapshotsToKeep",
        )
        monthly_schedule.used_bytes = AAZIntType(
            serialized_name="usedBytes",
        )

        weekly_schedule = _schema_snapshot_policy_read.properties.weekly_schedule
        weekly_schedule.day = AAZStrType()
        weekly_schedule.hour = AAZIntType()
        weekly_schedule.minute = AAZIntType()
        weekly_schedule.snapshots_to_keep = AAZIntType(
            serialized_name="snapshotsToKeep",
        )
        weekly_schedule.used_bytes = AAZIntType(
            serialized_name="usedBytes",
        )

        system_data = _schema_snapshot_policy_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        tags = _schema_snapshot_policy_read.tags
        tags.Element = AAZStrType()

        _schema.etag = cls._schema_snapshot_policy_read.etag
        _schema.id = cls._schema_snapshot_policy_read.id
        _schema.location = cls._schema_snapshot_policy_read.location
        _schema.name = cls._schema_snapshot_policy_read.name
        _schema.properties = cls._schema_snapshot_policy_read.properties
        _schema.system_data = cls._schema_snapshot_policy_read.system_data
        _schema.tags = cls._schema_snapshot_policy_read.tags
        _schema.type = cls._schema_snapshot_policy_read.type


__all__ = ["Update"]
