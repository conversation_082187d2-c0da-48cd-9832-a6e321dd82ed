# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "netappfiles snapshot policy show",
)
class Show(AAZCommand):
    """Get a snapshot Policy

    :example: Get an ANF snapshot policy
        az netappfiles snapshot policy show -g mygroup --account-name myaccname --snapshot-policy-name mysnapshotpolicyname
    """

    _aaz_info = {
        "version": "2025-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.netapp/netappaccounts/{}/snapshotpolicies/{}", "2025-01-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.account_name = AAZStrArg(
            options=["-a", "--account-name"],
            help="The name of the NetApp account",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9][a-zA-Z0-9\\-_]{0,127}$",
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.snapshot_policy_name = AAZStrArg(
            options=["-n", "--name", "--snapshot-policy-name"],
            help="The name of the snapshot policy",
            required=True,
            id_part="child_name_1",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.SnapshotPoliciesGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class SnapshotPoliciesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.NetApp/netAppAccounts/{accountName}/snapshotPolicies/{snapshotPolicyName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "MgmtErrorFormat"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "snapshotPolicyName", self.ctx.args.snapshot_policy_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2025-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.etag = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"required": True, "client_flatten": True},
            )
            _schema_on_200.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.daily_schedule = AAZObjectType(
                serialized_name="dailySchedule",
            )
            properties.enabled = AAZBoolType()
            properties.hourly_schedule = AAZObjectType(
                serialized_name="hourlySchedule",
            )
            properties.monthly_schedule = AAZObjectType(
                serialized_name="monthlySchedule",
            )
            properties.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            properties.weekly_schedule = AAZObjectType(
                serialized_name="weeklySchedule",
            )

            daily_schedule = cls._schema_on_200.properties.daily_schedule
            daily_schedule.hour = AAZIntType()
            daily_schedule.minute = AAZIntType()
            daily_schedule.snapshots_to_keep = AAZIntType(
                serialized_name="snapshotsToKeep",
            )
            daily_schedule.used_bytes = AAZIntType(
                serialized_name="usedBytes",
            )

            hourly_schedule = cls._schema_on_200.properties.hourly_schedule
            hourly_schedule.minute = AAZIntType()
            hourly_schedule.snapshots_to_keep = AAZIntType(
                serialized_name="snapshotsToKeep",
            )
            hourly_schedule.used_bytes = AAZIntType(
                serialized_name="usedBytes",
            )

            monthly_schedule = cls._schema_on_200.properties.monthly_schedule
            monthly_schedule.days_of_month = AAZStrType(
                serialized_name="daysOfMonth",
            )
            monthly_schedule.hour = AAZIntType()
            monthly_schedule.minute = AAZIntType()
            monthly_schedule.snapshots_to_keep = AAZIntType(
                serialized_name="snapshotsToKeep",
            )
            monthly_schedule.used_bytes = AAZIntType(
                serialized_name="usedBytes",
            )

            weekly_schedule = cls._schema_on_200.properties.weekly_schedule
            weekly_schedule.day = AAZStrType()
            weekly_schedule.hour = AAZIntType()
            weekly_schedule.minute = AAZIntType()
            weekly_schedule.snapshots_to_keep = AAZIntType(
                serialized_name="snapshotsToKeep",
            )
            weekly_schedule.used_bytes = AAZIntType(
                serialized_name="usedBytes",
            )

            system_data = cls._schema_on_200.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _ShowHelper:
    """Helper class for Show"""


__all__ = ["Show"]
