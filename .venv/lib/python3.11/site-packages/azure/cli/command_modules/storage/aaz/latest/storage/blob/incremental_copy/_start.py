# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "storage blob incremental-copy start",
)
class Start(AAZCommand):
    """Copies an incremental copy of a blob asynchronously. Create Copy Incremental operation copies a snapshot of the source page blob to a destination page blob. The snapshot is copied such that only the differential changes between the previously copied snapshot are transferred to the destination. The copied snapshots are complete copies of the original snapshot and can be read or copied from as usual.
    """

    _aaz_info = {
        "version": "2025-07-05",
        "resources": [
            ["data-plane:microsoft.blobstorage", "/{containername}/{}?comp=incrementalcopy", "2025-07-05"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return None

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group "Client"

        _args_schema = cls._args_schema
        _args_schema.account_name = AAZStrArg(
            options=["--account-name"],
            arg_group="Client",
            required=True,
        )

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.x_ms_if_tags = AAZStrArg(
            options=["--x-ms-if-tags"],
            help="Specify a SQL where clause on blob tags to operate only on blobs with a matching value.",
        )
        _args_schema.x_ms_version = AAZStrArg(
            options=["--x-ms-version"],
            help="Specifies the version of the operation to use for this request.",
            required=True,
            default="2025-07-05",
            enum={"2025-07-05": "2025-07-05"},
        )
        _args_schema.destination_blob = AAZStrArg(
            options=["-b", "--destination-blob"],
            help="Name of the destination blob. If the exists, it will be overwritten.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-zA-Z0-9]+(?:/[a-zA-Z0-9]+)*(?:\\.[a-zA-Z0-9]+){0,1}$",
                max_length=1024,
                min_length=1,
            ),
        )
        _args_schema.destination_container = AAZStrArg(
            options=["-c", "--destination-container"],
            help="The container name.",
            required=True,
        )
        _args_schema.comp = AAZStrArg(
            options=["--comp"],
            help="comp",
            required=True,
            enum={"incrementalcopy": "incrementalcopy"},
        )
        _args_schema.timeout = AAZIntArg(
            options=["--timeout"],
            help="The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a>",
            fmt=AAZIntArgFormat(
                minimum=0,
            ),
        )

        # define Arg Group "Copy Source"

        _args_schema = cls._args_schema
        _args_schema.source_uri = AAZStrArg(
            options=["-u", "--source-uri"],
            arg_group="Copy Source",
            help="Specifies the name of the source page blob snapshot. This value is a URL of up to 2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would appear in a request URI. The source blob must either be public or must be authenticated via a shared access signature.",
            required=True,
        )

        # define Arg Group "Pre-condition"

        _args_schema = cls._args_schema
        _args_schema.destination_if_match = AAZStrArg(
            options=["--destination-if-match"],
            arg_group="Pre-condition",
            help="Specify an ETag value to operate only on blobs with a matching value.",
        )
        _args_schema.destination_if_modified_since = AAZDateTimeArg(
            options=["--destination-if-modified-since"],
            arg_group="Pre-condition",
            help="Specify this header value to operate only on a blob if it has been modified since the specified date/time.",
        )
        _args_schema.destination_if_none_match = AAZStrArg(
            options=["--destination-if-none-match"],
            arg_group="Pre-condition",
            help="Specify an ETag value to operate only on blobs without a matching value.",
        )
        _args_schema.destination_if_unmodified_since = AAZDateTimeArg(
            options=["--destination-if-unmodified-since"],
            arg_group="Pre-condition",
            help="Specify this header value to operate only on a blob if it has not been modified since the specified date/time.",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.PageBlobCopyIncremental(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    class PageBlobCopyIncremental(AAZHttpOperation):
        CLIENT_TYPE = "AAZMicrosoftBlobstorageDataPlaneClient_storage"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.on_202(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/{containerName}/{blob}?comp=incrementalcopy",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    skip_quote=True,
                    required=True,
                ),
                **self.serialize_url_param(
                    "blob", self.ctx.args.destination_blob,
                    required=True,
                ),
                **self.serialize_url_param(
                    "containerName", self.ctx.args.destination_container,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "comp", self.ctx.args.comp,
                    required=True,
                ),
                **self.serialize_query_param(
                    "timeout", self.ctx.args.timeout,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "If-Match", self.ctx.args.destination_if_match,
                ),
                **self.serialize_header_param(
                    "If-Modified-Since", self.ctx.args.destination_if_modified_since,
                ),
                **self.serialize_header_param(
                    "If-None-Match", self.ctx.args.destination_if_none_match,
                ),
                **self.serialize_header_param(
                    "If-Unmodified-Since", self.ctx.args.destination_if_unmodified_since,
                ),
                **self.serialize_header_param(
                    "x-ms-copy-source", self.ctx.args.source_uri,
                    required=True,
                ),
                **self.serialize_header_param(
                    "x-ms-if-tags", self.ctx.args.x_ms_if_tags,
                ),
                **self.serialize_header_param(
                    "x-ms-version", self.ctx.args.x_ms_version,
                    required=True,
                ),
            }
            return parameters

        def on_202(self, session):
            pass


class _StartHelper:
    """Helper class for Start"""


__all__ = ["Start"]
