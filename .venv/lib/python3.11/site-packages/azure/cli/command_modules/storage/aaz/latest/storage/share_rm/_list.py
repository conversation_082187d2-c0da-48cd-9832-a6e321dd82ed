# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "storage share-rm list",
)
class List(AAZCommand):
    """List all shares.

    :example: List the Azure file shares under the storage account 'mystorageaccount' (account name) in resource group 'MyResourceGroup'.
        az storage share-rm list -g MyResourceGroup --storage-account mystorageaccount

    :example: List the Azure file shares under the storage account 'mystorageaccount' (account id).
        az storage share-rm list --storage-account mystorageaccount

    :example: List all file shares include deleted under the storage account 'mystorageaccount' .
        az storage share-rm list --storage-account mystorageaccount --include-deleted

    :example: List all file shares include its all snapshots under the storage account 'mystorageaccount'
        az storage share-rm list --storage-account mystorageaccount --include-snapshot

    :example: List all file shares include its all snapshots and deleted file shares under the storage account 'mystorageaccount'
        az storage share-rm list --storage-account mystorageaccount --include-deleted --include-snapshot
    """

    _aaz_info = {
        "version": "2024-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.storage/storageaccounts/{}/fileservices/default/shares", "2024-01-01"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.storage_account = AAZStrArg(
            options=["--account-name", "--storage-account"],
            help="The name of the storage account within the specified resource group. Storage account names must be between 3 and 24 characters in length and use numbers and lower-case letters only.",
            required=True,
            fmt=AAZStrArgFormat(
                pattern="^[a-z0-9]+$",
                max_length=24,
                min_length=3,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.expand = AAZStrArg(
            options=["--expand"],
            help="Optional, used to expand the properties within share's properties. Valid values are: deleted, snapshots. Should be passed as a string with delimiter ','",
        )
        _args_schema.filter = AAZStrArg(
            options=["--filter"],
            help="Optional. When specified, only share names starting with the filter will be listed.",
        )
        _args_schema.maxpagesize = AAZStrArg(
            options=["--maxpagesize"],
            help="Optional. Specified maximum number of shares that can be included in the list.",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.FileSharesList(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class FileSharesList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{accountName}/fileServices/default/shares",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.storage_account,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$expand", self.ctx.args.expand,
                ),
                **self.serialize_query_param(
                    "$filter", self.ctx.args.filter,
                ),
                **self.serialize_query_param(
                    "$maxpagesize", self.ctx.args.maxpagesize,
                ),
                **self.serialize_query_param(
                    "api-version", "2024-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType(
                flags={"read_only": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType(
                flags={"read_only": True},
            )
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.access_tier = AAZStrType(
                serialized_name="accessTier",
            )
            properties.access_tier_change_time = AAZStrType(
                serialized_name="accessTierChangeTime",
                flags={"read_only": True},
            )
            properties.access_tier_status = AAZStrType(
                serialized_name="accessTierStatus",
                flags={"read_only": True},
            )
            properties.deleted = AAZBoolType(
                flags={"read_only": True},
            )
            properties.deleted_time = AAZStrType(
                serialized_name="deletedTime",
                flags={"read_only": True},
            )
            properties.enabled_protocols = AAZStrType(
                serialized_name="enabledProtocols",
            )
            properties.file_share_paid_bursting = AAZObjectType(
                serialized_name="fileSharePaidBursting",
            )
            properties.included_burst_iops = AAZIntType(
                serialized_name="includedBurstIops",
                flags={"read_only": True},
            )
            properties.last_modified_time = AAZStrType(
                serialized_name="lastModifiedTime",
                flags={"read_only": True},
            )
            properties.lease_duration = AAZStrType(
                serialized_name="leaseDuration",
                flags={"read_only": True},
            )
            properties.lease_state = AAZStrType(
                serialized_name="leaseState",
                flags={"read_only": True},
            )
            properties.lease_status = AAZStrType(
                serialized_name="leaseStatus",
                flags={"read_only": True},
            )
            properties.max_burst_credits_for_iops = AAZIntType(
                serialized_name="maxBurstCreditsForIops",
                flags={"read_only": True},
            )
            properties.metadata = AAZDictType()
            properties.next_allowed_provisioned_bandwidth_downgrade_time = AAZStrType(
                serialized_name="nextAllowedProvisionedBandwidthDowngradeTime",
                flags={"read_only": True},
            )
            properties.next_allowed_provisioned_iops_downgrade_time = AAZStrType(
                serialized_name="nextAllowedProvisionedIopsDowngradeTime",
                flags={"read_only": True},
            )
            properties.next_allowed_quota_downgrade_time = AAZStrType(
                serialized_name="nextAllowedQuotaDowngradeTime",
                flags={"read_only": True},
            )
            properties.provisioned_bandwidth_mibps = AAZIntType(
                serialized_name="provisionedBandwidthMibps",
            )
            properties.provisioned_iops = AAZIntType(
                serialized_name="provisionedIops",
            )
            properties.remaining_retention_days = AAZIntType(
                serialized_name="remainingRetentionDays",
                flags={"read_only": True},
            )
            properties.root_squash = AAZStrType(
                serialized_name="rootSquash",
            )
            properties.share_quota = AAZIntType(
                serialized_name="shareQuota",
            )
            properties.share_usage_bytes = AAZIntType(
                serialized_name="shareUsageBytes",
                flags={"read_only": True},
            )
            properties.signed_identifiers = AAZListType(
                serialized_name="signedIdentifiers",
            )
            properties.snapshot_time = AAZStrType(
                serialized_name="snapshotTime",
                flags={"read_only": True},
            )
            properties.version = AAZStrType(
                flags={"read_only": True},
            )

            file_share_paid_bursting = cls._schema_on_200.value.Element.properties.file_share_paid_bursting
            file_share_paid_bursting.paid_bursting_enabled = AAZBoolType(
                serialized_name="paidBurstingEnabled",
            )
            file_share_paid_bursting.paid_bursting_max_bandwidth_mibps = AAZIntType(
                serialized_name="paidBurstingMaxBandwidthMibps",
            )
            file_share_paid_bursting.paid_bursting_max_iops = AAZIntType(
                serialized_name="paidBurstingMaxIops",
            )

            metadata = cls._schema_on_200.value.Element.properties.metadata
            metadata.Element = AAZStrType()

            signed_identifiers = cls._schema_on_200.value.Element.properties.signed_identifiers
            signed_identifiers.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.signed_identifiers.Element
            _element.access_policy = AAZObjectType(
                serialized_name="accessPolicy",
            )
            _element.id = AAZStrType()

            access_policy = cls._schema_on_200.value.Element.properties.signed_identifiers.Element.access_policy
            access_policy.expiry_time = AAZStrType(
                serialized_name="expiryTime",
            )
            access_policy.permission = AAZStrType()
            access_policy.start_time = AAZStrType(
                serialized_name="startTime",
            )

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""


__all__ = ["List"]
