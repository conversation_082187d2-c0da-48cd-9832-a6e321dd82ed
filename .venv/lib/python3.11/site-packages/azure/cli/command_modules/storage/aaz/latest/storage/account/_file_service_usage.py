# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "storage account file-service-usage",
)
class FileServiceUsage(AAZCommand):
    """Get the usage of file service in storage account including account limits, file share limits and constants used in recommendations and bursting formula.

    :example: Get file service usage for the storage account, only works for provisioned v2
        az storage account file-service-usage --account-name sa1 -g rg1
    """

    _aaz_info = {
        "version": "2024-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.storage/storageaccounts/{}/fileservices/{}/usages/{}", "2024-01-01"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.file_services_name = AAZStrArg(
            options=["--file-services-name"],
            help="The name of the file Service within the specified storage account. File Service Name must be \"default\"",
            required=True,
            id_part="child_name_1",
            default="default",
            enum={"default": "default"},
        )
        _args_schema.account_name = AAZStrArg(
            options=["--account-name"],
            help="The name of the storage account within the specified resource group. Storage account names must be between 3 and 24 characters in length and use numbers and lower-case letters only.",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-z0-9]+$",
                max_length=24,
                min_length=3,
            ),
        )
        _args_schema.file_service_usages_name = AAZStrArg(
            options=["-n", "--name", "--file-service-usages-name"],
            help="The name of the file service usage. File Service Usage Name must be \"default\"",
            required=True,
            id_part="child_name_2",
            default="default",
            enum={"default": "default"},
            fmt=AAZStrArgFormat(
                pattern="^[a-z][a-z0-9]*$",
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.FileServicesGetServiceUsage(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class FileServicesGetServiceUsage(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{accountName}/fileServices/{FileServicesName}/usages/{fileServiceUsagesName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "FileServicesName", self.ctx.args.file_services_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "accountName", self.ctx.args.account_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "fileServiceUsagesName", self.ctx.args.file_service_usages_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType(
                flags={"read_only": True},
            )

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"read_only": True},
            )
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.bursting_constants = AAZObjectType(
                serialized_name="burstingConstants",
                flags={"read_only": True},
            )
            properties.file_share_limits = AAZObjectType(
                serialized_name="fileShareLimits",
                flags={"read_only": True},
            )
            properties.file_share_recommendations = AAZObjectType(
                serialized_name="fileShareRecommendations",
                flags={"read_only": True},
            )
            properties.storage_account_limits = AAZObjectType(
                serialized_name="storageAccountLimits",
                flags={"read_only": True},
            )
            properties.storage_account_usage = AAZObjectType(
                serialized_name="storageAccountUsage",
                flags={"read_only": True},
            )

            bursting_constants = cls._schema_on_200.properties.bursting_constants
            bursting_constants.burst_floor_iops = AAZIntType(
                serialized_name="burstFloorIOPS",
                flags={"read_only": True},
            )
            bursting_constants.burst_io_scalar = AAZFloatType(
                serialized_name="burstIOScalar",
                flags={"read_only": True},
            )
            bursting_constants.burst_timeframe_seconds = AAZIntType(
                serialized_name="burstTimeframeSeconds",
                flags={"read_only": True},
            )

            file_share_limits = cls._schema_on_200.properties.file_share_limits
            file_share_limits.max_provisioned_bandwidth_mi_b_per_sec = AAZIntType(
                serialized_name="maxProvisionedBandwidthMiBPerSec",
                flags={"read_only": True},
            )
            file_share_limits.max_provisioned_iops = AAZIntType(
                serialized_name="maxProvisionedIOPS",
                flags={"read_only": True},
            )
            file_share_limits.max_provisioned_storage_gi_b = AAZIntType(
                serialized_name="maxProvisionedStorageGiB",
                flags={"read_only": True},
            )
            file_share_limits.min_provisioned_bandwidth_mi_b_per_sec = AAZIntType(
                serialized_name="minProvisionedBandwidthMiBPerSec",
                flags={"read_only": True},
            )
            file_share_limits.min_provisioned_iops = AAZIntType(
                serialized_name="minProvisionedIOPS",
                flags={"read_only": True},
            )
            file_share_limits.min_provisioned_storage_gi_b = AAZIntType(
                serialized_name="minProvisionedStorageGiB",
                flags={"read_only": True},
            )

            file_share_recommendations = cls._schema_on_200.properties.file_share_recommendations
            file_share_recommendations.bandwidth_scalar = AAZFloatType(
                serialized_name="bandwidthScalar",
                flags={"read_only": True},
            )
            file_share_recommendations.base_bandwidth_mi_b_per_sec = AAZIntType(
                serialized_name="baseBandwidthMiBPerSec",
                flags={"read_only": True},
            )
            file_share_recommendations.base_iops = AAZIntType(
                serialized_name="baseIOPS",
                flags={"read_only": True},
            )
            file_share_recommendations.io_scalar = AAZFloatType(
                serialized_name="ioScalar",
                flags={"read_only": True},
            )

            storage_account_limits = cls._schema_on_200.properties.storage_account_limits
            storage_account_limits.max_file_shares = AAZIntType(
                serialized_name="maxFileShares",
                flags={"read_only": True},
            )
            storage_account_limits.max_provisioned_bandwidth_mi_b_per_sec = AAZIntType(
                serialized_name="maxProvisionedBandwidthMiBPerSec",
                flags={"read_only": True},
            )
            storage_account_limits.max_provisioned_iops = AAZIntType(
                serialized_name="maxProvisionedIOPS",
                flags={"read_only": True},
            )
            storage_account_limits.max_provisioned_storage_gi_b = AAZIntType(
                serialized_name="maxProvisionedStorageGiB",
                flags={"read_only": True},
            )

            storage_account_usage = cls._schema_on_200.properties.storage_account_usage
            storage_account_usage.live_shares = AAZObjectType(
                serialized_name="liveShares",
                flags={"read_only": True},
            )
            _FileServiceUsageHelper._build_schema_account_usage_elements_read(storage_account_usage.live_shares)
            storage_account_usage.soft_deleted_shares = AAZObjectType(
                serialized_name="softDeletedShares",
                flags={"read_only": True},
            )
            _FileServiceUsageHelper._build_schema_account_usage_elements_read(storage_account_usage.soft_deleted_shares)

            return cls._schema_on_200


class _FileServiceUsageHelper:
    """Helper class for FileServiceUsage"""

    _schema_account_usage_elements_read = None

    @classmethod
    def _build_schema_account_usage_elements_read(cls, _schema):
        if cls._schema_account_usage_elements_read is not None:
            _schema.file_share_count = cls._schema_account_usage_elements_read.file_share_count
            _schema.provisioned_bandwidth_mi_b_per_sec = cls._schema_account_usage_elements_read.provisioned_bandwidth_mi_b_per_sec
            _schema.provisioned_iops = cls._schema_account_usage_elements_read.provisioned_iops
            _schema.provisioned_storage_gi_b = cls._schema_account_usage_elements_read.provisioned_storage_gi_b
            return

        cls._schema_account_usage_elements_read = _schema_account_usage_elements_read = AAZObjectType(
            flags={"read_only": True}
        )

        account_usage_elements_read = _schema_account_usage_elements_read
        account_usage_elements_read.file_share_count = AAZIntType(
            serialized_name="fileShareCount",
            flags={"read_only": True},
        )
        account_usage_elements_read.provisioned_bandwidth_mi_b_per_sec = AAZIntType(
            serialized_name="provisionedBandwidthMiBPerSec",
            flags={"read_only": True},
        )
        account_usage_elements_read.provisioned_iops = AAZIntType(
            serialized_name="provisionedIOPS",
            flags={"read_only": True},
        )
        account_usage_elements_read.provisioned_storage_gi_b = AAZIntType(
            serialized_name="provisionedStorageGiB",
            flags={"read_only": True},
        )

        _schema.file_share_count = cls._schema_account_usage_elements_read.file_share_count
        _schema.provisioned_bandwidth_mi_b_per_sec = cls._schema_account_usage_elements_read.provisioned_bandwidth_mi_b_per_sec
        _schema.provisioned_iops = cls._schema_account_usage_elements_read.provisioned_iops
        _schema.provisioned_storage_gi_b = cls._schema_account_usage_elements_read.provisioned_storage_gi_b


__all__ = ["FileServiceUsage"]
