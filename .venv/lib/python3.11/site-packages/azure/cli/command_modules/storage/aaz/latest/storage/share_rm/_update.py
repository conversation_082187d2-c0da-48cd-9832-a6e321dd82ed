# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "storage share-rm update",
)
class Update(AAZCommand):
    """Update a new share under the specified account as described by request body. The share resource includes metadata and properties for that share. It does not include a list of the files contained by the share. 

    :example: Update the properties for an Azure file share 'myfileshare' under the storage account 'mystorageaccount' (account name) in resource group 'MyResourceGroup'.
        az storage share-rm update -g MyResourceGroup --storage-account mystorageaccount --name myfileshare --quota 3 --metadata key1=value1 key2=value2

    :example: Update the properties for an Azure file share 'myfileshare' under the storage account 'mystorageaccount' (account id).
        az storage share-rm update --storage-account mystorageaccount --name myfileshare --quota 3 --metadata key1=value1 key2=value2

    :example: Update the properties for an Azure file shares by resource id.
        az storage share-rm update --ids file-share-id --quota 3 --metadata key1=value1 key2=value2
    """

    _aaz_info = {
        "version": "2024-01-01",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.storage/storageaccounts/{}/fileservices/default/shares/{}", "2024-01-01"],
        ]
    }

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.storage_account = AAZStrArg(
            options=["--account-name", "--storage-account"],
            help="The name of the storage account within the specified resource group. Storage account names must be between 3 and 24 characters in length and use numbers and lower-case letters only.",
            required=True,
            id_part="name",
            fmt=AAZStrArgFormat(
                pattern="^[a-z0-9]+$",
                max_length=24,
                min_length=3,
            ),
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.share_name = AAZStrArg(
            options=["-n", "--name", "--share-name"],
            help="The name of the file share within the specified storage account. File share names must be between 3 and 63 characters in length and use numbers, lower-case letters and dash (-) only. Every dash (-) character must be immediately preceded and followed by a letter or number.",
            required=True,
            id_part="child_name_2",
            fmt=AAZStrArgFormat(
                max_length=63,
                min_length=3,
            ),
        )

        # define Arg Group "Properties"

        _args_schema = cls._args_schema
        _args_schema.access_tier = AAZStrArg(
            options=["--access-tier"],
            arg_group="Properties",
            help="Access tier for specific share. GpV2 account can choose between TransactionOptimized (default), Hot, and Cool. FileStorage account can choose Premium.",
            nullable=True,
            enum={"Cool": "Cool", "Hot": "Hot", "Premium": "Premium", "TransactionOptimized": "TransactionOptimized"},
        )
        _args_schema.metadata = AAZDictArg(
            options=["--metadata"],
            arg_group="Properties",
            help="A name-value pair to associate with the share as metadata.",
            nullable=True,
        )
        _args_schema.root_squash = AAZStrArg(
            options=["--root-squash"],
            arg_group="Properties",
            help="Reduction of the access rights for the remote superuser. The property is for NFS share only. The default is NoRootSquash.",
            nullable=True,
            enum={"AllSquash": "AllSquash", "NoRootSquash": "NoRootSquash", "RootSquash": "RootSquash"},
        )
        _args_schema.quota = AAZIntArg(
            options=["-q", "--quota"],
            arg_group="Properties",
            help="The provisioned size of the share, in gibibytes. Must be greater than 0, and less than or equal to 5TB (5120). For Large File Shares, the maximum size is 102400. For file shares created under Files Provisioned v2 account type, please refer to the GetFileServiceUsage API response for the minimum and maximum allowed provisioned storage size.",
            nullable=True,
        )

        metadata = cls._args_schema.metadata
        metadata.Element = AAZStrArg(
            nullable=True,
        )

        # define Arg Group "Provisioned V1 Paid Bursting"

        _args_schema = cls._args_schema
        _args_schema.paid_bursting_enabled = AAZBoolArg(
            options=["--paid-bursting-enabled"],
            arg_group="Provisioned V1 Paid Bursting",
            help="Indicates whether paid bursting is enabled for the share. This property is only for file shares created under Files Provisioned v1 SSD account type.",
            nullable=True,
        )
        _args_schema.paid_bursting_max_bandwidth_mibps = AAZIntArg(
            options=["--bursting-max-mibps", "--paid-bursting-max-bandwidth-mibps"],
            arg_group="Provisioned V1 Paid Bursting",
            help="The maximum paid bursting bandwidth for the share, in mebibytes per second. This property is only for file shares created under Files Provisioned v1 SSD account type. The maximum allowed value is 10340 which is the maximum allowed bandwidth for a share.",
            nullable=True,
        )
        _args_schema.paid_bursting_max_iops = AAZIntArg(
            options=["--paid-bursting-max-iops"],
            arg_group="Provisioned V1 Paid Bursting",
            help="The maximum paid bursting IOPS for the share. This property is only for file shares created under Files Provisioned v1 SSD account type. The maximum allowed value is 102400 which is the maximum allowed IOPS for a share.",
            nullable=True,
        )

        # define Arg Group "Provisioned V2"

        _args_schema = cls._args_schema
        _args_schema.provisioned_bandwidth_mibps = AAZIntArg(
            options=["--provisioned-bandwidth", "--provisioned-bandwidth-mibps"],
            arg_group="Provisioned V2",
            help="The provisioned bandwidth of the share, in mebibytes per second. This property is only for file shares created under Files Provisioned v2 account type. Please refer to the GetFileServiceUsage API response for the minimum and maximum allowed value for provisioned bandwidth.",
            nullable=True,
        )
        _args_schema.provisioned_iops = AAZIntArg(
            options=["--provisioned-iops"],
            arg_group="Provisioned V2",
            help="The provisioned IOPS of the share. This property is only for file shares created under Files Provisioned v2 account type. Please refer to the GetFileServiceUsage API response for the minimum and maximum allowed value for provisioned IOPS.",
            nullable=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.FileSharesGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        self.FileSharesCreate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class FileSharesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{accountName}/fileServices/default/shares/{shareName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.storage_account,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "shareName", self.ctx.args.share_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_file_share_read(cls._schema_on_200)

            return cls._schema_on_200

    class FileSharesCreate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200, 201]:
                return self.on_200_201(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{accountName}/fileServices/default/shares/{shareName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "accountName", self.ctx.args.storage_account,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "shareName", self.ctx.args.share_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2024-01-01",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200_201(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200_201
            )

        _schema_on_200_201 = None

        @classmethod
        def _build_schema_on_200_201(cls):
            if cls._schema_on_200_201 is not None:
                return cls._schema_on_200_201

            cls._schema_on_200_201 = AAZObjectType()
            _UpdateHelper._build_schema_file_share_read(cls._schema_on_200_201)

            return cls._schema_on_200_201

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("accessTier", AAZStrType, ".access_tier")
                properties.set_prop("fileSharePaidBursting", AAZObjectType)
                properties.set_prop("metadata", AAZDictType, ".metadata")
                properties.set_prop("provisionedBandwidthMibps", AAZIntType, ".provisioned_bandwidth_mibps")
                properties.set_prop("provisionedIops", AAZIntType, ".provisioned_iops")
                properties.set_prop("rootSquash", AAZStrType, ".root_squash")
                properties.set_prop("shareQuota", AAZIntType, ".quota")

            file_share_paid_bursting = _builder.get(".properties.fileSharePaidBursting")
            if file_share_paid_bursting is not None:
                file_share_paid_bursting.set_prop("paidBurstingEnabled", AAZBoolType, ".paid_bursting_enabled")
                file_share_paid_bursting.set_prop("paidBurstingMaxBandwidthMibps", AAZIntType, ".paid_bursting_max_bandwidth_mibps")
                file_share_paid_bursting.set_prop("paidBurstingMaxIops", AAZIntType, ".paid_bursting_max_iops")

            metadata = _builder.get(".properties.metadata")
            if metadata is not None:
                metadata.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_file_share_read = None

    @classmethod
    def _build_schema_file_share_read(cls, _schema):
        if cls._schema_file_share_read is not None:
            _schema.etag = cls._schema_file_share_read.etag
            _schema.id = cls._schema_file_share_read.id
            _schema.name = cls._schema_file_share_read.name
            _schema.properties = cls._schema_file_share_read.properties
            _schema.type = cls._schema_file_share_read.type
            return

        cls._schema_file_share_read = _schema_file_share_read = AAZObjectType()

        file_share_read = _schema_file_share_read
        file_share_read.etag = AAZStrType(
            flags={"read_only": True},
        )
        file_share_read.id = AAZStrType(
            flags={"read_only": True},
        )
        file_share_read.name = AAZStrType(
            flags={"read_only": True},
        )
        file_share_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        file_share_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_file_share_read.properties
        properties.access_tier = AAZStrType(
            serialized_name="accessTier",
        )
        properties.access_tier_change_time = AAZStrType(
            serialized_name="accessTierChangeTime",
            flags={"read_only": True},
        )
        properties.access_tier_status = AAZStrType(
            serialized_name="accessTierStatus",
            flags={"read_only": True},
        )
        properties.deleted = AAZBoolType(
            flags={"read_only": True},
        )
        properties.deleted_time = AAZStrType(
            serialized_name="deletedTime",
            flags={"read_only": True},
        )
        properties.enabled_protocols = AAZStrType(
            serialized_name="enabledProtocols",
        )
        properties.file_share_paid_bursting = AAZObjectType(
            serialized_name="fileSharePaidBursting",
        )
        properties.included_burst_iops = AAZIntType(
            serialized_name="includedBurstIops",
            flags={"read_only": True},
        )
        properties.last_modified_time = AAZStrType(
            serialized_name="lastModifiedTime",
            flags={"read_only": True},
        )
        properties.lease_duration = AAZStrType(
            serialized_name="leaseDuration",
            flags={"read_only": True},
        )
        properties.lease_state = AAZStrType(
            serialized_name="leaseState",
            flags={"read_only": True},
        )
        properties.lease_status = AAZStrType(
            serialized_name="leaseStatus",
            flags={"read_only": True},
        )
        properties.max_burst_credits_for_iops = AAZIntType(
            serialized_name="maxBurstCreditsForIops",
            flags={"read_only": True},
        )
        properties.metadata = AAZDictType()
        properties.next_allowed_provisioned_bandwidth_downgrade_time = AAZStrType(
            serialized_name="nextAllowedProvisionedBandwidthDowngradeTime",
            flags={"read_only": True},
        )
        properties.next_allowed_provisioned_iops_downgrade_time = AAZStrType(
            serialized_name="nextAllowedProvisionedIopsDowngradeTime",
            flags={"read_only": True},
        )
        properties.next_allowed_quota_downgrade_time = AAZStrType(
            serialized_name="nextAllowedQuotaDowngradeTime",
            flags={"read_only": True},
        )
        properties.provisioned_bandwidth_mibps = AAZIntType(
            serialized_name="provisionedBandwidthMibps",
        )
        properties.provisioned_iops = AAZIntType(
            serialized_name="provisionedIops",
        )
        properties.remaining_retention_days = AAZIntType(
            serialized_name="remainingRetentionDays",
            flags={"read_only": True},
        )
        properties.root_squash = AAZStrType(
            serialized_name="rootSquash",
        )
        properties.share_quota = AAZIntType(
            serialized_name="shareQuota",
        )
        properties.share_usage_bytes = AAZIntType(
            serialized_name="shareUsageBytes",
            flags={"read_only": True},
        )
        properties.signed_identifiers = AAZListType(
            serialized_name="signedIdentifiers",
        )
        properties.snapshot_time = AAZStrType(
            serialized_name="snapshotTime",
            flags={"read_only": True},
        )
        properties.version = AAZStrType(
            flags={"read_only": True},
        )

        file_share_paid_bursting = _schema_file_share_read.properties.file_share_paid_bursting
        file_share_paid_bursting.paid_bursting_enabled = AAZBoolType(
            serialized_name="paidBurstingEnabled",
        )
        file_share_paid_bursting.paid_bursting_max_bandwidth_mibps = AAZIntType(
            serialized_name="paidBurstingMaxBandwidthMibps",
        )
        file_share_paid_bursting.paid_bursting_max_iops = AAZIntType(
            serialized_name="paidBurstingMaxIops",
        )

        metadata = _schema_file_share_read.properties.metadata
        metadata.Element = AAZStrType()

        signed_identifiers = _schema_file_share_read.properties.signed_identifiers
        signed_identifiers.Element = AAZObjectType()

        _element = _schema_file_share_read.properties.signed_identifiers.Element
        _element.access_policy = AAZObjectType(
            serialized_name="accessPolicy",
        )
        _element.id = AAZStrType()

        access_policy = _schema_file_share_read.properties.signed_identifiers.Element.access_policy
        access_policy.expiry_time = AAZStrType(
            serialized_name="expiryTime",
        )
        access_policy.permission = AAZStrType()
        access_policy.start_time = AAZStrType(
            serialized_name="startTime",
        )

        _schema.etag = cls._schema_file_share_read.etag
        _schema.id = cls._schema_file_share_read.id
        _schema.name = cls._schema_file_share_read.name
        _schema.properties = cls._schema_file_share_read.properties
        _schema.type = cls._schema_file_share_read.type


__all__ = ["Update"]
