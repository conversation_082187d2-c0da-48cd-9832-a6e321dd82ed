# pylint: disable=too-many-lines, too-many-instance-attributes, redefined-builtin
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from collections.abc import MutableMapping
import datetime
from typing import Any, Dict, List, Literal, Optional, Union

import msrest.serialization as _serialization

from ._sdk_enums import (
    Applicability,
    AccessMode,
    CheckNameAvailabilityReason,
    ActiveRevisionsMode,
    Type,
    Scheme,
    CookieExpirationConvention,
    BindingType,
    AppProtocol,
    LogLevel,
    ExtendedLocationTypes,
    ForwardProxyConvention,
    UnauthenticatedClientActionV2,
    IngressTransportMethod,
    IngressClientCertificateMode,
    Affinity,
    Action,
    TriggerType,
    ManagedCertificateDomainControlValidation,
    ManagedServiceIdentityType,
    CreatedByType,
    StorageType,
    IdentitySettingsLifeCycle,
    LifecycleType,
    SessionNetworkStatus,
    ContainerType,
    PoolManagementType
)

JSON = MutableMapping[str, Any]


class AllowedAudiencesValidation(_serialization.Model):
    """The configuration settings of the Allowed Audiences validation flow.

    :ivar allowed_audiences: The configuration settings of the allowed list of audiences from which
     to validate the JWT token.
    :vartype allowed_audiences: list[str]
    """

    _attribute_map = {
        "allowed_audiences": {"key": "allowedAudiences", "type": "[str]"},
    }

    def __init__(self, *, allowed_audiences: Optional[List[str]] = None, **kwargs: Any) -> None:
        """
        :keyword allowed_audiences: The configuration settings of the allowed list of audiences from
         which to validate the JWT token.
        :paramtype allowed_audiences: list[str]
        """
        super().__init__(**kwargs)
        self.allowed_audiences = allowed_audiences


class AllowedPrincipals(_serialization.Model):
    """The configuration settings of the Azure Active Directory allowed principals.

    :ivar groups: The list of the allowed groups.
    :vartype groups: list[str]
    :ivar identities: The list of the allowed identities.
    :vartype identities: list[str]
    """

    _attribute_map = {
        "groups": {"key": "groups", "type": "[str]"},
        "identities": {"key": "identities", "type": "[str]"},
    }

    def __init__(
        self, *, groups: Optional[List[str]] = None, identities: Optional[List[str]] = None, **kwargs: Any
    ) -> None:
        """
        :keyword groups: The list of the allowed groups.
        :paramtype groups: list[str]
        :keyword identities: The list of the allowed identities.
        :paramtype identities: list[str]
        """
        super().__init__(**kwargs)
        self.groups = groups
        self.identities = identities


class Apple(_serialization.Model):
    """The configuration settings of the Apple provider.

    :ivar enabled: :code:`<code>false</code>` if the Apple provider should not be enabled despite
     the set registration; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the Apple registration.
    :vartype registration: ~azure.mgmt.appcontainers.models.AppleRegistration
    :ivar login: The configuration settings of the login flow.
    :vartype login: ~azure.mgmt.appcontainers.models.LoginScopes
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "AppleRegistration"},
        "login": {"key": "login", "type": "LoginScopes"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["AppleRegistration"] = None,
        login: Optional["LoginScopes"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the Apple provider should not be enabled
         despite the set registration; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the Apple registration.
        :paramtype registration: ~azure.mgmt.appcontainers.models.AppleRegistration
        :keyword login: The configuration settings of the login flow.
        :paramtype login: ~azure.mgmt.appcontainers.models.LoginScopes
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration
        self.login = login


class AppleRegistration(_serialization.Model):
    """The configuration settings of the registration for the Apple provider.

    :ivar client_id: The Client ID of the app used for login.
    :vartype client_id: str
    :ivar client_secret_setting_name: The app setting name that contains the client secret.
    :vartype client_secret_setting_name: str
    """

    _attribute_map = {
        "client_id": {"key": "clientId", "type": "str"},
        "client_secret_setting_name": {"key": "clientSecretSettingName", "type": "str"},
    }

    def __init__(
        self, *, client_id: Optional[str] = None, client_secret_setting_name: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword client_id: The Client ID of the app used for login.
        :paramtype client_id: str
        :keyword client_secret_setting_name: The app setting name that contains the client secret.
        :paramtype client_secret_setting_name: str
        """
        super().__init__(**kwargs)
        self.client_id = client_id
        self.client_secret_setting_name = client_secret_setting_name


class AppLogsConfiguration(_serialization.Model):
    """Configuration of application logs.

    :ivar destination: Logs destination, can be 'log-analytics', 'azure-monitor' or 'none'.
    :vartype destination: str
    :ivar log_analytics_configuration: Log Analytics configuration, must only be provided when
     destination is configured as 'log-analytics'.
    :vartype log_analytics_configuration:
     ~azure.mgmt.appcontainers.models.LogAnalyticsConfiguration
    """

    _attribute_map = {
        "destination": {"key": "destination", "type": "str"},
        "log_analytics_configuration": {"key": "logAnalyticsConfiguration", "type": "LogAnalyticsConfiguration"},
    }

    def __init__(
        self,
        *,
        destination: Optional[str] = None,
        log_analytics_configuration: Optional["LogAnalyticsConfiguration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword destination: Logs destination, can be 'log-analytics', 'azure-monitor' or 'none'.
        :paramtype destination: str
        :keyword log_analytics_configuration: Log Analytics configuration, must only be provided when
         destination is configured as 'log-analytics'.
        :paramtype log_analytics_configuration:
         ~azure.mgmt.appcontainers.models.LogAnalyticsConfiguration
        """
        super().__init__(**kwargs)
        self.destination = destination
        self.log_analytics_configuration = log_analytics_configuration


class AppRegistration(_serialization.Model):
    """The configuration settings of the app registration for providers that have app ids and app
    secrets.

    :ivar app_id: The App ID of the app used for login.
    :vartype app_id: str
    :ivar app_secret_setting_name: The app setting name that contains the app secret.
    :vartype app_secret_setting_name: str
    """

    _attribute_map = {
        "app_id": {"key": "appId", "type": "str"},
        "app_secret_setting_name": {"key": "appSecretSettingName", "type": "str"},
    }

    def __init__(
        self, *, app_id: Optional[str] = None, app_secret_setting_name: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword app_id: The App ID of the app used for login.
        :paramtype app_id: str
        :keyword app_secret_setting_name: The app setting name that contains the app secret.
        :paramtype app_secret_setting_name: str
        """
        super().__init__(**kwargs)
        self.app_id = app_id
        self.app_secret_setting_name = app_secret_setting_name


class Resource(_serialization.Model):
    """Common fields that are returned in the response for all Azure Resource Manager resources.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.system_data = None


class ProxyResource(Resource):
    """The resource model definition for a Azure Resource Manager proxy resource. It will not have
    tags and a location.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    """


class AuthConfig(ProxyResource):
    """Configuration settings for the Azure ContainerApp Service Authentication / Authorization
    feature.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar platform: The configuration settings of the platform of ContainerApp Service
     Authentication/Authorization.
    :vartype platform: ~azure.mgmt.appcontainers.models.AuthPlatform
    :ivar global_validation: The configuration settings that determines the validation flow of
     users using  Service Authentication/Authorization.
    :vartype global_validation: ~azure.mgmt.appcontainers.models.GlobalValidation
    :ivar identity_providers: The configuration settings of each of the identity providers used to
     configure ContainerApp Service Authentication/Authorization.
    :vartype identity_providers: ~azure.mgmt.appcontainers.models.IdentityProviders
    :ivar login: The configuration settings of the login flow of users using ContainerApp Service
     Authentication/Authorization.
    :vartype login: ~azure.mgmt.appcontainers.models.Login
    :ivar http_settings: The configuration settings of the HTTP requests for authentication and
     authorization requests made against ContainerApp Service Authentication/Authorization.
    :vartype http_settings: ~azure.mgmt.appcontainers.models.HttpSettings
    :ivar encryption_settings: The configuration settings of the secrets references of encryption
     key and signing key for ContainerApp Service Authentication/Authorization.
    :vartype encryption_settings: ~azure.mgmt.appcontainers.models.EncryptionSettings
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "platform": {"key": "properties.platform", "type": "AuthPlatform"},
        "global_validation": {"key": "properties.globalValidation", "type": "GlobalValidation"},
        "identity_providers": {"key": "properties.identityProviders", "type": "IdentityProviders"},
        "login": {"key": "properties.login", "type": "Login"},
        "http_settings": {"key": "properties.httpSettings", "type": "HttpSettings"},
        "encryption_settings": {"key": "properties.encryptionSettings", "type": "EncryptionSettings"},
    }

    def __init__(
        self,
        *,
        platform: Optional["AuthPlatform"] = None,
        global_validation: Optional["GlobalValidation"] = None,
        identity_providers: Optional["IdentityProviders"] = None,
        login: Optional["Login"] = None,
        http_settings: Optional["HttpSettings"] = None,
        encryption_settings: Optional["EncryptionSettings"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword platform: The configuration settings of the platform of ContainerApp Service
         Authentication/Authorization.
        :paramtype platform: ~azure.mgmt.appcontainers.models.AuthPlatform
        :keyword global_validation: The configuration settings that determines the validation flow of
         users using  Service Authentication/Authorization.
        :paramtype global_validation: ~azure.mgmt.appcontainers.models.GlobalValidation
        :keyword identity_providers: The configuration settings of each of the identity providers used
         to configure ContainerApp Service Authentication/Authorization.
        :paramtype identity_providers: ~azure.mgmt.appcontainers.models.IdentityProviders
        :keyword login: The configuration settings of the login flow of users using ContainerApp
         Service Authentication/Authorization.
        :paramtype login: ~azure.mgmt.appcontainers.models.Login
        :keyword http_settings: The configuration settings of the HTTP requests for authentication and
         authorization requests made against ContainerApp Service Authentication/Authorization.
        :paramtype http_settings: ~azure.mgmt.appcontainers.models.HttpSettings
        :keyword encryption_settings: The configuration settings of the secrets references of
         encryption key and signing key for ContainerApp Service Authentication/Authorization.
        :paramtype encryption_settings: ~azure.mgmt.appcontainers.models.EncryptionSettings
        """
        super().__init__(**kwargs)
        self.platform = platform
        self.global_validation = global_validation
        self.identity_providers = identity_providers
        self.login = login
        self.http_settings = http_settings
        self.encryption_settings = encryption_settings


class AuthConfigCollection(_serialization.Model):
    """AuthConfig collection ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.AuthConfig]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[AuthConfig]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["AuthConfig"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.AuthConfig]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class AuthPlatform(_serialization.Model):
    """The configuration settings of the platform of ContainerApp Service
    Authentication/Authorization.

    :ivar enabled: :code:`<code>true</code>` if the Authentication / Authorization feature is
     enabled for the current app; otherwise, :code:`<code>false</code>`.
    :vartype enabled: bool
    :ivar runtime_version: The RuntimeVersion of the Authentication / Authorization feature in use
     for the current app.
     The setting in this value can control the behavior of certain features in the Authentication /
     Authorization module.
    :vartype runtime_version: str
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "runtime_version": {"key": "runtimeVersion", "type": "str"},
    }

    def __init__(self, *, enabled: Optional[bool] = None, runtime_version: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword enabled: :code:`<code>true</code>` if the Authentication / Authorization feature is
         enabled for the current app; otherwise, :code:`<code>false</code>`.
        :paramtype enabled: bool
        :keyword runtime_version: The RuntimeVersion of the Authentication / Authorization feature in
         use for the current app.
         The setting in this value can control the behavior of certain features in the Authentication /
         Authorization module.
        :paramtype runtime_version: str
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.runtime_version = runtime_version


class AvailableOperations(_serialization.Model):
    """Available operations of the service.

    :ivar value: Collection of available operation details.
    :vartype value: list[~azure.mgmt.appcontainers.models.OperationDetail]
    :ivar next_link: URL client should use to fetch the next page (per server side paging).
     It's null for now, added for future use.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[OperationDetail]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["OperationDetail"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Collection of available operation details.
        :paramtype value: list[~azure.mgmt.appcontainers.models.OperationDetail]
        :keyword next_link: URL client should use to fetch the next page (per server side paging).
         It's null for now, added for future use.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AvailableWorkloadProfile(ProxyResource):
    """A workload profile with specific hardware configure to run container apps.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar location: Region of the workload profile.
    :vartype location: str
    :ivar properties: Revision resource specific properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.AvailableWorkloadProfileProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "AvailableWorkloadProfileProperties"},
    }

    def __init__(
        self,
        *,
        location: Optional[str] = None,
        properties: Optional["AvailableWorkloadProfileProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword location: Region of the workload profile.
        :paramtype location: str
        :keyword properties: Revision resource specific properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.AvailableWorkloadProfileProperties
        """
        super().__init__(**kwargs)
        self.location = location
        self.properties = properties


class AvailableWorkloadProfileProperties(_serialization.Model):
    """Revision resource specific properties.

    :ivar category: Used to categorize workload profiles.
    :vartype category: str
    :ivar applicability: indicates whether the profile is default for the location. Known values
     are: "LocationDefault" and "Custom".
    :vartype applicability: str or ~azure.mgmt.appcontainers.models.Applicability
    :ivar cores: Number of cores in CPU.
    :vartype cores: int
    :ivar memory_gi_b: Memory in GiB.
    :vartype memory_gi_b: int
    :ivar gpus: Number of GPUs.
    :vartype gpus: int
    :ivar display_name: The everyday name of the workload profile.
    :vartype display_name: str
    """

    _attribute_map = {
        "category": {"key": "category", "type": "str"},
        "applicability": {"key": "applicability", "type": "str"},
        "cores": {"key": "cores", "type": "int"},
        "memory_gi_b": {"key": "memoryGiB", "type": "int"},
        "gpus": {"key": "gpus", "type": "int"},
        "display_name": {"key": "displayName", "type": "str"},
    }

    def __init__(
        self,
        *,
        category: Optional[str] = None,
        applicability: Optional[Union[str, "Applicability"]] = None,
        cores: Optional[int] = None,
        memory_gi_b: Optional[int] = None,
        gpus: Optional[int] = None,
        display_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword category: Used to categorize workload profiles.
        :paramtype category: str
        :keyword applicability: indicates whether the profile is default for the location. Known values
         are: "LocationDefault" and "Custom".
        :paramtype applicability: str or ~azure.mgmt.appcontainers.models.Applicability
        :keyword cores: Number of cores in CPU.
        :paramtype cores: int
        :keyword memory_gi_b: Memory in GiB.
        :paramtype memory_gi_b: int
        :keyword gpus: Number of GPUs.
        :paramtype gpus: int
        :keyword display_name: The everyday name of the workload profile.
        :paramtype display_name: str
        """
        super().__init__(**kwargs)
        self.category = category
        self.applicability = applicability
        self.cores = cores
        self.memory_gi_b = memory_gi_b
        self.gpus = gpus
        self.display_name = display_name


class AvailableWorkloadProfilesCollection(_serialization.Model):
    """Collection of available workload profiles in the location.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of workload profiles. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.AvailableWorkloadProfile]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[AvailableWorkloadProfile]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["AvailableWorkloadProfile"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of workload profiles. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.AvailableWorkloadProfile]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class AzureActiveDirectory(_serialization.Model):
    """The configuration settings of the Azure Active directory provider.

    :ivar enabled: :code:`<code>false</code>` if the Azure Active Directory provider should not be
     enabled despite the set registration; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the Azure Active Directory app registration.
    :vartype registration: ~azure.mgmt.appcontainers.models.AzureActiveDirectoryRegistration
    :ivar login: The configuration settings of the Azure Active Directory login flow.
    :vartype login: ~azure.mgmt.appcontainers.models.AzureActiveDirectoryLogin
    :ivar validation: The configuration settings of the Azure Active Directory token validation
     flow.
    :vartype validation: ~azure.mgmt.appcontainers.models.AzureActiveDirectoryValidation
    :ivar is_auto_provisioned: Gets a value indicating whether the Azure AD configuration was
     auto-provisioned using 1st party tooling.
     This is an internal flag primarily intended to support the Azure Management Portal. Users
     should not
     read or write to this property.
    :vartype is_auto_provisioned: bool
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "AzureActiveDirectoryRegistration"},
        "login": {"key": "login", "type": "AzureActiveDirectoryLogin"},
        "validation": {"key": "validation", "type": "AzureActiveDirectoryValidation"},
        "is_auto_provisioned": {"key": "isAutoProvisioned", "type": "bool"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["AzureActiveDirectoryRegistration"] = None,
        login: Optional["AzureActiveDirectoryLogin"] = None,
        validation: Optional["AzureActiveDirectoryValidation"] = None,
        is_auto_provisioned: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the Azure Active Directory provider should not
         be enabled despite the set registration; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the Azure Active Directory app
         registration.
        :paramtype registration: ~azure.mgmt.appcontainers.models.AzureActiveDirectoryRegistration
        :keyword login: The configuration settings of the Azure Active Directory login flow.
        :paramtype login: ~azure.mgmt.appcontainers.models.AzureActiveDirectoryLogin
        :keyword validation: The configuration settings of the Azure Active Directory token validation
         flow.
        :paramtype validation: ~azure.mgmt.appcontainers.models.AzureActiveDirectoryValidation
        :keyword is_auto_provisioned: Gets a value indicating whether the Azure AD configuration was
         auto-provisioned using 1st party tooling.
         This is an internal flag primarily intended to support the Azure Management Portal. Users
         should not
         read or write to this property.
        :paramtype is_auto_provisioned: bool
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration
        self.login = login
        self.validation = validation
        self.is_auto_provisioned = is_auto_provisioned


class AzureActiveDirectoryLogin(_serialization.Model):
    """The configuration settings of the Azure Active Directory login flow.

    :ivar login_parameters: Login parameters to send to the OpenID Connect authorization endpoint
     when
     a user logs in. Each parameter must be in the form "key=value".
    :vartype login_parameters: list[str]
    :ivar disable_www_authenticate: :code:`<code>true</code>` if the www-authenticate provider
     should be omitted from the request; otherwise, :code:`<code>false</code>`.
    :vartype disable_www_authenticate: bool
    """

    _attribute_map = {
        "login_parameters": {"key": "loginParameters", "type": "[str]"},
        "disable_www_authenticate": {"key": "disableWWWAuthenticate", "type": "bool"},
    }

    def __init__(
        self,
        *,
        login_parameters: Optional[List[str]] = None,
        disable_www_authenticate: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword login_parameters: Login parameters to send to the OpenID Connect authorization
         endpoint when
         a user logs in. Each parameter must be in the form "key=value".
        :paramtype login_parameters: list[str]
        :keyword disable_www_authenticate: :code:`<code>true</code>` if the www-authenticate provider
         should be omitted from the request; otherwise, :code:`<code>false</code>`.
        :paramtype disable_www_authenticate: bool
        """
        super().__init__(**kwargs)
        self.login_parameters = login_parameters
        self.disable_www_authenticate = disable_www_authenticate


class AzureActiveDirectoryRegistration(_serialization.Model):
    """The configuration settings of the Azure Active Directory app registration.

    :ivar open_id_issuer: The OpenID Connect Issuer URI that represents the entity which issues
     access tokens for this application.
     When using Azure Active Directory, this value is the URI of the directory tenant, e.g.
     https://login.microsoftonline.com/v2.0/{tenant-guid}/.
     This URI is a case-sensitive identifier for the token issuer.
     More information on OpenID Connect Discovery:
     http://openid.net/specs/openid-connect-discovery-1_0.html.
    :vartype open_id_issuer: str
    :ivar client_id: The Client ID of this relying party application, known as the client_id.
     This setting is required for enabling OpenID Connection authentication with Azure Active
     Directory or
     other 3rd party OpenID Connect providers.
     More information on OpenID Connect: http://openid.net/specs/openid-connect-core-1_0.html.
    :vartype client_id: str
    :ivar client_secret_setting_name: The app setting name that contains the client secret of the
     relying party application.
    :vartype client_secret_setting_name: str
    :ivar client_secret_certificate_thumbprint: An alternative to the client secret, that is the
     thumbprint of a certificate used for signing purposes. This property acts as
     a replacement for the Client Secret. It is also optional.
    :vartype client_secret_certificate_thumbprint: str
    :ivar client_secret_certificate_subject_alternative_name: An alternative to the client secret
     thumbprint, that is the subject alternative name of a certificate used for signing purposes.
     This property acts as
     a replacement for the Client Secret Certificate Thumbprint. It is also optional.
    :vartype client_secret_certificate_subject_alternative_name: str
    :ivar client_secret_certificate_issuer: An alternative to the client secret thumbprint, that is
     the issuer of a certificate used for signing purposes. This property acts as
     a replacement for the Client Secret Certificate Thumbprint. It is also optional.
    :vartype client_secret_certificate_issuer: str
    """

    _attribute_map = {
        "open_id_issuer": {"key": "openIdIssuer", "type": "str"},
        "client_id": {"key": "clientId", "type": "str"},
        "client_secret_setting_name": {"key": "clientSecretSettingName", "type": "str"},
        "client_secret_certificate_thumbprint": {"key": "clientSecretCertificateThumbprint", "type": "str"},
        "client_secret_certificate_subject_alternative_name": {
            "key": "clientSecretCertificateSubjectAlternativeName",
            "type": "str",
        },
        "client_secret_certificate_issuer": {"key": "clientSecretCertificateIssuer", "type": "str"},
    }

    def __init__(
        self,
        *,
        open_id_issuer: Optional[str] = None,
        client_id: Optional[str] = None,
        client_secret_setting_name: Optional[str] = None,
        client_secret_certificate_thumbprint: Optional[str] = None,
        client_secret_certificate_subject_alternative_name: Optional[str] = None,
        client_secret_certificate_issuer: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword open_id_issuer: The OpenID Connect Issuer URI that represents the entity which issues
         access tokens for this application.
         When using Azure Active Directory, this value is the URI of the directory tenant, e.g.
         https://login.microsoftonline.com/v2.0/{tenant-guid}/.
         This URI is a case-sensitive identifier for the token issuer.
         More information on OpenID Connect Discovery:
         http://openid.net/specs/openid-connect-discovery-1_0.html.
        :paramtype open_id_issuer: str
        :keyword client_id: The Client ID of this relying party application, known as the client_id.
         This setting is required for enabling OpenID Connection authentication with Azure Active
         Directory or
         other 3rd party OpenID Connect providers.
         More information on OpenID Connect: http://openid.net/specs/openid-connect-core-1_0.html.
        :paramtype client_id: str
        :keyword client_secret_setting_name: The app setting name that contains the client secret of
         the relying party application.
        :paramtype client_secret_setting_name: str
        :keyword client_secret_certificate_thumbprint: An alternative to the client secret, that is the
         thumbprint of a certificate used for signing purposes. This property acts as
         a replacement for the Client Secret. It is also optional.
        :paramtype client_secret_certificate_thumbprint: str
        :keyword client_secret_certificate_subject_alternative_name: An alternative to the client
         secret thumbprint, that is the subject alternative name of a certificate used for signing
         purposes. This property acts as
         a replacement for the Client Secret Certificate Thumbprint. It is also optional.
        :paramtype client_secret_certificate_subject_alternative_name: str
        :keyword client_secret_certificate_issuer: An alternative to the client secret thumbprint, that
         is the issuer of a certificate used for signing purposes. This property acts as
         a replacement for the Client Secret Certificate Thumbprint. It is also optional.
        :paramtype client_secret_certificate_issuer: str
        """
        super().__init__(**kwargs)
        self.open_id_issuer = open_id_issuer
        self.client_id = client_id
        self.client_secret_setting_name = client_secret_setting_name
        self.client_secret_certificate_thumbprint = client_secret_certificate_thumbprint
        self.client_secret_certificate_subject_alternative_name = client_secret_certificate_subject_alternative_name
        self.client_secret_certificate_issuer = client_secret_certificate_issuer


class AzureActiveDirectoryValidation(_serialization.Model):
    """The configuration settings of the Azure Active Directory token validation flow.

    :ivar jwt_claim_checks: The configuration settings of the checks that should be made while
     validating the JWT Claims.
    :vartype jwt_claim_checks: ~azure.mgmt.appcontainers.models.JwtClaimChecks
    :ivar allowed_audiences: The list of audiences that can make successful
     authentication/authorization requests.
    :vartype allowed_audiences: list[str]
    :ivar default_authorization_policy: The configuration settings of the default authorization
     policy.
    :vartype default_authorization_policy:
     ~azure.mgmt.appcontainers.models.DefaultAuthorizationPolicy
    """

    _attribute_map = {
        "jwt_claim_checks": {"key": "jwtClaimChecks", "type": "JwtClaimChecks"},
        "allowed_audiences": {"key": "allowedAudiences", "type": "[str]"},
        "default_authorization_policy": {"key": "defaultAuthorizationPolicy", "type": "DefaultAuthorizationPolicy"},
    }

    def __init__(
        self,
        *,
        jwt_claim_checks: Optional["JwtClaimChecks"] = None,
        allowed_audiences: Optional[List[str]] = None,
        default_authorization_policy: Optional["DefaultAuthorizationPolicy"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword jwt_claim_checks: The configuration settings of the checks that should be made while
         validating the JWT Claims.
        :paramtype jwt_claim_checks: ~azure.mgmt.appcontainers.models.JwtClaimChecks
        :keyword allowed_audiences: The list of audiences that can make successful
         authentication/authorization requests.
        :paramtype allowed_audiences: list[str]
        :keyword default_authorization_policy: The configuration settings of the default authorization
         policy.
        :paramtype default_authorization_policy:
         ~azure.mgmt.appcontainers.models.DefaultAuthorizationPolicy
        """
        super().__init__(**kwargs)
        self.jwt_claim_checks = jwt_claim_checks
        self.allowed_audiences = allowed_audiences
        self.default_authorization_policy = default_authorization_policy


class AzureCredentials(_serialization.Model):
    """Container App credentials.

    :ivar client_id: Client Id.
    :vartype client_id: str
    :ivar client_secret: Client Secret.
    :vartype client_secret: str
    :ivar tenant_id: Tenant Id.
    :vartype tenant_id: str
    :ivar kind: Kind of auth github does for deploying the template.
    :vartype kind: str
    :ivar subscription_id: Subscription Id.
    :vartype subscription_id: str
    """

    _attribute_map = {
        "client_id": {"key": "clientId", "type": "str"},
        "client_secret": {"key": "clientSecret", "type": "str"},
        "tenant_id": {"key": "tenantId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "subscription_id": {"key": "subscriptionId", "type": "str"},
    }

    def __init__(
        self,
        *,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        tenant_id: Optional[str] = None,
        kind: Optional[str] = None,
        subscription_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword client_id: Client Id.
        :paramtype client_id: str
        :keyword client_secret: Client Secret.
        :paramtype client_secret: str
        :keyword tenant_id: Tenant Id.
        :paramtype tenant_id: str
        :keyword kind: Kind of auth github does for deploying the template.
        :paramtype kind: str
        :keyword subscription_id: Subscription Id.
        :paramtype subscription_id: str
        """
        super().__init__(**kwargs)
        self.client_id = client_id
        self.client_secret = client_secret
        self.tenant_id = tenant_id
        self.kind = kind
        self.subscription_id = subscription_id


class AzureFileProperties(_serialization.Model):
    """Azure File Properties.

    :ivar account_name: Storage account name for azure file.
    :vartype account_name: str
    :ivar account_key: Storage account key for azure file.
    :vartype account_key: str
    :ivar access_mode: Access mode for storage. Known values are: "ReadOnly" and "ReadWrite".
    :vartype access_mode: str or ~azure.mgmt.appcontainers.models.AccessMode
    :ivar share_name: Azure file share name.
    :vartype share_name: str
    """

    _attribute_map = {
        "account_name": {"key": "accountName", "type": "str"},
        "account_key": {"key": "accountKey", "type": "str"},
        "access_mode": {"key": "accessMode", "type": "str"},
        "share_name": {"key": "shareName", "type": "str"},
    }

    def __init__(
        self,
        *,
        account_name: Optional[str] = None,
        account_key: Optional[str] = None,
        access_mode: Optional[Union[str, "AccessMode"]] = None,
        share_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword account_name: Storage account name for azure file.
        :paramtype account_name: str
        :keyword account_key: Storage account key for azure file.
        :paramtype account_key: str
        :keyword access_mode: Access mode for storage. Known values are: "ReadOnly" and "ReadWrite".
        :paramtype access_mode: str or ~azure.mgmt.appcontainers.models.AccessMode
        :keyword share_name: Azure file share name.
        :paramtype share_name: str
        """
        super().__init__(**kwargs)
        self.account_name = account_name
        self.account_key = account_key
        self.access_mode = access_mode
        self.share_name = share_name


class AzureStaticWebApps(_serialization.Model):
    """The configuration settings of the Azure Static Web Apps provider.

    :ivar enabled: :code:`<code>false</code>` if the Azure Static Web Apps provider should not be
     enabled despite the set registration; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the Azure Static Web Apps registration.
    :vartype registration: ~azure.mgmt.appcontainers.models.AzureStaticWebAppsRegistration
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "AzureStaticWebAppsRegistration"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["AzureStaticWebAppsRegistration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the Azure Static Web Apps provider should not
         be enabled despite the set registration; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the Azure Static Web Apps registration.
        :paramtype registration: ~azure.mgmt.appcontainers.models.AzureStaticWebAppsRegistration
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration


class AzureStaticWebAppsRegistration(_serialization.Model):
    """The configuration settings of the registration for the Azure Static Web Apps provider.

    :ivar client_id: The Client ID of the app used for login.
    :vartype client_id: str
    """

    _attribute_map = {
        "client_id": {"key": "clientId", "type": "str"},
    }

    def __init__(self, *, client_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword client_id: The Client ID of the app used for login.
        :paramtype client_id: str
        """
        super().__init__(**kwargs)
        self.client_id = client_id


class BaseContainer(_serialization.Model):
    """Container App base container definition.

    :ivar image: Container image tag.
    :vartype image: str
    :ivar name: Custom container name.
    :vartype name: str
    :ivar command: Container start command.
    :vartype command: list[str]
    :ivar args: Container start command arguments.
    :vartype args: list[str]
    :ivar env: Container environment variables.
    :vartype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
    :ivar resources: Container resource requirements.
    :vartype resources: ~azure.mgmt.appcontainers.models.ContainerResources
    :ivar volume_mounts: Container volume mounts.
    :vartype volume_mounts: list[~azure.mgmt.appcontainers.models.VolumeMount]
    """

    _attribute_map = {
        "image": {"key": "image", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "command": {"key": "command", "type": "[str]"},
        "args": {"key": "args", "type": "[str]"},
        "env": {"key": "env", "type": "[EnvironmentVar]"},
        "resources": {"key": "resources", "type": "ContainerResources"},
        "volume_mounts": {"key": "volumeMounts", "type": "[VolumeMount]"},
    }

    def __init__(
        self,
        *,
        image: Optional[str] = None,
        name: Optional[str] = None,
        command: Optional[List[str]] = None,
        args: Optional[List[str]] = None,
        env: Optional[List["EnvironmentVar"]] = None,
        resources: Optional["ContainerResources"] = None,
        volume_mounts: Optional[List["VolumeMount"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword image: Container image tag.
        :paramtype image: str
        :keyword name: Custom container name.
        :paramtype name: str
        :keyword command: Container start command.
        :paramtype command: list[str]
        :keyword args: Container start command arguments.
        :paramtype args: list[str]
        :keyword env: Container environment variables.
        :paramtype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
        :keyword resources: Container resource requirements.
        :paramtype resources: ~azure.mgmt.appcontainers.models.ContainerResources
        :keyword volume_mounts: Container volume mounts.
        :paramtype volume_mounts: list[~azure.mgmt.appcontainers.models.VolumeMount]
        """
        super().__init__(**kwargs)
        self.image = image
        self.name = name
        self.command = command
        self.args = args
        self.env = env
        self.resources = resources
        self.volume_mounts = volume_mounts


class BillingMeter(ProxyResource):
    """Billing meter.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar location: Region for the billing meter.
    :vartype location: str
    :ivar properties: Revision resource specific properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.BillingMeterProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "BillingMeterProperties"},
    }

    def __init__(
        self,
        *,
        location: Optional[str] = None,
        properties: Optional["BillingMeterProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword location: Region for the billing meter.
        :paramtype location: str
        :keyword properties: Revision resource specific properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.BillingMeterProperties
        """
        super().__init__(**kwargs)
        self.location = location
        self.properties = properties


class BillingMeterCollection(_serialization.Model):
    """Collection of billing meters.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of billing meters. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.BillingMeter]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[BillingMeter]"},
    }

    def __init__(self, *, value: List["BillingMeter"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of billing meters. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.BillingMeter]
        """
        super().__init__(**kwargs)
        self.value = value


class BillingMeterProperties(_serialization.Model):
    """Revision resource specific properties.

    :ivar category: Used to categorize billing meters.
    :vartype category: str
    :ivar meter_type: Billing meter type.
    :vartype meter_type: str
    :ivar display_name: The everyday name of the billing meter.
    :vartype display_name: str
    """

    _attribute_map = {
        "category": {"key": "category", "type": "str"},
        "meter_type": {"key": "meterType", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
    }

    def __init__(
        self,
        *,
        category: Optional[str] = None,
        meter_type: Optional[str] = None,
        display_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword category: Used to categorize billing meters.
        :paramtype category: str
        :keyword meter_type: Billing meter type.
        :paramtype meter_type: str
        :keyword display_name: The everyday name of the billing meter.
        :paramtype display_name: str
        """
        super().__init__(**kwargs)
        self.category = category
        self.meter_type = meter_type
        self.display_name = display_name


class BlobStorageTokenStore(_serialization.Model):
    """The configuration settings of the storage of the tokens if blob storage is used.

    All required parameters must be populated in order to send to server.

    :ivar sas_url_setting_name: The name of the app secrets containing the SAS URL of the blob
     storage containing the tokens. Required.
    :vartype sas_url_setting_name: str
    """

    _validation = {
        "sas_url_setting_name": {"required": True},
    }

    _attribute_map = {
        "sas_url_setting_name": {"key": "sasUrlSettingName", "type": "str"},
    }

    def __init__(self, *, sas_url_setting_name: str, **kwargs: Any) -> None:
        """
        :keyword sas_url_setting_name: The name of the app secrets containing the SAS URL of the blob
         storage containing the tokens. Required.
        :paramtype sas_url_setting_name: str
        """
        super().__init__(**kwargs)
        self.sas_url_setting_name = sas_url_setting_name


class TrackedResource(Resource):
    """The resource model definition for an Azure Resource Manager tracked top level resource which
    has 'tags' and a 'location'.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
    }

    def __init__(self, *, location: str, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        """
        super().__init__(**kwargs)
        self.tags = tags
        self.location = location


class Certificate(TrackedResource):
    """Certificate used for Custom Domain bindings of Container Apps in a Managed Environment.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar properties: Certificate resource specific properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.CertificateProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "CertificateProperties"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        properties: Optional["CertificateProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword properties: Certificate resource specific properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.CertificateProperties
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.properties = properties


class CertificateCollection(_serialization.Model):
    """Collection of Certificates.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.Certificate]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Certificate]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["Certificate"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.Certificate]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class CertificateKeyVaultProperties(_serialization.Model):
    """Properties for a certificate stored in a Key Vault.

    :ivar identity: Resource ID of a managed identity to authenticate with Azure Key Vault, or
     System to use a system-assigned identity.
    :vartype identity: str
    :ivar key_vault_url: URL pointing to the Azure Key Vault secret that holds the certificate.
    :vartype key_vault_url: str
    """

    _attribute_map = {
        "identity": {"key": "identity", "type": "str"},
        "key_vault_url": {"key": "keyVaultUrl", "type": "str"},
    }

    def __init__(self, *, identity: Optional[str] = None, key_vault_url: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword identity: Resource ID of a managed identity to authenticate with Azure Key Vault, or
         System to use a system-assigned identity.
        :paramtype identity: str
        :keyword key_vault_url: URL pointing to the Azure Key Vault secret that holds the certificate.
        :paramtype key_vault_url: str
        """
        super().__init__(**kwargs)
        self.identity = identity
        self.key_vault_url = key_vault_url


class CertificatePatch(_serialization.Model):
    """A certificate to update.

    :ivar tags: Application-specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, *, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword tags: Application-specific metadata in the form of key-value pairs.
        :paramtype tags: dict[str, str]
        """
        super().__init__(**kwargs)
        self.tags = tags


class CertificateProperties(_serialization.Model):
    """Certificate resource specific properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar provisioning_state: Provisioning state of the certificate. Known values are: "Succeeded",
     "Failed", "Canceled", "DeleteFailed", and "Pending".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.CertificateProvisioningState
    :ivar certificate_key_vault_properties: Properties for a certificate stored in a Key Vault.
    :vartype certificate_key_vault_properties:
     ~azure.mgmt.appcontainers.models.CertificateKeyVaultProperties
    :ivar password: Certificate password.
    :vartype password: str
    :ivar subject_name: Subject name of the certificate.
    :vartype subject_name: str
    :ivar subject_alternative_names: Subject alternative names the certificate applies to.
    :vartype subject_alternative_names: list[str]
    :ivar value: PFX or PEM blob.
    :vartype value: bytes
    :ivar issuer: Certificate issuer.
    :vartype issuer: str
    :ivar issue_date: Certificate issue Date.
    :vartype issue_date: ~datetime.datetime
    :ivar expiration_date: Certificate expiration date.
    :vartype expiration_date: ~datetime.datetime
    :ivar thumbprint: Certificate thumbprint.
    :vartype thumbprint: str
    :ivar valid: Is the certificate valid?.
    :vartype valid: bool
    :ivar public_key_hash: Public key hash.
    :vartype public_key_hash: str
    """

    _validation = {
        "provisioning_state": {"readonly": True},
        "subject_name": {"readonly": True},
        "subject_alternative_names": {"readonly": True},
        "issuer": {"readonly": True},
        "issue_date": {"readonly": True},
        "expiration_date": {"readonly": True},
        "thumbprint": {"readonly": True},
        "valid": {"readonly": True},
        "public_key_hash": {"readonly": True},
    }

    _attribute_map = {
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "certificate_key_vault_properties": {
            "key": "certificateKeyVaultProperties",
            "type": "CertificateKeyVaultProperties",
        },
        "password": {"key": "password", "type": "str"},
        "subject_name": {"key": "subjectName", "type": "str"},
        "subject_alternative_names": {"key": "subjectAlternativeNames", "type": "[str]"},
        "value": {"key": "value", "type": "bytearray"},
        "issuer": {"key": "issuer", "type": "str"},
        "issue_date": {"key": "issueDate", "type": "iso-8601"},
        "expiration_date": {"key": "expirationDate", "type": "iso-8601"},
        "thumbprint": {"key": "thumbprint", "type": "str"},
        "valid": {"key": "valid", "type": "bool"},
        "public_key_hash": {"key": "publicKeyHash", "type": "str"},
    }

    def __init__(
        self,
        *,
        certificate_key_vault_properties: Optional["CertificateKeyVaultProperties"] = None,
        password: Optional[str] = None,
        value: Optional[bytes] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword certificate_key_vault_properties: Properties for a certificate stored in a Key Vault.
        :paramtype certificate_key_vault_properties:
         ~azure.mgmt.appcontainers.models.CertificateKeyVaultProperties
        :keyword password: Certificate password.
        :paramtype password: str
        :keyword value: PFX or PEM blob.
        :paramtype value: bytes
        """
        super().__init__(**kwargs)
        self.provisioning_state = None
        self.certificate_key_vault_properties = certificate_key_vault_properties
        self.password = password
        self.subject_name = None
        self.subject_alternative_names = None
        self.value = value
        self.issuer = None
        self.issue_date = None
        self.expiration_date = None
        self.thumbprint = None
        self.valid = None
        self.public_key_hash = None


class CheckNameAvailabilityRequest(_serialization.Model):
    """The check availability request body.

    :ivar name: The name of the resource for which availability needs to be checked.
    :vartype name: str
    :ivar type: The resource type.
    :vartype type: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(self, *, name: Optional[str] = None, type: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword name: The name of the resource for which availability needs to be checked.
        :paramtype name: str
        :keyword type: The resource type.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.type = type


class CheckNameAvailabilityResponse(_serialization.Model):
    """The check availability result.

    :ivar name_available: Indicates if the resource name is available.
    :vartype name_available: bool
    :ivar reason: The reason why the given name is not available. Known values are: "Invalid" and
     "AlreadyExists".
    :vartype reason: str or ~azure.mgmt.appcontainers.models.CheckNameAvailabilityReason
    :ivar message: Detailed reason why the given name is available.
    :vartype message: str
    """

    _attribute_map = {
        "name_available": {"key": "nameAvailable", "type": "bool"},
        "reason": {"key": "reason", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(
        self,
        *,
        name_available: Optional[bool] = None,
        reason: Optional[Union[str, "CheckNameAvailabilityReason"]] = None,
        message: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name_available: Indicates if the resource name is available.
        :paramtype name_available: bool
        :keyword reason: The reason why the given name is not available. Known values are: "Invalid"
         and "AlreadyExists".
        :paramtype reason: str or ~azure.mgmt.appcontainers.models.CheckNameAvailabilityReason
        :keyword message: Detailed reason why the given name is available.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.name_available = name_available
        self.reason = reason
        self.message = message


class ClientRegistration(_serialization.Model):
    """The configuration settings of the app registration for providers that have client ids and
    client secrets.

    :ivar client_id: The Client ID of the app used for login.
    :vartype client_id: str
    :ivar client_secret_setting_name: The app setting name that contains the client secret.
    :vartype client_secret_setting_name: str
    """

    _attribute_map = {
        "client_id": {"key": "clientId", "type": "str"},
        "client_secret_setting_name": {"key": "clientSecretSettingName", "type": "str"},
    }

    def __init__(
        self, *, client_id: Optional[str] = None, client_secret_setting_name: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword client_id: The Client ID of the app used for login.
        :paramtype client_id: str
        :keyword client_secret_setting_name: The app setting name that contains the client secret.
        :paramtype client_secret_setting_name: str
        """
        super().__init__(**kwargs)
        self.client_id = client_id
        self.client_secret_setting_name = client_secret_setting_name


class Configuration(_serialization.Model):
    """Non versioned Container App configuration properties that define the mutable settings of a
    Container app.

    :ivar secrets: Collection of secrets used by a Container app.
    :vartype secrets: list[~azure.mgmt.appcontainers.models.Secret]
    :ivar active_revisions_mode: ActiveRevisionsMode controls how active revisions are handled for
     the Container app:


     .. raw:: html

        <list><item>Multiple: multiple revisions can be active.</item><item>Single: Only one
     revision can be active at a time. Revision weights can not be used in this mode. If no value if
     provided, this is the default.</item></list>. Known values are: "Multiple" and "Single".
    :vartype active_revisions_mode: str or ~azure.mgmt.appcontainers.models.ActiveRevisionsMode
    :ivar ingress: Ingress configurations.
    :vartype ingress: ~azure.mgmt.appcontainers.models.Ingress
    :ivar registries: Collection of private container registry credentials for containers used by
     the Container app.
    :vartype registries: list[~azure.mgmt.appcontainers.models.RegistryCredentials]
    :ivar dapr: Dapr configuration for the Container App.
    :vartype dapr: ~azure.mgmt.appcontainers.models.Dapr
    :ivar runtime: App runtime configuration for the Container App.
    :vartype runtime: ~azure.mgmt.appcontainers.models.Runtime
    :ivar max_inactive_revisions: Optional. Max inactive revisions a Container App can have.
    :vartype max_inactive_revisions: int
    :ivar service: Container App to be a dev Container App Service.
    :vartype service: ~azure.mgmt.appcontainers.models.Service
    :ivar identity_settings: Optional settings for Managed Identities that are assigned to the
     Container App. If a Managed Identity is not specified here, default settings will be used.
    :vartype identity_settings: list[~azure.mgmt.appcontainers.models.IdentitySettings]
    """

    _attribute_map = {
        "secrets": {"key": "secrets", "type": "[Secret]"},
        "active_revisions_mode": {"key": "activeRevisionsMode", "type": "str"},
        "ingress": {"key": "ingress", "type": "Ingress"},
        "registries": {"key": "registries", "type": "[RegistryCredentials]"},
        "dapr": {"key": "dapr", "type": "Dapr"},
        "runtime": {"key": "runtime", "type": "Runtime"},
        "max_inactive_revisions": {"key": "maxInactiveRevisions", "type": "int"},
        "service": {"key": "service", "type": "Service"},
        "identity_settings": {"key": "identitySettings", "type": "[IdentitySettings]"},
    }

    def __init__(
        self,
        *,
        secrets: Optional[List["Secret"]] = None,
        active_revisions_mode: Union[str, "ActiveRevisionsMode"] = "Single",
        ingress: Optional["Ingress"] = None,
        registries: Optional[List["RegistryCredentials"]] = None,
        dapr: Optional["Dapr"] = None,
        runtime: Optional["Runtime"] = None,
        max_inactive_revisions: Optional[int] = None,
        service: Optional["Service"] = None,
        identity_settings: Optional[List["IdentitySettings"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword secrets: Collection of secrets used by a Container app.
        :paramtype secrets: list[~azure.mgmt.appcontainers.models.Secret]
        :keyword active_revisions_mode: ActiveRevisionsMode controls how active revisions are handled
         for the Container app:


         .. raw:: html

            <list><item>Multiple: multiple revisions can be active.</item><item>Single: Only one
         revision can be active at a time. Revision weights can not be used in this mode. If no value if
         provided, this is the default.</item></list>. Known values are: "Multiple" and "Single".
        :paramtype active_revisions_mode: str or ~azure.mgmt.appcontainers.models.ActiveRevisionsMode
        :keyword ingress: Ingress configurations.
        :paramtype ingress: ~azure.mgmt.appcontainers.models.Ingress
        :keyword registries: Collection of private container registry credentials for containers used
         by the Container app.
        :paramtype registries: list[~azure.mgmt.appcontainers.models.RegistryCredentials]
        :keyword dapr: Dapr configuration for the Container App.
        :paramtype dapr: ~azure.mgmt.appcontainers.models.Dapr
        :keyword runtime: App runtime configuration for the Container App.
        :paramtype runtime: ~azure.mgmt.appcontainers.models.Runtime
        :keyword max_inactive_revisions: Optional. Max inactive revisions a Container App can have.
        :paramtype max_inactive_revisions: int
        :keyword service: Container App to be a dev Container App Service.
        :paramtype service: ~azure.mgmt.appcontainers.models.Service
        :keyword identity_settings: Optional settings for Managed Identities that are assigned to the
         Container App. If a Managed Identity is not specified here, default settings will be used.
        :paramtype identity_settings: list[~azure.mgmt.appcontainers.models.IdentitySettings]
        """
        super().__init__(**kwargs)
        self.secrets = secrets
        self.active_revisions_mode = active_revisions_mode
        self.ingress = ingress
        self.registries = registries
        self.dapr = dapr
        self.runtime = runtime
        self.max_inactive_revisions = max_inactive_revisions
        self.service = service
        self.identity_settings = identity_settings


class ConnectedEnvironment(TrackedResource):
    """An environment for Kubernetes cluster specialized for web workloads by Azure App Service.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar extended_location: The complex type of the extended location.
    :vartype extended_location: ~azure.mgmt.appcontainers.models.ExtendedLocation
    :ivar provisioning_state: Provisioning state of the Kubernetes Environment. Known values are:
     "Succeeded", "Failed", "Canceled", "Waiting", "InitializationInProgress",
     "InfrastructureSetupInProgress", "InfrastructureSetupComplete", and "ScheduledForDelete".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.ConnectedEnvironmentProvisioningState
    :ivar deployment_errors: Any errors that occurred during deployment or deployment validation.
    :vartype deployment_errors: str
    :ivar default_domain: Default Domain Name for the cluster.
    :vartype default_domain: str
    :ivar static_ip: Static IP of the connectedEnvironment.
    :vartype static_ip: str
    :ivar dapr_ai_connection_string: Application Insights connection string used by Dapr to export
     Service to Service communication telemetry.
    :vartype dapr_ai_connection_string: str
    :ivar custom_domain_configuration: Custom domain configuration for the environment.
    :vartype custom_domain_configuration:
     ~azure.mgmt.appcontainers.models.CustomDomainConfiguration
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "provisioning_state": {"readonly": True},
        "deployment_errors": {"readonly": True},
        "default_domain": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "extended_location": {"key": "extendedLocation", "type": "ExtendedLocation"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "deployment_errors": {"key": "properties.deploymentErrors", "type": "str"},
        "default_domain": {"key": "properties.defaultDomain", "type": "str"},
        "static_ip": {"key": "properties.staticIp", "type": "str"},
        "dapr_ai_connection_string": {"key": "properties.daprAIConnectionString", "type": "str"},
        "custom_domain_configuration": {
            "key": "properties.customDomainConfiguration",
            "type": "CustomDomainConfiguration",
        },
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        extended_location: Optional["ExtendedLocation"] = None,
        static_ip: Optional[str] = None,
        dapr_ai_connection_string: Optional[str] = None,
        custom_domain_configuration: Optional["CustomDomainConfiguration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword extended_location: The complex type of the extended location.
        :paramtype extended_location: ~azure.mgmt.appcontainers.models.ExtendedLocation
        :keyword static_ip: Static IP of the connectedEnvironment.
        :paramtype static_ip: str
        :keyword dapr_ai_connection_string: Application Insights connection string used by Dapr to
         export Service to Service communication telemetry.
        :paramtype dapr_ai_connection_string: str
        :keyword custom_domain_configuration: Custom domain configuration for the environment.
        :paramtype custom_domain_configuration:
         ~azure.mgmt.appcontainers.models.CustomDomainConfiguration
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.extended_location = extended_location
        self.provisioning_state = None
        self.deployment_errors = None
        self.default_domain = None
        self.static_ip = static_ip
        self.dapr_ai_connection_string = dapr_ai_connection_string
        self.custom_domain_configuration = custom_domain_configuration


class ConnectedEnvironmentCollection(_serialization.Model):
    """Collection of connectedEnvironments.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar value: Collection of resources.
    :vartype value: list[~azure.mgmt.appcontainers.models.ConnectedEnvironment]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ConnectedEnvironment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: Optional[List["ConnectedEnvironment"]] = None, **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources.
        :paramtype value: list[~azure.mgmt.appcontainers.models.ConnectedEnvironment]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class ConnectedEnvironmentStorage(ProxyResource):
    """Storage resource for connectedEnvironment.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar properties: Storage properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.ConnectedEnvironmentStorageProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "ConnectedEnvironmentStorageProperties"},
    }

    def __init__(
        self, *, properties: Optional["ConnectedEnvironmentStorageProperties"] = None, **kwargs: Any
    ) -> None:
        """
        :keyword properties: Storage properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.ConnectedEnvironmentStorageProperties
        """
        super().__init__(**kwargs)
        self.properties = properties


class ConnectedEnvironmentStorageProperties(_serialization.Model):
    """Storage properties.

    :ivar azure_file: Azure file properties.
    :vartype azure_file: ~azure.mgmt.appcontainers.models.AzureFileProperties
    """

    _attribute_map = {
        "azure_file": {"key": "azureFile", "type": "AzureFileProperties"},
    }

    def __init__(self, *, azure_file: Optional["AzureFileProperties"] = None, **kwargs: Any) -> None:
        """
        :keyword azure_file: Azure file properties.
        :paramtype azure_file: ~azure.mgmt.appcontainers.models.AzureFileProperties
        """
        super().__init__(**kwargs)
        self.azure_file = azure_file


class ConnectedEnvironmentStoragesCollection(_serialization.Model):
    """Collection of Storage for Environments.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of storage resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.ConnectedEnvironmentStorage]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ConnectedEnvironmentStorage]"},
    }

    def __init__(self, *, value: List["ConnectedEnvironmentStorage"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of storage resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.ConnectedEnvironmentStorage]
        """
        super().__init__(**kwargs)
        self.value = value


class Container(BaseContainer):
    """Container App container definition.

    :ivar image: Container image tag.
    :vartype image: str
    :ivar name: Custom container name.
    :vartype name: str
    :ivar command: Container start command.
    :vartype command: list[str]
    :ivar args: Container start command arguments.
    :vartype args: list[str]
    :ivar env: Container environment variables.
    :vartype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
    :ivar resources: Container resource requirements.
    :vartype resources: ~azure.mgmt.appcontainers.models.ContainerResources
    :ivar volume_mounts: Container volume mounts.
    :vartype volume_mounts: list[~azure.mgmt.appcontainers.models.VolumeMount]
    :ivar probes: List of probes for the container.
    :vartype probes: list[~azure.mgmt.appcontainers.models.ContainerAppProbe]
    """

    _attribute_map = {
        "image": {"key": "image", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "command": {"key": "command", "type": "[str]"},
        "args": {"key": "args", "type": "[str]"},
        "env": {"key": "env", "type": "[EnvironmentVar]"},
        "resources": {"key": "resources", "type": "ContainerResources"},
        "volume_mounts": {"key": "volumeMounts", "type": "[VolumeMount]"},
        "probes": {"key": "probes", "type": "[ContainerAppProbe]"},
    }

    def __init__(
        self,
        *,
        image: Optional[str] = None,
        name: Optional[str] = None,
        command: Optional[List[str]] = None,
        args: Optional[List[str]] = None,
        env: Optional[List["EnvironmentVar"]] = None,
        resources: Optional["ContainerResources"] = None,
        volume_mounts: Optional[List["VolumeMount"]] = None,
        probes: Optional[List["ContainerAppProbe"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword image: Container image tag.
        :paramtype image: str
        :keyword name: Custom container name.
        :paramtype name: str
        :keyword command: Container start command.
        :paramtype command: list[str]
        :keyword args: Container start command arguments.
        :paramtype args: list[str]
        :keyword env: Container environment variables.
        :paramtype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
        :keyword resources: Container resource requirements.
        :paramtype resources: ~azure.mgmt.appcontainers.models.ContainerResources
        :keyword volume_mounts: Container volume mounts.
        :paramtype volume_mounts: list[~azure.mgmt.appcontainers.models.VolumeMount]
        :keyword probes: List of probes for the container.
        :paramtype probes: list[~azure.mgmt.appcontainers.models.ContainerAppProbe]
        """
        super().__init__(
            image=image,
            name=name,
            command=command,
            args=args,
            env=env,
            resources=resources,
            volume_mounts=volume_mounts,
            **kwargs
        )
        self.probes = probes


class ContainerApp(TrackedResource):
    """Container App.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar extended_location: The complex type of the extended location.
    :vartype extended_location: ~azure.mgmt.appcontainers.models.ExtendedLocation
    :ivar identity: managed identities for the Container App to interact with other Azure services
     without maintaining any secrets or credentials in code.
    :vartype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
    :ivar managed_by: The fully qualified resource ID of the resource that manages this resource.
     Indicates if this resource is managed by another Azure resource. If this is present, complete
     mode deployment will not delete the resource if it is removed from the template since it is
     managed by another resource.
    :vartype managed_by: str
    :ivar provisioning_state: Provisioning state of the Container App. Known values are:
     "InProgress", "Succeeded", "Failed", "Canceled", and "Deleting".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.ContainerAppProvisioningState
    :ivar running_status: Running status of the Container App. Known values are: "Progressing",
     "Running", "Stopped", "Suspended", and "Ready".
    :vartype running_status: str or ~azure.mgmt.appcontainers.models.ContainerAppRunningStatus
    :ivar managed_environment_id: Deprecated. Resource ID of the Container App's environment.
    :vartype managed_environment_id: str
    :ivar environment_id: Resource ID of environment.
    :vartype environment_id: str
    :ivar workload_profile_name: Workload profile name to pin for container app execution.
    :vartype workload_profile_name: str
    :ivar latest_revision_name: Name of the latest revision of the Container App.
    :vartype latest_revision_name: str
    :ivar latest_ready_revision_name: Name of the latest ready revision of the Container App.
    :vartype latest_ready_revision_name: str
    :ivar latest_revision_fqdn: Fully Qualified Domain Name of the latest revision of the Container
     App.
    :vartype latest_revision_fqdn: str
    :ivar custom_domain_verification_id: Id used to verify domain name ownership.
    :vartype custom_domain_verification_id: str
    :ivar configuration: Non versioned Container App configuration properties.
    :vartype configuration: ~azure.mgmt.appcontainers.models.Configuration
    :ivar template: Container App versioned application definition.
    :vartype template: ~azure.mgmt.appcontainers.models.Template
    :ivar outbound_ip_addresses: Outbound IP Addresses for container app.
    :vartype outbound_ip_addresses: list[str]
    :ivar event_stream_endpoint: The endpoint of the eventstream of the container app.
    :vartype event_stream_endpoint: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "provisioning_state": {"readonly": True},
        "running_status": {"readonly": True},
        "latest_revision_name": {"readonly": True},
        "latest_ready_revision_name": {"readonly": True},
        "latest_revision_fqdn": {"readonly": True},
        "custom_domain_verification_id": {"readonly": True},
        "outbound_ip_addresses": {"readonly": True},
        "event_stream_endpoint": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "extended_location": {"key": "extendedLocation", "type": "ExtendedLocation"},
        "identity": {"key": "identity", "type": "ManagedServiceIdentity"},
        "managed_by": {"key": "managedBy", "type": "str"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "running_status": {"key": "properties.runningStatus", "type": "str"},
        "managed_environment_id": {"key": "properties.managedEnvironmentId", "type": "str"},
        "environment_id": {"key": "properties.environmentId", "type": "str"},
        "workload_profile_name": {"key": "properties.workloadProfileName", "type": "str"},
        "latest_revision_name": {"key": "properties.latestRevisionName", "type": "str"},
        "latest_ready_revision_name": {"key": "properties.latestReadyRevisionName", "type": "str"},
        "latest_revision_fqdn": {"key": "properties.latestRevisionFqdn", "type": "str"},
        "custom_domain_verification_id": {"key": "properties.customDomainVerificationId", "type": "str"},
        "configuration": {"key": "properties.configuration", "type": "Configuration"},
        "template": {"key": "properties.template", "type": "Template"},
        "outbound_ip_addresses": {"key": "properties.outboundIpAddresses", "type": "[str]"},
        "event_stream_endpoint": {"key": "properties.eventStreamEndpoint", "type": "str"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        extended_location: Optional["ExtendedLocation"] = None,
        identity: Optional["ManagedServiceIdentity"] = None,
        managed_by: Optional[str] = None,
        managed_environment_id: Optional[str] = None,
        environment_id: Optional[str] = None,
        workload_profile_name: Optional[str] = None,
        configuration: Optional["Configuration"] = None,
        template: Optional["Template"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword extended_location: The complex type of the extended location.
        :paramtype extended_location: ~azure.mgmt.appcontainers.models.ExtendedLocation
        :keyword identity: managed identities for the Container App to interact with other Azure
         services without maintaining any secrets or credentials in code.
        :paramtype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
        :keyword managed_by: The fully qualified resource ID of the resource that manages this
         resource. Indicates if this resource is managed by another Azure resource. If this is present,
         complete mode deployment will not delete the resource if it is removed from the template since
         it is managed by another resource.
        :paramtype managed_by: str
        :keyword managed_environment_id: Deprecated. Resource ID of the Container App's environment.
        :paramtype managed_environment_id: str
        :keyword environment_id: Resource ID of environment.
        :paramtype environment_id: str
        :keyword workload_profile_name: Workload profile name to pin for container app execution.
        :paramtype workload_profile_name: str
        :keyword configuration: Non versioned Container App configuration properties.
        :paramtype configuration: ~azure.mgmt.appcontainers.models.Configuration
        :keyword template: Container App versioned application definition.
        :paramtype template: ~azure.mgmt.appcontainers.models.Template
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.extended_location = extended_location
        self.identity = identity
        self.managed_by = managed_by
        self.provisioning_state = None
        self.running_status = None
        self.managed_environment_id = managed_environment_id
        self.environment_id = environment_id
        self.workload_profile_name = workload_profile_name
        self.latest_revision_name = None
        self.latest_ready_revision_name = None
        self.latest_revision_fqdn = None
        self.custom_domain_verification_id = None
        self.configuration = configuration
        self.template = template
        self.outbound_ip_addresses = None
        self.event_stream_endpoint = None


class ContainerAppAuthToken(TrackedResource):
    """Container App Auth Token.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar token: Auth token value.
    :vartype token: str
    :ivar expires: Token expiration date.
    :vartype expires: ~datetime.datetime
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "token": {"readonly": True},
        "expires": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "token": {"key": "properties.token", "type": "str"},
        "expires": {"key": "properties.expires", "type": "iso-8601"},
    }

    def __init__(self, *, location: str, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.token = None
        self.expires = None


class ContainerAppCollection(_serialization.Model):
    """Container App collection ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.ContainerApp]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ContainerApp]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["ContainerApp"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.ContainerApp]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class ContainerAppJobExecutions(_serialization.Model):
    """Container App executions collection ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.JobExecution]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[JobExecution]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["JobExecution"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.JobExecution]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class ContainerAppProbe(_serialization.Model):
    """Probe describes a health check to be performed against a container to determine whether it is
    alive or ready to receive traffic.

    :ivar failure_threshold: Minimum consecutive failures for the probe to be considered failed
     after having succeeded. Defaults to 3. Minimum value is 1. Maximum value is 10.
    :vartype failure_threshold: int
    :ivar http_get: HTTPGet specifies the http request to perform.
    :vartype http_get: ~azure.mgmt.appcontainers.models.ContainerAppProbeHttpGet
    :ivar initial_delay_seconds: Number of seconds after the container has started before liveness
     probes are initiated. Minimum value is 1. Maximum value is 60.
    :vartype initial_delay_seconds: int
    :ivar period_seconds: How often (in seconds) to perform the probe. Default to 10 seconds.
     Minimum value is 1. Maximum value is 240.
    :vartype period_seconds: int
    :ivar success_threshold: Minimum consecutive successes for the probe to be considered
     successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum
     value is 1. Maximum value is 10.
    :vartype success_threshold: int
    :ivar tcp_socket: TCPSocket specifies an action involving a TCP port. TCP hooks not yet
     supported.
    :vartype tcp_socket: ~azure.mgmt.appcontainers.models.ContainerAppProbeTcpSocket
    :ivar termination_grace_period_seconds: Optional duration in seconds the pod needs to terminate
     gracefully upon probe failure. The grace period is the duration in seconds after the processes
     running in the pod are sent a termination signal and the time when the processes are forcibly
     halted with a kill signal. Set this value longer than the expected cleanup time for your
     process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise,
     this value overrides the value provided by the pod spec. Value must be non-negative integer.
     The value zero indicates stop immediately via the kill signal (no opportunity to shut down).
     This is an alpha field and requires enabling ProbeTerminationGracePeriod feature gate. Maximum
     value is 3600 seconds (1 hour).
    :vartype termination_grace_period_seconds: int
    :ivar timeout_seconds: Number of seconds after which the probe times out. Defaults to 1 second.
     Minimum value is 1. Maximum value is 240.
    :vartype timeout_seconds: int
    :ivar type: The type of probe. Known values are: "Liveness", "Readiness", and "Startup".
    :vartype type: str or ~azure.mgmt.appcontainers.models.Type
    """

    _attribute_map = {
        "failure_threshold": {"key": "failureThreshold", "type": "int"},
        "http_get": {"key": "httpGet", "type": "ContainerAppProbeHttpGet"},
        "initial_delay_seconds": {"key": "initialDelaySeconds", "type": "int"},
        "period_seconds": {"key": "periodSeconds", "type": "int"},
        "success_threshold": {"key": "successThreshold", "type": "int"},
        "tcp_socket": {"key": "tcpSocket", "type": "ContainerAppProbeTcpSocket"},
        "termination_grace_period_seconds": {"key": "terminationGracePeriodSeconds", "type": "int"},
        "timeout_seconds": {"key": "timeoutSeconds", "type": "int"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        failure_threshold: Optional[int] = None,
        http_get: Optional["ContainerAppProbeHttpGet"] = None,
        initial_delay_seconds: Optional[int] = None,
        period_seconds: Optional[int] = None,
        success_threshold: Optional[int] = None,
        tcp_socket: Optional["ContainerAppProbeTcpSocket"] = None,
        termination_grace_period_seconds: Optional[int] = None,
        timeout_seconds: Optional[int] = None,
        type: Optional[Union[str, "Type"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword failure_threshold: Minimum consecutive failures for the probe to be considered failed
         after having succeeded. Defaults to 3. Minimum value is 1. Maximum value is 10.
        :paramtype failure_threshold: int
        :keyword http_get: HTTPGet specifies the http request to perform.
        :paramtype http_get: ~azure.mgmt.appcontainers.models.ContainerAppProbeHttpGet
        :keyword initial_delay_seconds: Number of seconds after the container has started before
         liveness probes are initiated. Minimum value is 1. Maximum value is 60.
        :paramtype initial_delay_seconds: int
        :keyword period_seconds: How often (in seconds) to perform the probe. Default to 10 seconds.
         Minimum value is 1. Maximum value is 240.
        :paramtype period_seconds: int
        :keyword success_threshold: Minimum consecutive successes for the probe to be considered
         successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum
         value is 1. Maximum value is 10.
        :paramtype success_threshold: int
        :keyword tcp_socket: TCPSocket specifies an action involving a TCP port. TCP hooks not yet
         supported.
        :paramtype tcp_socket: ~azure.mgmt.appcontainers.models.ContainerAppProbeTcpSocket
        :keyword termination_grace_period_seconds: Optional duration in seconds the pod needs to
         terminate gracefully upon probe failure. The grace period is the duration in seconds after the
         processes running in the pod are sent a termination signal and the time when the processes are
         forcibly halted with a kill signal. Set this value longer than the expected cleanup time for
         your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used.
         Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative
         integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut
         down). This is an alpha field and requires enabling ProbeTerminationGracePeriod feature gate.
         Maximum value is 3600 seconds (1 hour).
        :paramtype termination_grace_period_seconds: int
        :keyword timeout_seconds: Number of seconds after which the probe times out. Defaults to 1
         second. Minimum value is 1. Maximum value is 240.
        :paramtype timeout_seconds: int
        :keyword type: The type of probe. Known values are: "Liveness", "Readiness", and "Startup".
        :paramtype type: str or ~azure.mgmt.appcontainers.models.Type
        """
        super().__init__(**kwargs)
        self.failure_threshold = failure_threshold
        self.http_get = http_get
        self.initial_delay_seconds = initial_delay_seconds
        self.period_seconds = period_seconds
        self.success_threshold = success_threshold
        self.tcp_socket = tcp_socket
        self.termination_grace_period_seconds = termination_grace_period_seconds
        self.timeout_seconds = timeout_seconds
        self.type = type


class ContainerAppProbeHttpGet(_serialization.Model):
    """HTTPGet specifies the http request to perform.

    All required parameters must be populated in order to send to server.

    :ivar host: Host name to connect to, defaults to the pod IP. You probably want to set "Host" in
     httpHeaders instead.
    :vartype host: str
    :ivar http_headers: Custom headers to set in the request. HTTP allows repeated headers.
    :vartype http_headers:
     list[~azure.mgmt.appcontainers.models.ContainerAppProbeHttpGetHttpHeadersItem]
    :ivar path: Path to access on the HTTP server.
    :vartype path: str
    :ivar port: Name or number of the port to access on the container. Number must be in the range
     1 to 65535. Name must be an IANA_SVC_NAME. Required.
    :vartype port: int
    :ivar scheme: Scheme to use for connecting to the host. Defaults to HTTP. Known values are:
     "HTTP" and "HTTPS".
    :vartype scheme: str or ~azure.mgmt.appcontainers.models.Scheme
    """

    _validation = {
        "port": {"required": True},
    }

    _attribute_map = {
        "host": {"key": "host", "type": "str"},
        "http_headers": {"key": "httpHeaders", "type": "[ContainerAppProbeHttpGetHttpHeadersItem]"},
        "path": {"key": "path", "type": "str"},
        "port": {"key": "port", "type": "int"},
        "scheme": {"key": "scheme", "type": "str"},
    }

    def __init__(
        self,
        *,
        port: int,
        host: Optional[str] = None,
        http_headers: Optional[List["ContainerAppProbeHttpGetHttpHeadersItem"]] = None,
        path: Optional[str] = None,
        scheme: Optional[Union[str, "Scheme"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword host: Host name to connect to, defaults to the pod IP. You probably want to set "Host"
         in httpHeaders instead.
        :paramtype host: str
        :keyword http_headers: Custom headers to set in the request. HTTP allows repeated headers.
        :paramtype http_headers:
         list[~azure.mgmt.appcontainers.models.ContainerAppProbeHttpGetHttpHeadersItem]
        :keyword path: Path to access on the HTTP server.
        :paramtype path: str
        :keyword port: Name or number of the port to access on the container. Number must be in the
         range 1 to 65535. Name must be an IANA_SVC_NAME. Required.
        :paramtype port: int
        :keyword scheme: Scheme to use for connecting to the host. Defaults to HTTP. Known values are:
         "HTTP" and "HTTPS".
        :paramtype scheme: str or ~azure.mgmt.appcontainers.models.Scheme
        """
        super().__init__(**kwargs)
        self.host = host
        self.http_headers = http_headers
        self.path = path
        self.port = port
        self.scheme = scheme


class ContainerAppProbeHttpGetHttpHeadersItem(_serialization.Model):
    """HTTPHeader describes a custom header to be used in HTTP probes.

    All required parameters must be populated in order to send to server.

    :ivar name: The header field name. Required.
    :vartype name: str
    :ivar value: The header field value. Required.
    :vartype value: str
    """

    _validation = {
        "name": {"required": True},
        "value": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
    }

    def __init__(self, *, name: str, value: str, **kwargs: Any) -> None:
        """
        :keyword name: The header field name. Required.
        :paramtype name: str
        :keyword value: The header field value. Required.
        :paramtype value: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.value = value


class ContainerAppProbeTcpSocket(_serialization.Model):
    """TCPSocket specifies an action involving a TCP port. TCP hooks not yet supported.

    All required parameters must be populated in order to send to server.

    :ivar host: Optional: Host name to connect to, defaults to the pod IP.
    :vartype host: str
    :ivar port: Number or name of the port to access on the container. Number must be in the range
     1 to 65535. Name must be an IANA_SVC_NAME. Required.
    :vartype port: int
    """

    _validation = {
        "port": {"required": True},
    }

    _attribute_map = {
        "host": {"key": "host", "type": "str"},
        "port": {"key": "port", "type": "int"},
    }

    def __init__(self, *, port: int, host: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword host: Optional: Host name to connect to, defaults to the pod IP.
        :paramtype host: str
        :keyword port: Number or name of the port to access on the container. Number must be in the
         range 1 to 65535. Name must be an IANA_SVC_NAME. Required.
        :paramtype port: int
        """
        super().__init__(**kwargs)
        self.host = host
        self.port = port


class ContainerAppSecret(_serialization.Model):
    """Container App Secret.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar name: Secret Name.
    :vartype name: str
    :ivar value: Secret Value.
    :vartype value: str
    :ivar identity: Resource ID of a managed identity to authenticate with Azure Key Vault, or
     System to use a system-assigned identity.
    :vartype identity: str
    :ivar key_vault_url: Azure Key Vault URL pointing to the secret referenced by the container
     app.
    :vartype key_vault_url: str
    """

    _validation = {
        "name": {"readonly": True},
        "value": {"readonly": True},
        "identity": {"readonly": True},
        "key_vault_url": {"readonly": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
        "identity": {"key": "identity", "type": "str"},
        "key_vault_url": {"key": "keyVaultUrl", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.name = None
        self.value = None
        self.identity = None
        self.key_vault_url = None


class ContainerResources(_serialization.Model):
    """Container App container resource requirements.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar cpu: Required CPU in cores, e.g. 0.5.
    :vartype cpu: float
    :ivar memory: Required memory, e.g. "250Mb".
    :vartype memory: str
    :ivar ephemeral_storage: Ephemeral Storage, e.g. "1Gi".
    :vartype ephemeral_storage: str
    """

    _validation = {
        "ephemeral_storage": {"readonly": True},
    }

    _attribute_map = {
        "cpu": {"key": "cpu", "type": "float"},
        "memory": {"key": "memory", "type": "str"},
        "ephemeral_storage": {"key": "ephemeralStorage", "type": "str"},
    }

    def __init__(self, *, cpu: Optional[float] = None, memory: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword cpu: Required CPU in cores, e.g. 0.5.
        :paramtype cpu: float
        :keyword memory: Required memory, e.g. "250Mb".
        :paramtype memory: str
        """
        super().__init__(**kwargs)
        self.cpu = cpu
        self.memory = memory
        self.ephemeral_storage = None


class CookieExpiration(_serialization.Model):
    """The configuration settings of the session cookie's expiration.

    :ivar convention: The convention used when determining the session cookie's expiration. Known
     values are: "FixedTime" and "IdentityProviderDerived".
    :vartype convention: str or ~azure.mgmt.appcontainers.models.CookieExpirationConvention
    :ivar time_to_expiration: The time after the request is made when the session cookie should
     expire.
    :vartype time_to_expiration: str
    """

    _attribute_map = {
        "convention": {"key": "convention", "type": "str"},
        "time_to_expiration": {"key": "timeToExpiration", "type": "str"},
    }

    def __init__(
        self,
        *,
        convention: Optional[Union[str, "CookieExpirationConvention"]] = None,
        time_to_expiration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword convention: The convention used when determining the session cookie's expiration.
         Known values are: "FixedTime" and "IdentityProviderDerived".
        :paramtype convention: str or ~azure.mgmt.appcontainers.models.CookieExpirationConvention
        :keyword time_to_expiration: The time after the request is made when the session cookie should
         expire.
        :paramtype time_to_expiration: str
        """
        super().__init__(**kwargs)
        self.convention = convention
        self.time_to_expiration = time_to_expiration


class CorsPolicy(_serialization.Model):
    """Cross-Origin-Resource-Sharing policy.

    All required parameters must be populated in order to send to server.

    :ivar allowed_origins: Specifies the content for the access-control-allow-origins header.
     Required.
    :vartype allowed_origins: list[str]
    :ivar allowed_methods: Specifies the content for the access-control-allow-methods header.
    :vartype allowed_methods: list[str]
    :ivar allowed_headers: Specifies the content for the access-control-allow-headers header.
    :vartype allowed_headers: list[str]
    :ivar expose_headers: Specifies the content for the access-control-expose-headers header.
    :vartype expose_headers: list[str]
    :ivar max_age: Specifies the content for the access-control-max-age header.
    :vartype max_age: int
    :ivar allow_credentials: Specifies whether the resource allows credentials.
    :vartype allow_credentials: bool
    """

    _validation = {
        "allowed_origins": {"required": True},
    }

    _attribute_map = {
        "allowed_origins": {"key": "allowedOrigins", "type": "[str]"},
        "allowed_methods": {"key": "allowedMethods", "type": "[str]"},
        "allowed_headers": {"key": "allowedHeaders", "type": "[str]"},
        "expose_headers": {"key": "exposeHeaders", "type": "[str]"},
        "max_age": {"key": "maxAge", "type": "int"},
        "allow_credentials": {"key": "allowCredentials", "type": "bool"},
    }

    def __init__(
        self,
        *,
        allowed_origins: List[str],
        allowed_methods: Optional[List[str]] = None,
        allowed_headers: Optional[List[str]] = None,
        expose_headers: Optional[List[str]] = None,
        max_age: Optional[int] = None,
        allow_credentials: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword allowed_origins: Specifies the content for the access-control-allow-origins header.
         Required.
        :paramtype allowed_origins: list[str]
        :keyword allowed_methods: Specifies the content for the access-control-allow-methods header.
        :paramtype allowed_methods: list[str]
        :keyword allowed_headers: Specifies the content for the access-control-allow-headers header.
        :paramtype allowed_headers: list[str]
        :keyword expose_headers: Specifies the content for the access-control-expose-headers header.
        :paramtype expose_headers: list[str]
        :keyword max_age: Specifies the content for the access-control-max-age header.
        :paramtype max_age: int
        :keyword allow_credentials: Specifies whether the resource allows credentials.
        :paramtype allow_credentials: bool
        """
        super().__init__(**kwargs)
        self.allowed_origins = allowed_origins
        self.allowed_methods = allowed_methods
        self.allowed_headers = allowed_headers
        self.expose_headers = expose_headers
        self.max_age = max_age
        self.allow_credentials = allow_credentials


class CustomContainerTemplate(_serialization.Model):
    """Custom container configuration.

    :ivar registry_credentials: Private container registry credentials for containers used by the
     sessions of the session pool.
    :vartype registry_credentials: ~azure.mgmt.appcontainers.models.SessionRegistryCredentials
    :ivar containers: List of container definitions for the sessions of the session pool.
    :vartype containers: list[~azure.mgmt.appcontainers.models.SessionContainer]
    :ivar ingress: Session pool ingress configuration.
    :vartype ingress: ~azure.mgmt.appcontainers.models.SessionIngress
    """

    _attribute_map = {
        "registry_credentials": {"key": "registryCredentials", "type": "SessionRegistryCredentials"},
        "containers": {"key": "containers", "type": "[SessionContainer]"},
        "ingress": {"key": "ingress", "type": "SessionIngress"},
    }

    def __init__(
        self,
        *,
        registry_credentials: Optional["SessionRegistryCredentials"] = None,
        containers: Optional[List["SessionContainer"]] = None,
        ingress: Optional["SessionIngress"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword registry_credentials: Private container registry credentials for containers used by
         the sessions of the session pool.
        :paramtype registry_credentials: ~azure.mgmt.appcontainers.models.SessionRegistryCredentials
        :keyword containers: List of container definitions for the sessions of the session pool.
        :paramtype containers: list[~azure.mgmt.appcontainers.models.SessionContainer]
        :keyword ingress: Session pool ingress configuration.
        :paramtype ingress: ~azure.mgmt.appcontainers.models.SessionIngress
        """
        super().__init__(**kwargs)
        self.registry_credentials = registry_credentials
        self.containers = containers
        self.ingress = ingress


class CustomDomain(_serialization.Model):
    """Custom Domain of a Container App.

    All required parameters must be populated in order to send to server.

    :ivar name: Hostname. Required.
    :vartype name: str
    :ivar binding_type: Custom Domain binding type. Known values are: "Disabled" and "SniEnabled".
    :vartype binding_type: str or ~azure.mgmt.appcontainers.models.BindingType
    :ivar certificate_id: Resource Id of the Certificate to be bound to this hostname. Must exist
     in the Managed Environment.
    :vartype certificate_id: str
    """

    _validation = {
        "name": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "binding_type": {"key": "bindingType", "type": "str"},
        "certificate_id": {"key": "certificateId", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: str,
        binding_type: Optional[Union[str, "BindingType"]] = None,
        certificate_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Hostname. Required.
        :paramtype name: str
        :keyword binding_type: Custom Domain binding type. Known values are: "Disabled" and
         "SniEnabled".
        :paramtype binding_type: str or ~azure.mgmt.appcontainers.models.BindingType
        :keyword certificate_id: Resource Id of the Certificate to be bound to this hostname. Must
         exist in the Managed Environment.
        :paramtype certificate_id: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.binding_type = binding_type
        self.certificate_id = certificate_id


class CustomDomainConfiguration(_serialization.Model):
    """Configuration properties for apps environment custom domain.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar custom_domain_verification_id: Id used to verify domain name ownership.
    :vartype custom_domain_verification_id: str
    :ivar dns_suffix: Dns suffix for the environment domain.
    :vartype dns_suffix: str
    :ivar certificate_key_vault_properties: Certificate stored in Azure Key Vault.
    :vartype certificate_key_vault_properties:
     ~azure.mgmt.appcontainers.models.CertificateKeyVaultProperties
    :ivar certificate_value: PFX or PEM blob.
    :vartype certificate_value: bytes
    :ivar certificate_password: Certificate password.
    :vartype certificate_password: str
    :ivar expiration_date: Certificate expiration date.
    :vartype expiration_date: ~datetime.datetime
    :ivar thumbprint: Certificate thumbprint.
    :vartype thumbprint: str
    :ivar subject_name: Subject name of the certificate.
    :vartype subject_name: str
    """

    _validation = {
        "custom_domain_verification_id": {"readonly": True},
        "expiration_date": {"readonly": True},
        "thumbprint": {"readonly": True},
        "subject_name": {"readonly": True},
    }

    _attribute_map = {
        "custom_domain_verification_id": {"key": "customDomainVerificationId", "type": "str"},
        "dns_suffix": {"key": "dnsSuffix", "type": "str"},
        "certificate_key_vault_properties": {
            "key": "certificateKeyVaultProperties",
            "type": "CertificateKeyVaultProperties",
        },
        "certificate_value": {"key": "certificateValue", "type": "bytearray"},
        "certificate_password": {"key": "certificatePassword", "type": "str"},
        "expiration_date": {"key": "expirationDate", "type": "iso-8601"},
        "thumbprint": {"key": "thumbprint", "type": "str"},
        "subject_name": {"key": "subjectName", "type": "str"},
    }

    def __init__(
        self,
        *,
        dns_suffix: Optional[str] = None,
        certificate_key_vault_properties: Optional["CertificateKeyVaultProperties"] = None,
        certificate_value: Optional[bytes] = None,
        certificate_password: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword dns_suffix: Dns suffix for the environment domain.
        :paramtype dns_suffix: str
        :keyword certificate_key_vault_properties: Certificate stored in Azure Key Vault.
        :paramtype certificate_key_vault_properties:
         ~azure.mgmt.appcontainers.models.CertificateKeyVaultProperties
        :keyword certificate_value: PFX or PEM blob.
        :paramtype certificate_value: bytes
        :keyword certificate_password: Certificate password.
        :paramtype certificate_password: str
        """
        super().__init__(**kwargs)
        self.custom_domain_verification_id = None
        self.dns_suffix = dns_suffix
        self.certificate_key_vault_properties = certificate_key_vault_properties
        self.certificate_value = certificate_value
        self.certificate_password = certificate_password
        self.expiration_date = None
        self.thumbprint = None
        self.subject_name = None


class CustomHostnameAnalysisResult(_serialization.Model):
    """Custom domain analysis.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar host_name: Host name that was analyzed.
    :vartype host_name: str
    :ivar is_hostname_already_verified: :code:`<code>true</code>` if hostname is already verified;
     otherwise, :code:`<code>false</code>`.
    :vartype is_hostname_already_verified: bool
    :ivar custom_domain_verification_test: DNS verification test result. Known values are:
     "Passed", "Failed", and "Skipped".
    :vartype custom_domain_verification_test: str or
     ~azure.mgmt.appcontainers.models.DnsVerificationTestResult
    :ivar custom_domain_verification_failure_info: Raw failure information if DNS verification
     fails.
    :vartype custom_domain_verification_failure_info:
     ~azure.mgmt.appcontainers.models.CustomHostnameAnalysisResultCustomDomainVerificationFailureInfo
    :ivar has_conflict_on_managed_environment: :code:`<code>true</code>` if there is a conflict on
     the Container App's managed environment; otherwise, :code:`<code>false</code>`.
    :vartype has_conflict_on_managed_environment: bool
    :ivar conflict_with_environment_custom_domain: :code:`<code>true</code>` if there is a conflict
     on the Container App's managed environment level custom domain; otherwise,
     :code:`<code>false</code>`.
    :vartype conflict_with_environment_custom_domain: bool
    :ivar conflicting_container_app_resource_id: Name of the conflicting Container App on the
     Managed Environment if it's within the same subscription.
    :vartype conflicting_container_app_resource_id: str
    :ivar c_name_records: CName records visible for this hostname.
    :vartype c_name_records: list[str]
    :ivar txt_records: TXT records visible for this hostname.
    :vartype txt_records: list[str]
    :ivar a_records: A records visible for this hostname.
    :vartype a_records: list[str]
    :ivar alternate_c_name_records: Alternate CName records visible for this hostname.
    :vartype alternate_c_name_records: list[str]
    :ivar alternate_txt_records: Alternate TXT records visible for this hostname.
    :vartype alternate_txt_records: list[str]
    """

    _validation = {
        "host_name": {"readonly": True},
        "is_hostname_already_verified": {"readonly": True},
        "custom_domain_verification_test": {"readonly": True},
        "custom_domain_verification_failure_info": {"readonly": True},
        "has_conflict_on_managed_environment": {"readonly": True},
        "conflict_with_environment_custom_domain": {"readonly": True},
        "conflicting_container_app_resource_id": {"readonly": True},
    }

    _attribute_map = {
        "host_name": {"key": "hostName", "type": "str"},
        "is_hostname_already_verified": {"key": "isHostnameAlreadyVerified", "type": "bool"},
        "custom_domain_verification_test": {"key": "customDomainVerificationTest", "type": "str"},
        "custom_domain_verification_failure_info": {
            "key": "customDomainVerificationFailureInfo",
            "type": "CustomHostnameAnalysisResultCustomDomainVerificationFailureInfo",
        },
        "has_conflict_on_managed_environment": {"key": "hasConflictOnManagedEnvironment", "type": "bool"},
        "conflict_with_environment_custom_domain": {"key": "conflictWithEnvironmentCustomDomain", "type": "bool"},
        "conflicting_container_app_resource_id": {"key": "conflictingContainerAppResourceId", "type": "str"},
        "c_name_records": {"key": "cNameRecords", "type": "[str]"},
        "txt_records": {"key": "txtRecords", "type": "[str]"},
        "a_records": {"key": "aRecords", "type": "[str]"},
        "alternate_c_name_records": {"key": "alternateCNameRecords", "type": "[str]"},
        "alternate_txt_records": {"key": "alternateTxtRecords", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        c_name_records: Optional[List[str]] = None,
        txt_records: Optional[List[str]] = None,
        a_records: Optional[List[str]] = None,
        alternate_c_name_records: Optional[List[str]] = None,
        alternate_txt_records: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword c_name_records: CName records visible for this hostname.
        :paramtype c_name_records: list[str]
        :keyword txt_records: TXT records visible for this hostname.
        :paramtype txt_records: list[str]
        :keyword a_records: A records visible for this hostname.
        :paramtype a_records: list[str]
        :keyword alternate_c_name_records: Alternate CName records visible for this hostname.
        :paramtype alternate_c_name_records: list[str]
        :keyword alternate_txt_records: Alternate TXT records visible for this hostname.
        :paramtype alternate_txt_records: list[str]
        """
        super().__init__(**kwargs)
        self.host_name = None
        self.is_hostname_already_verified = None
        self.custom_domain_verification_test = None
        self.custom_domain_verification_failure_info = None
        self.has_conflict_on_managed_environment = None
        self.conflict_with_environment_custom_domain = None
        self.conflicting_container_app_resource_id = None
        self.c_name_records = c_name_records
        self.txt_records = txt_records
        self.a_records = a_records
        self.alternate_c_name_records = alternate_c_name_records
        self.alternate_txt_records = alternate_txt_records


class CustomHostnameAnalysisResultCustomDomainVerificationFailureInfo(
    _serialization.Model
):  # pylint: disable=name-too-long
    """Raw failure information if DNS verification fails.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: Standardized string to programmatically identify the error.
    :vartype code: str
    :ivar message: Detailed error description and debugging information.
    :vartype message: str
    :ivar target: Detailed error description and debugging information.
    :vartype target: str
    :ivar details: Details or the error.
    :vartype details:
     list[~azure.mgmt.appcontainers.models.CustomHostnameAnalysisResultCustomDomainVerificationFailureInfoDetailsItem]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {
            "key": "details",
            "type": "[CustomHostnameAnalysisResultCustomDomainVerificationFailureInfoDetailsItem]",
        },
    }

    def __init__(
        self,
        *,
        details: Optional[
            List["CustomHostnameAnalysisResultCustomDomainVerificationFailureInfoDetailsItem"]
        ] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword details: Details or the error.
        :paramtype details:
         list[~azure.mgmt.appcontainers.models.CustomHostnameAnalysisResultCustomDomainVerificationFailureInfoDetailsItem]  # pylint: disable=line-too-long
        """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = details


class CustomHostnameAnalysisResultCustomDomainVerificationFailureInfoDetailsItem(
    _serialization.Model
):  # pylint: disable=name-too-long
    """Detailed errors.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: Standardized string to programmatically identify the error.
    :vartype code: str
    :ivar message: Detailed error description and debugging information.
    :vartype message: str
    :ivar target: Detailed error description and debugging information.
    :vartype target: str
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None


class CustomOpenIdConnectProvider(_serialization.Model):
    """The configuration settings of the custom Open ID Connect provider.

    :ivar enabled: :code:`<code>false</code>` if the custom Open ID provider provider should not be
     enabled; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the app registration for the custom Open ID
     Connect provider.
    :vartype registration: ~azure.mgmt.appcontainers.models.OpenIdConnectRegistration
    :ivar login: The configuration settings of the login flow of the custom Open ID Connect
     provider.
    :vartype login: ~azure.mgmt.appcontainers.models.OpenIdConnectLogin
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "OpenIdConnectRegistration"},
        "login": {"key": "login", "type": "OpenIdConnectLogin"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["OpenIdConnectRegistration"] = None,
        login: Optional["OpenIdConnectLogin"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the custom Open ID provider provider should not
         be enabled; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the app registration for the custom Open
         ID Connect provider.
        :paramtype registration: ~azure.mgmt.appcontainers.models.OpenIdConnectRegistration
        :keyword login: The configuration settings of the login flow of the custom Open ID Connect
         provider.
        :paramtype login: ~azure.mgmt.appcontainers.models.OpenIdConnectLogin
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration
        self.login = login


class CustomScaleRule(_serialization.Model):
    """Container App container Custom scaling rule.

    :ivar type: Type of the custom scale rule
     eg: azure-servicebus, redis etc.
    :vartype type: str
    :ivar metadata: Metadata properties to describe custom scale rule.
    :vartype metadata: dict[str, str]
    :ivar auth: Authentication secrets for the custom scale rule.
    :vartype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
    :ivar identity: The resource ID of a user-assigned managed identity that is assigned to the
     Container App, or 'system' for system-assigned identity.
    :vartype identity: str
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "metadata": {"key": "metadata", "type": "{str}"},
        "auth": {"key": "auth", "type": "[ScaleRuleAuth]"},
        "identity": {"key": "identity", "type": "str"},
    }

    def __init__(
        self,
        *,
        type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None,
        auth: Optional[List["ScaleRuleAuth"]] = None,
        identity: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Type of the custom scale rule
         eg: azure-servicebus, redis etc.
        :paramtype type: str
        :keyword metadata: Metadata properties to describe custom scale rule.
        :paramtype metadata: dict[str, str]
        :keyword auth: Authentication secrets for the custom scale rule.
        :paramtype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
        :keyword identity: The resource ID of a user-assigned managed identity that is assigned to the
         Container App, or 'system' for system-assigned identity.
        :paramtype identity: str
        """
        super().__init__(**kwargs)
        self.type = type
        self.metadata = metadata
        self.auth = auth
        self.identity = identity


class Dapr(_serialization.Model):
    """Container App Dapr configuration.

    :ivar enabled: Boolean indicating if the Dapr side car is enabled.
    :vartype enabled: bool
    :ivar app_id: Dapr application identifier.
    :vartype app_id: str
    :ivar app_protocol: Tells Dapr which protocol your application is using. Valid options are http
     and grpc. Default is http. Known values are: "http" and "grpc".
    :vartype app_protocol: str or ~azure.mgmt.appcontainers.models.AppProtocol
    :ivar app_port: Tells Dapr which port your application is listening on.
    :vartype app_port: int
    :ivar http_read_buffer_size: Dapr max size of http header read buffer in KB to handle when
     sending multi-KB headers. Default is 65KB.
    :vartype http_read_buffer_size: int
    :ivar http_max_request_size: Increasing max size of request body http and grpc servers
     parameter in MB to handle uploading of big files. Default is 4 MB.
    :vartype http_max_request_size: int
    :ivar log_level: Sets the log level for the Dapr sidecar. Allowed values are debug, info, warn,
     error. Default is info. Known values are: "info", "debug", "warn", and "error".
    :vartype log_level: str or ~azure.mgmt.appcontainers.models.LogLevel
    :ivar enable_api_logging: Enables API logging for the Dapr sidecar.
    :vartype enable_api_logging: bool
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "app_id": {"key": "appId", "type": "str"},
        "app_protocol": {"key": "appProtocol", "type": "str"},
        "app_port": {"key": "appPort", "type": "int"},
        "http_read_buffer_size": {"key": "httpReadBufferSize", "type": "int"},
        "http_max_request_size": {"key": "httpMaxRequestSize", "type": "int"},
        "log_level": {"key": "logLevel", "type": "str"},
        "enable_api_logging": {"key": "enableApiLogging", "type": "bool"},
    }

    def __init__(
        self,
        *,
        enabled: bool = False,
        app_id: Optional[str] = None,
        app_protocol: Union[str, "AppProtocol"] = "http",
        app_port: Optional[int] = None,
        http_read_buffer_size: Optional[int] = None,
        http_max_request_size: Optional[int] = None,
        log_level: Optional[Union[str, "LogLevel"]] = None,
        enable_api_logging: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: Boolean indicating if the Dapr side car is enabled.
        :paramtype enabled: bool
        :keyword app_id: Dapr application identifier.
        :paramtype app_id: str
        :keyword app_protocol: Tells Dapr which protocol your application is using. Valid options are
         http and grpc. Default is http. Known values are: "http" and "grpc".
        :paramtype app_protocol: str or ~azure.mgmt.appcontainers.models.AppProtocol
        :keyword app_port: Tells Dapr which port your application is listening on.
        :paramtype app_port: int
        :keyword http_read_buffer_size: Dapr max size of http header read buffer in KB to handle when
         sending multi-KB headers. Default is 65KB.
        :paramtype http_read_buffer_size: int
        :keyword http_max_request_size: Increasing max size of request body http and grpc servers
         parameter in MB to handle uploading of big files. Default is 4 MB.
        :paramtype http_max_request_size: int
        :keyword log_level: Sets the log level for the Dapr sidecar. Allowed values are debug, info,
         warn, error. Default is info. Known values are: "info", "debug", "warn", and "error".
        :paramtype log_level: str or ~azure.mgmt.appcontainers.models.LogLevel
        :keyword enable_api_logging: Enables API logging for the Dapr sidecar.
        :paramtype enable_api_logging: bool
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.app_id = app_id
        self.app_protocol = app_protocol
        self.app_port = app_port
        self.http_read_buffer_size = http_read_buffer_size
        self.http_max_request_size = http_max_request_size
        self.log_level = log_level
        self.enable_api_logging = enable_api_logging


class DaprComponent(ProxyResource):
    """Dapr Component.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar component_type: Component type.
    :vartype component_type: str
    :ivar version: Component version.
    :vartype version: str
    :ivar ignore_errors: Boolean describing if the component errors are ignores.
    :vartype ignore_errors: bool
    :ivar init_timeout: Initialization timeout.
    :vartype init_timeout: str
    :ivar secrets: Collection of secrets used by a Dapr component.
    :vartype secrets: list[~azure.mgmt.appcontainers.models.Secret]
    :ivar secret_store_component: Name of a Dapr component to retrieve component secrets from.
    :vartype secret_store_component: str
    :ivar metadata: Component metadata.
    :vartype metadata: list[~azure.mgmt.appcontainers.models.DaprMetadata]
    :ivar scopes: Names of container apps that can use this Dapr component.
    :vartype scopes: list[str]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "component_type": {"key": "properties.componentType", "type": "str"},
        "version": {"key": "properties.version", "type": "str"},
        "ignore_errors": {"key": "properties.ignoreErrors", "type": "bool"},
        "init_timeout": {"key": "properties.initTimeout", "type": "str"},
        "secrets": {"key": "properties.secrets", "type": "[Secret]"},
        "secret_store_component": {"key": "properties.secretStoreComponent", "type": "str"},
        "metadata": {"key": "properties.metadata", "type": "[DaprMetadata]"},
        "scopes": {"key": "properties.scopes", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        component_type: Optional[str] = None,
        version: Optional[str] = None,
        ignore_errors: bool = False,
        init_timeout: Optional[str] = None,
        secrets: Optional[List["Secret"]] = None,
        secret_store_component: Optional[str] = None,
        metadata: Optional[List["DaprMetadata"]] = None,
        scopes: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword component_type: Component type.
        :paramtype component_type: str
        :keyword version: Component version.
        :paramtype version: str
        :keyword ignore_errors: Boolean describing if the component errors are ignores.
        :paramtype ignore_errors: bool
        :keyword init_timeout: Initialization timeout.
        :paramtype init_timeout: str
        :keyword secrets: Collection of secrets used by a Dapr component.
        :paramtype secrets: list[~azure.mgmt.appcontainers.models.Secret]
        :keyword secret_store_component: Name of a Dapr component to retrieve component secrets from.
        :paramtype secret_store_component: str
        :keyword metadata: Component metadata.
        :paramtype metadata: list[~azure.mgmt.appcontainers.models.DaprMetadata]
        :keyword scopes: Names of container apps that can use this Dapr component.
        :paramtype scopes: list[str]
        """
        super().__init__(**kwargs)
        self.component_type = component_type
        self.version = version
        self.ignore_errors = ignore_errors
        self.init_timeout = init_timeout
        self.secrets = secrets
        self.secret_store_component = secret_store_component
        self.metadata = metadata
        self.scopes = scopes


class DaprComponentsCollection(_serialization.Model):
    """Dapr Components ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.DaprComponent]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[DaprComponent]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["DaprComponent"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.DaprComponent]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class DaprConfiguration(_serialization.Model):
    """Configuration properties Dapr component.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar version: The version of Dapr.
    :vartype version: str
    """

    _validation = {
        "version": {"readonly": True},
    }

    _attribute_map = {
        "version": {"key": "version", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.version = None


class DaprMetadata(_serialization.Model):
    """Dapr component metadata.

    :ivar name: Metadata property name.
    :vartype name: str
    :ivar value: Metadata property value.
    :vartype value: str
    :ivar secret_ref: Name of the Dapr Component secret from which to pull the metadata property
     value.
    :vartype secret_ref: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
        "secret_ref": {"key": "secretRef", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        value: Optional[str] = None,
        secret_ref: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Metadata property name.
        :paramtype name: str
        :keyword value: Metadata property value.
        :paramtype value: str
        :keyword secret_ref: Name of the Dapr Component secret from which to pull the metadata property
         value.
        :paramtype secret_ref: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.value = value
        self.secret_ref = secret_ref


class DaprSecret(_serialization.Model):
    """Dapr component Secret for ListSecrets Action.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar name: Secret Name.
    :vartype name: str
    :ivar value: Secret Value.
    :vartype value: str
    """

    _validation = {
        "name": {"readonly": True},
        "value": {"readonly": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.name = None
        self.value = None


class DaprSecretsCollection(_serialization.Model):
    """Dapr component Secrets Collection for ListSecrets Action.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of secrets used by a Dapr component. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.DaprSecret]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[DaprSecret]"},
    }

    def __init__(self, *, value: List["DaprSecret"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of secrets used by a Dapr component. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.DaprSecret]
        """
        super().__init__(**kwargs)
        self.value = value


class DefaultAuthorizationPolicy(_serialization.Model):
    """The configuration settings of the Azure Active Directory default authorization policy.

    :ivar allowed_principals: The configuration settings of the Azure Active Directory allowed
     principals.
    :vartype allowed_principals: ~azure.mgmt.appcontainers.models.AllowedPrincipals
    :ivar allowed_applications: The configuration settings of the Azure Active Directory allowed
     applications.
    :vartype allowed_applications: list[str]
    """

    _attribute_map = {
        "allowed_principals": {"key": "allowedPrincipals", "type": "AllowedPrincipals"},
        "allowed_applications": {"key": "allowedApplications", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        allowed_principals: Optional["AllowedPrincipals"] = None,
        allowed_applications: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword allowed_principals: The configuration settings of the Azure Active Directory allowed
         principals.
        :paramtype allowed_principals: ~azure.mgmt.appcontainers.models.AllowedPrincipals
        :keyword allowed_applications: The configuration settings of the Azure Active Directory allowed
         applications.
        :paramtype allowed_applications: list[str]
        """
        super().__init__(**kwargs)
        self.allowed_principals = allowed_principals
        self.allowed_applications = allowed_applications


class DefaultErrorResponse(_serialization.Model):
    """App Service error response.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar error: Error model.
    :vartype error: ~azure.mgmt.appcontainers.models.DefaultErrorResponseError
    """

    _validation = {
        "error": {"readonly": True},
    }

    _attribute_map = {
        "error": {"key": "error", "type": "DefaultErrorResponseError"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.error = None


class DefaultErrorResponseError(_serialization.Model):
    """Error model.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: Standardized string to programmatically identify the error.
    :vartype code: str
    :ivar message: Detailed error description and debugging information.
    :vartype message: str
    :ivar target: Detailed error description and debugging information.
    :vartype target: str
    :ivar details: Details or the error.
    :vartype details: list[~azure.mgmt.appcontainers.models.DefaultErrorResponseErrorDetailsItem]
    :ivar innererror: More information to debug error.
    :vartype innererror: str
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "innererror": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[DefaultErrorResponseErrorDetailsItem]"},
        "innererror": {"key": "innererror", "type": "str"},
    }

    def __init__(
        self, *, details: Optional[List["DefaultErrorResponseErrorDetailsItem"]] = None, **kwargs: Any
    ) -> None:
        """
        :keyword details: Details or the error.
        :paramtype details: list[~azure.mgmt.appcontainers.models.DefaultErrorResponseErrorDetailsItem]
        """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = details
        self.innererror = None


class DefaultErrorResponseErrorDetailsItem(_serialization.Model):
    """Detailed errors.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: Standardized string to programmatically identify the error.
    :vartype code: str
    :ivar message: Detailed error description and debugging information.
    :vartype message: str
    :ivar target: Detailed error description and debugging information.
    :vartype target: str
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None


class DiagnosticDataProviderMetadata(_serialization.Model):
    """Details of a diagnostics data provider.

    :ivar provider_name: Name of data provider.
    :vartype provider_name: str
    :ivar property_bag: Collection of properties.
    :vartype property_bag:
     list[~azure.mgmt.appcontainers.models.DiagnosticDataProviderMetadataPropertyBagItem]
    """

    _attribute_map = {
        "provider_name": {"key": "providerName", "type": "str"},
        "property_bag": {"key": "propertyBag", "type": "[DiagnosticDataProviderMetadataPropertyBagItem]"},
    }

    def __init__(
        self,
        *,
        provider_name: Optional[str] = None,
        property_bag: Optional[List["DiagnosticDataProviderMetadataPropertyBagItem"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword provider_name: Name of data provider.
        :paramtype provider_name: str
        :keyword property_bag: Collection of properties.
        :paramtype property_bag:
         list[~azure.mgmt.appcontainers.models.DiagnosticDataProviderMetadataPropertyBagItem]
        """
        super().__init__(**kwargs)
        self.provider_name = provider_name
        self.property_bag = property_bag


class DiagnosticDataProviderMetadataPropertyBagItem(_serialization.Model):  # pylint: disable=name-too-long
    """Property details.

    :ivar name: Property name.
    :vartype name: str
    :ivar value: Property value.
    :vartype value: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
    }

    def __init__(self, *, name: Optional[str] = None, value: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword name: Property name.
        :paramtype name: str
        :keyword value: Property value.
        :paramtype value: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.value = value


class DiagnosticDataTableResponseColumn(_serialization.Model):
    """Diagnostics data column.

    :ivar column_name: Column name.
    :vartype column_name: str
    :ivar data_type: Data type of the column.
    :vartype data_type: str
    :ivar column_type: Column type.
    :vartype column_type: str
    """

    _attribute_map = {
        "column_name": {"key": "columnName", "type": "str"},
        "data_type": {"key": "dataType", "type": "str"},
        "column_type": {"key": "columnType", "type": "str"},
    }

    def __init__(
        self,
        *,
        column_name: Optional[str] = None,
        data_type: Optional[str] = None,
        column_type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword column_name: Column name.
        :paramtype column_name: str
        :keyword data_type: Data type of the column.
        :paramtype data_type: str
        :keyword column_type: Column type.
        :paramtype column_type: str
        """
        super().__init__(**kwargs)
        self.column_name = column_name
        self.data_type = data_type
        self.column_type = column_type


class DiagnosticDataTableResponseObject(_serialization.Model):
    """Diagnostics data table.

    :ivar table_name: Table name.
    :vartype table_name: str
    :ivar columns: Columns in the table.
    :vartype columns: list[~azure.mgmt.appcontainers.models.DiagnosticDataTableResponseColumn]
    :ivar rows: Rows in the table.
    :vartype rows: list[JSON]
    """

    _attribute_map = {
        "table_name": {"key": "tableName", "type": "str"},
        "columns": {"key": "columns", "type": "[DiagnosticDataTableResponseColumn]"},
        "rows": {"key": "rows", "type": "[object]"},
    }

    def __init__(
        self,
        *,
        table_name: Optional[str] = None,
        columns: Optional[List["DiagnosticDataTableResponseColumn"]] = None,
        rows: Optional[List[JSON]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword table_name: Table name.
        :paramtype table_name: str
        :keyword columns: Columns in the table.
        :paramtype columns: list[~azure.mgmt.appcontainers.models.DiagnosticDataTableResponseColumn]
        :keyword rows: Rows in the table.
        :paramtype rows: list[JSON]
        """
        super().__init__(**kwargs)
        self.table_name = table_name
        self.columns = columns
        self.rows = rows


class DiagnosticRendering(_serialization.Model):
    """Rendering details of a diagnostics table.

    :ivar type: Rendering type.
    :vartype type: int
    :ivar title: Title of the table.
    :vartype title: str
    :ivar description: Description of the table.
    :vartype description: str
    :ivar is_visible: Flag if the table should be rendered.
    :vartype is_visible: bool
    """

    _attribute_map = {
        "type": {"key": "type", "type": "int"},
        "title": {"key": "title", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "is_visible": {"key": "isVisible", "type": "bool"},
    }

    def __init__(
        self,
        *,
        type: Optional[int] = None,
        title: Optional[str] = None,
        description: Optional[str] = None,
        is_visible: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Rendering type.
        :paramtype type: int
        :keyword title: Title of the table.
        :paramtype title: str
        :keyword description: Description of the table.
        :paramtype description: str
        :keyword is_visible: Flag if the table should be rendered.
        :paramtype is_visible: bool
        """
        super().__init__(**kwargs)
        self.type = type
        self.title = title
        self.description = description
        self.is_visible = is_visible


class Diagnostics(ProxyResource):
    """Diagnostics data for a resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar properties: Diagnostics resource specific properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.DiagnosticsProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "DiagnosticsProperties"},
    }

    def __init__(self, *, properties: Optional["DiagnosticsProperties"] = None, **kwargs: Any) -> None:
        """
        :keyword properties: Diagnostics resource specific properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.DiagnosticsProperties
        """
        super().__init__(**kwargs)
        self.properties = properties


class DiagnosticsCollection(_serialization.Model):
    """Diagnostics data collection for a resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of diagnostic data. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.Diagnostics]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Diagnostics]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["Diagnostics"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of diagnostic data. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.Diagnostics]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class DiagnosticsDataApiResponse(_serialization.Model):
    """Diagnostics data returned from a detector.

    :ivar table: Table response.
    :vartype table: ~azure.mgmt.appcontainers.models.DiagnosticDataTableResponseObject
    :ivar rendering_properties: Details of the table response.
    :vartype rendering_properties: ~azure.mgmt.appcontainers.models.DiagnosticRendering
    """

    _attribute_map = {
        "table": {"key": "table", "type": "DiagnosticDataTableResponseObject"},
        "rendering_properties": {"key": "renderingProperties", "type": "DiagnosticRendering"},
    }

    def __init__(
        self,
        *,
        table: Optional["DiagnosticDataTableResponseObject"] = None,
        rendering_properties: Optional["DiagnosticRendering"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword table: Table response.
        :paramtype table: ~azure.mgmt.appcontainers.models.DiagnosticDataTableResponseObject
        :keyword rendering_properties: Details of the table response.
        :paramtype rendering_properties: ~azure.mgmt.appcontainers.models.DiagnosticRendering
        """
        super().__init__(**kwargs)
        self.table = table
        self.rendering_properties = rendering_properties


class DiagnosticsDefinition(_serialization.Model):
    """Metadata of the diagnostics response.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Unique detector name.
    :vartype id: str
    :ivar name: Display Name of the detector.
    :vartype name: str
    :ivar description: Details of the diagnostics info.
    :vartype description: str
    :ivar author: Authors' names of the detector.
    :vartype author: str
    :ivar category: Category of the detector.
    :vartype category: str
    :ivar support_topic_list: List of support topics.
    :vartype support_topic_list: list[~azure.mgmt.appcontainers.models.DiagnosticSupportTopic]
    :ivar analysis_types: List of analysis types.
    :vartype analysis_types: list[str]
    :ivar type: Authors' names of the detector.
    :vartype type: str
    :ivar score: Authors' names of the detector.
    :vartype score: float
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "description": {"readonly": True},
        "author": {"readonly": True},
        "category": {"readonly": True},
        "type": {"readonly": True},
        "score": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "author": {"key": "author", "type": "str"},
        "category": {"key": "category", "type": "str"},
        "support_topic_list": {"key": "supportTopicList", "type": "[DiagnosticSupportTopic]"},
        "analysis_types": {"key": "analysisTypes", "type": "[str]"},
        "type": {"key": "type", "type": "str"},
        "score": {"key": "score", "type": "float"},
    }

    def __init__(
        self,
        *,
        support_topic_list: Optional[List["DiagnosticSupportTopic"]] = None,
        analysis_types: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword support_topic_list: List of support topics.
        :paramtype support_topic_list: list[~azure.mgmt.appcontainers.models.DiagnosticSupportTopic]
        :keyword analysis_types: List of analysis types.
        :paramtype analysis_types: list[str]
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.description = None
        self.author = None
        self.category = None
        self.support_topic_list = support_topic_list
        self.analysis_types = analysis_types
        self.type = None
        self.score = None


class DiagnosticsProperties(_serialization.Model):
    """Diagnostics resource specific properties.

    :ivar metadata: Metadata of the diagnostics response.
    :vartype metadata: ~azure.mgmt.appcontainers.models.DiagnosticsDefinition
    :ivar dataset: Set of data collections associated with the response.
    :vartype dataset: list[~azure.mgmt.appcontainers.models.DiagnosticsDataApiResponse]
    :ivar status: Status of the diagnostics response.
    :vartype status: ~azure.mgmt.appcontainers.models.DiagnosticsStatus
    :ivar data_provider_metadata: List of data providers' metadata.
    :vartype data_provider_metadata:
     ~azure.mgmt.appcontainers.models.DiagnosticDataProviderMetadata
    """

    _attribute_map = {
        "metadata": {"key": "metadata", "type": "DiagnosticsDefinition"},
        "dataset": {"key": "dataset", "type": "[DiagnosticsDataApiResponse]"},
        "status": {"key": "status", "type": "DiagnosticsStatus"},
        "data_provider_metadata": {"key": "dataProviderMetadata", "type": "DiagnosticDataProviderMetadata"},
    }

    def __init__(
        self,
        *,
        metadata: Optional["DiagnosticsDefinition"] = None,
        dataset: Optional[List["DiagnosticsDataApiResponse"]] = None,
        status: Optional["DiagnosticsStatus"] = None,
        data_provider_metadata: Optional["DiagnosticDataProviderMetadata"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword metadata: Metadata of the diagnostics response.
        :paramtype metadata: ~azure.mgmt.appcontainers.models.DiagnosticsDefinition
        :keyword dataset: Set of data collections associated with the response.
        :paramtype dataset: list[~azure.mgmt.appcontainers.models.DiagnosticsDataApiResponse]
        :keyword status: Status of the diagnostics response.
        :paramtype status: ~azure.mgmt.appcontainers.models.DiagnosticsStatus
        :keyword data_provider_metadata: List of data providers' metadata.
        :paramtype data_provider_metadata:
         ~azure.mgmt.appcontainers.models.DiagnosticDataProviderMetadata
        """
        super().__init__(**kwargs)
        self.metadata = metadata
        self.dataset = dataset
        self.status = status
        self.data_provider_metadata = data_provider_metadata


class DiagnosticsStatus(_serialization.Model):
    """Rendering details of a diagnostics table.

    :ivar message: Diagnostic message.
    :vartype message: str
    :ivar status_id: Status.
    :vartype status_id: int
    """

    _attribute_map = {
        "message": {"key": "message", "type": "str"},
        "status_id": {"key": "statusId", "type": "int"},
    }

    def __init__(self, *, message: Optional[str] = None, status_id: Optional[int] = None, **kwargs: Any) -> None:
        """
        :keyword message: Diagnostic message.
        :paramtype message: str
        :keyword status_id: Status.
        :paramtype status_id: int
        """
        super().__init__(**kwargs)
        self.message = message
        self.status_id = status_id


class DiagnosticSupportTopic(_serialization.Model):
    """Support topic information.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Unique topic identifier.
    :vartype id: str
    :ivar pes_id: PES identifier.
    :vartype pes_id: str
    """

    _validation = {
        "id": {"readonly": True},
        "pes_id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "pes_id": {"key": "pesId", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None
        self.pes_id = None


class DynamicPoolConfiguration(_serialization.Model):
    """Dynamic pool configuration.

    :ivar lifecycle_configuration: The lifecycle configuration of a session in the dynamic session
     pool.
    :vartype lifecycle_configuration: ~azure.mgmt.appcontainers.models.LifecycleConfiguration
    """

    _attribute_map = {
        "lifecycle_configuration": {"key": "lifecycleConfiguration", "type": "LifecycleConfiguration"},
    }

    def __init__(
        self, *, lifecycle_configuration: Optional["LifecycleConfiguration"] = None, **kwargs: Any
    ) -> None:
        """
        :keyword lifecycle_configuration: The lifecycle configuration of a session in the dynamic
         session pool.
        :paramtype lifecycle_configuration: ~azure.mgmt.appcontainers.models.LifecycleConfiguration
        """
        super().__init__(**kwargs)
        self.lifecycle_configuration = lifecycle_configuration


class EncryptionSettings(_serialization.Model):
    """The configuration settings of the secrets references of encryption key and signing key for
    ContainerApp Service Authentication/Authorization.

    :ivar container_app_auth_encryption_secret_name: The secret name which is referenced for
     EncryptionKey.
    :vartype container_app_auth_encryption_secret_name: str
    :ivar container_app_auth_signing_secret_name: The secret name which is referenced for
     SigningKey.
    :vartype container_app_auth_signing_secret_name: str
    """

    _attribute_map = {
        "container_app_auth_encryption_secret_name": {"key": "containerAppAuthEncryptionSecretName", "type": "str"},
        "container_app_auth_signing_secret_name": {"key": "containerAppAuthSigningSecretName", "type": "str"},
    }

    def __init__(
        self,
        *,
        container_app_auth_encryption_secret_name: Optional[str] = None,
        container_app_auth_signing_secret_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword container_app_auth_encryption_secret_name: The secret name which is referenced for
         EncryptionKey.
        :paramtype container_app_auth_encryption_secret_name: str
        :keyword container_app_auth_signing_secret_name: The secret name which is referenced for
         SigningKey.
        :paramtype container_app_auth_signing_secret_name: str
        """
        super().__init__(**kwargs)
        self.container_app_auth_encryption_secret_name = container_app_auth_encryption_secret_name
        self.container_app_auth_signing_secret_name = container_app_auth_signing_secret_name


class EnvironmentAuthToken(TrackedResource):
    """Environment Auth Token.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar token: Auth token value.
    :vartype token: str
    :ivar expires: Token expiration date.
    :vartype expires: ~datetime.datetime
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "token": {"readonly": True},
        "expires": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "token": {"key": "properties.token", "type": "str"},
        "expires": {"key": "properties.expires", "type": "iso-8601"},
    }

    def __init__(self, *, location: str, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.token = None
        self.expires = None


class EnvironmentVar(_serialization.Model):
    """Container App container environment variable.

    :ivar name: Environment variable name.
    :vartype name: str
    :ivar value: Non-secret environment variable value.
    :vartype value: str
    :ivar secret_ref: Name of the Container App secret from which to pull the environment variable
     value.
    :vartype secret_ref: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
        "secret_ref": {"key": "secretRef", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        value: Optional[str] = None,
        secret_ref: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Environment variable name.
        :paramtype name: str
        :keyword value: Non-secret environment variable value.
        :paramtype value: str
        :keyword secret_ref: Name of the Container App secret from which to pull the environment
         variable value.
        :paramtype secret_ref: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.value = value
        self.secret_ref = secret_ref


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.appcontainers.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info: list[~azure.mgmt.appcontainers.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.appcontainers.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.appcontainers.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class ExtendedLocation(_serialization.Model):
    """The complex type of the extended location.

    :ivar name: The name of the extended location.
    :vartype name: str
    :ivar type: The type of the extended location. "CustomLocation"
    :vartype type: str or ~azure.mgmt.appcontainers.models.ExtendedLocationTypes
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        type: Optional[Union[str, "ExtendedLocationTypes"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: The name of the extended location.
        :paramtype name: str
        :keyword type: The type of the extended location. "CustomLocation"
        :paramtype type: str or ~azure.mgmt.appcontainers.models.ExtendedLocationTypes
        """
        super().__init__(**kwargs)
        self.name = name
        self.type = type


class Facebook(_serialization.Model):
    """The configuration settings of the Facebook provider.

    :ivar enabled: :code:`<code>false</code>` if the Facebook provider should not be enabled
     despite the set registration; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the app registration for the Facebook
     provider.
    :vartype registration: ~azure.mgmt.appcontainers.models.AppRegistration
    :ivar graph_api_version: The version of the Facebook api to be used while logging in.
    :vartype graph_api_version: str
    :ivar login: The configuration settings of the login flow.
    :vartype login: ~azure.mgmt.appcontainers.models.LoginScopes
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "AppRegistration"},
        "graph_api_version": {"key": "graphApiVersion", "type": "str"},
        "login": {"key": "login", "type": "LoginScopes"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["AppRegistration"] = None,
        graph_api_version: Optional[str] = None,
        login: Optional["LoginScopes"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the Facebook provider should not be enabled
         despite the set registration; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the app registration for the Facebook
         provider.
        :paramtype registration: ~azure.mgmt.appcontainers.models.AppRegistration
        :keyword graph_api_version: The version of the Facebook api to be used while logging in.
        :paramtype graph_api_version: str
        :keyword login: The configuration settings of the login flow.
        :paramtype login: ~azure.mgmt.appcontainers.models.LoginScopes
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration
        self.graph_api_version = graph_api_version
        self.login = login


class ForwardProxy(_serialization.Model):
    """The configuration settings of a forward proxy used to make the requests.

    :ivar convention: The convention used to determine the url of the request made. Known values
     are: "NoProxy", "Standard", and "Custom".
    :vartype convention: str or ~azure.mgmt.appcontainers.models.ForwardProxyConvention
    :ivar custom_host_header_name: The name of the header containing the host of the request.
    :vartype custom_host_header_name: str
    :ivar custom_proto_header_name: The name of the header containing the scheme of the request.
    :vartype custom_proto_header_name: str
    """

    _attribute_map = {
        "convention": {"key": "convention", "type": "str"},
        "custom_host_header_name": {"key": "customHostHeaderName", "type": "str"},
        "custom_proto_header_name": {"key": "customProtoHeaderName", "type": "str"},
    }

    def __init__(
        self,
        *,
        convention: Optional[Union[str, "ForwardProxyConvention"]] = None,
        custom_host_header_name: Optional[str] = None,
        custom_proto_header_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword convention: The convention used to determine the url of the request made. Known values
         are: "NoProxy", "Standard", and "Custom".
        :paramtype convention: str or ~azure.mgmt.appcontainers.models.ForwardProxyConvention
        :keyword custom_host_header_name: The name of the header containing the host of the request.
        :paramtype custom_host_header_name: str
        :keyword custom_proto_header_name: The name of the header containing the scheme of the request.
        :paramtype custom_proto_header_name: str
        """
        super().__init__(**kwargs)
        self.convention = convention
        self.custom_host_header_name = custom_host_header_name
        self.custom_proto_header_name = custom_proto_header_name


class GitHub(_serialization.Model):
    """The configuration settings of the GitHub provider.

    :ivar enabled: :code:`<code>false</code>` if the GitHub provider should not be enabled despite
     the set registration; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the app registration for the GitHub provider.
    :vartype registration: ~azure.mgmt.appcontainers.models.ClientRegistration
    :ivar login: The configuration settings of the login flow.
    :vartype login: ~azure.mgmt.appcontainers.models.LoginScopes
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "ClientRegistration"},
        "login": {"key": "login", "type": "LoginScopes"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["ClientRegistration"] = None,
        login: Optional["LoginScopes"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the GitHub provider should not be enabled
         despite the set registration; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the app registration for the GitHub
         provider.
        :paramtype registration: ~azure.mgmt.appcontainers.models.ClientRegistration
        :keyword login: The configuration settings of the login flow.
        :paramtype login: ~azure.mgmt.appcontainers.models.LoginScopes
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration
        self.login = login


class GithubActionConfiguration(_serialization.Model):
    """Configuration properties that define the mutable settings of a Container App SourceControl.

    :ivar registry_info: Registry configurations.
    :vartype registry_info: ~azure.mgmt.appcontainers.models.RegistryInfo
    :ivar azure_credentials: AzureCredentials configurations.
    :vartype azure_credentials: ~azure.mgmt.appcontainers.models.AzureCredentials
    :ivar context_path: Context path.
    :vartype context_path: str
    :ivar github_personal_access_token: One time Github PAT to configure github environment.
    :vartype github_personal_access_token: str
    :ivar image: Image name.
    :vartype image: str
    :ivar publish_type: Code or Image.
    :vartype publish_type: str
    :ivar os: Operation system.
    :vartype os: str
    :ivar runtime_stack: Runtime stack.
    :vartype runtime_stack: str
    :ivar runtime_version: Runtime version.
    :vartype runtime_version: str
    """

    _attribute_map = {
        "registry_info": {"key": "registryInfo", "type": "RegistryInfo"},
        "azure_credentials": {"key": "azureCredentials", "type": "AzureCredentials"},
        "context_path": {"key": "contextPath", "type": "str"},
        "github_personal_access_token": {"key": "githubPersonalAccessToken", "type": "str"},
        "image": {"key": "image", "type": "str"},
        "publish_type": {"key": "publishType", "type": "str"},
        "os": {"key": "os", "type": "str"},
        "runtime_stack": {"key": "runtimeStack", "type": "str"},
        "runtime_version": {"key": "runtimeVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        registry_info: Optional["RegistryInfo"] = None,
        azure_credentials: Optional["AzureCredentials"] = None,
        context_path: Optional[str] = None,
        github_personal_access_token: Optional[str] = None,
        image: Optional[str] = None,
        publish_type: Optional[str] = None,
        os: Optional[str] = None,
        runtime_stack: Optional[str] = None,
        runtime_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword registry_info: Registry configurations.
        :paramtype registry_info: ~azure.mgmt.appcontainers.models.RegistryInfo
        :keyword azure_credentials: AzureCredentials configurations.
        :paramtype azure_credentials: ~azure.mgmt.appcontainers.models.AzureCredentials
        :keyword context_path: Context path.
        :paramtype context_path: str
        :keyword github_personal_access_token: One time Github PAT to configure github environment.
        :paramtype github_personal_access_token: str
        :keyword image: Image name.
        :paramtype image: str
        :keyword publish_type: Code or Image.
        :paramtype publish_type: str
        :keyword os: Operation system.
        :paramtype os: str
        :keyword runtime_stack: Runtime stack.
        :paramtype runtime_stack: str
        :keyword runtime_version: Runtime version.
        :paramtype runtime_version: str
        """
        super().__init__(**kwargs)
        self.registry_info = registry_info
        self.azure_credentials = azure_credentials
        self.context_path = context_path
        self.github_personal_access_token = github_personal_access_token
        self.image = image
        self.publish_type = publish_type
        self.os = os
        self.runtime_stack = runtime_stack
        self.runtime_version = runtime_version


class GlobalValidation(_serialization.Model):
    """The configuration settings that determines the validation flow of users using ContainerApp
    Service Authentication/Authorization.

    :ivar unauthenticated_client_action: The action to take when an unauthenticated client attempts
     to access the app. Known values are: "RedirectToLoginPage", "AllowAnonymous", "Return401", and
     "Return403".
    :vartype unauthenticated_client_action: str or
     ~azure.mgmt.appcontainers.models.UnauthenticatedClientActionV2
    :ivar redirect_to_provider: The default authentication provider to use when multiple providers
     are configured.
     This setting is only needed if multiple providers are configured and the unauthenticated
     client
     action is set to "RedirectToLoginPage".
    :vartype redirect_to_provider: str
    :ivar excluded_paths: The paths for which unauthenticated flow would not be redirected to the
     login page.
    :vartype excluded_paths: list[str]
    """

    _attribute_map = {
        "unauthenticated_client_action": {"key": "unauthenticatedClientAction", "type": "str"},
        "redirect_to_provider": {"key": "redirectToProvider", "type": "str"},
        "excluded_paths": {"key": "excludedPaths", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        unauthenticated_client_action: Optional[Union[str, "UnauthenticatedClientActionV2"]] = None,
        redirect_to_provider: Optional[str] = None,
        excluded_paths: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword unauthenticated_client_action: The action to take when an unauthenticated client
         attempts to access the app. Known values are: "RedirectToLoginPage", "AllowAnonymous",
         "Return401", and "Return403".
        :paramtype unauthenticated_client_action: str or
         ~azure.mgmt.appcontainers.models.UnauthenticatedClientActionV2
        :keyword redirect_to_provider: The default authentication provider to use when multiple
         providers are configured.
         This setting is only needed if multiple providers are configured and the unauthenticated
         client
         action is set to "RedirectToLoginPage".
        :paramtype redirect_to_provider: str
        :keyword excluded_paths: The paths for which unauthenticated flow would not be redirected to
         the login page.
        :paramtype excluded_paths: list[str]
        """
        super().__init__(**kwargs)
        self.unauthenticated_client_action = unauthenticated_client_action
        self.redirect_to_provider = redirect_to_provider
        self.excluded_paths = excluded_paths


class Google(_serialization.Model):
    """The configuration settings of the Google provider.

    :ivar enabled: :code:`<code>false</code>` if the Google provider should not be enabled despite
     the set registration; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the app registration for the Google provider.
    :vartype registration: ~azure.mgmt.appcontainers.models.ClientRegistration
    :ivar login: The configuration settings of the login flow.
    :vartype login: ~azure.mgmt.appcontainers.models.LoginScopes
    :ivar validation: The configuration settings of the Azure Active Directory token validation
     flow.
    :vartype validation: ~azure.mgmt.appcontainers.models.AllowedAudiencesValidation
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "ClientRegistration"},
        "login": {"key": "login", "type": "LoginScopes"},
        "validation": {"key": "validation", "type": "AllowedAudiencesValidation"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["ClientRegistration"] = None,
        login: Optional["LoginScopes"] = None,
        validation: Optional["AllowedAudiencesValidation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the Google provider should not be enabled
         despite the set registration; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the app registration for the Google
         provider.
        :paramtype registration: ~azure.mgmt.appcontainers.models.ClientRegistration
        :keyword login: The configuration settings of the login flow.
        :paramtype login: ~azure.mgmt.appcontainers.models.LoginScopes
        :keyword validation: The configuration settings of the Azure Active Directory token validation
         flow.
        :paramtype validation: ~azure.mgmt.appcontainers.models.AllowedAudiencesValidation
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration
        self.login = login
        self.validation = validation


class HttpScaleRule(_serialization.Model):
    """Container App container Http scaling rule.

    :ivar metadata: Metadata properties to describe http scale rule.
    :vartype metadata: dict[str, str]
    :ivar auth: Authentication secrets for the custom scale rule.
    :vartype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
    :ivar identity: The resource ID of a user-assigned managed identity that is assigned to the
     Container App, or 'system' for system-assigned identity.
    :vartype identity: str
    """

    _attribute_map = {
        "metadata": {"key": "metadata", "type": "{str}"},
        "auth": {"key": "auth", "type": "[ScaleRuleAuth]"},
        "identity": {"key": "identity", "type": "str"},
    }

    def __init__(
        self,
        *,
        metadata: Optional[Dict[str, str]] = None,
        auth: Optional[List["ScaleRuleAuth"]] = None,
        identity: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword metadata: Metadata properties to describe http scale rule.
        :paramtype metadata: dict[str, str]
        :keyword auth: Authentication secrets for the custom scale rule.
        :paramtype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
        :keyword identity: The resource ID of a user-assigned managed identity that is assigned to the
         Container App, or 'system' for system-assigned identity.
        :paramtype identity: str
        """
        super().__init__(**kwargs)
        self.metadata = metadata
        self.auth = auth
        self.identity = identity


class HttpSettings(_serialization.Model):
    """The configuration settings of the HTTP requests for authentication and authorization requests
    made against ContainerApp Service Authentication/Authorization.

    :ivar require_https: :code:`<code>false</code>` if the authentication/authorization responses
     not having the HTTPS scheme are permissible; otherwise, :code:`<code>true</code>`.
    :vartype require_https: bool
    :ivar routes: The configuration settings of the paths HTTP requests.
    :vartype routes: ~azure.mgmt.appcontainers.models.HttpSettingsRoutes
    :ivar forward_proxy: The configuration settings of a forward proxy used to make the requests.
    :vartype forward_proxy: ~azure.mgmt.appcontainers.models.ForwardProxy
    """

    _attribute_map = {
        "require_https": {"key": "requireHttps", "type": "bool"},
        "routes": {"key": "routes", "type": "HttpSettingsRoutes"},
        "forward_proxy": {"key": "forwardProxy", "type": "ForwardProxy"},
    }

    def __init__(
        self,
        *,
        require_https: Optional[bool] = None,
        routes: Optional["HttpSettingsRoutes"] = None,
        forward_proxy: Optional["ForwardProxy"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword require_https: :code:`<code>false</code>` if the authentication/authorization
         responses not having the HTTPS scheme are permissible; otherwise, :code:`<code>true</code>`.
        :paramtype require_https: bool
        :keyword routes: The configuration settings of the paths HTTP requests.
        :paramtype routes: ~azure.mgmt.appcontainers.models.HttpSettingsRoutes
        :keyword forward_proxy: The configuration settings of a forward proxy used to make the
         requests.
        :paramtype forward_proxy: ~azure.mgmt.appcontainers.models.ForwardProxy
        """
        super().__init__(**kwargs)
        self.require_https = require_https
        self.routes = routes
        self.forward_proxy = forward_proxy


class HttpSettingsRoutes(_serialization.Model):
    """The configuration settings of the paths HTTP requests.

    :ivar api_prefix: The prefix that should precede all the authentication/authorization paths.
    :vartype api_prefix: str
    """

    _attribute_map = {
        "api_prefix": {"key": "apiPrefix", "type": "str"},
    }

    def __init__(self, *, api_prefix: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword api_prefix: The prefix that should precede all the authentication/authorization paths.
        :paramtype api_prefix: str
        """
        super().__init__(**kwargs)
        self.api_prefix = api_prefix


class IdentityProviders(_serialization.Model):
    """The configuration settings of each of the identity providers used to configure ContainerApp
    Service Authentication/Authorization.

    :ivar azure_active_directory: The configuration settings of the Azure Active directory
     provider.
    :vartype azure_active_directory: ~azure.mgmt.appcontainers.models.AzureActiveDirectory
    :ivar facebook: The configuration settings of the Facebook provider.
    :vartype facebook: ~azure.mgmt.appcontainers.models.Facebook
    :ivar git_hub: The configuration settings of the GitHub provider.
    :vartype git_hub: ~azure.mgmt.appcontainers.models.GitHub
    :ivar google: The configuration settings of the Google provider.
    :vartype google: ~azure.mgmt.appcontainers.models.Google
    :ivar twitter: The configuration settings of the Twitter provider.
    :vartype twitter: ~azure.mgmt.appcontainers.models.Twitter
    :ivar apple: The configuration settings of the Apple provider.
    :vartype apple: ~azure.mgmt.appcontainers.models.Apple
    :ivar azure_static_web_apps: The configuration settings of the Azure Static Web Apps provider.
    :vartype azure_static_web_apps: ~azure.mgmt.appcontainers.models.AzureStaticWebApps
    :ivar custom_open_id_connect_providers: The map of the name of the alias of each custom Open ID
     Connect provider to the
     configuration settings of the custom Open ID Connect provider.
    :vartype custom_open_id_connect_providers: dict[str,
     ~azure.mgmt.appcontainers.models.CustomOpenIdConnectProvider]
    """

    _attribute_map = {
        "azure_active_directory": {"key": "azureActiveDirectory", "type": "AzureActiveDirectory"},
        "facebook": {"key": "facebook", "type": "Facebook"},
        "git_hub": {"key": "gitHub", "type": "GitHub"},
        "google": {"key": "google", "type": "Google"},
        "twitter": {"key": "twitter", "type": "Twitter"},
        "apple": {"key": "apple", "type": "Apple"},
        "azure_static_web_apps": {"key": "azureStaticWebApps", "type": "AzureStaticWebApps"},
        "custom_open_id_connect_providers": {
            "key": "customOpenIdConnectProviders",
            "type": "{CustomOpenIdConnectProvider}",
        },
    }

    def __init__(
        self,
        *,
        azure_active_directory: Optional["AzureActiveDirectory"] = None,
        facebook: Optional["Facebook"] = None,
        git_hub: Optional["GitHub"] = None,
        google: Optional["Google"] = None,
        twitter: Optional["Twitter"] = None,
        apple: Optional["Apple"] = None,
        azure_static_web_apps: Optional["AzureStaticWebApps"] = None,
        custom_open_id_connect_providers: Optional[Dict[str, "CustomOpenIdConnectProvider"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword azure_active_directory: The configuration settings of the Azure Active directory
         provider.
        :paramtype azure_active_directory: ~azure.mgmt.appcontainers.models.AzureActiveDirectory
        :keyword facebook: The configuration settings of the Facebook provider.
        :paramtype facebook: ~azure.mgmt.appcontainers.models.Facebook
        :keyword git_hub: The configuration settings of the GitHub provider.
        :paramtype git_hub: ~azure.mgmt.appcontainers.models.GitHub
        :keyword google: The configuration settings of the Google provider.
        :paramtype google: ~azure.mgmt.appcontainers.models.Google
        :keyword twitter: The configuration settings of the Twitter provider.
        :paramtype twitter: ~azure.mgmt.appcontainers.models.Twitter
        :keyword apple: The configuration settings of the Apple provider.
        :paramtype apple: ~azure.mgmt.appcontainers.models.Apple
        :keyword azure_static_web_apps: The configuration settings of the Azure Static Web Apps
         provider.
        :paramtype azure_static_web_apps: ~azure.mgmt.appcontainers.models.AzureStaticWebApps
        :keyword custom_open_id_connect_providers: The map of the name of the alias of each custom Open
         ID Connect provider to the
         configuration settings of the custom Open ID Connect provider.
        :paramtype custom_open_id_connect_providers: dict[str,
         ~azure.mgmt.appcontainers.models.CustomOpenIdConnectProvider]
        """
        super().__init__(**kwargs)
        self.azure_active_directory = azure_active_directory
        self.facebook = facebook
        self.git_hub = git_hub
        self.google = google
        self.twitter = twitter
        self.apple = apple
        self.azure_static_web_apps = azure_static_web_apps
        self.custom_open_id_connect_providers = custom_open_id_connect_providers


class IdentitySettings(_serialization.Model):
    """Optional settings for a Managed Identity that is assigned to the Container App.

    All required parameters must be populated in order to send to server.

    :ivar identity: The resource ID of a user-assigned managed identity that is assigned to the
     Container App, or 'system' for system-assigned identity. Required.
    :vartype identity: str
    :ivar lifecycle: Use to select the lifecycle stages of a Container App during which the Managed
     Identity should be available. Known values are: "Init", "Main", "None", and "All".
    :vartype lifecycle: str or ~azure.mgmt.appcontainers.models.IdentitySettingsLifeCycle
    """

    _validation = {
        "identity": {"required": True},
    }

    _attribute_map = {
        "identity": {"key": "identity", "type": "str"},
        "lifecycle": {"key": "lifecycle", "type": "str"},
    }

    def __init__(
        self,
        *,
        identity: str,
        lifecycle: Optional[Union[str, "IdentitySettingsLifeCycle"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword identity: The resource ID of a user-assigned managed identity that is assigned to the
         Container App, or 'system' for system-assigned identity. Required.
        :paramtype identity: str
        :keyword lifecycle: Use to select the lifecycle stages of a Container App during which the
         Managed Identity should be available. Known values are: "Init", "Main", "None", and "All".
        :paramtype lifecycle: str or ~azure.mgmt.appcontainers.models.IdentitySettingsLifeCycle
        """
        super().__init__(**kwargs)
        self.identity = identity
        self.lifecycle = lifecycle


class Ingress(_serialization.Model):
    """Container App Ingress configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar fqdn: Hostname.
    :vartype fqdn: str
    :ivar external: Bool indicating if app exposes an external http endpoint.
    :vartype external: bool
    :ivar target_port: Target Port in containers for traffic from ingress.
    :vartype target_port: int
    :ivar exposed_port: Exposed Port in containers for TCP traffic from ingress.
    :vartype exposed_port: int
    :ivar transport: Ingress transport protocol. Known values are: "auto", "http", "http2", and
     "tcp".
    :vartype transport: str or ~azure.mgmt.appcontainers.models.IngressTransportMethod
    :ivar traffic: Traffic weights for app's revisions.
    :vartype traffic: list[~azure.mgmt.appcontainers.models.TrafficWeight]
    :ivar custom_domains: custom domain bindings for Container Apps' hostnames.
    :vartype custom_domains: list[~azure.mgmt.appcontainers.models.CustomDomain]
    :ivar allow_insecure: Bool indicating if HTTP connections to is allowed. If set to false HTTP
     connections are automatically redirected to HTTPS connections.
    :vartype allow_insecure: bool
    :ivar ip_security_restrictions: Rules to restrict incoming IP address.
    :vartype ip_security_restrictions:
     list[~azure.mgmt.appcontainers.models.IpSecurityRestrictionRule]
    :ivar sticky_sessions: Sticky Sessions for Single Revision Mode.
    :vartype sticky_sessions: ~azure.mgmt.appcontainers.models.IngressStickySessions
    :ivar client_certificate_mode: Client certificate mode for mTLS authentication. Ignore
     indicates server drops client certificate on forwarding. Accept indicates server forwards
     client certificate but does not require a client certificate. Require indicates server requires
     a client certificate. Known values are: "ignore", "accept", and "require".
    :vartype client_certificate_mode: str or
     ~azure.mgmt.appcontainers.models.IngressClientCertificateMode
    :ivar cors_policy: CORS policy for container app.
    :vartype cors_policy: ~azure.mgmt.appcontainers.models.CorsPolicy
    :ivar additional_port_mappings: Settings to expose additional ports on container app.
    :vartype additional_port_mappings: list[~azure.mgmt.appcontainers.models.IngressPortMapping]
    """

    _validation = {
        "fqdn": {"readonly": True},
    }

    _attribute_map = {
        "fqdn": {"key": "fqdn", "type": "str"},
        "external": {"key": "external", "type": "bool"},
        "target_port": {"key": "targetPort", "type": "int"},
        "exposed_port": {"key": "exposedPort", "type": "int"},
        "transport": {"key": "transport", "type": "str"},
        "traffic": {"key": "traffic", "type": "[TrafficWeight]"},
        "custom_domains": {"key": "customDomains", "type": "[CustomDomain]"},
        "allow_insecure": {"key": "allowInsecure", "type": "bool"},
        "ip_security_restrictions": {"key": "ipSecurityRestrictions", "type": "[IpSecurityRestrictionRule]"},
        "sticky_sessions": {"key": "stickySessions", "type": "IngressStickySessions"},
        "client_certificate_mode": {"key": "clientCertificateMode", "type": "str"},
        "cors_policy": {"key": "corsPolicy", "type": "CorsPolicy"},
        "additional_port_mappings": {"key": "additionalPortMappings", "type": "[IngressPortMapping]"},
    }

    def __init__(
        self,
        *,
        external: bool = False,
        target_port: Optional[int] = None,
        exposed_port: Optional[int] = None,
        transport: Union[str, "IngressTransportMethod"] = "auto",
        traffic: Optional[List["TrafficWeight"]] = None,
        custom_domains: Optional[List["CustomDomain"]] = None,
        allow_insecure: bool = False,
        ip_security_restrictions: Optional[List["IpSecurityRestrictionRule"]] = None,
        sticky_sessions: Optional["IngressStickySessions"] = None,
        client_certificate_mode: Optional[Union[str, "IngressClientCertificateMode"]] = None,
        cors_policy: Optional["CorsPolicy"] = None,
        additional_port_mappings: Optional[List["IngressPortMapping"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword external: Bool indicating if app exposes an external http endpoint.
        :paramtype external: bool
        :keyword target_port: Target Port in containers for traffic from ingress.
        :paramtype target_port: int
        :keyword exposed_port: Exposed Port in containers for TCP traffic from ingress.
        :paramtype exposed_port: int
        :keyword transport: Ingress transport protocol. Known values are: "auto", "http", "http2", and
         "tcp".
        :paramtype transport: str or ~azure.mgmt.appcontainers.models.IngressTransportMethod
        :keyword traffic: Traffic weights for app's revisions.
        :paramtype traffic: list[~azure.mgmt.appcontainers.models.TrafficWeight]
        :keyword custom_domains: custom domain bindings for Container Apps' hostnames.
        :paramtype custom_domains: list[~azure.mgmt.appcontainers.models.CustomDomain]
        :keyword allow_insecure: Bool indicating if HTTP connections to is allowed. If set to false
         HTTP connections are automatically redirected to HTTPS connections.
        :paramtype allow_insecure: bool
        :keyword ip_security_restrictions: Rules to restrict incoming IP address.
        :paramtype ip_security_restrictions:
         list[~azure.mgmt.appcontainers.models.IpSecurityRestrictionRule]
        :keyword sticky_sessions: Sticky Sessions for Single Revision Mode.
        :paramtype sticky_sessions: ~azure.mgmt.appcontainers.models.IngressStickySessions
        :keyword client_certificate_mode: Client certificate mode for mTLS authentication. Ignore
         indicates server drops client certificate on forwarding. Accept indicates server forwards
         client certificate but does not require a client certificate. Require indicates server requires
         a client certificate. Known values are: "ignore", "accept", and "require".
        :paramtype client_certificate_mode: str or
         ~azure.mgmt.appcontainers.models.IngressClientCertificateMode
        :keyword cors_policy: CORS policy for container app.
        :paramtype cors_policy: ~azure.mgmt.appcontainers.models.CorsPolicy
        :keyword additional_port_mappings: Settings to expose additional ports on container app.
        :paramtype additional_port_mappings: list[~azure.mgmt.appcontainers.models.IngressPortMapping]
        """
        super().__init__(**kwargs)
        self.fqdn = None
        self.external = external
        self.target_port = target_port
        self.exposed_port = exposed_port
        self.transport = transport
        self.traffic = traffic
        self.custom_domains = custom_domains
        self.allow_insecure = allow_insecure
        self.ip_security_restrictions = ip_security_restrictions
        self.sticky_sessions = sticky_sessions
        self.client_certificate_mode = client_certificate_mode
        self.cors_policy = cors_policy
        self.additional_port_mappings = additional_port_mappings


class IngressPortMapping(_serialization.Model):
    """Port mappings of container app ingress.

    All required parameters must be populated in order to send to server.

    :ivar external: Specifies whether the app port is accessible outside of the environment.
     Required.
    :vartype external: bool
    :ivar target_port: Specifies the port user's container listens on. Required.
    :vartype target_port: int
    :ivar exposed_port: Specifies the exposed port for the target port. If not specified, it
     defaults to target port.
    :vartype exposed_port: int
    """

    _validation = {
        "external": {"required": True},
        "target_port": {"required": True},
    }

    _attribute_map = {
        "external": {"key": "external", "type": "bool"},
        "target_port": {"key": "targetPort", "type": "int"},
        "exposed_port": {"key": "exposedPort", "type": "int"},
    }

    def __init__(self, *, external: bool, target_port: int, exposed_port: Optional[int] = None, **kwargs: Any) -> None:
        """
        :keyword external: Specifies whether the app port is accessible outside of the environment.
         Required.
        :paramtype external: bool
        :keyword target_port: Specifies the port user's container listens on. Required.
        :paramtype target_port: int
        :keyword exposed_port: Specifies the exposed port for the target port. If not specified, it
         defaults to target port.
        :paramtype exposed_port: int
        """
        super().__init__(**kwargs)
        self.external = external
        self.target_port = target_port
        self.exposed_port = exposed_port


class IngressStickySessions(_serialization.Model):
    """Sticky Sessions for Single Revision Mode.

    :ivar affinity: Sticky Session Affinity. Known values are: "sticky" and "none".
    :vartype affinity: str or ~azure.mgmt.appcontainers.models.Affinity
    """

    _attribute_map = {
        "affinity": {"key": "affinity", "type": "str"},
    }

    def __init__(self, *, affinity: Optional[Union[str, "Affinity"]] = None, **kwargs: Any) -> None:
        """
        :keyword affinity: Sticky Session Affinity. Known values are: "sticky" and "none".
        :paramtype affinity: str or ~azure.mgmt.appcontainers.models.Affinity
        """
        super().__init__(**kwargs)
        self.affinity = affinity


class InitContainer(BaseContainer):
    """Container App init container definition.

    :ivar image: Container image tag.
    :vartype image: str
    :ivar name: Custom container name.
    :vartype name: str
    :ivar command: Container start command.
    :vartype command: list[str]
    :ivar args: Container start command arguments.
    :vartype args: list[str]
    :ivar env: Container environment variables.
    :vartype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
    :ivar resources: Container resource requirements.
    :vartype resources: ~azure.mgmt.appcontainers.models.ContainerResources
    :ivar volume_mounts: Container volume mounts.
    :vartype volume_mounts: list[~azure.mgmt.appcontainers.models.VolumeMount]
    """


class IpSecurityRestrictionRule(_serialization.Model):
    """Rule to restrict incoming IP address.

    All required parameters must be populated in order to send to server.

    :ivar name: Name for the IP restriction rule. Required.
    :vartype name: str
    :ivar description: Describe the IP restriction rule that is being sent to the container-app.
     This is an optional field.
    :vartype description: str
    :ivar ip_address_range: CIDR notation to match incoming IP address. Required.
    :vartype ip_address_range: str
    :ivar action: Allow or Deny rules to determine for incoming IP. Note: Rules can only consist of
     ALL Allow or ALL Deny. Required. Known values are: "Allow" and "Deny".
    :vartype action: str or ~azure.mgmt.appcontainers.models.Action
    """

    _validation = {
        "name": {"required": True},
        "ip_address_range": {"required": True},
        "action": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "ip_address_range": {"key": "ipAddressRange", "type": "str"},
        "action": {"key": "action", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: str,
        ip_address_range: str,
        action: Union[str, "Action"],
        description: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Name for the IP restriction rule. Required.
        :paramtype name: str
        :keyword description: Describe the IP restriction rule that is being sent to the container-app.
         This is an optional field.
        :paramtype description: str
        :keyword ip_address_range: CIDR notation to match incoming IP address. Required.
        :paramtype ip_address_range: str
        :keyword action: Allow or Deny rules to determine for incoming IP. Note: Rules can only consist
         of ALL Allow or ALL Deny. Required. Known values are: "Allow" and "Deny".
        :paramtype action: str or ~azure.mgmt.appcontainers.models.Action
        """
        super().__init__(**kwargs)
        self.name = name
        self.description = description
        self.ip_address_range = ip_address_range
        self.action = action


class JavaComponent(ProxyResource):
    """Java Component.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar properties: Java Component resource specific properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.JavaComponentProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "JavaComponentProperties"},
    }

    def __init__(self, *, properties: Optional["JavaComponentProperties"] = None, **kwargs: Any) -> None:
        """
        :keyword properties: Java Component resource specific properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.JavaComponentProperties
        """
        super().__init__(**kwargs)
        self.properties = properties


class JavaComponentConfigurationProperty(_serialization.Model):
    """Configuration properties for a Java Component.

    :ivar property_name: The name of the property.
    :vartype property_name: str
    :ivar value: The value of the property.
    :vartype value: str
    """

    _attribute_map = {
        "property_name": {"key": "propertyName", "type": "str"},
        "value": {"key": "value", "type": "str"},
    }

    def __init__(self, *, property_name: Optional[str] = None, value: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword property_name: The name of the property.
        :paramtype property_name: str
        :keyword value: The value of the property.
        :paramtype value: str
        """
        super().__init__(**kwargs)
        self.property_name = property_name
        self.value = value


class JavaComponentIngress(_serialization.Model):
    """Container App Ingress configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar fqdn: Hostname of the Java Component endpoint.
    :vartype fqdn: str
    """

    _validation = {
        "fqdn": {"readonly": True},
    }

    _attribute_map = {
        "fqdn": {"key": "fqdn", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.fqdn = None


class JavaComponentProperties(_serialization.Model):
    """Java Component common properties.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    SpringBootAdminComponent, SpringCloudConfigComponent, SpringCloudEurekaComponent

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar component_type: Type of the Java Component. Required. Known values are:
     "SpringBootAdmin", "SpringCloudEureka", and "SpringCloudConfig".
    :vartype component_type: str or ~azure.mgmt.appcontainers.models.JavaComponentType
    :ivar provisioning_state: Provisioning state of the Java Component. Known values are:
     "Succeeded", "Failed", "Canceled", "Deleting", and "InProgress".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.JavaComponentProvisioningState
    :ivar configurations: List of Java Components configuration properties.
    :vartype configurations:
     list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
    :ivar scale: Java component scaling configurations.
    :vartype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
    :ivar service_binds: List of Java Components that are bound to the Java component.
    :vartype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
    """

    _validation = {
        "component_type": {"required": True},
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "component_type": {"key": "componentType", "type": "str"},
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "configurations": {"key": "configurations", "type": "[JavaComponentConfigurationProperty]"},
        "scale": {"key": "scale", "type": "JavaComponentPropertiesScale"},
        "service_binds": {"key": "serviceBinds", "type": "[JavaComponentServiceBind]"},
    }

    _subtype_map = {
        "component_type": {
            "SpringBootAdmin": "SpringBootAdminComponent",
            "SpringCloudConfig": "SpringCloudConfigComponent",
            "SpringCloudEureka": "SpringCloudEurekaComponent",
        }
    }

    def __init__(
        self,
        *,
        configurations: Optional[List["JavaComponentConfigurationProperty"]] = None,
        scale: Optional["JavaComponentPropertiesScale"] = None,
        service_binds: Optional[List["JavaComponentServiceBind"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword configurations: List of Java Components configuration properties.
        :paramtype configurations:
         list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
        :keyword scale: Java component scaling configurations.
        :paramtype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
        :keyword service_binds: List of Java Components that are bound to the Java component.
        :paramtype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
        """
        super().__init__(**kwargs)
        self.component_type: Optional[str] = None
        self.provisioning_state = None
        self.configurations = configurations
        self.scale = scale
        self.service_binds = service_binds


class JavaComponentPropertiesScale(_serialization.Model):
    """Java component scaling configurations.

    :ivar min_replicas: Optional. Minimum number of Java component replicas. Defaults to 1 if not
     set.
    :vartype min_replicas: int
    :ivar max_replicas: Optional. Maximum number of Java component replicas.
    :vartype max_replicas: int
    """

    _attribute_map = {
        "min_replicas": {"key": "minReplicas", "type": "int"},
        "max_replicas": {"key": "maxReplicas", "type": "int"},
    }

    def __init__(
        self, *, min_replicas: Optional[int] = None, max_replicas: Optional[int] = None, **kwargs: Any
    ) -> None:
        """
        :keyword min_replicas: Optional. Minimum number of Java component replicas. Defaults to 1 if
         not set.
        :paramtype min_replicas: int
        :keyword max_replicas: Optional. Maximum number of Java component replicas.
        :paramtype max_replicas: int
        """
        super().__init__(**kwargs)
        self.min_replicas = min_replicas
        self.max_replicas = max_replicas


class JavaComponentsCollection(_serialization.Model):
    """Java Components ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.JavaComponent]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[JavaComponent]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["JavaComponent"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.JavaComponent]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class JavaComponentServiceBind(_serialization.Model):
    """Configuration to bind a Java Component to another Java Component.

    :ivar name: Name of the service bind.
    :vartype name: str
    :ivar service_id: Resource id of the target service.
    :vartype service_id: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "service_id": {"key": "serviceId", "type": "str"},
    }

    def __init__(self, *, name: Optional[str] = None, service_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword name: Name of the service bind.
        :paramtype name: str
        :keyword service_id: Resource id of the target service.
        :paramtype service_id: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.service_id = service_id


class ContainerAppsJob(TrackedResource):
    """Container App Job.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar identity: Managed identities needed by a container app job to interact with other Azure
     services to not maintain any secrets or credentials in code.
    :vartype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
    :ivar provisioning_state: Provisioning state of the Container Apps Job. Known values are:
     "InProgress", "Succeeded", "Failed", "Canceled", and "Deleting".
    :vartype provisioning_state: str or ~azure.mgmt.appcontainers.models.JobProvisioningState
    :ivar environment_id: Resource ID of environment.
    :vartype environment_id: str
    :ivar workload_profile_name: Workload profile name to pin for container apps job execution.
    :vartype workload_profile_name: str
    :ivar configuration: Container Apps Job configuration properties.
    :vartype configuration: ~azure.mgmt.appcontainers.models.JobConfiguration
    :ivar template: Container Apps job definition.
    :vartype template: ~azure.mgmt.appcontainers.models.JobTemplate
    :ivar outbound_ip_addresses: Outbound IP Addresses of a container apps job.
    :vartype outbound_ip_addresses: list[str]
    :ivar event_stream_endpoint: The endpoint of the eventstream of the container apps job.
    :vartype event_stream_endpoint: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "provisioning_state": {"readonly": True},
        "outbound_ip_addresses": {"readonly": True},
        "event_stream_endpoint": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "identity": {"key": "identity", "type": "ManagedServiceIdentity"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "environment_id": {"key": "properties.environmentId", "type": "str"},
        "workload_profile_name": {"key": "properties.workloadProfileName", "type": "str"},
        "configuration": {"key": "properties.configuration", "type": "JobConfiguration"},
        "template": {"key": "properties.template", "type": "JobTemplate"},
        "outbound_ip_addresses": {"key": "properties.outboundIpAddresses", "type": "[str]"},
        "event_stream_endpoint": {"key": "properties.eventStreamEndpoint", "type": "str"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        identity: Optional["ManagedServiceIdentity"] = None,
        environment_id: Optional[str] = None,
        workload_profile_name: Optional[str] = None,
        configuration: Optional["JobConfiguration"] = None,
        template: Optional["JobTemplate"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword identity: Managed identities needed by a container app job to interact with other
         Azure services to not maintain any secrets or credentials in code.
        :paramtype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
        :keyword environment_id: Resource ID of environment.
        :paramtype environment_id: str
        :keyword workload_profile_name: Workload profile name to pin for container apps job execution.
        :paramtype workload_profile_name: str
        :keyword configuration: Container Apps Job configuration properties.
        :paramtype configuration: ~azure.mgmt.appcontainers.models.JobConfiguration
        :keyword template: Container Apps job definition.
        :paramtype template: ~azure.mgmt.appcontainers.models.JobTemplate
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.identity = identity
        self.provisioning_state = None
        self.environment_id = environment_id
        self.workload_profile_name = workload_profile_name
        self.configuration = configuration
        self.template = template
        self.outbound_ip_addresses = None
        self.event_stream_endpoint = None


class Job(TrackedResource):
    """Container App Job.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar identity: Managed identities needed by a container app job to interact with other Azure
     services to not maintain any secrets or credentials in code.
    :vartype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
    :ivar provisioning_state: Provisioning state of the Container Apps Job. Known values are:
     "InProgress", "Succeeded", "Failed", "Canceled", and "Deleting".
    :vartype provisioning_state: str or ~azure.mgmt.appcontainers.models.JobProvisioningState
    :ivar environment_id: Resource ID of environment.
    :vartype environment_id: str
    :ivar workload_profile_name: Workload profile name to pin for container apps job execution.
    :vartype workload_profile_name: str
    :ivar configuration: Container Apps Job configuration properties.
    :vartype configuration: ~azure.mgmt.appcontainers.models.JobConfiguration
    :ivar template: Container Apps job definition.
    :vartype template: ~azure.mgmt.appcontainers.models.JobTemplate
    :ivar outbound_ip_addresses: Outbound IP Addresses of a container apps job.
    :vartype outbound_ip_addresses: list[str]
    :ivar event_stream_endpoint: The endpoint of the eventstream of the container apps job.
    :vartype event_stream_endpoint: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "provisioning_state": {"readonly": True},
        "outbound_ip_addresses": {"readonly": True},
        "event_stream_endpoint": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "identity": {"key": "identity", "type": "ManagedServiceIdentity"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "environment_id": {"key": "properties.environmentId", "type": "str"},
        "workload_profile_name": {"key": "properties.workloadProfileName", "type": "str"},
        "configuration": {"key": "properties.configuration", "type": "JobConfiguration"},
        "template": {"key": "properties.template", "type": "JobTemplate"},
        "outbound_ip_addresses": {"key": "properties.outboundIpAddresses", "type": "[str]"},
        "event_stream_endpoint": {"key": "properties.eventStreamEndpoint", "type": "str"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        identity: Optional["ManagedServiceIdentity"] = None,
        environment_id: Optional[str] = None,
        workload_profile_name: Optional[str] = None,
        configuration: Optional["JobConfiguration"] = None,
        template: Optional["JobTemplate"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword identity: Managed identities needed by a container app job to interact with other
         Azure services to not maintain any secrets or credentials in code.
        :paramtype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
        :keyword environment_id: Resource ID of environment.
        :paramtype environment_id: str
        :keyword workload_profile_name: Workload profile name to pin for container apps job execution.
        :paramtype workload_profile_name: str
        :keyword configuration: Container Apps Job configuration properties.
        :paramtype configuration: ~azure.mgmt.appcontainers.models.JobConfiguration
        :keyword template: Container Apps job definition.
        :paramtype template: ~azure.mgmt.appcontainers.models.JobTemplate
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.identity = identity
        self.provisioning_state = None
        self.environment_id = environment_id
        self.workload_profile_name = workload_profile_name
        self.configuration = configuration
        self.template = template
        self.outbound_ip_addresses = None
        self.event_stream_endpoint = None


class JobConfiguration(_serialization.Model):
    """Non versioned Container Apps Job configuration properties.

    All required parameters must be populated in order to send to server.

    :ivar secrets: Collection of secrets used by a Container Apps Job.
    :vartype secrets: list[~azure.mgmt.appcontainers.models.Secret]
    :ivar trigger_type: Trigger type of the job. Known values are: "Schedule", "Event", and
     "Manual".
    :vartype trigger_type: str or ~azure.mgmt.appcontainers.models.TriggerType
    :ivar replica_timeout: Maximum number of seconds a replica is allowed to run. Required.
    :vartype replica_timeout: int
    :ivar replica_retry_limit: Maximum number of retries before failing the job.
    :vartype replica_retry_limit: int
    :ivar manual_trigger_config: Manual trigger configuration for a single execution job.
     Properties replicaCompletionCount and parallelism would be set to 1 by default.
    :vartype manual_trigger_config:
     ~azure.mgmt.appcontainers.models.JobConfigurationManualTriggerConfig
    :ivar schedule_trigger_config: Cron formatted repeating trigger schedule ("\\ * * * * *") for
     cronjobs. Properties completions and parallelism would be set to 1 by default.
    :vartype schedule_trigger_config:
     ~azure.mgmt.appcontainers.models.JobConfigurationScheduleTriggerConfig
    :ivar event_trigger_config: Trigger configuration of an event driven job.
    :vartype event_trigger_config:
     ~azure.mgmt.appcontainers.models.JobConfigurationEventTriggerConfig
    :ivar registries: Collection of private container registry credentials used by a Container apps
     job.
    :vartype registries: list[~azure.mgmt.appcontainers.models.RegistryCredentials]
    :ivar identity_settings: Optional settings for Managed Identities that are assigned to the
     Container App Job. If a Managed Identity is not specified here, default settings will be used.
    :vartype identity_settings: list[~azure.mgmt.appcontainers.models.IdentitySettings]
    """

    _validation = {
        "trigger_type": {"required": True},
        "replica_timeout": {"required": True},
    }

    _attribute_map = {
        "secrets": {"key": "secrets", "type": "[Secret]"},
        "trigger_type": {"key": "triggerType", "type": "str"},
        "replica_timeout": {"key": "replicaTimeout", "type": "int"},
        "replica_retry_limit": {"key": "replicaRetryLimit", "type": "int"},
        "manual_trigger_config": {"key": "manualTriggerConfig", "type": "JobConfigurationManualTriggerConfig"},
        "schedule_trigger_config": {"key": "scheduleTriggerConfig", "type": "JobConfigurationScheduleTriggerConfig"},
        "event_trigger_config": {"key": "eventTriggerConfig", "type": "JobConfigurationEventTriggerConfig"},
        "registries": {"key": "registries", "type": "[RegistryCredentials]"},
        "identity_settings": {"key": "identitySettings", "type": "[IdentitySettings]"},
    }

    def __init__(
        self,
        *,
        trigger_type: Union[str, "TriggerType"] = "Manual",
        replica_timeout: int,
        secrets: Optional[List["Secret"]] = None,
        replica_retry_limit: Optional[int] = None,
        manual_trigger_config: Optional["JobConfigurationManualTriggerConfig"] = None,
        schedule_trigger_config: Optional["JobConfigurationScheduleTriggerConfig"] = None,
        event_trigger_config: Optional["JobConfigurationEventTriggerConfig"] = None,
        registries: Optional[List["RegistryCredentials"]] = None,
        identity_settings: Optional[List["IdentitySettings"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword secrets: Collection of secrets used by a Container Apps Job.
        :paramtype secrets: list[~azure.mgmt.appcontainers.models.Secret]
        :keyword trigger_type: Trigger type of the job. Known values are: "Schedule", "Event", and
         "Manual".
        :paramtype trigger_type: str or ~azure.mgmt.appcontainers.models.TriggerType
        :keyword replica_timeout: Maximum number of seconds a replica is allowed to run. Required.
        :paramtype replica_timeout: int
        :keyword replica_retry_limit: Maximum number of retries before failing the job.
        :paramtype replica_retry_limit: int
        :keyword manual_trigger_config: Manual trigger configuration for a single execution job.
         Properties replicaCompletionCount and parallelism would be set to 1 by default.
        :paramtype manual_trigger_config:
         ~azure.mgmt.appcontainers.models.JobConfigurationManualTriggerConfig
        :keyword schedule_trigger_config: Cron formatted repeating trigger schedule ("\\ * * * * *")
         for cronjobs. Properties completions and parallelism would be set to 1 by default.
        :paramtype schedule_trigger_config:
         ~azure.mgmt.appcontainers.models.JobConfigurationScheduleTriggerConfig
        :keyword event_trigger_config: Trigger configuration of an event driven job.
        :paramtype event_trigger_config:
         ~azure.mgmt.appcontainers.models.JobConfigurationEventTriggerConfig
        :keyword registries: Collection of private container registry credentials used by a Container
         apps job.
        :paramtype registries: list[~azure.mgmt.appcontainers.models.RegistryCredentials]
        :keyword identity_settings: Optional settings for Managed Identities that are assigned to the
         Container App Job. If a Managed Identity is not specified here, default settings will be used.
        :paramtype identity_settings: list[~azure.mgmt.appcontainers.models.IdentitySettings]
        """
        super().__init__(**kwargs)
        self.secrets = secrets
        self.trigger_type = trigger_type
        self.replica_timeout = replica_timeout
        self.replica_retry_limit = replica_retry_limit
        self.manual_trigger_config = manual_trigger_config
        self.schedule_trigger_config = schedule_trigger_config
        self.event_trigger_config = event_trigger_config
        self.registries = registries
        self.identity_settings = identity_settings


class JobConfigurationEventTriggerConfig(_serialization.Model):
    """Trigger configuration of an event driven job.

    :ivar replica_completion_count: Minimum number of successful replica completions before overall
     job completion.
    :vartype replica_completion_count: int
    :ivar parallelism: Number of parallel replicas of a job that can run at a given time.
    :vartype parallelism: int
    :ivar scale: Scaling configurations for event driven jobs.
    :vartype scale: ~azure.mgmt.appcontainers.models.JobScale
    """

    _attribute_map = {
        "replica_completion_count": {"key": "replicaCompletionCount", "type": "int"},
        "parallelism": {"key": "parallelism", "type": "int"},
        "scale": {"key": "scale", "type": "JobScale"},
    }

    def __init__(
        self,
        *,
        replica_completion_count: Optional[int] = None,
        parallelism: Optional[int] = None,
        scale: Optional["JobScale"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword replica_completion_count: Minimum number of successful replica completions before
         overall job completion.
        :paramtype replica_completion_count: int
        :keyword parallelism: Number of parallel replicas of a job that can run at a given time.
        :paramtype parallelism: int
        :keyword scale: Scaling configurations for event driven jobs.
        :paramtype scale: ~azure.mgmt.appcontainers.models.JobScale
        """
        super().__init__(**kwargs)
        self.replica_completion_count = replica_completion_count
        self.parallelism = parallelism
        self.scale = scale


class JobConfigurationManualTriggerConfig(_serialization.Model):
    """Manual trigger configuration for a single execution job. Properties replicaCompletionCount and
    parallelism would be set to 1 by default.

    :ivar replica_completion_count: Minimum number of successful replica completions before overall
     job completion.
    :vartype replica_completion_count: int
    :ivar parallelism: Number of parallel replicas of a job that can run at a given time.
    :vartype parallelism: int
    """

    _attribute_map = {
        "replica_completion_count": {"key": "replicaCompletionCount", "type": "int"},
        "parallelism": {"key": "parallelism", "type": "int"},
    }

    def __init__(
        self, *, replica_completion_count: Optional[int] = None, parallelism: Optional[int] = None, **kwargs: Any
    ) -> None:
        """
        :keyword replica_completion_count: Minimum number of successful replica completions before
         overall job completion.
        :paramtype replica_completion_count: int
        :keyword parallelism: Number of parallel replicas of a job that can run at a given time.
        :paramtype parallelism: int
        """
        super().__init__(**kwargs)
        self.replica_completion_count = replica_completion_count
        self.parallelism = parallelism


class JobConfigurationScheduleTriggerConfig(_serialization.Model):
    """Cron formatted repeating trigger schedule ("\\ * * * * *") for cronjobs. Properties completions
    and parallelism would be set to 1 by default.

    All required parameters must be populated in order to send to server.

    :ivar replica_completion_count: Minimum number of successful replica completions before overall
     job completion.
    :vartype replica_completion_count: int
    :ivar cron_expression: Cron formatted repeating schedule ("\\ * * * * *") of a Cron Job.
     Required.
    :vartype cron_expression: str
    :ivar parallelism: Number of parallel replicas of a job that can run at a given time.
    :vartype parallelism: int
    """

    _validation = {
        "cron_expression": {"required": True},
    }

    _attribute_map = {
        "replica_completion_count": {"key": "replicaCompletionCount", "type": "int"},
        "cron_expression": {"key": "cronExpression", "type": "str"},
        "parallelism": {"key": "parallelism", "type": "int"},
    }

    def __init__(
        self,
        *,
        cron_expression: str,
        replica_completion_count: Optional[int] = None,
        parallelism: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword replica_completion_count: Minimum number of successful replica completions before
         overall job completion.
        :paramtype replica_completion_count: int
        :keyword cron_expression: Cron formatted repeating schedule ("\\ * * * * *") of a Cron Job.
         Required.
        :paramtype cron_expression: str
        :keyword parallelism: Number of parallel replicas of a job that can run at a given time.
        :paramtype parallelism: int
        """
        super().__init__(**kwargs)
        self.replica_completion_count = replica_completion_count
        self.cron_expression = cron_expression
        self.parallelism = parallelism


class JobExecution(_serialization.Model):
    """Container Apps Job execution.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar name: Job execution Name.
    :vartype name: str
    :ivar id: Job execution Id.
    :vartype id: str
    :ivar type: Job execution type.
    :vartype type: str
    :ivar status: Current running State of the job. Known values are: "Running", "Processing",
     "Stopped", "Degraded", "Failed", "Unknown", and "Succeeded".
    :vartype status: str or ~azure.mgmt.appcontainers.models.JobExecutionRunningState
    :ivar start_time: Job execution start time.
    :vartype start_time: ~datetime.datetime
    :ivar end_time: Job execution end time.
    :vartype end_time: ~datetime.datetime
    :ivar template: Job's execution container.
    :vartype template: ~azure.mgmt.appcontainers.models.JobExecutionTemplate
    """

    _validation = {
        "status": {"readonly": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "id": {"key": "id", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "start_time": {"key": "properties.startTime", "type": "iso-8601"},
        "end_time": {"key": "properties.endTime", "type": "iso-8601"},
        "template": {"key": "properties.template", "type": "JobExecutionTemplate"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        type: Optional[str] = None,
        start_time: Optional[datetime.datetime] = None,
        end_time: Optional[datetime.datetime] = None,
        template: Optional["JobExecutionTemplate"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Job execution Name.
        :paramtype name: str
        :keyword id: Job execution Id.
        :paramtype id: str
        :keyword type: Job execution type.
        :paramtype type: str
        :keyword start_time: Job execution start time.
        :paramtype start_time: ~datetime.datetime
        :keyword end_time: Job execution end time.
        :paramtype end_time: ~datetime.datetime
        :keyword template: Job's execution container.
        :paramtype template: ~azure.mgmt.appcontainers.models.JobExecutionTemplate
        """
        super().__init__(**kwargs)
        self.name = name
        self.id = id
        self.type = type
        self.status = None
        self.start_time = start_time
        self.end_time = end_time
        self.template = template


class JobExecutionBase(_serialization.Model):
    """Container App's Job execution name.

    :ivar name: Job execution name.
    :vartype name: str
    :ivar id: Job execution Id.
    :vartype id: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "id": {"key": "id", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Job execution name.
        :paramtype name: str
        :keyword id: Job execution Id.
        :paramtype id: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.id = id


class JobExecutionContainer(_serialization.Model):
    """Container Apps Jobs execution container definition.

    :ivar image: Container image tag.
    :vartype image: str
    :ivar name: Custom container name.
    :vartype name: str
    :ivar command: Container start command.
    :vartype command: list[str]
    :ivar args: Container start command arguments.
    :vartype args: list[str]
    :ivar env: Container environment variables.
    :vartype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
    :ivar resources: Container resource requirements.
    :vartype resources: ~azure.mgmt.appcontainers.models.ContainerResources
    """

    _attribute_map = {
        "image": {"key": "image", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "command": {"key": "command", "type": "[str]"},
        "args": {"key": "args", "type": "[str]"},
        "env": {"key": "env", "type": "[EnvironmentVar]"},
        "resources": {"key": "resources", "type": "ContainerResources"},
    }

    def __init__(
        self,
        *,
        image: Optional[str] = None,
        name: Optional[str] = None,
        command: Optional[List[str]] = None,
        args: Optional[List[str]] = None,
        env: Optional[List["EnvironmentVar"]] = None,
        resources: Optional["ContainerResources"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword image: Container image tag.
        :paramtype image: str
        :keyword name: Custom container name.
        :paramtype name: str
        :keyword command: Container start command.
        :paramtype command: list[str]
        :keyword args: Container start command arguments.
        :paramtype args: list[str]
        :keyword env: Container environment variables.
        :paramtype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
        :keyword resources: Container resource requirements.
        :paramtype resources: ~azure.mgmt.appcontainers.models.ContainerResources
        """
        super().__init__(**kwargs)
        self.image = image
        self.name = name
        self.command = command
        self.args = args
        self.env = env
        self.resources = resources


class JobExecutionNamesCollection(_serialization.Model):
    """Container App executions names list.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.JobExecutionBase]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[JobExecutionBase]"},
    }

    def __init__(self, *, value: List["JobExecutionBase"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.JobExecutionBase]
        """
        super().__init__(**kwargs)
        self.value = value


class JobExecutionTemplate(_serialization.Model):
    """Job's execution template, containing container configuration for a job's execution.

    :ivar containers: List of container definitions for the Container Apps Job.
    :vartype containers: list[~azure.mgmt.appcontainers.models.JobExecutionContainer]
    :ivar init_containers: List of specialized containers that run before job containers.
    :vartype init_containers: list[~azure.mgmt.appcontainers.models.JobExecutionContainer]
    """

    _attribute_map = {
        "containers": {"key": "containers", "type": "[JobExecutionContainer]"},
        "init_containers": {"key": "initContainers", "type": "[JobExecutionContainer]"},
    }

    def __init__(
        self,
        *,
        containers: Optional[List["JobExecutionContainer"]] = None,
        init_containers: Optional[List["JobExecutionContainer"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword containers: List of container definitions for the Container Apps Job.
        :paramtype containers: list[~azure.mgmt.appcontainers.models.JobExecutionContainer]
        :keyword init_containers: List of specialized containers that run before job containers.
        :paramtype init_containers: list[~azure.mgmt.appcontainers.models.JobExecutionContainer]
        """
        super().__init__(**kwargs)
        self.containers = containers
        self.init_containers = init_containers


class JobPatchProperties(_serialization.Model):
    """Container Apps Job resource specific properties.

    :ivar identity: Managed identities needed by a container app job to interact with other Azure
     services to not maintain any secrets or credentials in code.
    :vartype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar properties:
    :vartype properties: ~azure.mgmt.appcontainers.models.JobPatchPropertiesProperties
    """

    _attribute_map = {
        "identity": {"key": "identity", "type": "ManagedServiceIdentity"},
        "tags": {"key": "tags", "type": "{str}"},
        "properties": {"key": "properties", "type": "JobPatchPropertiesProperties"},
    }

    def __init__(
        self,
        *,
        identity: Optional["ManagedServiceIdentity"] = None,
        tags: Optional[Dict[str, str]] = None,
        properties: Optional["JobPatchPropertiesProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword identity: Managed identities needed by a container app job to interact with other
         Azure services to not maintain any secrets or credentials in code.
        :paramtype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword properties:
        :paramtype properties: ~azure.mgmt.appcontainers.models.JobPatchPropertiesProperties
        """
        super().__init__(**kwargs)
        self.identity = identity
        self.tags = tags
        self.properties = properties


class JobPatchPropertiesProperties(_serialization.Model):
    """JobPatchPropertiesProperties.

    :ivar environment_id: Resource ID of environment.
    :vartype environment_id: str
    :ivar configuration: Container Apps Job configuration properties.
    :vartype configuration: ~azure.mgmt.appcontainers.models.JobConfiguration
    :ivar template: Container Apps job definition.
    :vartype template: ~azure.mgmt.appcontainers.models.JobTemplate
    :ivar outbound_ip_addresses: Outbound IP Addresses of a container apps job.
    :vartype outbound_ip_addresses: list[str]
    :ivar event_stream_endpoint: The endpoint of the eventstream of the container apps job.
    :vartype event_stream_endpoint: str
    """

    _attribute_map = {
        "environment_id": {"key": "environmentId", "type": "str"},
        "configuration": {"key": "configuration", "type": "JobConfiguration"},
        "template": {"key": "template", "type": "JobTemplate"},
        "outbound_ip_addresses": {"key": "outboundIpAddresses", "type": "[str]"},
        "event_stream_endpoint": {"key": "eventStreamEndpoint", "type": "str"},
    }

    def __init__(
        self,
        *,
        environment_id: Optional[str] = None,
        configuration: Optional["JobConfiguration"] = None,
        template: Optional["JobTemplate"] = None,
        outbound_ip_addresses: Optional[List[str]] = None,
        event_stream_endpoint: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword environment_id: Resource ID of environment.
        :paramtype environment_id: str
        :keyword configuration: Container Apps Job configuration properties.
        :paramtype configuration: ~azure.mgmt.appcontainers.models.JobConfiguration
        :keyword template: Container Apps job definition.
        :paramtype template: ~azure.mgmt.appcontainers.models.JobTemplate
        :keyword outbound_ip_addresses: Outbound IP Addresses of a container apps job.
        :paramtype outbound_ip_addresses: list[str]
        :keyword event_stream_endpoint: The endpoint of the eventstream of the container apps job.
        :paramtype event_stream_endpoint: str
        """
        super().__init__(**kwargs)
        self.environment_id = environment_id
        self.configuration = configuration
        self.template = template
        self.outbound_ip_addresses = outbound_ip_addresses
        self.event_stream_endpoint = event_stream_endpoint


class JobScale(_serialization.Model):
    """Scaling configurations for event driven jobs.

    :ivar polling_interval: Interval to check each event source in seconds. Defaults to 30s.
    :vartype polling_interval: int
    :ivar min_executions: Minimum number of job executions that are created for a trigger, default
     0.
    :vartype min_executions: int
    :ivar max_executions: Maximum number of job executions that are created for a trigger, default
     100.
    :vartype max_executions: int
    :ivar rules: Scaling rules.
    :vartype rules: list[~azure.mgmt.appcontainers.models.JobScaleRule]
    """

    _attribute_map = {
        "polling_interval": {"key": "pollingInterval", "type": "int"},
        "min_executions": {"key": "minExecutions", "type": "int"},
        "max_executions": {"key": "maxExecutions", "type": "int"},
        "rules": {"key": "rules", "type": "[JobScaleRule]"},
    }

    def __init__(
        self,
        *,
        polling_interval: Optional[int] = None,
        min_executions: int = 0,
        max_executions: int = 100,
        rules: Optional[List["JobScaleRule"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword polling_interval: Interval to check each event source in seconds. Defaults to 30s.
        :paramtype polling_interval: int
        :keyword min_executions: Minimum number of job executions that are created for a trigger,
         default 0.
        :paramtype min_executions: int
        :keyword max_executions: Maximum number of job executions that are created for a trigger,
         default 100.
        :paramtype max_executions: int
        :keyword rules: Scaling rules.
        :paramtype rules: list[~azure.mgmt.appcontainers.models.JobScaleRule]
        """
        super().__init__(**kwargs)
        self.polling_interval = polling_interval
        self.min_executions = min_executions
        self.max_executions = max_executions
        self.rules = rules


class JobScaleRule(_serialization.Model):
    """Scaling rule.

    :ivar name: Scale Rule Name.
    :vartype name: str
    :ivar type: Type of the scale rule
     eg: azure-servicebus, redis etc.
    :vartype type: str
    :ivar metadata: Metadata properties to describe the scale rule.
    :vartype metadata: JSON
    :ivar auth: Authentication secrets for the scale rule.
    :vartype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
    :ivar identity: The resource ID of a user-assigned managed identity that is assigned to the
     Container App, or 'system' for system-assigned identity.
    :vartype identity: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "metadata": {"key": "metadata", "type": "object"},
        "auth": {"key": "auth", "type": "[ScaleRuleAuth]"},
        "identity": {"key": "identity", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        type: Optional[str] = None,
        metadata: Optional[JSON] = None,
        auth: Optional[List["ScaleRuleAuth"]] = None,
        identity: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Scale Rule Name.
        :paramtype name: str
        :keyword type: Type of the scale rule
         eg: azure-servicebus, redis etc.
        :paramtype type: str
        :keyword metadata: Metadata properties to describe the scale rule.
        :paramtype metadata: JSON
        :keyword auth: Authentication secrets for the scale rule.
        :paramtype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
        :keyword identity: The resource ID of a user-assigned managed identity that is assigned to the
         Container App, or 'system' for system-assigned identity.
        :paramtype identity: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.type = type
        self.metadata = metadata
        self.auth = auth
        self.identity = identity


class JobsCollection(_serialization.Model):
    """Container Apps Jobs collection ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.Job]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Job]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["Job"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.Job]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class JobSecretsCollection(_serialization.Model):
    """Container Apps Job Secrets Collection ARM resource.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.Secret]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Secret]"},
    }

    def __init__(self, *, value: List["Secret"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.Secret]
        """
        super().__init__(**kwargs)
        self.value = value


class JobTemplate(_serialization.Model):
    """Container Apps Job versioned application definition. Defines the desired state of an immutable
    revision. Any changes to this section Will result in a new revision being created.

    :ivar init_containers: List of specialized containers that run before app containers.
    :vartype init_containers: list[~azure.mgmt.appcontainers.models.InitContainer]
    :ivar containers: List of container definitions for the Container App.
    :vartype containers: list[~azure.mgmt.appcontainers.models.Container]
    :ivar volumes: List of volume definitions for the Container App.
    :vartype volumes: list[~azure.mgmt.appcontainers.models.Volume]
    """

    _attribute_map = {
        "init_containers": {"key": "initContainers", "type": "[InitContainer]"},
        "containers": {"key": "containers", "type": "[Container]"},
        "volumes": {"key": "volumes", "type": "[Volume]"},
    }

    def __init__(
        self,
        *,
        init_containers: Optional[List["InitContainer"]] = None,
        containers: Optional[List["Container"]] = None,
        volumes: Optional[List["Volume"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword init_containers: List of specialized containers that run before app containers.
        :paramtype init_containers: list[~azure.mgmt.appcontainers.models.InitContainer]
        :keyword containers: List of container definitions for the Container App.
        :paramtype containers: list[~azure.mgmt.appcontainers.models.Container]
        :keyword volumes: List of volume definitions for the Container App.
        :paramtype volumes: list[~azure.mgmt.appcontainers.models.Volume]
        """
        super().__init__(**kwargs)
        self.init_containers = init_containers
        self.containers = containers
        self.volumes = volumes


class JwtClaimChecks(_serialization.Model):
    """The configuration settings of the checks that should be made while validating the JWT Claims.

    :ivar allowed_groups: The list of the allowed groups.
    :vartype allowed_groups: list[str]
    :ivar allowed_client_applications: The list of the allowed client applications.
    :vartype allowed_client_applications: list[str]
    """

    _attribute_map = {
        "allowed_groups": {"key": "allowedGroups", "type": "[str]"},
        "allowed_client_applications": {"key": "allowedClientApplications", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        allowed_groups: Optional[List[str]] = None,
        allowed_client_applications: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword allowed_groups: The list of the allowed groups.
        :paramtype allowed_groups: list[str]
        :keyword allowed_client_applications: The list of the allowed client applications.
        :paramtype allowed_client_applications: list[str]
        """
        super().__init__(**kwargs)
        self.allowed_groups = allowed_groups
        self.allowed_client_applications = allowed_client_applications


class KedaConfiguration(_serialization.Model):
    """Configuration properties Keda component.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar version: The version of Keda.
    :vartype version: str
    """

    _validation = {
        "version": {"readonly": True},
    }

    _attribute_map = {
        "version": {"key": "version", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.version = None


class LifecycleConfiguration(_serialization.Model):
    """The lifecycle configuration properties of a session in the dynamic session pool.

    :ivar lifecycle_type: The lifecycle type of the session pool. Known values are: "Timed" and
     "OnContainerExit".
    :vartype lifecycle_type: str or ~azure.mgmt.appcontainers.models.LifecycleType
    :ivar cooldown_period_in_seconds: The cooldown period of a session in seconds when the
     lifecycle type is 'Timed'.
    :vartype cooldown_period_in_seconds: int
    :ivar max_alive_period_in_seconds: The maximum alive period of a session in seconds when the
     lifecycle type is 'OnContainerExit'.
    :vartype max_alive_period_in_seconds: int
    """

    _attribute_map = {
        "lifecycle_type": {"key": "lifecycleType", "type": "str"},
        "cooldown_period_in_seconds": {"key": "cooldownPeriodInSeconds", "type": "int"},
        "max_alive_period_in_seconds": {"key": "maxAlivePeriodInSeconds", "type": "int"},
    }

    def __init__(
        self,
        *,
        lifecycle_type: Optional[Union[str, "LifecycleType"]] = None,
        cooldown_period_in_seconds: Optional[int] = None,
        max_alive_period_in_seconds: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword lifecycle_type: The lifecycle type of the session pool. Known values are: "Timed" and
         "OnContainerExit".
        :paramtype lifecycle_type: str or ~azure.mgmt.appcontainers.models.LifecycleType
        :keyword cooldown_period_in_seconds: The cooldown period of a session in seconds when the
         lifecycle type is 'Timed'.
        :paramtype cooldown_period_in_seconds: int
        :keyword max_alive_period_in_seconds: The maximum alive period of a session in seconds when the
         lifecycle type is 'OnContainerExit'.
        :paramtype max_alive_period_in_seconds: int
        """
        super().__init__(**kwargs)
        self.lifecycle_type = lifecycle_type
        self.cooldown_period_in_seconds = cooldown_period_in_seconds
        self.max_alive_period_in_seconds = max_alive_period_in_seconds


class ListUsagesResult(_serialization.Model):
    """ListUsagesResult.

    :ivar value: The list of compute resource usages.
    :vartype value: list[~azure.mgmt.appcontainers.models.Usage]
    :ivar next_link: The URI to fetch the next page of compute resource usage information. Call
     ListNext() with this to fetch the next page of compute resource usage information.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[Usage]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["Usage"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: The list of compute resource usages.
        :paramtype value: list[~azure.mgmt.appcontainers.models.Usage]
        :keyword next_link: The URI to fetch the next page of compute resource usage information. Call
         ListNext() with this to fetch the next page of compute resource usage information.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class LogAnalyticsConfiguration(_serialization.Model):
    """Log Analytics configuration, must only be provided when destination is configured as
    'log-analytics'.

    :ivar customer_id: Log analytics customer id.
    :vartype customer_id: str
    :ivar shared_key: Log analytics customer key.
    :vartype shared_key: str
    """

    _attribute_map = {
        "customer_id": {"key": "customerId", "type": "str"},
        "shared_key": {"key": "sharedKey", "type": "str"},
    }

    def __init__(self, *, customer_id: Optional[str] = None, shared_key: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword customer_id: Log analytics customer id.
        :paramtype customer_id: str
        :keyword shared_key: Log analytics customer key.
        :paramtype shared_key: str
        """
        super().__init__(**kwargs)
        self.customer_id = customer_id
        self.shared_key = shared_key


class Login(_serialization.Model):
    """The configuration settings of the login flow of users using ContainerApp Service
    Authentication/Authorization.

    :ivar routes: The routes that specify the endpoints used for login and logout requests.
    :vartype routes: ~azure.mgmt.appcontainers.models.LoginRoutes
    :ivar token_store: The configuration settings of the token store.
    :vartype token_store: ~azure.mgmt.appcontainers.models.TokenStore
    :ivar preserve_url_fragments_for_logins: :code:`<code>true</code>` if the fragments from the
     request are preserved after the login request is made; otherwise, :code:`<code>false</code>`.
    :vartype preserve_url_fragments_for_logins: bool
    :ivar allowed_external_redirect_urls: External URLs that can be redirected to as part of
     logging in or logging out of the app. Note that the query string part of the URL is ignored.
     This is an advanced setting typically only needed by Windows Store application backends.
     Note that URLs within the current domain are always implicitly allowed.
    :vartype allowed_external_redirect_urls: list[str]
    :ivar cookie_expiration: The configuration settings of the session cookie's expiration.
    :vartype cookie_expiration: ~azure.mgmt.appcontainers.models.CookieExpiration
    :ivar nonce: The configuration settings of the nonce used in the login flow.
    :vartype nonce: ~azure.mgmt.appcontainers.models.Nonce
    """

    _attribute_map = {
        "routes": {"key": "routes", "type": "LoginRoutes"},
        "token_store": {"key": "tokenStore", "type": "TokenStore"},
        "preserve_url_fragments_for_logins": {"key": "preserveUrlFragmentsForLogins", "type": "bool"},
        "allowed_external_redirect_urls": {"key": "allowedExternalRedirectUrls", "type": "[str]"},
        "cookie_expiration": {"key": "cookieExpiration", "type": "CookieExpiration"},
        "nonce": {"key": "nonce", "type": "Nonce"},
    }

    def __init__(
        self,
        *,
        routes: Optional["LoginRoutes"] = None,
        token_store: Optional["TokenStore"] = None,
        preserve_url_fragments_for_logins: Optional[bool] = None,
        allowed_external_redirect_urls: Optional[List[str]] = None,
        cookie_expiration: Optional["CookieExpiration"] = None,
        nonce: Optional["Nonce"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword routes: The routes that specify the endpoints used for login and logout requests.
        :paramtype routes: ~azure.mgmt.appcontainers.models.LoginRoutes
        :keyword token_store: The configuration settings of the token store.
        :paramtype token_store: ~azure.mgmt.appcontainers.models.TokenStore
        :keyword preserve_url_fragments_for_logins: :code:`<code>true</code>` if the fragments from the
         request are preserved after the login request is made; otherwise, :code:`<code>false</code>`.
        :paramtype preserve_url_fragments_for_logins: bool
        :keyword allowed_external_redirect_urls: External URLs that can be redirected to as part of
         logging in or logging out of the app. Note that the query string part of the URL is ignored.
         This is an advanced setting typically only needed by Windows Store application backends.
         Note that URLs within the current domain are always implicitly allowed.
        :paramtype allowed_external_redirect_urls: list[str]
        :keyword cookie_expiration: The configuration settings of the session cookie's expiration.
        :paramtype cookie_expiration: ~azure.mgmt.appcontainers.models.CookieExpiration
        :keyword nonce: The configuration settings of the nonce used in the login flow.
        :paramtype nonce: ~azure.mgmt.appcontainers.models.Nonce
        """
        super().__init__(**kwargs)
        self.routes = routes
        self.token_store = token_store
        self.preserve_url_fragments_for_logins = preserve_url_fragments_for_logins
        self.allowed_external_redirect_urls = allowed_external_redirect_urls
        self.cookie_expiration = cookie_expiration
        self.nonce = nonce


class LoginRoutes(_serialization.Model):
    """The routes that specify the endpoints used for login and logout requests.

    :ivar logout_endpoint: The endpoint at which a logout request should be made.
    :vartype logout_endpoint: str
    """

    _attribute_map = {
        "logout_endpoint": {"key": "logoutEndpoint", "type": "str"},
    }

    def __init__(self, *, logout_endpoint: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword logout_endpoint: The endpoint at which a logout request should be made.
        :paramtype logout_endpoint: str
        """
        super().__init__(**kwargs)
        self.logout_endpoint = logout_endpoint


class LoginScopes(_serialization.Model):
    """The configuration settings of the login flow, including the scopes that should be requested.

    :ivar scopes: A list of the scopes that should be requested while authenticating.
    :vartype scopes: list[str]
    """

    _attribute_map = {
        "scopes": {"key": "scopes", "type": "[str]"},
    }

    def __init__(self, *, scopes: Optional[List[str]] = None, **kwargs: Any) -> None:
        """
        :keyword scopes: A list of the scopes that should be requested while authenticating.
        :paramtype scopes: list[str]
        """
        super().__init__(**kwargs)
        self.scopes = scopes


class ManagedCertificate(TrackedResource):
    """Managed certificates used for Custom Domain bindings of Container Apps in a Managed
    Environment.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar properties: Certificate resource specific properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.ManagedCertificateProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "ManagedCertificateProperties"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        properties: Optional["ManagedCertificateProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword properties: Certificate resource specific properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.ManagedCertificateProperties
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.properties = properties


class ManagedCertificateCollection(_serialization.Model):
    """Collection of Managed Certificates.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.ManagedCertificate]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ManagedCertificate]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["ManagedCertificate"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.ManagedCertificate]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class ManagedCertificatePatch(_serialization.Model):
    """A managed certificate to update.

    :ivar tags: Application-specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, *, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword tags: Application-specific metadata in the form of key-value pairs.
        :paramtype tags: dict[str, str]
        """
        super().__init__(**kwargs)
        self.tags = tags


class ManagedCertificateProperties(_serialization.Model):
    """Certificate resource specific properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar provisioning_state: Provisioning state of the certificate. Known values are: "Succeeded",
     "Failed", "Canceled", "DeleteFailed", and "Pending".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.CertificateProvisioningState
    :ivar subject_name: Subject name of the certificate.
    :vartype subject_name: str
    :ivar error: Any error occurred during the certificate provision.
    :vartype error: str
    :ivar domain_control_validation: Selected type of domain control validation for managed
     certificates. Known values are: "CNAME", "HTTP", and "TXT".
    :vartype domain_control_validation: str or
     ~azure.mgmt.appcontainers.models.ManagedCertificateDomainControlValidation
    :ivar validation_token: A TXT token used for DNS TXT domain control validation when issuing
     this type of managed certificates.
    :vartype validation_token: str
    """

    _validation = {
        "provisioning_state": {"readonly": True},
        "error": {"readonly": True},
        "validation_token": {"readonly": True},
    }

    _attribute_map = {
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "subject_name": {"key": "subjectName", "type": "str"},
        "error": {"key": "error", "type": "str"},
        "domain_control_validation": {"key": "domainControlValidation", "type": "str"},
        "validation_token": {"key": "validationToken", "type": "str"},
    }

    def __init__(
        self,
        *,
        subject_name: Optional[str] = None,
        domain_control_validation: Optional[Union[str, "ManagedCertificateDomainControlValidation"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword subject_name: Subject name of the certificate.
        :paramtype subject_name: str
        :keyword domain_control_validation: Selected type of domain control validation for managed
         certificates. Known values are: "CNAME", "HTTP", and "TXT".
        :paramtype domain_control_validation: str or
         ~azure.mgmt.appcontainers.models.ManagedCertificateDomainControlValidation
        """
        super().__init__(**kwargs)
        self.provisioning_state = None
        self.subject_name = subject_name
        self.error = None
        self.domain_control_validation = domain_control_validation
        self.validation_token = None


class ManagedEnvironment(TrackedResource):
    """An environment for hosting container apps.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar kind: Kind of the Environment.
    :vartype kind: str
    :ivar identity: Managed identities for the Managed Environment to interact with other Azure
     services without maintaining any secrets or credentials in code.
    :vartype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
    :ivar provisioning_state: Provisioning state of the Environment. Known values are: "Succeeded",
     "Failed", "Canceled", "Waiting", "InitializationInProgress", "InfrastructureSetupInProgress",
     "InfrastructureSetupComplete", "ScheduledForDelete", "UpgradeRequested", and "UpgradeFailed".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.EnvironmentProvisioningState
    :ivar dapr_ai_instrumentation_key: Azure Monitor instrumentation key used by Dapr to export
     Service to Service communication telemetry.
    :vartype dapr_ai_instrumentation_key: str
    :ivar dapr_ai_connection_string: Application Insights connection string used by Dapr to export
     Service to Service communication telemetry.
    :vartype dapr_ai_connection_string: str
    :ivar vnet_configuration: Vnet configuration for the environment.
    :vartype vnet_configuration: ~azure.mgmt.appcontainers.models.VnetConfiguration
    :ivar deployment_errors: Any errors that occurred during deployment or deployment validation.
    :vartype deployment_errors: str
    :ivar default_domain: Default Domain Name for the cluster.
    :vartype default_domain: str
    :ivar static_ip: Static IP of the Environment.
    :vartype static_ip: str
    :ivar app_logs_configuration: Cluster configuration which enables the log daemon to export app
     logs to configured destination.
    :vartype app_logs_configuration: ~azure.mgmt.appcontainers.models.AppLogsConfiguration
    :ivar zone_redundant: Whether or not this Managed Environment is zone-redundant.
    :vartype zone_redundant: bool
    :ivar custom_domain_configuration: Custom domain configuration for the environment.
    :vartype custom_domain_configuration:
     ~azure.mgmt.appcontainers.models.CustomDomainConfiguration
    :ivar event_stream_endpoint: The endpoint of the eventstream of the Environment.
    :vartype event_stream_endpoint: str
    :ivar workload_profiles: Workload profiles configured for the Managed Environment.
    :vartype workload_profiles: list[~azure.mgmt.appcontainers.models.WorkloadProfile]
    :ivar keda_configuration: The configuration of Keda component.
    :vartype keda_configuration: ~azure.mgmt.appcontainers.models.KedaConfiguration
    :ivar dapr_configuration: The configuration of Dapr component.
    :vartype dapr_configuration: ~azure.mgmt.appcontainers.models.DaprConfiguration
    :ivar infrastructure_resource_group: Name of the platform-managed resource group created for
     the Managed Environment to host infrastructure resources. If a subnet ID is provided, this
     resource group will be created in the same subscription as the subnet.
    :vartype infrastructure_resource_group: str
    :ivar peer_authentication: Peer authentication settings for the Managed Environment.
    :vartype peer_authentication:
     ~azure.mgmt.appcontainers.models.ManagedEnvironmentPropertiesPeerAuthentication
    :ivar peer_traffic_configuration: Peer traffic settings for the Managed Environment.
    :vartype peer_traffic_configuration:
     ~azure.mgmt.appcontainers.models.ManagedEnvironmentPropertiesPeerTrafficConfiguration
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "provisioning_state": {"readonly": True},
        "deployment_errors": {"readonly": True},
        "default_domain": {"readonly": True},
        "static_ip": {"readonly": True},
        "event_stream_endpoint": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "identity": {"key": "identity", "type": "ManagedServiceIdentity"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "dapr_ai_instrumentation_key": {"key": "properties.daprAIInstrumentationKey", "type": "str"},
        "dapr_ai_connection_string": {"key": "properties.daprAIConnectionString", "type": "str"},
        "vnet_configuration": {"key": "properties.vnetConfiguration", "type": "VnetConfiguration"},
        "deployment_errors": {"key": "properties.deploymentErrors", "type": "str"},
        "default_domain": {"key": "properties.defaultDomain", "type": "str"},
        "static_ip": {"key": "properties.staticIp", "type": "str"},
        "app_logs_configuration": {"key": "properties.appLogsConfiguration", "type": "AppLogsConfiguration"},
        "zone_redundant": {"key": "properties.zoneRedundant", "type": "bool"},
        "custom_domain_configuration": {
            "key": "properties.customDomainConfiguration",
            "type": "CustomDomainConfiguration",
        },
        "event_stream_endpoint": {"key": "properties.eventStreamEndpoint", "type": "str"},
        "workload_profiles": {"key": "properties.workloadProfiles", "type": "[WorkloadProfile]"},
        "keda_configuration": {"key": "properties.kedaConfiguration", "type": "KedaConfiguration"},
        "dapr_configuration": {"key": "properties.daprConfiguration", "type": "DaprConfiguration"},
        "infrastructure_resource_group": {"key": "properties.infrastructureResourceGroup", "type": "str"},
        "peer_authentication": {
            "key": "properties.peerAuthentication",
            "type": "ManagedEnvironmentPropertiesPeerAuthentication",
        },
        "peer_traffic_configuration": {
            "key": "properties.peerTrafficConfiguration",
            "type": "ManagedEnvironmentPropertiesPeerTrafficConfiguration",
        },
    }

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        kind: Optional[str] = None,
        identity: Optional["ManagedServiceIdentity"] = None,
        dapr_ai_instrumentation_key: Optional[str] = None,
        dapr_ai_connection_string: Optional[str] = None,
        vnet_configuration: Optional["VnetConfiguration"] = None,
        app_logs_configuration: Optional["AppLogsConfiguration"] = None,
        zone_redundant: Optional[bool] = None,
        custom_domain_configuration: Optional["CustomDomainConfiguration"] = None,
        workload_profiles: Optional[List["WorkloadProfile"]] = None,
        keda_configuration: Optional["KedaConfiguration"] = None,
        dapr_configuration: Optional["DaprConfiguration"] = None,
        infrastructure_resource_group: Optional[str] = None,
        peer_authentication: Optional["ManagedEnvironmentPropertiesPeerAuthentication"] = None,
        peer_traffic_configuration: Optional["ManagedEnvironmentPropertiesPeerTrafficConfiguration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword kind: Kind of the Environment.
        :paramtype kind: str
        :keyword identity: Managed identities for the Managed Environment to interact with other Azure
         services without maintaining any secrets or credentials in code.
        :paramtype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
        :keyword dapr_ai_instrumentation_key: Azure Monitor instrumentation key used by Dapr to export
         Service to Service communication telemetry.
        :paramtype dapr_ai_instrumentation_key: str
        :keyword dapr_ai_connection_string: Application Insights connection string used by Dapr to
         export Service to Service communication telemetry.
        :paramtype dapr_ai_connection_string: str
        :keyword vnet_configuration: Vnet configuration for the environment.
        :paramtype vnet_configuration: ~azure.mgmt.appcontainers.models.VnetConfiguration
        :keyword app_logs_configuration: Cluster configuration which enables the log daemon to export
         app logs to configured destination.
        :paramtype app_logs_configuration: ~azure.mgmt.appcontainers.models.AppLogsConfiguration
        :keyword zone_redundant: Whether or not this Managed Environment is zone-redundant.
        :paramtype zone_redundant: bool
        :keyword custom_domain_configuration: Custom domain configuration for the environment.
        :paramtype custom_domain_configuration:
         ~azure.mgmt.appcontainers.models.CustomDomainConfiguration
        :keyword workload_profiles: Workload profiles configured for the Managed Environment.
        :paramtype workload_profiles: list[~azure.mgmt.appcontainers.models.WorkloadProfile]
        :keyword keda_configuration: The configuration of Keda component.
        :paramtype keda_configuration: ~azure.mgmt.appcontainers.models.KedaConfiguration
        :keyword dapr_configuration: The configuration of Dapr component.
        :paramtype dapr_configuration: ~azure.mgmt.appcontainers.models.DaprConfiguration
        :keyword infrastructure_resource_group: Name of the platform-managed resource group created for
         the Managed Environment to host infrastructure resources. If a subnet ID is provided, this
         resource group will be created in the same subscription as the subnet.
        :paramtype infrastructure_resource_group: str
        :keyword peer_authentication: Peer authentication settings for the Managed Environment.
        :paramtype peer_authentication:
         ~azure.mgmt.appcontainers.models.ManagedEnvironmentPropertiesPeerAuthentication
        :keyword peer_traffic_configuration: Peer traffic settings for the Managed Environment.
        :paramtype peer_traffic_configuration:
         ~azure.mgmt.appcontainers.models.ManagedEnvironmentPropertiesPeerTrafficConfiguration
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.kind = kind
        self.identity = identity
        self.provisioning_state = None
        self.dapr_ai_instrumentation_key = dapr_ai_instrumentation_key
        self.dapr_ai_connection_string = dapr_ai_connection_string
        self.vnet_configuration = vnet_configuration
        self.deployment_errors = None
        self.default_domain = None
        self.static_ip = None
        self.app_logs_configuration = app_logs_configuration
        self.zone_redundant = zone_redundant
        self.custom_domain_configuration = custom_domain_configuration
        self.event_stream_endpoint = None
        self.workload_profiles = workload_profiles
        self.keda_configuration = keda_configuration
        self.dapr_configuration = dapr_configuration
        self.infrastructure_resource_group = infrastructure_resource_group
        self.peer_authentication = peer_authentication
        self.peer_traffic_configuration = peer_traffic_configuration


class ManagedEnvironmentPropertiesPeerAuthentication(_serialization.Model):  # pylint: disable=name-too-long
    """Peer authentication settings for the Managed Environment.

    :ivar mtls: Mutual TLS authentication settings for the Managed Environment.
    :vartype mtls: ~azure.mgmt.appcontainers.models.Mtls
    """

    _attribute_map = {
        "mtls": {"key": "mtls", "type": "Mtls"},
    }

    def __init__(self, *, mtls: Optional["Mtls"] = None, **kwargs: Any) -> None:
        """
        :keyword mtls: Mutual TLS authentication settings for the Managed Environment.
        :paramtype mtls: ~azure.mgmt.appcontainers.models.Mtls
        """
        super().__init__(**kwargs)
        self.mtls = mtls


class ManagedEnvironmentPropertiesPeerTrafficConfiguration(_serialization.Model):  # pylint: disable=name-too-long
    """Peer traffic settings for the Managed Environment.

    :ivar encryption: Peer traffic encryption settings for the Managed Environment.
    :vartype encryption:
     ~azure.mgmt.appcontainers.models.ManagedEnvironmentPropertiesPeerTrafficConfigurationEncryption
    """

    _attribute_map = {
        "encryption": {"key": "encryption", "type": "ManagedEnvironmentPropertiesPeerTrafficConfigurationEncryption"},
    }

    def __init__(
        self,
        *,
        encryption: Optional["ManagedEnvironmentPropertiesPeerTrafficConfigurationEncryption"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword encryption: Peer traffic encryption settings for the Managed Environment.
        :paramtype encryption:
         ~azure.mgmt.appcontainers.models.ManagedEnvironmentPropertiesPeerTrafficConfigurationEncryption
        """
        super().__init__(**kwargs)
        self.encryption = encryption


class ManagedEnvironmentPropertiesPeerTrafficConfigurationEncryption(
    _serialization.Model
):  # pylint: disable=name-too-long
    """Peer traffic encryption settings for the Managed Environment.

    :ivar enabled: Boolean indicating whether the peer traffic encryption is enabled.
    :vartype enabled: bool
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
    }

    def __init__(self, *, enabled: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword enabled: Boolean indicating whether the peer traffic encryption is enabled.
        :paramtype enabled: bool
        """
        super().__init__(**kwargs)
        self.enabled = enabled


class ManagedEnvironmentsCollection(_serialization.Model):
    """Collection of Environments.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.ManagedEnvironment]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ManagedEnvironment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["ManagedEnvironment"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.ManagedEnvironment]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class ManagedEnvironmentStorage(ProxyResource):
    """Storage resource for managedEnvironment.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar properties: Storage properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.ManagedEnvironmentStorageProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "ManagedEnvironmentStorageProperties"},
    }

    def __init__(
        self, *, properties: Optional["ManagedEnvironmentStorageProperties"] = None, **kwargs: Any
    ) -> None:
        """
        :keyword properties: Storage properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.ManagedEnvironmentStorageProperties
        """
        super().__init__(**kwargs)
        self.properties = properties


class ManagedEnvironmentStorageProperties(_serialization.Model):
    """Storage properties.

    :ivar azure_file: Azure file properties.
    :vartype azure_file: ~azure.mgmt.appcontainers.models.AzureFileProperties
    :ivar nfs_azure_file: NFS Azure file properties.
    :vartype nfs_azure_file: ~azure.mgmt.appcontainers.models.NfsAzureFileProperties
    """

    _attribute_map = {
        "azure_file": {"key": "azureFile", "type": "AzureFileProperties"},
        "nfs_azure_file": {"key": "nfsAzureFile", "type": "NfsAzureFileProperties"},
    }

    def __init__(
        self,
        *,
        azure_file: Optional["AzureFileProperties"] = None,
        nfs_azure_file: Optional["NfsAzureFileProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword azure_file: Azure file properties.
        :paramtype azure_file: ~azure.mgmt.appcontainers.models.AzureFileProperties
        :keyword nfs_azure_file: NFS Azure file properties.
        :paramtype nfs_azure_file: ~azure.mgmt.appcontainers.models.NfsAzureFileProperties
        """
        super().__init__(**kwargs)
        self.azure_file = azure_file
        self.nfs_azure_file = nfs_azure_file


class ManagedEnvironmentStoragesCollection(_serialization.Model):
    """Collection of Storage for Environments.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of storage resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.ManagedEnvironmentStorage]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ManagedEnvironmentStorage]"},
    }

    def __init__(self, *, value: List["ManagedEnvironmentStorage"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of storage resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.ManagedEnvironmentStorage]
        """
        super().__init__(**kwargs)
        self.value = value


class ManagedIdentitySetting(_serialization.Model):
    """Optional settings for a Managed Identity that is assigned to the Session pool.

    All required parameters must be populated in order to send to server.

    :ivar identity: The resource ID of a user-assigned managed identity that is assigned to the
     Session Pool, or 'system' for system-assigned identity. Required.
    :vartype identity: str
    :ivar lifecycle: Use to select the lifecycle stages of a Session Pool during which the Managed
     Identity should be available. Known values are: "Init", "Main", "None", and "All".
    :vartype lifecycle: str or ~azure.mgmt.appcontainers.models.IdentitySettingsLifeCycle
    """

    _validation = {
        "identity": {"required": True},
    }

    _attribute_map = {
        "identity": {"key": "identity", "type": "str"},
        "lifecycle": {"key": "lifecycle", "type": "str"},
    }

    def __init__(
        self,
        *,
        identity: str,
        lifecycle: Optional[Union[str, "IdentitySettingsLifeCycle"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword identity: The resource ID of a user-assigned managed identity that is assigned to the
         Session Pool, or 'system' for system-assigned identity. Required.
        :paramtype identity: str
        :keyword lifecycle: Use to select the lifecycle stages of a Session Pool during which the
         Managed Identity should be available. Known values are: "Init", "Main", "None", and "All".
        :paramtype lifecycle: str or ~azure.mgmt.appcontainers.models.IdentitySettingsLifeCycle
        """
        super().__init__(**kwargs)
        self.identity = identity
        self.lifecycle = lifecycle


class ManagedServiceIdentity(_serialization.Model):
    """Managed service identity (system assigned and/or user assigned identities).

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar principal_id: The service principal ID of the system assigned identity. This property
     will only be provided for a system assigned identity.
    :vartype principal_id: str
    :ivar tenant_id: The tenant ID of the system assigned identity. This property will only be
     provided for a system assigned identity.
    :vartype tenant_id: str
    :ivar type: Type of managed service identity (where both SystemAssigned and UserAssigned types
     are allowed). Required. Known values are: "None", "SystemAssigned", "UserAssigned", and
     "SystemAssigned,UserAssigned".
    :vartype type: str or ~azure.mgmt.appcontainers.models.ManagedServiceIdentityType
    :ivar user_assigned_identities: The set of user assigned identities associated with the
     resource. The userAssignedIdentities dictionary keys will be ARM resource ids in the form:
     '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}.  # pylint: disable=line-too-long
     The dictionary values can be empty objects ({}) in requests.
    :vartype user_assigned_identities: dict[str,
     ~azure.mgmt.appcontainers.models.UserAssignedIdentity]
    """

    _validation = {
        "principal_id": {"readonly": True},
        "tenant_id": {"readonly": True},
        "type": {"required": True},
    }

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "tenant_id": {"key": "tenantId", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "user_assigned_identities": {"key": "userAssignedIdentities", "type": "{UserAssignedIdentity}"},
    }

    def __init__(
        self,
        *,
        type: Union[str, "ManagedServiceIdentityType"],
        user_assigned_identities: Optional[Dict[str, "UserAssignedIdentity"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Type of managed service identity (where both SystemAssigned and UserAssigned
         types are allowed). Required. Known values are: "None", "SystemAssigned", "UserAssigned", and
         "SystemAssigned,UserAssigned".
        :paramtype type: str or ~azure.mgmt.appcontainers.models.ManagedServiceIdentityType
        :keyword user_assigned_identities: The set of user assigned identities associated with the
         resource. The userAssignedIdentities dictionary keys will be ARM resource ids in the form:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}.  # pylint: disable=line-too-long
         The dictionary values can be empty objects ({}) in requests.
        :paramtype user_assigned_identities: dict[str,
         ~azure.mgmt.appcontainers.models.UserAssignedIdentity]
        """
        super().__init__(**kwargs)
        self.principal_id = None
        self.tenant_id = None
        self.type = type
        self.user_assigned_identities = user_assigned_identities


class Mtls(_serialization.Model):
    """Configuration properties for mutual TLS authentication.

    :ivar enabled: Boolean indicating whether the mutual TLS authentication is enabled.
    :vartype enabled: bool
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
    }

    def __init__(self, *, enabled: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword enabled: Boolean indicating whether the mutual TLS authentication is enabled.
        :paramtype enabled: bool
        """
        super().__init__(**kwargs)
        self.enabled = enabled


class NfsAzureFileProperties(_serialization.Model):
    """NFS Azure File Properties.

    :ivar server: Server for NFS azure file. Specify the Azure storage account server address.
    :vartype server: str
    :ivar access_mode: Access mode for storage. Known values are: "ReadOnly" and "ReadWrite".
    :vartype access_mode: str or ~azure.mgmt.appcontainers.models.AccessMode
    :ivar share_name: NFS Azure file share name.
    :vartype share_name: str
    """

    _attribute_map = {
        "server": {"key": "server", "type": "str"},
        "access_mode": {"key": "accessMode", "type": "str"},
        "share_name": {"key": "shareName", "type": "str"},
    }

    def __init__(
        self,
        *,
        server: Optional[str] = None,
        access_mode: Optional[Union[str, "AccessMode"]] = None,
        share_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword server: Server for NFS azure file. Specify the Azure storage account server address.
        :paramtype server: str
        :keyword access_mode: Access mode for storage. Known values are: "ReadOnly" and "ReadWrite".
        :paramtype access_mode: str or ~azure.mgmt.appcontainers.models.AccessMode
        :keyword share_name: NFS Azure file share name.
        :paramtype share_name: str
        """
        super().__init__(**kwargs)
        self.server = server
        self.access_mode = access_mode
        self.share_name = share_name


class Nonce(_serialization.Model):
    """The configuration settings of the nonce used in the login flow.

    :ivar validate_nonce: :code:`<code>false</code>` if the nonce should not be validated while
     completing the login flow; otherwise, :code:`<code>true</code>`.
    :vartype validate_nonce: bool
    :ivar nonce_expiration_interval: The time after the request is made when the nonce should
     expire.
    :vartype nonce_expiration_interval: str
    """

    _attribute_map = {
        "validate_nonce": {"key": "validateNonce", "type": "bool"},
        "nonce_expiration_interval": {"key": "nonceExpirationInterval", "type": "str"},
    }

    def __init__(
        self, *, validate_nonce: Optional[bool] = None, nonce_expiration_interval: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword validate_nonce: :code:`<code>false</code>` if the nonce should not be validated while
         completing the login flow; otherwise, :code:`<code>true</code>`.
        :paramtype validate_nonce: bool
        :keyword nonce_expiration_interval: The time after the request is made when the nonce should
         expire.
        :paramtype nonce_expiration_interval: str
        """
        super().__init__(**kwargs)
        self.validate_nonce = validate_nonce
        self.nonce_expiration_interval = nonce_expiration_interval


class OpenIdConnectClientCredential(_serialization.Model):
    """The authentication client credentials of the custom Open ID Connect provider.

    :ivar method: The method that should be used to authenticate the user. Default value is
     "ClientSecretPost".
    :vartype method: str
    :ivar client_secret_setting_name: The app setting that contains the client secret for the
     custom Open ID Connect provider.
    :vartype client_secret_setting_name: str
    """

    _attribute_map = {
        "method": {"key": "method", "type": "str"},
        "client_secret_setting_name": {"key": "clientSecretSettingName", "type": "str"},
    }

    def __init__(
        self,
        *,
        method: Optional[Literal["ClientSecretPost"]] = None,
        client_secret_setting_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword method: The method that should be used to authenticate the user. Default value is
         "ClientSecretPost".
        :paramtype method: str
        :keyword client_secret_setting_name: The app setting that contains the client secret for the
         custom Open ID Connect provider.
        :paramtype client_secret_setting_name: str
        """
        super().__init__(**kwargs)
        self.method = method
        self.client_secret_setting_name = client_secret_setting_name


class OpenIdConnectConfig(_serialization.Model):
    """The configuration settings of the endpoints used for the custom Open ID Connect provider.

    :ivar authorization_endpoint: The endpoint to be used to make an authorization request.
    :vartype authorization_endpoint: str
    :ivar token_endpoint: The endpoint to be used to request a token.
    :vartype token_endpoint: str
    :ivar issuer: The endpoint that issues the token.
    :vartype issuer: str
    :ivar certification_uri: The endpoint that provides the keys necessary to validate the token.
    :vartype certification_uri: str
    :ivar well_known_open_id_configuration: The endpoint that contains all the configuration
     endpoints for the provider.
    :vartype well_known_open_id_configuration: str
    """

    _attribute_map = {
        "authorization_endpoint": {"key": "authorizationEndpoint", "type": "str"},
        "token_endpoint": {"key": "tokenEndpoint", "type": "str"},
        "issuer": {"key": "issuer", "type": "str"},
        "certification_uri": {"key": "certificationUri", "type": "str"},
        "well_known_open_id_configuration": {"key": "wellKnownOpenIdConfiguration", "type": "str"},
    }

    def __init__(
        self,
        *,
        authorization_endpoint: Optional[str] = None,
        token_endpoint: Optional[str] = None,
        issuer: Optional[str] = None,
        certification_uri: Optional[str] = None,
        well_known_open_id_configuration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword authorization_endpoint: The endpoint to be used to make an authorization request.
        :paramtype authorization_endpoint: str
        :keyword token_endpoint: The endpoint to be used to request a token.
        :paramtype token_endpoint: str
        :keyword issuer: The endpoint that issues the token.
        :paramtype issuer: str
        :keyword certification_uri: The endpoint that provides the keys necessary to validate the
         token.
        :paramtype certification_uri: str
        :keyword well_known_open_id_configuration: The endpoint that contains all the configuration
         endpoints for the provider.
        :paramtype well_known_open_id_configuration: str
        """
        super().__init__(**kwargs)
        self.authorization_endpoint = authorization_endpoint
        self.token_endpoint = token_endpoint
        self.issuer = issuer
        self.certification_uri = certification_uri
        self.well_known_open_id_configuration = well_known_open_id_configuration


class OpenIdConnectLogin(_serialization.Model):
    """The configuration settings of the login flow of the custom Open ID Connect provider.

    :ivar name_claim_type: The name of the claim that contains the users name.
    :vartype name_claim_type: str
    :ivar scopes: A list of the scopes that should be requested while authenticating.
    :vartype scopes: list[str]
    """

    _attribute_map = {
        "name_claim_type": {"key": "nameClaimType", "type": "str"},
        "scopes": {"key": "scopes", "type": "[str]"},
    }

    def __init__(
        self, *, name_claim_type: Optional[str] = None, scopes: Optional[List[str]] = None, **kwargs: Any
    ) -> None:
        """
        :keyword name_claim_type: The name of the claim that contains the users name.
        :paramtype name_claim_type: str
        :keyword scopes: A list of the scopes that should be requested while authenticating.
        :paramtype scopes: list[str]
        """
        super().__init__(**kwargs)
        self.name_claim_type = name_claim_type
        self.scopes = scopes


class OpenIdConnectRegistration(_serialization.Model):
    """The configuration settings of the app registration for the custom Open ID Connect provider.

    :ivar client_id: The client id of the custom Open ID Connect provider.
    :vartype client_id: str
    :ivar client_credential: The authentication credentials of the custom Open ID Connect provider.
    :vartype client_credential: ~azure.mgmt.appcontainers.models.OpenIdConnectClientCredential
    :ivar open_id_connect_configuration: The configuration settings of the endpoints used for the
     custom Open ID Connect provider.
    :vartype open_id_connect_configuration: ~azure.mgmt.appcontainers.models.OpenIdConnectConfig
    """

    _attribute_map = {
        "client_id": {"key": "clientId", "type": "str"},
        "client_credential": {"key": "clientCredential", "type": "OpenIdConnectClientCredential"},
        "open_id_connect_configuration": {"key": "openIdConnectConfiguration", "type": "OpenIdConnectConfig"},
    }

    def __init__(
        self,
        *,
        client_id: Optional[str] = None,
        client_credential: Optional["OpenIdConnectClientCredential"] = None,
        open_id_connect_configuration: Optional["OpenIdConnectConfig"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword client_id: The client id of the custom Open ID Connect provider.
        :paramtype client_id: str
        :keyword client_credential: The authentication credentials of the custom Open ID Connect
         provider.
        :paramtype client_credential: ~azure.mgmt.appcontainers.models.OpenIdConnectClientCredential
        :keyword open_id_connect_configuration: The configuration settings of the endpoints used for
         the custom Open ID Connect provider.
        :paramtype open_id_connect_configuration: ~azure.mgmt.appcontainers.models.OpenIdConnectConfig
        """
        super().__init__(**kwargs)
        self.client_id = client_id
        self.client_credential = client_credential
        self.open_id_connect_configuration = open_id_connect_configuration


class OperationDetail(_serialization.Model):
    """Operation detail payload.

    :ivar name: Name of the operation.
    :vartype name: str
    :ivar is_data_action: Indicates whether the operation is a data action.
    :vartype is_data_action: bool
    :ivar display: Display of the operation.
    :vartype display: ~azure.mgmt.appcontainers.models.OperationDisplay
    :ivar origin: Origin of the operation.
    :vartype origin: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "is_data_action": {"key": "isDataAction", "type": "bool"},
        "display": {"key": "display", "type": "OperationDisplay"},
        "origin": {"key": "origin", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        is_data_action: Optional[bool] = None,
        display: Optional["OperationDisplay"] = None,
        origin: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Name of the operation.
        :paramtype name: str
        :keyword is_data_action: Indicates whether the operation is a data action.
        :paramtype is_data_action: bool
        :keyword display: Display of the operation.
        :paramtype display: ~azure.mgmt.appcontainers.models.OperationDisplay
        :keyword origin: Origin of the operation.
        :paramtype origin: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.is_data_action = is_data_action
        self.display = display
        self.origin = origin


class OperationDisplay(_serialization.Model):
    """Operation display payload.

    :ivar provider: Resource provider of the operation.
    :vartype provider: str
    :ivar resource: Resource of the operation.
    :vartype resource: str
    :ivar operation: Localized friendly name for the operation.
    :vartype operation: str
    :ivar description: Localized friendly description for the operation.
    :vartype description: str
    """

    _attribute_map = {
        "provider": {"key": "provider", "type": "str"},
        "resource": {"key": "resource", "type": "str"},
        "operation": {"key": "operation", "type": "str"},
        "description": {"key": "description", "type": "str"},
    }

    def __init__(
        self,
        *,
        provider: Optional[str] = None,
        resource: Optional[str] = None,
        operation: Optional[str] = None,
        description: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword provider: Resource provider of the operation.
        :paramtype provider: str
        :keyword resource: Resource of the operation.
        :paramtype resource: str
        :keyword operation: Localized friendly name for the operation.
        :paramtype operation: str
        :keyword description: Localized friendly description for the operation.
        :paramtype description: str
        """
        super().__init__(**kwargs)
        self.provider = provider
        self.resource = resource
        self.operation = operation
        self.description = description


class QueueScaleRule(_serialization.Model):
    """Container App container Azure Queue based scaling rule.

    :ivar account_name: Storage account name. required if using managed identity to authenticate.
    :vartype account_name: str
    :ivar queue_name: Queue name.
    :vartype queue_name: str
    :ivar queue_length: Queue length.
    :vartype queue_length: int
    :ivar auth: Authentication secrets for the queue scale rule.
    :vartype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
    :ivar identity: The resource ID of a user-assigned managed identity that is assigned to the
     Container App, or 'system' for system-assigned identity.
    :vartype identity: str
    """

    _attribute_map = {
        "account_name": {"key": "accountName", "type": "str"},
        "queue_name": {"key": "queueName", "type": "str"},
        "queue_length": {"key": "queueLength", "type": "int"},
        "auth": {"key": "auth", "type": "[ScaleRuleAuth]"},
        "identity": {"key": "identity", "type": "str"},
    }

    def __init__(
        self,
        *,
        account_name: Optional[str] = None,
        queue_name: Optional[str] = None,
        queue_length: Optional[int] = None,
        auth: Optional[List["ScaleRuleAuth"]] = None,
        identity: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword account_name: Storage account name. required if using managed identity to
         authenticate.
        :paramtype account_name: str
        :keyword queue_name: Queue name.
        :paramtype queue_name: str
        :keyword queue_length: Queue length.
        :paramtype queue_length: int
        :keyword auth: Authentication secrets for the queue scale rule.
        :paramtype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
        :keyword identity: The resource ID of a user-assigned managed identity that is assigned to the
         Container App, or 'system' for system-assigned identity.
        :paramtype identity: str
        """
        super().__init__(**kwargs)
        self.account_name = account_name
        self.queue_name = queue_name
        self.queue_length = queue_length
        self.auth = auth
        self.identity = identity


class RegistryCredentials(_serialization.Model):
    """Container App Private Registry.

    :ivar server: Container Registry Server.
    :vartype server: str
    :ivar username: Container Registry Username.
    :vartype username: str
    :ivar password_secret_ref: The name of the Secret that contains the registry login password.
    :vartype password_secret_ref: str
    :ivar identity: A Managed Identity to use to authenticate with Azure Container Registry. For
     user-assigned identities, use the full user-assigned identity Resource ID. For system-assigned
     identities, use 'system'.
    :vartype identity: str
    """

    _attribute_map = {
        "server": {"key": "server", "type": "str"},
        "username": {"key": "username", "type": "str"},
        "password_secret_ref": {"key": "passwordSecretRef", "type": "str"},
        "identity": {"key": "identity", "type": "str"},
    }

    def __init__(
        self,
        *,
        server: Optional[str] = None,
        username: Optional[str] = None,
        password_secret_ref: Optional[str] = None,
        identity: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword server: Container Registry Server.
        :paramtype server: str
        :keyword username: Container Registry Username.
        :paramtype username: str
        :keyword password_secret_ref: The name of the Secret that contains the registry login password.
        :paramtype password_secret_ref: str
        :keyword identity: A Managed Identity to use to authenticate with Azure Container Registry. For
         user-assigned identities, use the full user-assigned identity Resource ID. For system-assigned
         identities, use 'system'.
        :paramtype identity: str
        """
        super().__init__(**kwargs)
        self.server = server
        self.username = username
        self.password_secret_ref = password_secret_ref
        self.identity = identity


class RegistryInfo(_serialization.Model):
    """Container App registry information.

    :ivar registry_url: registry server Url.
    :vartype registry_url: str
    :ivar registry_user_name: registry username.
    :vartype registry_user_name: str
    :ivar registry_password: registry secret.
    :vartype registry_password: str
    """

    _attribute_map = {
        "registry_url": {"key": "registryUrl", "type": "str"},
        "registry_user_name": {"key": "registryUserName", "type": "str"},
        "registry_password": {"key": "registryPassword", "type": "str"},
    }

    def __init__(
        self,
        *,
        registry_url: Optional[str] = None,
        registry_user_name: Optional[str] = None,
        registry_password: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword registry_url: registry server Url.
        :paramtype registry_url: str
        :keyword registry_user_name: registry username.
        :paramtype registry_user_name: str
        :keyword registry_password: registry secret.
        :paramtype registry_password: str
        """
        super().__init__(**kwargs)
        self.registry_url = registry_url
        self.registry_user_name = registry_user_name
        self.registry_password = registry_password


class Replica(ProxyResource):
    """Container App Revision Replica.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar created_time: Timestamp describing when the pod was created by controller.
    :vartype created_time: ~datetime.datetime
    :ivar running_state: Current running state of the replica. Known values are: "Running",
     "NotRunning", and "Unknown".
    :vartype running_state: str or ~azure.mgmt.appcontainers.models.ContainerAppReplicaRunningState
    :ivar running_state_details: The details of replica current running state.
    :vartype running_state_details: str
    :ivar containers: The containers collection under a replica.
    :vartype containers: list[~azure.mgmt.appcontainers.models.ReplicaContainer]
    :ivar init_containers: The init containers collection under a replica.
    :vartype init_containers: list[~azure.mgmt.appcontainers.models.ReplicaContainer]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "created_time": {"readonly": True},
        "running_state": {"readonly": True},
        "running_state_details": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "created_time": {"key": "properties.createdTime", "type": "iso-8601"},
        "running_state": {"key": "properties.runningState", "type": "str"},
        "running_state_details": {"key": "properties.runningStateDetails", "type": "str"},
        "containers": {"key": "properties.containers", "type": "[ReplicaContainer]"},
        "init_containers": {"key": "properties.initContainers", "type": "[ReplicaContainer]"},
    }

    def __init__(
        self,
        *,
        containers: Optional[List["ReplicaContainer"]] = None,
        init_containers: Optional[List["ReplicaContainer"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword containers: The containers collection under a replica.
        :paramtype containers: list[~azure.mgmt.appcontainers.models.ReplicaContainer]
        :keyword init_containers: The init containers collection under a replica.
        :paramtype init_containers: list[~azure.mgmt.appcontainers.models.ReplicaContainer]
        """
        super().__init__(**kwargs)
        self.created_time = None
        self.running_state = None
        self.running_state_details = None
        self.containers = containers
        self.init_containers = init_containers


class ReplicaCollection(_serialization.Model):
    """Container App Revision Replicas collection ARM resource.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.Replica]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Replica]"},
    }

    def __init__(self, *, value: List["Replica"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.Replica]
        """
        super().__init__(**kwargs)
        self.value = value


class ReplicaContainer(_serialization.Model):
    """Container object under Container App Revision Replica.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar name: The Name of the Container.
    :vartype name: str
    :ivar container_id: The Id of the Container.
    :vartype container_id: str
    :ivar ready: The container ready status.
    :vartype ready: bool
    :ivar started: The container start status.
    :vartype started: bool
    :ivar restart_count: The container restart count.
    :vartype restart_count: int
    :ivar running_state: Current running state of the container. Known values are: "Running",
     "Terminated", and "Waiting".
    :vartype running_state: str or
     ~azure.mgmt.appcontainers.models.ContainerAppContainerRunningState
    :ivar running_state_details: The details of container current running state.
    :vartype running_state_details: str
    :ivar log_stream_endpoint: Log Stream endpoint.
    :vartype log_stream_endpoint: str
    :ivar exec_endpoint: Container exec endpoint.
    :vartype exec_endpoint: str
    """

    _validation = {
        "running_state": {"readonly": True},
        "running_state_details": {"readonly": True},
        "log_stream_endpoint": {"readonly": True},
        "exec_endpoint": {"readonly": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "container_id": {"key": "containerId", "type": "str"},
        "ready": {"key": "ready", "type": "bool"},
        "started": {"key": "started", "type": "bool"},
        "restart_count": {"key": "restartCount", "type": "int"},
        "running_state": {"key": "runningState", "type": "str"},
        "running_state_details": {"key": "runningStateDetails", "type": "str"},
        "log_stream_endpoint": {"key": "logStreamEndpoint", "type": "str"},
        "exec_endpoint": {"key": "execEndpoint", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        container_id: Optional[str] = None,
        ready: Optional[bool] = None,
        started: Optional[bool] = None,
        restart_count: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: The Name of the Container.
        :paramtype name: str
        :keyword container_id: The Id of the Container.
        :paramtype container_id: str
        :keyword ready: The container ready status.
        :paramtype ready: bool
        :keyword started: The container start status.
        :paramtype started: bool
        :keyword restart_count: The container restart count.
        :paramtype restart_count: int
        """
        super().__init__(**kwargs)
        self.name = name
        self.container_id = container_id
        self.ready = ready
        self.started = started
        self.restart_count = restart_count
        self.running_state = None
        self.running_state_details = None
        self.log_stream_endpoint = None
        self.exec_endpoint = None


class Revision(ProxyResource):
    """Container App Revision.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar created_time: Timestamp describing when the revision was created
     by controller.
    :vartype created_time: ~datetime.datetime
    :ivar last_active_time: Timestamp describing when the revision was last active. Only meaningful
     when revision is inactive.
    :vartype last_active_time: ~datetime.datetime
    :ivar fqdn: Fully qualified domain name of the revision.
    :vartype fqdn: str
    :ivar template: Container App Revision Template with all possible settings and the
     defaults if user did not provide them. The defaults are populated
     as they were at the creation time.
    :vartype template: ~azure.mgmt.appcontainers.models.Template
    :ivar active: Boolean describing if the Revision is Active.
    :vartype active: bool
    :ivar replicas: Number of pods currently running for this revision.
    :vartype replicas: int
    :ivar traffic_weight: Traffic weight assigned to this revision.
    :vartype traffic_weight: int
    :ivar provisioning_error: Optional Field - Platform Error Message.
    :vartype provisioning_error: str
    :ivar health_state: Current health State of the revision. Known values are: "Healthy",
     "Unhealthy", and "None".
    :vartype health_state: str or ~azure.mgmt.appcontainers.models.RevisionHealthState
    :ivar provisioning_state: Current provisioning State of the revision. Known values are:
     "Provisioning", "Provisioned", "Failed", "Deprovisioning", and "Deprovisioned".
    :vartype provisioning_state: str or ~azure.mgmt.appcontainers.models.RevisionProvisioningState
    :ivar running_state: Current running state of the revision. Known values are: "Running",
     "Processing", "Stopped", "Degraded", "Failed", and "Unknown".
    :vartype running_state: str or ~azure.mgmt.appcontainers.models.RevisionRunningState
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "created_time": {"readonly": True},
        "last_active_time": {"readonly": True},
        "fqdn": {"readonly": True},
        "template": {"readonly": True},
        "active": {"readonly": True},
        "replicas": {"readonly": True},
        "traffic_weight": {"readonly": True},
        "provisioning_error": {"readonly": True},
        "health_state": {"readonly": True},
        "provisioning_state": {"readonly": True},
        "running_state": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "created_time": {"key": "properties.createdTime", "type": "iso-8601"},
        "last_active_time": {"key": "properties.lastActiveTime", "type": "iso-8601"},
        "fqdn": {"key": "properties.fqdn", "type": "str"},
        "template": {"key": "properties.template", "type": "Template"},
        "active": {"key": "properties.active", "type": "bool"},
        "replicas": {"key": "properties.replicas", "type": "int"},
        "traffic_weight": {"key": "properties.trafficWeight", "type": "int"},
        "provisioning_error": {"key": "properties.provisioningError", "type": "str"},
        "health_state": {"key": "properties.healthState", "type": "str"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "running_state": {"key": "properties.runningState", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.created_time = None
        self.last_active_time = None
        self.fqdn = None
        self.template = None
        self.active = None
        self.replicas = None
        self.traffic_weight = None
        self.provisioning_error = None
        self.health_state = None
        self.provisioning_state = None
        self.running_state = None


class RevisionCollection(_serialization.Model):
    """Container App Revisions collection ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.Revision]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Revision]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["Revision"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.Revision]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class Runtime(_serialization.Model):
    """Container App Runtime configuration.

    :ivar java: Java app configuration.
    :vartype java: ~azure.mgmt.appcontainers.models.RuntimeJava
    """

    _attribute_map = {
        "java": {"key": "java", "type": "RuntimeJava"},
    }

    def __init__(self, *, java: Optional["RuntimeJava"] = None, **kwargs: Any) -> None:
        """
        :keyword java: Java app configuration.
        :paramtype java: ~azure.mgmt.appcontainers.models.RuntimeJava
        """
        super().__init__(**kwargs)
        self.java = java


class RuntimeJava(_serialization.Model):
    """Java app configuration.

    :ivar enable_metrics: Enable jmx core metrics for the java app.
    :vartype enable_metrics: bool
    """

    _attribute_map = {
        "enable_metrics": {"key": "enableMetrics", "type": "bool"},
    }

    def __init__(self, *, enable_metrics: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword enable_metrics: Enable jmx core metrics for the java app.
        :paramtype enable_metrics: bool
        """
        super().__init__(**kwargs)
        self.enable_metrics = enable_metrics


class Scale(_serialization.Model):
    """Container App scaling configurations.

    :ivar min_replicas: Optional. Minimum number of container replicas.
    :vartype min_replicas: int
    :ivar max_replicas: Optional. Maximum number of container replicas. Defaults to 10 if not set.
    :vartype max_replicas: int
    :ivar cooldown_period: Optional. KEDA Cooldown Period in seconds. Defaults to 300 seconds if
     not set.
    :vartype cooldown_period: int
    :ivar polling_interval: Optional. KEDA Polling Interval in seconds. Defaults to 30 seconds if
     not set.
    :vartype polling_interval: int
    :ivar rules: Scaling rules.
    :vartype rules: list[~azure.mgmt.appcontainers.models.ScaleRule]
    """

    _attribute_map = {
        "min_replicas": {"key": "minReplicas", "type": "int"},
        "max_replicas": {"key": "maxReplicas", "type": "int"},
        "cooldown_period": {"key": "cooldownPeriod", "type": "int"},
        "polling_interval": {"key": "pollingInterval", "type": "int"},
        "rules": {"key": "rules", "type": "[ScaleRule]"},
    }

    def __init__(
        self,
        *,
        min_replicas: Optional[int] = None,
        max_replicas: int = 10,
        cooldown_period: Optional[int] = None,
        polling_interval: Optional[int] = None,
        rules: Optional[List["ScaleRule"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword min_replicas: Optional. Minimum number of container replicas.
        :paramtype min_replicas: int
        :keyword max_replicas: Optional. Maximum number of container replicas. Defaults to 10 if not
         set.
        :paramtype max_replicas: int
        :keyword cooldown_period: Optional. KEDA Cooldown Period in seconds. Defaults to 300 seconds if
         not set.
        :paramtype cooldown_period: int
        :keyword polling_interval: Optional. KEDA Polling Interval in seconds. Defaults to 30 seconds
         if not set.
        :paramtype polling_interval: int
        :keyword rules: Scaling rules.
        :paramtype rules: list[~azure.mgmt.appcontainers.models.ScaleRule]
        """
        super().__init__(**kwargs)
        self.min_replicas = min_replicas
        self.max_replicas = max_replicas
        self.cooldown_period = cooldown_period
        self.polling_interval = polling_interval
        self.rules = rules


class ScaleConfiguration(_serialization.Model):
    """Scale configuration.

    :ivar max_concurrent_sessions: The maximum count of sessions at the same time.
    :vartype max_concurrent_sessions: int
    :ivar ready_session_instances: The minimum count of ready session instances.
    :vartype ready_session_instances: int
    """

    _attribute_map = {
        "max_concurrent_sessions": {"key": "maxConcurrentSessions", "type": "int"},
        "ready_session_instances": {"key": "readySessionInstances", "type": "int"},
    }

    def __init__(
        self,
        *,
        max_concurrent_sessions: Optional[int] = None,
        ready_session_instances: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword max_concurrent_sessions: The maximum count of sessions at the same time.
        :paramtype max_concurrent_sessions: int
        :keyword ready_session_instances: The minimum count of ready session instances.
        :paramtype ready_session_instances: int
        """
        super().__init__(**kwargs)
        self.max_concurrent_sessions = max_concurrent_sessions
        self.ready_session_instances = ready_session_instances


class ScaleRule(_serialization.Model):
    """Container App container scaling rule.

    :ivar name: Scale Rule Name.
    :vartype name: str
    :ivar azure_queue: Azure Queue based scaling.
    :vartype azure_queue: ~azure.mgmt.appcontainers.models.QueueScaleRule
    :ivar custom: Custom scale rule.
    :vartype custom: ~azure.mgmt.appcontainers.models.CustomScaleRule
    :ivar http: HTTP requests based scaling.
    :vartype http: ~azure.mgmt.appcontainers.models.HttpScaleRule
    :ivar tcp: Tcp requests based scaling.
    :vartype tcp: ~azure.mgmt.appcontainers.models.TcpScaleRule
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "azure_queue": {"key": "azureQueue", "type": "QueueScaleRule"},
        "custom": {"key": "custom", "type": "CustomScaleRule"},
        "http": {"key": "http", "type": "HttpScaleRule"},
        "tcp": {"key": "tcp", "type": "TcpScaleRule"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        azure_queue: Optional["QueueScaleRule"] = None,
        custom: Optional["CustomScaleRule"] = None,
        http: Optional["HttpScaleRule"] = None,
        tcp: Optional["TcpScaleRule"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Scale Rule Name.
        :paramtype name: str
        :keyword azure_queue: Azure Queue based scaling.
        :paramtype azure_queue: ~azure.mgmt.appcontainers.models.QueueScaleRule
        :keyword custom: Custom scale rule.
        :paramtype custom: ~azure.mgmt.appcontainers.models.CustomScaleRule
        :keyword http: HTTP requests based scaling.
        :paramtype http: ~azure.mgmt.appcontainers.models.HttpScaleRule
        :keyword tcp: Tcp requests based scaling.
        :paramtype tcp: ~azure.mgmt.appcontainers.models.TcpScaleRule
        """
        super().__init__(**kwargs)
        self.name = name
        self.azure_queue = azure_queue
        self.custom = custom
        self.http = http
        self.tcp = tcp


class ScaleRuleAuth(_serialization.Model):
    """Auth Secrets for Scale Rule.

    :ivar secret_ref: Name of the secret from which to pull the auth params.
    :vartype secret_ref: str
    :ivar trigger_parameter: Trigger Parameter that uses the secret.
    :vartype trigger_parameter: str
    """

    _attribute_map = {
        "secret_ref": {"key": "secretRef", "type": "str"},
        "trigger_parameter": {"key": "triggerParameter", "type": "str"},
    }

    def __init__(
        self, *, secret_ref: Optional[str] = None, trigger_parameter: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword secret_ref: Name of the secret from which to pull the auth params.
        :paramtype secret_ref: str
        :keyword trigger_parameter: Trigger Parameter that uses the secret.
        :paramtype trigger_parameter: str
        """
        super().__init__(**kwargs)
        self.secret_ref = secret_ref
        self.trigger_parameter = trigger_parameter


class Secret(_serialization.Model):
    """Secret definition.

    :ivar name: Secret Name.
    :vartype name: str
    :ivar value: Secret Value.
    :vartype value: str
    :ivar identity: Resource ID of a managed identity to authenticate with Azure Key Vault, or
     System to use a system-assigned identity.
    :vartype identity: str
    :ivar key_vault_url: Azure Key Vault URL pointing to the secret referenced by the container
     app.
    :vartype key_vault_url: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
        "identity": {"key": "identity", "type": "str"},
        "key_vault_url": {"key": "keyVaultUrl", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        value: Optional[str] = None,
        identity: Optional[str] = None,
        key_vault_url: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Secret Name.
        :paramtype name: str
        :keyword value: Secret Value.
        :paramtype value: str
        :keyword identity: Resource ID of a managed identity to authenticate with Azure Key Vault, or
         System to use a system-assigned identity.
        :paramtype identity: str
        :keyword key_vault_url: Azure Key Vault URL pointing to the secret referenced by the container
         app.
        :paramtype key_vault_url: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.value = value
        self.identity = identity
        self.key_vault_url = key_vault_url


class SecretsCollection(_serialization.Model):
    """Container App Secrets Collection ARM resource.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.ContainerAppSecret]
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ContainerAppSecret]"},
    }

    def __init__(self, *, value: List["ContainerAppSecret"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.ContainerAppSecret]
        """
        super().__init__(**kwargs)
        self.value = value


class SecretVolumeItem(_serialization.Model):
    """Secret to be added to volume.

    :ivar secret_ref: Name of the Container App secret from which to pull the secret value.
    :vartype secret_ref: str
    :ivar path: Path to project secret to. If no path is provided, path defaults to name of secret
     listed in secretRef.
    :vartype path: str
    """

    _attribute_map = {
        "secret_ref": {"key": "secretRef", "type": "str"},
        "path": {"key": "path", "type": "str"},
    }

    def __init__(self, *, secret_ref: Optional[str] = None, path: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword secret_ref: Name of the Container App secret from which to pull the secret value.
        :paramtype secret_ref: str
        :keyword path: Path to project secret to. If no path is provided, path defaults to name of
         secret listed in secretRef.
        :paramtype path: str
        """
        super().__init__(**kwargs)
        self.secret_ref = secret_ref
        self.path = path


class Service(_serialization.Model):
    """Container App to be a dev service.

    All required parameters must be populated in order to send to server.

    :ivar type: Dev ContainerApp service type. Required.
    :vartype type: str
    """

    _validation = {
        "type": {"required": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
    }

    def __init__(self, *, type: str, **kwargs: Any) -> None:
        """
        :keyword type: Dev ContainerApp service type. Required.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.type = type


class ServiceBind(_serialization.Model):
    """Configuration to bind a ContainerApp to a dev ContainerApp Service.

    :ivar service_id: Resource id of the target service.
    :vartype service_id: str
    :ivar name: Name of the service bind.
    :vartype name: str
    """

    _attribute_map = {
        "service_id": {"key": "serviceId", "type": "str"},
        "name": {"key": "name", "type": "str"},
    }

    def __init__(self, *, service_id: Optional[str] = None, name: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword service_id: Resource id of the target service.
        :paramtype service_id: str
        :keyword name: Name of the service bind.
        :paramtype name: str
        """
        super().__init__(**kwargs)
        self.service_id = service_id
        self.name = name


class SessionContainer(_serialization.Model):
    """Container definitions for the sessions of the session pool.

    :ivar image: Container image tag.
    :vartype image: str
    :ivar name: Custom container name.
    :vartype name: str
    :ivar command: Container start command.
    :vartype command: list[str]
    :ivar args: Container start command arguments.
    :vartype args: list[str]
    :ivar env: Container environment variables.
    :vartype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
    :ivar resources: Container resource requirements.
    :vartype resources: ~azure.mgmt.appcontainers.models.SessionContainerResources
    """

    _attribute_map = {
        "image": {"key": "image", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "command": {"key": "command", "type": "[str]"},
        "args": {"key": "args", "type": "[str]"},
        "env": {"key": "env", "type": "[EnvironmentVar]"},
        "resources": {"key": "resources", "type": "SessionContainerResources"},
    }

    def __init__(
        self,
        *,
        image: Optional[str] = None,
        name: Optional[str] = None,
        command: Optional[List[str]] = None,
        args: Optional[List[str]] = None,
        env: Optional[List["EnvironmentVar"]] = None,
        resources: Optional["SessionContainerResources"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword image: Container image tag.
        :paramtype image: str
        :keyword name: Custom container name.
        :paramtype name: str
        :keyword command: Container start command.
        :paramtype command: list[str]
        :keyword args: Container start command arguments.
        :paramtype args: list[str]
        :keyword env: Container environment variables.
        :paramtype env: list[~azure.mgmt.appcontainers.models.EnvironmentVar]
        :keyword resources: Container resource requirements.
        :paramtype resources: ~azure.mgmt.appcontainers.models.SessionContainerResources
        """
        super().__init__(**kwargs)
        self.image = image
        self.name = name
        self.command = command
        self.args = args
        self.env = env
        self.resources = resources


class SessionContainerResources(_serialization.Model):
    """Container resource requirements for sessions of the session pool.

    :ivar cpu: Required CPU in cores, e.g. 0.5.
    :vartype cpu: float
    :ivar memory: Required memory, e.g. "250Mb".
    :vartype memory: str
    """

    _attribute_map = {
        "cpu": {"key": "cpu", "type": "float"},
        "memory": {"key": "memory", "type": "str"},
    }

    def __init__(self, *, cpu: Optional[float] = None, memory: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword cpu: Required CPU in cores, e.g. 0.5.
        :paramtype cpu: float
        :keyword memory: Required memory, e.g. "250Mb".
        :paramtype memory: str
        """
        super().__init__(**kwargs)
        self.cpu = cpu
        self.memory = memory


class SessionIngress(_serialization.Model):
    """Session pool ingress configuration.

    :ivar target_port: Target port in containers for traffic from ingress.
    :vartype target_port: int
    """

    _attribute_map = {
        "target_port": {"key": "targetPort", "type": "int"},
    }

    def __init__(self, *, target_port: Optional[int] = None, **kwargs: Any) -> None:
        """
        :keyword target_port: Target port in containers for traffic from ingress.
        :paramtype target_port: int
        """
        super().__init__(**kwargs)
        self.target_port = target_port


class SessionNetworkConfiguration(_serialization.Model):
    """Session network configuration.

    :ivar status: Network status for the sessions. Known values are: "EgressEnabled" and
     "EgressDisabled".
    :vartype status: str or ~azure.mgmt.appcontainers.models.SessionNetworkStatus
    """

    _attribute_map = {
        "status": {"key": "status", "type": "str"},
    }

    def __init__(self, *, status: Optional[Union[str, "SessionNetworkStatus"]] = None, **kwargs: Any) -> None:
        """
        :keyword status: Network status for the sessions. Known values are: "EgressEnabled" and
         "EgressDisabled".
        :paramtype status: str or ~azure.mgmt.appcontainers.models.SessionNetworkStatus
        """
        super().__init__(**kwargs)
        self.status = status


class SessionPool(TrackedResource):
    """Container App session pool.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar location: The geo-location where the resource lives. Required.
    :vartype location: str
    :ivar identity: Managed identities needed by a session pool to interact with other Azure
     services to not maintain any secrets or credentials in code.
    :vartype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
    :ivar environment_id: Resource ID of the session pool's environment.
    :vartype environment_id: str
    :ivar container_type: The container type of the sessions. Known values are: "CustomContainer"
     and "PythonLTS".
    :vartype container_type: str or ~azure.mgmt.appcontainers.models.ContainerType
    :ivar pool_management_type: The pool management type of the session pool. Known values are:
     "Manual" and "Dynamic".
    :vartype pool_management_type: str or ~azure.mgmt.appcontainers.models.PoolManagementType
    :ivar node_count: The number of nodes the session pool is using.
    :vartype node_count: int
    :ivar scale_configuration: The scale configuration of the session pool.
    :vartype scale_configuration: ~azure.mgmt.appcontainers.models.ScaleConfiguration
    :ivar secrets: The secrets of the session pool.
    :vartype secrets: list[~azure.mgmt.appcontainers.models.SessionPoolSecret]
    :ivar dynamic_pool_configuration: The pool configuration if the poolManagementType is dynamic.
    :vartype dynamic_pool_configuration: ~azure.mgmt.appcontainers.models.DynamicPoolConfiguration
    :ivar custom_container_template: The custom container configuration if the containerType is
     CustomContainer.
    :vartype custom_container_template: ~azure.mgmt.appcontainers.models.CustomContainerTemplate
    :ivar session_network_configuration: The network configuration of the sessions in the session
     pool.
    :vartype session_network_configuration:
     ~azure.mgmt.appcontainers.models.SessionNetworkConfiguration
    :ivar pool_management_endpoint: The endpoint to manage the pool.
    :vartype pool_management_endpoint: str
    :ivar provisioning_state: Provisioning state of the session pool. Known values are:
     "InProgress", "Succeeded", "Failed", "Canceled", and "Deleting".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.SessionPoolProvisioningState
    :ivar managed_identity_settings: Optional settings for a Managed Identity that is assigned to
     the Session pool.
    :vartype managed_identity_settings:
     list[~azure.mgmt.appcontainers.models.ManagedIdentitySetting]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "location": {"required": True},
        "node_count": {"readonly": True},
        "pool_management_endpoint": {"readonly": True},
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "tags": {"key": "tags", "type": "{str}"},
        "location": {"key": "location", "type": "str"},
        "identity": {"key": "identity", "type": "ManagedServiceIdentity"},
        "environment_id": {"key": "properties.environmentId", "type": "str"},
        "container_type": {"key": "properties.containerType", "type": "str"},
        "pool_management_type": {"key": "properties.poolManagementType", "type": "str"},
        "node_count": {"key": "properties.nodeCount", "type": "int"},
        "scale_configuration": {"key": "properties.scaleConfiguration", "type": "ScaleConfiguration"},
        "secrets": {"key": "properties.secrets", "type": "[SessionPoolSecret]"},
        "dynamic_pool_configuration": {
            "key": "properties.dynamicPoolConfiguration",
            "type": "DynamicPoolConfiguration",
        },
        "custom_container_template": {"key": "properties.customContainerTemplate", "type": "CustomContainerTemplate"},
        "session_network_configuration": {
            "key": "properties.sessionNetworkConfiguration",
            "type": "SessionNetworkConfiguration",
        },
        "pool_management_endpoint": {"key": "properties.poolManagementEndpoint", "type": "str"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "managed_identity_settings": {"key": "properties.managedIdentitySettings", "type": "[ManagedIdentitySetting]"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        identity: Optional["ManagedServiceIdentity"] = None,
        environment_id: Optional[str] = None,
        container_type: Optional[Union[str, "ContainerType"]] = None,
        pool_management_type: Optional[Union[str, "PoolManagementType"]] = None,
        scale_configuration: Optional["ScaleConfiguration"] = None,
        secrets: Optional[List["SessionPoolSecret"]] = None,
        dynamic_pool_configuration: Optional["DynamicPoolConfiguration"] = None,
        custom_container_template: Optional["CustomContainerTemplate"] = None,
        session_network_configuration: Optional["SessionNetworkConfiguration"] = None,
        managed_identity_settings: Optional[List["ManagedIdentitySetting"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword location: The geo-location where the resource lives. Required.
        :paramtype location: str
        :keyword identity: Managed identities needed by a session pool to interact with other Azure
         services to not maintain any secrets or credentials in code.
        :paramtype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
        :keyword environment_id: Resource ID of the session pool's environment.
        :paramtype environment_id: str
        :keyword container_type: The container type of the sessions. Known values are:
         "CustomContainer" and "PythonLTS".
        :paramtype container_type: str or ~azure.mgmt.appcontainers.models.ContainerType
        :keyword pool_management_type: The pool management type of the session pool. Known values are:
         "Manual" and "Dynamic".
        :paramtype pool_management_type: str or ~azure.mgmt.appcontainers.models.PoolManagementType
        :keyword scale_configuration: The scale configuration of the session pool.
        :paramtype scale_configuration: ~azure.mgmt.appcontainers.models.ScaleConfiguration
        :keyword secrets: The secrets of the session pool.
        :paramtype secrets: list[~azure.mgmt.appcontainers.models.SessionPoolSecret]
        :keyword dynamic_pool_configuration: The pool configuration if the poolManagementType is
         dynamic.
        :paramtype dynamic_pool_configuration:
         ~azure.mgmt.appcontainers.models.DynamicPoolConfiguration
        :keyword custom_container_template: The custom container configuration if the containerType is
         CustomContainer.
        :paramtype custom_container_template: ~azure.mgmt.appcontainers.models.CustomContainerTemplate
        :keyword session_network_configuration: The network configuration of the sessions in the
         session pool.
        :paramtype session_network_configuration:
         ~azure.mgmt.appcontainers.models.SessionNetworkConfiguration
        :keyword managed_identity_settings: Optional settings for a Managed Identity that is assigned
         to the Session pool.
        :paramtype managed_identity_settings:
         list[~azure.mgmt.appcontainers.models.ManagedIdentitySetting]
        """
        super().__init__(tags=tags, location=location, **kwargs)
        self.identity = identity
        self.environment_id = environment_id
        self.container_type = container_type
        self.pool_management_type = pool_management_type
        self.node_count = None
        self.scale_configuration = scale_configuration
        self.secrets = secrets
        self.dynamic_pool_configuration = dynamic_pool_configuration
        self.custom_container_template = custom_container_template
        self.session_network_configuration = session_network_configuration
        self.pool_management_endpoint = None
        self.provisioning_state = None
        self.managed_identity_settings = managed_identity_settings


class SessionPoolCollection(_serialization.Model):
    """Session pool collection Azure resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.SessionPool]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[SessionPool]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["SessionPool"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.SessionPool]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class SessionPoolSecret(_serialization.Model):
    """Secret definition.

    :ivar name: Secret Name.
    :vartype name: str
    :ivar value: Secret Value.
    :vartype value: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "value": {"key": "value", "type": "str"},
    }

    def __init__(self, *, name: Optional[str] = None, value: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword name: Secret Name.
        :paramtype name: str
        :keyword value: Secret Value.
        :paramtype value: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.value = value


class SessionPoolUpdatableProperties(_serialization.Model):
    """Container App session pool updatable properties.

    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar identity: Managed identities needed by a session pool to interact with other Azure
     services to not maintain any secrets or credentials in code.
    :vartype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
    :ivar scale_configuration: The scale configuration of the session pool.
    :vartype scale_configuration: ~azure.mgmt.appcontainers.models.ScaleConfiguration
    :ivar secrets: The secrets of the session pool.
    :vartype secrets: list[~azure.mgmt.appcontainers.models.SessionPoolSecret]
    :ivar dynamic_pool_configuration: The pool configuration if the poolManagementType is dynamic.
    :vartype dynamic_pool_configuration: ~azure.mgmt.appcontainers.models.DynamicPoolConfiguration
    :ivar custom_container_template: The custom container configuration if the containerType is
     CustomContainer.
    :vartype custom_container_template: ~azure.mgmt.appcontainers.models.CustomContainerTemplate
    :ivar session_network_configuration: The network configuration of the sessions in the session
     pool.
    :vartype session_network_configuration:
     ~azure.mgmt.appcontainers.models.SessionNetworkConfiguration
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
        "identity": {"key": "identity", "type": "ManagedServiceIdentity"},
        "scale_configuration": {"key": "properties.scaleConfiguration", "type": "ScaleConfiguration"},
        "secrets": {"key": "properties.secrets", "type": "[SessionPoolSecret]"},
        "dynamic_pool_configuration": {
            "key": "properties.dynamicPoolConfiguration",
            "type": "DynamicPoolConfiguration",
        },
        "custom_container_template": {"key": "properties.customContainerTemplate", "type": "CustomContainerTemplate"},
        "session_network_configuration": {
            "key": "properties.sessionNetworkConfiguration",
            "type": "SessionNetworkConfiguration",
        },
    }

    def __init__(
        self,
        *,
        tags: Optional[Dict[str, str]] = None,
        identity: Optional["ManagedServiceIdentity"] = None,
        scale_configuration: Optional["ScaleConfiguration"] = None,
        secrets: Optional[List["SessionPoolSecret"]] = None,
        dynamic_pool_configuration: Optional["DynamicPoolConfiguration"] = None,
        custom_container_template: Optional["CustomContainerTemplate"] = None,
        session_network_configuration: Optional["SessionNetworkConfiguration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword identity: Managed identities needed by a session pool to interact with other Azure
         services to not maintain any secrets or credentials in code.
        :paramtype identity: ~azure.mgmt.appcontainers.models.ManagedServiceIdentity
        :keyword scale_configuration: The scale configuration of the session pool.
        :paramtype scale_configuration: ~azure.mgmt.appcontainers.models.ScaleConfiguration
        :keyword secrets: The secrets of the session pool.
        :paramtype secrets: list[~azure.mgmt.appcontainers.models.SessionPoolSecret]
        :keyword dynamic_pool_configuration: The pool configuration if the poolManagementType is
         dynamic.
        :paramtype dynamic_pool_configuration:
         ~azure.mgmt.appcontainers.models.DynamicPoolConfiguration
        :keyword custom_container_template: The custom container configuration if the containerType is
         CustomContainer.
        :paramtype custom_container_template: ~azure.mgmt.appcontainers.models.CustomContainerTemplate
        :keyword session_network_configuration: The network configuration of the sessions in the
         session pool.
        :paramtype session_network_configuration:
         ~azure.mgmt.appcontainers.models.SessionNetworkConfiguration
        """
        super().__init__(**kwargs)
        self.tags = tags
        self.identity = identity
        self.scale_configuration = scale_configuration
        self.secrets = secrets
        self.dynamic_pool_configuration = dynamic_pool_configuration
        self.custom_container_template = custom_container_template
        self.session_network_configuration = session_network_configuration


class SessionRegistryCredentials(_serialization.Model):
    """Session pool private registry credentials.

    :ivar server: Container registry server.
    :vartype server: str
    :ivar username: Container registry username.
    :vartype username: str
    :ivar password_secret_ref: The name of the secret that contains the registry login password.
    :vartype password_secret_ref: str
    :ivar identity: A Managed Identity to use to authenticate with Azure Container Registry. For
     user-assigned identities, use the full user-assigned identity Resource ID. For system-assigned
     identities, use 'system'.
    :vartype identity: str
    """

    _attribute_map = {
        "server": {"key": "server", "type": "str"},
        "username": {"key": "username", "type": "str"},
        "password_secret_ref": {"key": "passwordSecretRef", "type": "str"},
        "identity": {"key": "identity", "type": "str"},
    }

    def __init__(
        self,
        *,
        server: Optional[str] = None,
        username: Optional[str] = None,
        password_secret_ref: Optional[str] = None,
        identity: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword server: Container registry server.
        :paramtype server: str
        :keyword username: Container registry username.
        :paramtype username: str
        :keyword password_secret_ref: The name of the secret that contains the registry login password.
        :paramtype password_secret_ref: str
        :keyword identity: A Managed Identity to use to authenticate with Azure Container Registry. For
         user-assigned identities, use the full user-assigned identity Resource ID. For system-assigned
         identities, use 'system'.
        :paramtype identity: str
        """
        super().__init__(**kwargs)
        self.server = server
        self.username = username
        self.password_secret_ref = password_secret_ref
        self.identity = identity


class SourceControl(ProxyResource):
    """Container App SourceControl.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar operation_state: Current provisioning State of the operation. Known values are:
     "InProgress", "Succeeded", "Failed", and "Canceled".
    :vartype operation_state: str or ~azure.mgmt.appcontainers.models.SourceControlOperationState
    :ivar repo_url: The repo url which will be integrated to ContainerApp.
    :vartype repo_url: str
    :ivar branch: The branch which will trigger the auto deployment.
    :vartype branch: str
    :ivar github_action_configuration: Container App Revision Template with all possible settings
     and the
     defaults if user did not provide them. The defaults are populated
     as they were at the creation time.
    :vartype github_action_configuration:
     ~azure.mgmt.appcontainers.models.GithubActionConfiguration
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "operation_state": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "operation_state": {"key": "properties.operationState", "type": "str"},
        "repo_url": {"key": "properties.repoUrl", "type": "str"},
        "branch": {"key": "properties.branch", "type": "str"},
        "github_action_configuration": {
            "key": "properties.githubActionConfiguration",
            "type": "GithubActionConfiguration",
        },
    }

    def __init__(
        self,
        *,
        repo_url: Optional[str] = None,
        branch: Optional[str] = None,
        github_action_configuration: Optional["GithubActionConfiguration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword repo_url: The repo url which will be integrated to ContainerApp.
        :paramtype repo_url: str
        :keyword branch: The branch which will trigger the auto deployment.
        :paramtype branch: str
        :keyword github_action_configuration: Container App Revision Template with all possible
         settings and the
         defaults if user did not provide them. The defaults are populated
         as they were at the creation time.
        :paramtype github_action_configuration:
         ~azure.mgmt.appcontainers.models.GithubActionConfiguration
        """
        super().__init__(**kwargs)
        self.operation_state = None
        self.repo_url = repo_url
        self.branch = branch
        self.github_action_configuration = github_action_configuration


class SourceControlCollection(_serialization.Model):
    """SourceControl collection ARM resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.SourceControl]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[SourceControl]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["SourceControl"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.SourceControl]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class SpringBootAdminComponent(JavaComponentProperties):
    """Spring Boot Admin properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar component_type: Type of the Java Component. Required. Known values are:
     "SpringBootAdmin", "SpringCloudEureka", and "SpringCloudConfig".
    :vartype component_type: str or ~azure.mgmt.appcontainers.models.JavaComponentType
    :ivar provisioning_state: Provisioning state of the Java Component. Known values are:
     "Succeeded", "Failed", "Canceled", "Deleting", and "InProgress".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.JavaComponentProvisioningState
    :ivar configurations: List of Java Components configuration properties.
    :vartype configurations:
     list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
    :ivar scale: Java component scaling configurations.
    :vartype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
    :ivar service_binds: List of Java Components that are bound to the Java component.
    :vartype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
    :ivar ingress: Java Component Ingress configurations.
    :vartype ingress: ~azure.mgmt.appcontainers.models.JavaComponentIngress
    """

    _validation = {
        "component_type": {"required": True},
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "component_type": {"key": "componentType", "type": "str"},
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "configurations": {"key": "configurations", "type": "[JavaComponentConfigurationProperty]"},
        "scale": {"key": "scale", "type": "JavaComponentPropertiesScale"},
        "service_binds": {"key": "serviceBinds", "type": "[JavaComponentServiceBind]"},
        "ingress": {"key": "ingress", "type": "JavaComponentIngress"},
    }

    def __init__(
        self,
        *,
        configurations: Optional[List["JavaComponentConfigurationProperty"]] = None,
        scale: Optional["JavaComponentPropertiesScale"] = None,
        service_binds: Optional[List["JavaComponentServiceBind"]] = None,
        ingress: Optional["JavaComponentIngress"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword configurations: List of Java Components configuration properties.
        :paramtype configurations:
         list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
        :keyword scale: Java component scaling configurations.
        :paramtype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
        :keyword service_binds: List of Java Components that are bound to the Java component.
        :paramtype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
        :keyword ingress: Java Component Ingress configurations.
        :paramtype ingress: ~azure.mgmt.appcontainers.models.JavaComponentIngress
        """
        super().__init__(configurations=configurations, scale=scale, service_binds=service_binds, **kwargs)
        self.component_type: str = "SpringBootAdmin"
        self.ingress = ingress


class SpringCloudConfigComponent(JavaComponentProperties):
    """Spring Cloud Config properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar component_type: Type of the Java Component. Required. Known values are:
     "SpringBootAdmin", "SpringCloudEureka", and "SpringCloudConfig".
    :vartype component_type: str or ~azure.mgmt.appcontainers.models.JavaComponentType
    :ivar provisioning_state: Provisioning state of the Java Component. Known values are:
     "Succeeded", "Failed", "Canceled", "Deleting", and "InProgress".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.JavaComponentProvisioningState
    :ivar configurations: List of Java Components configuration properties.
    :vartype configurations:
     list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
    :ivar scale: Java component scaling configurations.
    :vartype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
    :ivar service_binds: List of Java Components that are bound to the Java component.
    :vartype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
    """

    _validation = {
        "component_type": {"required": True},
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "component_type": {"key": "componentType", "type": "str"},
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "configurations": {"key": "configurations", "type": "[JavaComponentConfigurationProperty]"},
        "scale": {"key": "scale", "type": "JavaComponentPropertiesScale"},
        "service_binds": {"key": "serviceBinds", "type": "[JavaComponentServiceBind]"},
    }

    def __init__(
        self,
        *,
        configurations: Optional[List["JavaComponentConfigurationProperty"]] = None,
        scale: Optional["JavaComponentPropertiesScale"] = None,
        service_binds: Optional[List["JavaComponentServiceBind"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword configurations: List of Java Components configuration properties.
        :paramtype configurations:
         list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
        :keyword scale: Java component scaling configurations.
        :paramtype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
        :keyword service_binds: List of Java Components that are bound to the Java component.
        :paramtype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
        """
        super().__init__(configurations=configurations, scale=scale, service_binds=service_binds, **kwargs)
        self.component_type: str = "SpringCloudConfig"


class SpringCloudEurekaComponent(JavaComponentProperties):
    """Spring Cloud Eureka properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar component_type: Type of the Java Component. Required. Known values are:
     "SpringBootAdmin", "SpringCloudEureka", and "SpringCloudConfig".
    :vartype component_type: str or ~azure.mgmt.appcontainers.models.JavaComponentType
    :ivar provisioning_state: Provisioning state of the Java Component. Known values are:
     "Succeeded", "Failed", "Canceled", "Deleting", and "InProgress".
    :vartype provisioning_state: str or
     ~azure.mgmt.appcontainers.models.JavaComponentProvisioningState
    :ivar configurations: List of Java Components configuration properties.
    :vartype configurations:
     list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
    :ivar scale: Java component scaling configurations.
    :vartype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
    :ivar service_binds: List of Java Components that are bound to the Java component.
    :vartype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
    :ivar ingress: Java Component Ingress configurations.
    :vartype ingress: ~azure.mgmt.appcontainers.models.JavaComponentIngress
    """

    _validation = {
        "component_type": {"required": True},
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "component_type": {"key": "componentType", "type": "str"},
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "configurations": {"key": "configurations", "type": "[JavaComponentConfigurationProperty]"},
        "scale": {"key": "scale", "type": "JavaComponentPropertiesScale"},
        "service_binds": {"key": "serviceBinds", "type": "[JavaComponentServiceBind]"},
        "ingress": {"key": "ingress", "type": "JavaComponentIngress"},
    }

    def __init__(
        self,
        *,
        configurations: Optional[List["JavaComponentConfigurationProperty"]] = None,
        scale: Optional["JavaComponentPropertiesScale"] = None,
        service_binds: Optional[List["JavaComponentServiceBind"]] = None,
        ingress: Optional["JavaComponentIngress"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword configurations: List of Java Components configuration properties.
        :paramtype configurations:
         list[~azure.mgmt.appcontainers.models.JavaComponentConfigurationProperty]
        :keyword scale: Java component scaling configurations.
        :paramtype scale: ~azure.mgmt.appcontainers.models.JavaComponentPropertiesScale
        :keyword service_binds: List of Java Components that are bound to the Java component.
        :paramtype service_binds: list[~azure.mgmt.appcontainers.models.JavaComponentServiceBind]
        :keyword ingress: Java Component Ingress configurations.
        :paramtype ingress: ~azure.mgmt.appcontainers.models.JavaComponentIngress
        """
        super().__init__(configurations=configurations, scale=scale, service_binds=service_binds, **kwargs)
        self.component_type: str = "SpringCloudEureka"
        self.ingress = ingress


class SystemData(_serialization.Model):
    """Metadata pertaining to creation and last modification of the resource.

    :ivar created_by: The identity that created the resource.
    :vartype created_by: str
    :ivar created_by_type: The type of identity that created the resource. Known values are:
     "User", "Application", "ManagedIdentity", and "Key".
    :vartype created_by_type: str or ~azure.mgmt.appcontainers.models.CreatedByType
    :ivar created_at: The timestamp of resource creation (UTC).
    :vartype created_at: ~datetime.datetime
    :ivar last_modified_by: The identity that last modified the resource.
    :vartype last_modified_by: str
    :ivar last_modified_by_type: The type of identity that last modified the resource. Known values
     are: "User", "Application", "ManagedIdentity", and "Key".
    :vartype last_modified_by_type: str or ~azure.mgmt.appcontainers.models.CreatedByType
    :ivar last_modified_at: The timestamp of resource last modification (UTC).
    :vartype last_modified_at: ~datetime.datetime
    """

    _attribute_map = {
        "created_by": {"key": "createdBy", "type": "str"},
        "created_by_type": {"key": "createdByType", "type": "str"},
        "created_at": {"key": "createdAt", "type": "iso-8601"},
        "last_modified_by": {"key": "lastModifiedBy", "type": "str"},
        "last_modified_by_type": {"key": "lastModifiedByType", "type": "str"},
        "last_modified_at": {"key": "lastModifiedAt", "type": "iso-8601"},
    }

    def __init__(
        self,
        *,
        created_by: Optional[str] = None,
        created_by_type: Optional[Union[str, "CreatedByType"]] = None,
        created_at: Optional[datetime.datetime] = None,
        last_modified_by: Optional[str] = None,
        last_modified_by_type: Optional[Union[str, "CreatedByType"]] = None,
        last_modified_at: Optional[datetime.datetime] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword created_by: The identity that created the resource.
        :paramtype created_by: str
        :keyword created_by_type: The type of identity that created the resource. Known values are:
         "User", "Application", "ManagedIdentity", and "Key".
        :paramtype created_by_type: str or ~azure.mgmt.appcontainers.models.CreatedByType
        :keyword created_at: The timestamp of resource creation (UTC).
        :paramtype created_at: ~datetime.datetime
        :keyword last_modified_by: The identity that last modified the resource.
        :paramtype last_modified_by: str
        :keyword last_modified_by_type: The type of identity that last modified the resource. Known
         values are: "User", "Application", "ManagedIdentity", and "Key".
        :paramtype last_modified_by_type: str or ~azure.mgmt.appcontainers.models.CreatedByType
        :keyword last_modified_at: The timestamp of resource last modification (UTC).
        :paramtype last_modified_at: ~datetime.datetime
        """
        super().__init__(**kwargs)
        self.created_by = created_by
        self.created_by_type = created_by_type
        self.created_at = created_at
        self.last_modified_by = last_modified_by
        self.last_modified_by_type = last_modified_by_type
        self.last_modified_at = last_modified_at


class TcpScaleRule(_serialization.Model):
    """Container App container Tcp scaling rule.

    :ivar metadata: Metadata properties to describe tcp scale rule.
    :vartype metadata: dict[str, str]
    :ivar auth: Authentication secrets for the tcp scale rule.
    :vartype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
    :ivar identity: The resource ID of a user-assigned managed identity that is assigned to the
     Container App, or 'system' for system-assigned identity.
    :vartype identity: str
    """

    _attribute_map = {
        "metadata": {"key": "metadata", "type": "{str}"},
        "auth": {"key": "auth", "type": "[ScaleRuleAuth]"},
        "identity": {"key": "identity", "type": "str"},
    }

    def __init__(
        self,
        *,
        metadata: Optional[Dict[str, str]] = None,
        auth: Optional[List["ScaleRuleAuth"]] = None,
        identity: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword metadata: Metadata properties to describe tcp scale rule.
        :paramtype metadata: dict[str, str]
        :keyword auth: Authentication secrets for the tcp scale rule.
        :paramtype auth: list[~azure.mgmt.appcontainers.models.ScaleRuleAuth]
        :keyword identity: The resource ID of a user-assigned managed identity that is assigned to the
         Container App, or 'system' for system-assigned identity.
        :paramtype identity: str
        """
        super().__init__(**kwargs)
        self.metadata = metadata
        self.auth = auth
        self.identity = identity


class Template(_serialization.Model):
    """Container App versioned application definition.
    Defines the desired state of an immutable revision.
    Any changes to this section Will result in a new revision being created.

    :ivar revision_suffix: User friendly suffix that is appended to the revision name.
    :vartype revision_suffix: str
    :ivar termination_grace_period_seconds: Optional duration in seconds the Container App Instance
     needs to terminate gracefully. Value must be non-negative integer. The value zero indicates
     stop immediately via the kill signal (no opportunity to shut down). If this value is nil, the
     default grace period will be used instead. Set this value longer than the expected cleanup time
     for your process. Defaults to 30 seconds.
    :vartype termination_grace_period_seconds: int
    :ivar init_containers: List of specialized containers that run before app containers.
    :vartype init_containers: list[~azure.mgmt.appcontainers.models.InitContainer]
    :ivar containers: List of container definitions for the Container App.
    :vartype containers: list[~azure.mgmt.appcontainers.models.Container]
    :ivar scale: Scaling properties for the Container App.
    :vartype scale: ~azure.mgmt.appcontainers.models.Scale
    :ivar volumes: List of volume definitions for the Container App.
    :vartype volumes: list[~azure.mgmt.appcontainers.models.Volume]
    :ivar service_binds: List of container app services bound to the app.
    :vartype service_binds: list[~azure.mgmt.appcontainers.models.ServiceBind]
    """

    _attribute_map = {
        "revision_suffix": {"key": "revisionSuffix", "type": "str"},
        "termination_grace_period_seconds": {"key": "terminationGracePeriodSeconds", "type": "int"},
        "init_containers": {"key": "initContainers", "type": "[InitContainer]"},
        "containers": {"key": "containers", "type": "[Container]"},
        "scale": {"key": "scale", "type": "Scale"},
        "volumes": {"key": "volumes", "type": "[Volume]"},
        "service_binds": {"key": "serviceBinds", "type": "[ServiceBind]"},
    }

    def __init__(
        self,
        *,
        revision_suffix: Optional[str] = None,
        termination_grace_period_seconds: Optional[int] = None,
        init_containers: Optional[List["InitContainer"]] = None,
        containers: Optional[List["Container"]] = None,
        scale: Optional["Scale"] = None,
        volumes: Optional[List["Volume"]] = None,
        service_binds: Optional[List["ServiceBind"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword revision_suffix: User friendly suffix that is appended to the revision name.
        :paramtype revision_suffix: str
        :keyword termination_grace_period_seconds: Optional duration in seconds the Container App
         Instance needs to terminate gracefully. Value must be non-negative integer. The value zero
         indicates stop immediately via the kill signal (no opportunity to shut down). If this value is
         nil, the default grace period will be used instead. Set this value longer than the expected
         cleanup time for your process. Defaults to 30 seconds.
        :paramtype termination_grace_period_seconds: int
        :keyword init_containers: List of specialized containers that run before app containers.
        :paramtype init_containers: list[~azure.mgmt.appcontainers.models.InitContainer]
        :keyword containers: List of container definitions for the Container App.
        :paramtype containers: list[~azure.mgmt.appcontainers.models.Container]
        :keyword scale: Scaling properties for the Container App.
        :paramtype scale: ~azure.mgmt.appcontainers.models.Scale
        :keyword volumes: List of volume definitions for the Container App.
        :paramtype volumes: list[~azure.mgmt.appcontainers.models.Volume]
        :keyword service_binds: List of container app services bound to the app.
        :paramtype service_binds: list[~azure.mgmt.appcontainers.models.ServiceBind]
        """
        super().__init__(**kwargs)
        self.revision_suffix = revision_suffix
        self.termination_grace_period_seconds = termination_grace_period_seconds
        self.init_containers = init_containers
        self.containers = containers
        self.scale = scale
        self.volumes = volumes
        self.service_binds = service_binds


class TokenStore(_serialization.Model):
    """The configuration settings of the token store.

    :ivar enabled: :code:`<code>true</code>` to durably store platform-specific security tokens
     that are obtained during login flows; otherwise, :code:`<code>false</code>`.
      The default is :code:`<code>false</code>`.
    :vartype enabled: bool
    :ivar token_refresh_extension_hours: The number of hours after session token expiration that a
     session token can be used to
     call the token refresh API. The default is 72 hours.
    :vartype token_refresh_extension_hours: float
    :ivar azure_blob_storage: The configuration settings of the storage of the tokens if blob
     storage is used.
    :vartype azure_blob_storage: ~azure.mgmt.appcontainers.models.BlobStorageTokenStore
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "token_refresh_extension_hours": {"key": "tokenRefreshExtensionHours", "type": "float"},
        "azure_blob_storage": {"key": "azureBlobStorage", "type": "BlobStorageTokenStore"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        token_refresh_extension_hours: Optional[float] = None,
        azure_blob_storage: Optional["BlobStorageTokenStore"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>true</code>` to durably store platform-specific security tokens
         that are obtained during login flows; otherwise, :code:`<code>false</code>`.
          The default is :code:`<code>false</code>`.
        :paramtype enabled: bool
        :keyword token_refresh_extension_hours: The number of hours after session token expiration that
         a session token can be used to
         call the token refresh API. The default is 72 hours.
        :paramtype token_refresh_extension_hours: float
        :keyword azure_blob_storage: The configuration settings of the storage of the tokens if blob
         storage is used.
        :paramtype azure_blob_storage: ~azure.mgmt.appcontainers.models.BlobStorageTokenStore
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.token_refresh_extension_hours = token_refresh_extension_hours
        self.azure_blob_storage = azure_blob_storage


class TrafficWeight(_serialization.Model):
    """Traffic weight assigned to a revision.

    :ivar revision_name: Name of a revision.
    :vartype revision_name: str
    :ivar weight: Traffic weight assigned to a revision.
    :vartype weight: int
    :ivar latest_revision: Indicates that the traffic weight belongs to a latest stable revision.
    :vartype latest_revision: bool
    :ivar label: Associates a traffic label with a revision.
    :vartype label: str
    """

    _attribute_map = {
        "revision_name": {"key": "revisionName", "type": "str"},
        "weight": {"key": "weight", "type": "int"},
        "latest_revision": {"key": "latestRevision", "type": "bool"},
        "label": {"key": "label", "type": "str"},
    }

    def __init__(
        self,
        *,
        revision_name: Optional[str] = None,
        weight: Optional[int] = None,
        latest_revision: bool = False,
        label: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword revision_name: Name of a revision.
        :paramtype revision_name: str
        :keyword weight: Traffic weight assigned to a revision.
        :paramtype weight: int
        :keyword latest_revision: Indicates that the traffic weight belongs to a latest stable
         revision.
        :paramtype latest_revision: bool
        :keyword label: Associates a traffic label with a revision.
        :paramtype label: str
        """
        super().__init__(**kwargs)
        self.revision_name = revision_name
        self.weight = weight
        self.latest_revision = latest_revision
        self.label = label


class Twitter(_serialization.Model):
    """The configuration settings of the Twitter provider.

    :ivar enabled: :code:`<code>false</code>` if the Twitter provider should not be enabled despite
     the set registration; otherwise, :code:`<code>true</code>`.
    :vartype enabled: bool
    :ivar registration: The configuration settings of the app registration for the Twitter
     provider.
    :vartype registration: ~azure.mgmt.appcontainers.models.TwitterRegistration
    """

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "registration": {"key": "registration", "type": "TwitterRegistration"},
    }

    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        registration: Optional["TwitterRegistration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: :code:`<code>false</code>` if the Twitter provider should not be enabled
         despite the set registration; otherwise, :code:`<code>true</code>`.
        :paramtype enabled: bool
        :keyword registration: The configuration settings of the app registration for the Twitter
         provider.
        :paramtype registration: ~azure.mgmt.appcontainers.models.TwitterRegistration
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.registration = registration


class TwitterRegistration(_serialization.Model):
    """The configuration settings of the app registration for the Twitter provider.

    :ivar consumer_key: The OAuth 1.0a consumer key of the Twitter application used for sign-in.
     This setting is required for enabling Twitter Sign-In.
     Twitter Sign-In documentation: https://dev.twitter.com/web/sign-in.
    :vartype consumer_key: str
    :ivar consumer_secret_setting_name: The app setting name that contains the OAuth 1.0a consumer
     secret of the Twitter
     application used for sign-in.
    :vartype consumer_secret_setting_name: str
    """

    _attribute_map = {
        "consumer_key": {"key": "consumerKey", "type": "str"},
        "consumer_secret_setting_name": {"key": "consumerSecretSettingName", "type": "str"},
    }

    def __init__(
        self, *, consumer_key: Optional[str] = None, consumer_secret_setting_name: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword consumer_key: The OAuth 1.0a consumer key of the Twitter application used for sign-in.
         This setting is required for enabling Twitter Sign-In.
         Twitter Sign-In documentation: https://dev.twitter.com/web/sign-in.
        :paramtype consumer_key: str
        :keyword consumer_secret_setting_name: The app setting name that contains the OAuth 1.0a
         consumer secret of the Twitter
         application used for sign-in.
        :paramtype consumer_secret_setting_name: str
        """
        super().__init__(**kwargs)
        self.consumer_key = consumer_key
        self.consumer_secret_setting_name = consumer_secret_setting_name


class Usage(_serialization.Model):
    """Describes Compute Resource Usage.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar unit: An enum describing the unit of usage measurement. Required. Default value is
     "Count".
    :vartype unit: str
    :ivar current_value: The current usage of the resource. Required.
    :vartype current_value: float
    :ivar limit: The maximum permitted usage of the resource. Required.
    :vartype limit: float
    :ivar name: The name of the type of usage. Required.
    :vartype name: ~azure.mgmt.appcontainers.models.UsageName
    """

    _validation = {
        "unit": {"required": True, "constant": True},
        "current_value": {"required": True},
        "limit": {"required": True},
        "name": {"required": True},
    }

    _attribute_map = {
        "unit": {"key": "unit", "type": "str"},
        "current_value": {"key": "currentValue", "type": "float"},
        "limit": {"key": "limit", "type": "float"},
        "name": {"key": "name", "type": "UsageName"},
    }

    unit = "Count"

    def __init__(self, *, current_value: float, limit: float, name: "UsageName", **kwargs: Any) -> None:
        """
        :keyword current_value: The current usage of the resource. Required.
        :paramtype current_value: float
        :keyword limit: The maximum permitted usage of the resource. Required.
        :paramtype limit: float
        :keyword name: The name of the type of usage. Required.
        :paramtype name: ~azure.mgmt.appcontainers.models.UsageName
        """
        super().__init__(**kwargs)
        self.current_value = current_value
        self.limit = limit
        self.name = name


class UsageName(_serialization.Model):
    """The Usage Names.

    :ivar value: The name of the resource.
    :vartype value: str
    :ivar localized_value: The localized name of the resource.
    :vartype localized_value: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "str"},
        "localized_value": {"key": "localizedValue", "type": "str"},
    }

    def __init__(self, *, value: Optional[str] = None, localized_value: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword value: The name of the resource.
        :paramtype value: str
        :keyword localized_value: The localized name of the resource.
        :paramtype localized_value: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.localized_value = localized_value


class UserAssignedIdentity(_serialization.Model):
    """User assigned identity properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar principal_id: The principal ID of the assigned identity.
    :vartype principal_id: str
    :ivar client_id: The client ID of the assigned identity.
    :vartype client_id: str
    """

    _validation = {
        "principal_id": {"readonly": True},
        "client_id": {"readonly": True},
    }

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "client_id": {"key": "clientId", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.principal_id = None
        self.client_id = None


class VnetConfiguration(_serialization.Model):
    """Configuration properties for apps environment to join a Virtual Network.

    :ivar internal: Boolean indicating the environment only has an internal load balancer. These
     environments do not have a public static IP resource. They must provide infrastructureSubnetId
     if enabling this property.
    :vartype internal: bool
    :ivar infrastructure_subnet_id: Resource ID of a subnet for infrastructure components. Must not
     overlap with any other provided IP ranges.
    :vartype infrastructure_subnet_id: str
    :ivar docker_bridge_cidr: CIDR notation IP range assigned to the Docker bridge, network. Must
     not overlap with any other provided IP ranges.
    :vartype docker_bridge_cidr: str
    :ivar platform_reserved_cidr: IP range in CIDR notation that can be reserved for environment
     infrastructure IP addresses. Must not overlap with any other provided IP ranges.
    :vartype platform_reserved_cidr: str
    :ivar platform_reserved_dns_ip: An IP address from the IP range defined by platformReservedCidr
     that will be reserved for the internal DNS server.
    :vartype platform_reserved_dns_ip: str
    """

    _attribute_map = {
        "internal": {"key": "internal", "type": "bool"},
        "infrastructure_subnet_id": {"key": "infrastructureSubnetId", "type": "str"},
        "docker_bridge_cidr": {"key": "dockerBridgeCidr", "type": "str"},
        "platform_reserved_cidr": {"key": "platformReservedCidr", "type": "str"},
        "platform_reserved_dns_ip": {"key": "platformReservedDnsIP", "type": "str"},
    }

    def __init__(
        self,
        *,
        internal: Optional[bool] = None,
        infrastructure_subnet_id: Optional[str] = None,
        docker_bridge_cidr: Optional[str] = None,
        platform_reserved_cidr: Optional[str] = None,
        platform_reserved_dns_ip: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword internal: Boolean indicating the environment only has an internal load balancer. These
         environments do not have a public static IP resource. They must provide infrastructureSubnetId
         if enabling this property.
        :paramtype internal: bool
        :keyword infrastructure_subnet_id: Resource ID of a subnet for infrastructure components. Must
         not overlap with any other provided IP ranges.
        :paramtype infrastructure_subnet_id: str
        :keyword docker_bridge_cidr: CIDR notation IP range assigned to the Docker bridge, network.
         Must not overlap with any other provided IP ranges.
        :paramtype docker_bridge_cidr: str
        :keyword platform_reserved_cidr: IP range in CIDR notation that can be reserved for environment
         infrastructure IP addresses. Must not overlap with any other provided IP ranges.
        :paramtype platform_reserved_cidr: str
        :keyword platform_reserved_dns_ip: An IP address from the IP range defined by
         platformReservedCidr that will be reserved for the internal DNS server.
        :paramtype platform_reserved_dns_ip: str
        """
        super().__init__(**kwargs)
        self.internal = internal
        self.infrastructure_subnet_id = infrastructure_subnet_id
        self.docker_bridge_cidr = docker_bridge_cidr
        self.platform_reserved_cidr = platform_reserved_cidr
        self.platform_reserved_dns_ip = platform_reserved_dns_ip


class Volume(_serialization.Model):
    """Volume definitions for the Container App.

    :ivar name: Volume name.
    :vartype name: str
    :ivar storage_type: Storage type for the volume. If not provided, use EmptyDir. Known values
     are: "AzureFile", "EmptyDir", "Secret", and "NfsAzureFile".
    :vartype storage_type: str or ~azure.mgmt.appcontainers.models.StorageType
    :ivar storage_name: Name of storage resource. No need to provide for EmptyDir and Secret.
    :vartype storage_name: str
    :ivar secrets: List of secrets to be added in volume. If no secrets are provided, all secrets
     in collection will be added to volume.
    :vartype secrets: list[~azure.mgmt.appcontainers.models.SecretVolumeItem]
    :ivar mount_options: Mount options used while mounting the Azure file share or NFS Azure file
     share. Must be a comma-separated string.
    :vartype mount_options: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "storage_type": {"key": "storageType", "type": "str"},
        "storage_name": {"key": "storageName", "type": "str"},
        "secrets": {"key": "secrets", "type": "[SecretVolumeItem]"},
        "mount_options": {"key": "mountOptions", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        storage_type: Optional[Union[str, "StorageType"]] = None,
        storage_name: Optional[str] = None,
        secrets: Optional[List["SecretVolumeItem"]] = None,
        mount_options: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Volume name.
        :paramtype name: str
        :keyword storage_type: Storage type for the volume. If not provided, use EmptyDir. Known values
         are: "AzureFile", "EmptyDir", "Secret", and "NfsAzureFile".
        :paramtype storage_type: str or ~azure.mgmt.appcontainers.models.StorageType
        :keyword storage_name: Name of storage resource. No need to provide for EmptyDir and Secret.
        :paramtype storage_name: str
        :keyword secrets: List of secrets to be added in volume. If no secrets are provided, all
         secrets in collection will be added to volume.
        :paramtype secrets: list[~azure.mgmt.appcontainers.models.SecretVolumeItem]
        :keyword mount_options: Mount options used while mounting the Azure file share or NFS Azure
         file share. Must be a comma-separated string.
        :paramtype mount_options: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.storage_type = storage_type
        self.storage_name = storage_name
        self.secrets = secrets
        self.mount_options = mount_options


class VolumeMount(_serialization.Model):
    """Volume mount for the Container App.

    :ivar volume_name: This must match the Name of a Volume.
    :vartype volume_name: str
    :ivar mount_path: Path within the container at which the volume should be mounted.Must not
     contain ':'.
    :vartype mount_path: str
    :ivar sub_path: Path within the volume from which the container's volume should be mounted.
     Defaults to "" (volume's root).
    :vartype sub_path: str
    """

    _attribute_map = {
        "volume_name": {"key": "volumeName", "type": "str"},
        "mount_path": {"key": "mountPath", "type": "str"},
        "sub_path": {"key": "subPath", "type": "str"},
    }

    def __init__(
        self,
        *,
        volume_name: Optional[str] = None,
        mount_path: Optional[str] = None,
        sub_path: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword volume_name: This must match the Name of a Volume.
        :paramtype volume_name: str
        :keyword mount_path: Path within the container at which the volume should be mounted.Must not
         contain ':'.
        :paramtype mount_path: str
        :keyword sub_path: Path within the volume from which the container's volume should be mounted.
         Defaults to "" (volume's root).
        :paramtype sub_path: str
        """
        super().__init__(**kwargs)
        self.volume_name = volume_name
        self.mount_path = mount_path
        self.sub_path = sub_path


class WorkloadProfile(_serialization.Model):
    """Workload profile to scope container app execution.

    All required parameters must be populated in order to send to server.

    :ivar name: Workload profile type for the workloads to run on. Required.
    :vartype name: str
    :ivar workload_profile_type: Workload profile type for the workloads to run on. Required.
    :vartype workload_profile_type: str
    :ivar minimum_count: The minimum capacity.
    :vartype minimum_count: int
    :ivar maximum_count: The maximum capacity.
    :vartype maximum_count: int
    """

    _validation = {
        "name": {"required": True},
        "workload_profile_type": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "workload_profile_type": {"key": "workloadProfileType", "type": "str"},
        "minimum_count": {"key": "minimumCount", "type": "int"},
        "maximum_count": {"key": "maximumCount", "type": "int"},
    }

    def __init__(
        self,
        *,
        name: str,
        workload_profile_type: str,
        minimum_count: Optional[int] = None,
        maximum_count: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Workload profile type for the workloads to run on. Required.
        :paramtype name: str
        :keyword workload_profile_type: Workload profile type for the workloads to run on. Required.
        :paramtype workload_profile_type: str
        :keyword minimum_count: The minimum capacity.
        :paramtype minimum_count: int
        :keyword maximum_count: The maximum capacity.
        :paramtype maximum_count: int
        """
        super().__init__(**kwargs)
        self.name = name
        self.workload_profile_type = workload_profile_type
        self.minimum_count = minimum_count
        self.maximum_count = maximum_count


class WorkloadProfileStates(ProxyResource):
    """Collection of all the workload Profile States for a Managed Environment..

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. E.g.
     "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}".  # pylint: disable=line-too-long
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.appcontainers.models.SystemData
    :ivar properties: Workload Profile resource specific properties.
    :vartype properties: ~azure.mgmt.appcontainers.models.WorkloadProfileStatesProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "WorkloadProfileStatesProperties"},
    }

    def __init__(
        self, *, properties: Optional["WorkloadProfileStatesProperties"] = None, **kwargs: Any
    ) -> None:
        """
        :keyword properties: Workload Profile resource specific properties.
        :paramtype properties: ~azure.mgmt.appcontainers.models.WorkloadProfileStatesProperties
        """
        super().__init__(**kwargs)
        self.properties = properties


class WorkloadProfileStatesCollection(_serialization.Model):
    """Collection of workloadProfileStates.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar value: Collection of resources. Required.
    :vartype value: list[~azure.mgmt.appcontainers.models.WorkloadProfileStates]
    :ivar next_link: Link to next page of resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[WorkloadProfileStates]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["WorkloadProfileStates"], **kwargs: Any) -> None:
        """
        :keyword value: Collection of resources. Required.
        :paramtype value: list[~azure.mgmt.appcontainers.models.WorkloadProfileStates]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class WorkloadProfileStatesProperties(_serialization.Model):
    """Workload Profile resource specific properties.

    :ivar minimum_count: Minimum count of instances.
    :vartype minimum_count: int
    :ivar maximum_count: Maximum count of nodes.
    :vartype maximum_count: int
    :ivar current_count: Current count of nodes.
    :vartype current_count: int
    """

    _attribute_map = {
        "minimum_count": {"key": "minimumCount", "type": "int"},
        "maximum_count": {"key": "maximumCount", "type": "int"},
        "current_count": {"key": "currentCount", "type": "int"},
    }

    def __init__(
        self,
        *,
        minimum_count: Optional[int] = None,
        maximum_count: Optional[int] = None,
        current_count: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword minimum_count: Minimum count of instances.
        :paramtype minimum_count: int
        :keyword maximum_count: Maximum count of nodes.
        :paramtype maximum_count: int
        :keyword current_count: Current count of nodes.
        :paramtype current_count: int
        """
        super().__init__(**kwargs)
        self.minimum_count = minimum_count
        self.maximum_count = maximum_count
        self.current_count = current_count
