# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: disable=too-many-lines
# pylint: disable=too-many-statements, protected-access

from knack.log import get_logger

from .aaz.latest.databoxedge.order import Create as _OrderCreate


logger = get_logger(__name__)


class OrderCreate(_OrderCreate):
    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        schema = super()._build_arguments_schema(*args, **kwargs)
        schema.company_name._required = True
        schema.contact_person._required = True
        schema.email_list._required = True
        schema.phone._required = True
        schema.status._required = True
        schema.address_line1._required = True
        schema.city._required = True
        schema.country._required = True
        schema.postal_code._required = True
        schema.state._required = True
        return schema
