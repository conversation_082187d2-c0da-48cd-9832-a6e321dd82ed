# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge device list",
)
class List(AAZCommand):
    """List all the Data Box Edge/Data Box Gateway devices in a subscription.

    :example: Get all the Data Box Edge/Data Box Gateway devices in a resource group.
        az databoxedge device list --resource-group "GroupForEdgeAutomation"

    :example: Get all the Data Box Edge/Data Box Gateway devices in a subscription.
        az databoxedge device list
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.databoxedge/databoxedgedevices", "2021-02-01-preview"],
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.databoxedge/databoxedgedevices", "2021-02-01-preview"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.resource_group = AAZResourceGroupNameArg()
        _args_schema.expand = AAZStrArg(
            options=["--expand"],
            help="Specify $expand=details to populate additional fields related to the resource or Specify $skipToken=token to populate the next page in the list.",
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        condition_0 = has_value(self.ctx.subscription_id) and has_value(self.ctx.args.resource_group) is not True
        condition_1 = has_value(self.ctx.args.resource_group) and has_value(self.ctx.subscription_id)
        if condition_0:
            self.DevicesListBySubscription(ctx=self.ctx)()
        if condition_1:
            self.DevicesListByResourceGroup(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class DevicesListBySubscription(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$expand", self.ctx.args.expand,
                ),
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType(
                flags={"read_only": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType()
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.identity = AAZObjectType()
            _element.kind = AAZStrType(
                flags={"read_only": True},
            )
            _element.location = AAZStrType(
                flags={"required": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            _element.sku = AAZObjectType()
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            identity = cls._schema_on_200.value.Element.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()

            properties = cls._schema_on_200.value.Element.properties
            properties.configured_role_types = AAZListType(
                serialized_name="configuredRoleTypes",
                flags={"read_only": True},
            )
            properties.culture = AAZStrType(
                flags={"read_only": True},
            )
            properties.data_box_edge_device_status = AAZStrType(
                serialized_name="dataBoxEdgeDeviceStatus",
            )
            properties.description = AAZStrType()
            properties.device_hcs_version = AAZStrType(
                serialized_name="deviceHcsVersion",
                flags={"read_only": True},
            )
            properties.device_local_capacity = AAZIntType(
                serialized_name="deviceLocalCapacity",
                flags={"read_only": True},
            )
            properties.device_model = AAZStrType(
                serialized_name="deviceModel",
                flags={"read_only": True},
            )
            properties.device_software_version = AAZStrType(
                serialized_name="deviceSoftwareVersion",
                flags={"read_only": True},
            )
            properties.device_type = AAZStrType(
                serialized_name="deviceType",
                flags={"read_only": True},
            )
            properties.edge_profile = AAZObjectType(
                serialized_name="edgeProfile",
                flags={"read_only": True},
            )
            properties.friendly_name = AAZStrType(
                serialized_name="friendlyName",
            )
            properties.model_description = AAZStrType(
                serialized_name="modelDescription",
            )
            properties.node_count = AAZIntType(
                serialized_name="nodeCount",
                flags={"read_only": True},
            )
            properties.resource_move_details = AAZObjectType(
                serialized_name="resourceMoveDetails",
                flags={"read_only": True},
            )
            properties.serial_number = AAZStrType(
                serialized_name="serialNumber",
                flags={"read_only": True},
            )
            properties.time_zone = AAZStrType(
                serialized_name="timeZone",
                flags={"read_only": True},
            )

            configured_role_types = cls._schema_on_200.value.Element.properties.configured_role_types
            configured_role_types.Element = AAZStrType()

            edge_profile = cls._schema_on_200.value.Element.properties.edge_profile
            edge_profile.subscription = AAZObjectType()

            subscription = cls._schema_on_200.value.Element.properties.edge_profile.subscription
            subscription.id = AAZStrType()
            subscription.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            subscription.registration_date = AAZStrType(
                serialized_name="registrationDate",
            )
            subscription.registration_id = AAZStrType(
                serialized_name="registrationId",
            )
            subscription.state = AAZStrType()
            subscription.subscription_id = AAZStrType(
                serialized_name="subscriptionId",
            )

            properties = cls._schema_on_200.value.Element.properties.edge_profile.subscription.properties
            properties.location_placement_id = AAZStrType(
                serialized_name="locationPlacementId",
            )
            properties.quota_id = AAZStrType(
                serialized_name="quotaId",
            )
            properties.registered_features = AAZListType(
                serialized_name="registeredFeatures",
            )
            properties.serialized_details = AAZStrType(
                serialized_name="serializedDetails",
            )
            properties.tenant_id = AAZStrType(
                serialized_name="tenantId",
            )

            registered_features = cls._schema_on_200.value.Element.properties.edge_profile.subscription.properties.registered_features
            registered_features.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.edge_profile.subscription.properties.registered_features.Element
            _element.name = AAZStrType()
            _element.state = AAZStrType()

            resource_move_details = cls._schema_on_200.value.Element.properties.resource_move_details
            resource_move_details.operation_in_progress = AAZStrType(
                serialized_name="operationInProgress",
            )
            resource_move_details.operation_in_progress_lock_timeout_in_utc = AAZStrType(
                serialized_name="operationInProgressLockTimeoutInUTC",
            )

            sku = cls._schema_on_200.value.Element.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            tags = cls._schema_on_200.value.Element.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200

    class DevicesListByResourceGroup(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "$expand", self.ctx.args.expand,
                ),
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType(
                flags={"read_only": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.etag = AAZStrType()
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.identity = AAZObjectType()
            _element.kind = AAZStrType(
                flags={"read_only": True},
            )
            _element.location = AAZStrType(
                flags={"required": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            _element.sku = AAZObjectType()
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.tags = AAZDictType()
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            identity = cls._schema_on_200.value.Element.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()

            properties = cls._schema_on_200.value.Element.properties
            properties.configured_role_types = AAZListType(
                serialized_name="configuredRoleTypes",
                flags={"read_only": True},
            )
            properties.culture = AAZStrType(
                flags={"read_only": True},
            )
            properties.data_box_edge_device_status = AAZStrType(
                serialized_name="dataBoxEdgeDeviceStatus",
            )
            properties.description = AAZStrType()
            properties.device_hcs_version = AAZStrType(
                serialized_name="deviceHcsVersion",
                flags={"read_only": True},
            )
            properties.device_local_capacity = AAZIntType(
                serialized_name="deviceLocalCapacity",
                flags={"read_only": True},
            )
            properties.device_model = AAZStrType(
                serialized_name="deviceModel",
                flags={"read_only": True},
            )
            properties.device_software_version = AAZStrType(
                serialized_name="deviceSoftwareVersion",
                flags={"read_only": True},
            )
            properties.device_type = AAZStrType(
                serialized_name="deviceType",
                flags={"read_only": True},
            )
            properties.edge_profile = AAZObjectType(
                serialized_name="edgeProfile",
                flags={"read_only": True},
            )
            properties.friendly_name = AAZStrType(
                serialized_name="friendlyName",
            )
            properties.model_description = AAZStrType(
                serialized_name="modelDescription",
            )
            properties.node_count = AAZIntType(
                serialized_name="nodeCount",
                flags={"read_only": True},
            )
            properties.resource_move_details = AAZObjectType(
                serialized_name="resourceMoveDetails",
                flags={"read_only": True},
            )
            properties.serial_number = AAZStrType(
                serialized_name="serialNumber",
                flags={"read_only": True},
            )
            properties.time_zone = AAZStrType(
                serialized_name="timeZone",
                flags={"read_only": True},
            )

            configured_role_types = cls._schema_on_200.value.Element.properties.configured_role_types
            configured_role_types.Element = AAZStrType()

            edge_profile = cls._schema_on_200.value.Element.properties.edge_profile
            edge_profile.subscription = AAZObjectType()

            subscription = cls._schema_on_200.value.Element.properties.edge_profile.subscription
            subscription.id = AAZStrType()
            subscription.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            subscription.registration_date = AAZStrType(
                serialized_name="registrationDate",
            )
            subscription.registration_id = AAZStrType(
                serialized_name="registrationId",
            )
            subscription.state = AAZStrType()
            subscription.subscription_id = AAZStrType(
                serialized_name="subscriptionId",
            )

            properties = cls._schema_on_200.value.Element.properties.edge_profile.subscription.properties
            properties.location_placement_id = AAZStrType(
                serialized_name="locationPlacementId",
            )
            properties.quota_id = AAZStrType(
                serialized_name="quotaId",
            )
            properties.registered_features = AAZListType(
                serialized_name="registeredFeatures",
            )
            properties.serialized_details = AAZStrType(
                serialized_name="serializedDetails",
            )
            properties.tenant_id = AAZStrType(
                serialized_name="tenantId",
            )

            registered_features = cls._schema_on_200.value.Element.properties.edge_profile.subscription.properties.registered_features
            registered_features.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.properties.edge_profile.subscription.properties.registered_features.Element
            _element.name = AAZStrType()
            _element.state = AAZStrType()

            resource_move_details = cls._schema_on_200.value.Element.properties.resource_move_details
            resource_move_details.operation_in_progress = AAZStrType(
                serialized_name="operationInProgress",
            )
            resource_move_details.operation_in_progress_lock_timeout_in_utc = AAZStrType(
                serialized_name="operationInProgressLockTimeoutInUTC",
            )

            sku = cls._schema_on_200.value.Element.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            tags = cls._schema_on_200.value.Element.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""


__all__ = ["List"]
