# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge order update",
)
class Update(AAZCommand):
    """Update an order.

    :example: Update an order
        az databoxedge order update --phone "(*************"
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.databoxedge/databoxedgedevices/{}/orders/default", "2021-02-01-preview"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.device_name = AAZStrArg(
            options=["-d", "--device-name"],
            help="The order details of a device.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )

        # define Arg Group "ContactInformation"

        _args_schema = cls._args_schema
        _args_schema.company_name = AAZStrArg(
            options=["--company-name"],
            arg_group="ContactInformation",
            help="The name of the company.",
        )
        _args_schema.contact_person = AAZStrArg(
            options=["--contact-person"],
            arg_group="ContactInformation",
            help="The contact person name.",
        )
        _args_schema.email_list = AAZListArg(
            options=["--email-list"],
            arg_group="ContactInformation",
            help="The email list.",
        )
        _args_schema.phone = AAZStrArg(
            options=["--phone"],
            arg_group="ContactInformation",
            help="The phone number.",
        )

        email_list = cls._args_schema.email_list
        email_list.Element = AAZStrArg(
            nullable=True,
        )

        # define Arg Group "CurrentStatus"

        _args_schema = cls._args_schema
        _args_schema.comments = AAZStrArg(
            options=["--comments"],
            arg_group="CurrentStatus",
            help="Comments related to this status change.",
            nullable=True,
        )
        _args_schema.status = AAZStrArg(
            options=["--status"],
            arg_group="CurrentStatus",
            help="Status of the order as per the allowed status types.",
            enum={"Arriving": "Arriving", "AwaitingDrop": "AwaitingDrop", "AwaitingFulfillment": "AwaitingFulfillment", "AwaitingPickup": "AwaitingPickup", "AwaitingPreparation": "AwaitingPreparation", "AwaitingReturnShipment": "AwaitingReturnShipment", "AwaitingShipment": "AwaitingShipment", "CollectedAtMicrosoft": "CollectedAtMicrosoft", "Declined": "Declined", "Delivered": "Delivered", "LostDevice": "LostDevice", "PickupCompleted": "PickupCompleted", "ReplacementRequested": "ReplacementRequested", "ReturnInitiated": "ReturnInitiated", "Shipped": "Shipped", "ShippedBack": "ShippedBack", "Untracked": "Untracked"},
        )

        # define Arg Group "Properties"

        # define Arg Group "ShippingAddress"

        _args_schema = cls._args_schema
        _args_schema.address_line1 = AAZStrArg(
            options=["--address-line1"],
            arg_group="ShippingAddress",
            help="The address line1.",
            nullable=True,
        )
        _args_schema.address_line2 = AAZStrArg(
            options=["--address-line2"],
            arg_group="ShippingAddress",
            help="The address line2.",
            nullable=True,
        )
        _args_schema.address_line3 = AAZStrArg(
            options=["--address-line3"],
            arg_group="ShippingAddress",
            help="The address line3.",
            nullable=True,
        )
        _args_schema.city = AAZStrArg(
            options=["--city"],
            arg_group="ShippingAddress",
            help="The city name.",
            nullable=True,
        )
        _args_schema.country = AAZStrArg(
            options=["--country"],
            arg_group="ShippingAddress",
            help="The country name.",
        )
        _args_schema.postal_code = AAZStrArg(
            options=["--postal-code"],
            arg_group="ShippingAddress",
            help="The postal code.",
            nullable=True,
        )
        _args_schema.state = AAZStrArg(
            options=["--state"],
            arg_group="ShippingAddress",
            help="The state name.",
            nullable=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.OrdersGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.OrdersCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class OrdersGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}/orders/default",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_order_read(cls._schema_on_200)

            return cls._schema_on_200

    class OrdersCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}/orders/default",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_order_read(cls._schema_on_200)

            return cls._schema_on_200

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("contactInformation", AAZObjectType, ".", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("currentStatus", AAZObjectType)
                properties.set_prop("shippingAddress", AAZObjectType)

            contact_information = _builder.get(".properties.contactInformation")
            if contact_information is not None:
                contact_information.set_prop("companyName", AAZStrType, ".company_name", typ_kwargs={"flags": {"required": True}})
                contact_information.set_prop("contactPerson", AAZStrType, ".contact_person", typ_kwargs={"flags": {"required": True}})
                contact_information.set_prop("emailList", AAZListType, ".email_list", typ_kwargs={"flags": {"required": True}})
                contact_information.set_prop("phone", AAZStrType, ".phone", typ_kwargs={"flags": {"required": True}})

            email_list = _builder.get(".properties.contactInformation.emailList")
            if email_list is not None:
                email_list.set_elements(AAZStrType, ".")

            current_status = _builder.get(".properties.currentStatus")
            if current_status is not None:
                current_status.set_prop("comments", AAZStrType, ".comments")
                current_status.set_prop("status", AAZStrType, ".status", typ_kwargs={"flags": {"required": True}})

            shipping_address = _builder.get(".properties.shippingAddress")
            if shipping_address is not None:
                shipping_address.set_prop("addressLine1", AAZStrType, ".address_line1")
                shipping_address.set_prop("addressLine2", AAZStrType, ".address_line2")
                shipping_address.set_prop("addressLine3", AAZStrType, ".address_line3")
                shipping_address.set_prop("city", AAZStrType, ".city")
                shipping_address.set_prop("country", AAZStrType, ".country", typ_kwargs={"flags": {"required": True}})
                shipping_address.set_prop("postalCode", AAZStrType, ".postal_code")
                shipping_address.set_prop("state", AAZStrType, ".state")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_order_status_read = None

    @classmethod
    def _build_schema_order_status_read(cls, _schema):
        if cls._schema_order_status_read is not None:
            _schema.additional_order_details = cls._schema_order_status_read.additional_order_details
            _schema.comments = cls._schema_order_status_read.comments
            _schema.status = cls._schema_order_status_read.status
            _schema.tracking_information = cls._schema_order_status_read.tracking_information
            _schema.update_date_time = cls._schema_order_status_read.update_date_time
            return

        cls._schema_order_status_read = _schema_order_status_read = AAZObjectType()

        order_status_read = _schema_order_status_read
        order_status_read.additional_order_details = AAZDictType(
            serialized_name="additionalOrderDetails",
            flags={"read_only": True},
        )
        order_status_read.comments = AAZStrType()
        order_status_read.status = AAZStrType(
            flags={"required": True},
        )
        order_status_read.tracking_information = AAZObjectType(
            serialized_name="trackingInformation",
            flags={"read_only": True},
        )
        cls._build_schema_tracking_info_read(order_status_read.tracking_information)
        order_status_read.update_date_time = AAZStrType(
            serialized_name="updateDateTime",
            flags={"read_only": True},
        )

        additional_order_details = _schema_order_status_read.additional_order_details
        additional_order_details.Element = AAZStrType()

        _schema.additional_order_details = cls._schema_order_status_read.additional_order_details
        _schema.comments = cls._schema_order_status_read.comments
        _schema.status = cls._schema_order_status_read.status
        _schema.tracking_information = cls._schema_order_status_read.tracking_information
        _schema.update_date_time = cls._schema_order_status_read.update_date_time

    _schema_order_read = None

    @classmethod
    def _build_schema_order_read(cls, _schema):
        if cls._schema_order_read is not None:
            _schema.id = cls._schema_order_read.id
            _schema.name = cls._schema_order_read.name
            _schema.properties = cls._schema_order_read.properties
            _schema.provisioning_state = cls._schema_order_read.provisioning_state
            _schema.system_data = cls._schema_order_read.system_data
            _schema.type = cls._schema_order_read.type
            return

        cls._schema_order_read = _schema_order_read = AAZObjectType()

        order_read = _schema_order_read
        order_read.id = AAZStrType(
            flags={"read_only": True},
        )
        order_read.name = AAZStrType(
            flags={"read_only": True},
        )
        order_read.properties = AAZObjectType(
            flags={"client_flatten": True},
        )
        order_read.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        order_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        order_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_order_read.properties
        properties.contact_information = AAZObjectType(
            serialized_name="contactInformation",
            flags={"required": True},
        )
        properties.current_status = AAZObjectType(
            serialized_name="currentStatus",
        )
        cls._build_schema_order_status_read(properties.current_status)
        properties.delivery_tracking_info = AAZListType(
            serialized_name="deliveryTrackingInfo",
            flags={"read_only": True},
        )
        properties.order_history = AAZListType(
            serialized_name="orderHistory",
            flags={"read_only": True},
        )
        properties.return_tracking_info = AAZListType(
            serialized_name="returnTrackingInfo",
            flags={"read_only": True},
        )
        properties.serial_number = AAZStrType(
            serialized_name="serialNumber",
            flags={"read_only": True},
        )
        properties.shipment_type = AAZStrType(
            serialized_name="shipmentType",
        )
        properties.shipping_address = AAZObjectType(
            serialized_name="shippingAddress",
        )

        contact_information = _schema_order_read.properties.contact_information
        contact_information.company_name = AAZStrType(
            serialized_name="companyName",
            flags={"required": True},
        )
        contact_information.contact_person = AAZStrType(
            serialized_name="contactPerson",
            flags={"required": True},
        )
        contact_information.email_list = AAZListType(
            serialized_name="emailList",
            flags={"required": True},
        )
        contact_information.phone = AAZStrType(
            flags={"required": True},
        )

        email_list = _schema_order_read.properties.contact_information.email_list
        email_list.Element = AAZStrType()

        delivery_tracking_info = _schema_order_read.properties.delivery_tracking_info
        delivery_tracking_info.Element = AAZObjectType()
        cls._build_schema_tracking_info_read(delivery_tracking_info.Element)

        order_history = _schema_order_read.properties.order_history
        order_history.Element = AAZObjectType()
        cls._build_schema_order_status_read(order_history.Element)

        return_tracking_info = _schema_order_read.properties.return_tracking_info
        return_tracking_info.Element = AAZObjectType()
        cls._build_schema_tracking_info_read(return_tracking_info.Element)

        shipping_address = _schema_order_read.properties.shipping_address
        shipping_address.address_line1 = AAZStrType(
            serialized_name="addressLine1",
        )
        shipping_address.address_line2 = AAZStrType(
            serialized_name="addressLine2",
        )
        shipping_address.address_line3 = AAZStrType(
            serialized_name="addressLine3",
        )
        shipping_address.city = AAZStrType()
        shipping_address.country = AAZStrType(
            flags={"required": True},
        )
        shipping_address.postal_code = AAZStrType(
            serialized_name="postalCode",
        )
        shipping_address.state = AAZStrType()

        system_data = _schema_order_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        _schema.id = cls._schema_order_read.id
        _schema.name = cls._schema_order_read.name
        _schema.properties = cls._schema_order_read.properties
        _schema.provisioning_state = cls._schema_order_read.provisioning_state
        _schema.system_data = cls._schema_order_read.system_data
        _schema.type = cls._schema_order_read.type

    _schema_tracking_info_read = None

    @classmethod
    def _build_schema_tracking_info_read(cls, _schema):
        if cls._schema_tracking_info_read is not None:
            _schema.carrier_name = cls._schema_tracking_info_read.carrier_name
            _schema.serial_number = cls._schema_tracking_info_read.serial_number
            _schema.tracking_id = cls._schema_tracking_info_read.tracking_id
            _schema.tracking_url = cls._schema_tracking_info_read.tracking_url
            return

        cls._schema_tracking_info_read = _schema_tracking_info_read = AAZObjectType(
            flags={"read_only": True}
        )

        tracking_info_read = _schema_tracking_info_read
        tracking_info_read.carrier_name = AAZStrType(
            serialized_name="carrierName",
        )
        tracking_info_read.serial_number = AAZStrType(
            serialized_name="serialNumber",
        )
        tracking_info_read.tracking_id = AAZStrType(
            serialized_name="trackingId",
        )
        tracking_info_read.tracking_url = AAZStrType(
            serialized_name="trackingUrl",
        )

        _schema.carrier_name = cls._schema_tracking_info_read.carrier_name
        _schema.serial_number = cls._schema_tracking_info_read.serial_number
        _schema.tracking_id = cls._schema_tracking_info_read.tracking_id
        _schema.tracking_url = cls._schema_tracking_info_read.tracking_url


__all__ = ["Update"]
