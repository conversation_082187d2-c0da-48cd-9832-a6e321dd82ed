# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# swagger change: https://github.com/Azure/azure-rest-api-specs/pull/32901
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command_group(
    "databoxedge",
)
class __CMDGroup(AAZCommandGroup):
    """Manage device with databoxedge.
    """
    pass


__all__ = ["__CMDGroup"]
