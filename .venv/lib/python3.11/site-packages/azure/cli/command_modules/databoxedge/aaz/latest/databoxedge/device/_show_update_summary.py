# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge device show-update-summary",
)
class ShowUpdateSummary(AAZCommand):
    """Get information about the availability of updates based on the last scan of the device. It also gets information about any ongoing download or install jobs on the device.

    :example: Get update summary of the device.
        az databoxedge device show-update-summary --device-name testedgedevice --resource-group GroupForEdgeAutomation
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.databoxedge/databoxedgedevices/{}/updatesummary/default", "2021-02-01-preview"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.device_name = AAZStrArg(
            options=["-n", "--name", "--device-name"],
            help="The device name.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.DevicesGetUpdateSummary(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class DevicesGetUpdateSummary(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}/updateSummary/default",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.properties
            properties.device_last_scanned_date_time = AAZStrType(
                serialized_name="deviceLastScannedDateTime",
            )
            properties.device_version_number = AAZStrType(
                serialized_name="deviceVersionNumber",
            )
            properties.friendly_device_version_name = AAZStrType(
                serialized_name="friendlyDeviceVersionName",
            )
            properties.in_progress_download_job_id = AAZStrType(
                serialized_name="inProgressDownloadJobId",
                flags={"read_only": True},
            )
            properties.in_progress_download_job_started_date_time = AAZStrType(
                serialized_name="inProgressDownloadJobStartedDateTime",
                flags={"read_only": True},
            )
            properties.in_progress_install_job_id = AAZStrType(
                serialized_name="inProgressInstallJobId",
                flags={"read_only": True},
            )
            properties.in_progress_install_job_started_date_time = AAZStrType(
                serialized_name="inProgressInstallJobStartedDateTime",
                flags={"read_only": True},
            )
            properties.last_completed_download_job_date_time = AAZStrType(
                serialized_name="lastCompletedDownloadJobDateTime",
                flags={"read_only": True},
            )
            properties.last_completed_download_job_id = AAZStrType(
                serialized_name="lastCompletedDownloadJobId",
                flags={"read_only": True},
            )
            properties.last_completed_install_job_date_time = AAZStrType(
                serialized_name="lastCompletedInstallJobDateTime",
                flags={"read_only": True},
            )
            properties.last_completed_install_job_id = AAZStrType(
                serialized_name="lastCompletedInstallJobId",
                flags={"read_only": True},
            )
            properties.last_completed_scan_job_date_time = AAZStrType(
                serialized_name="lastCompletedScanJobDateTime",
            )
            properties.last_download_job_status = AAZStrType(
                serialized_name="lastDownloadJobStatus",
                flags={"read_only": True},
            )
            properties.last_install_job_status = AAZStrType(
                serialized_name="lastInstallJobStatus",
                flags={"read_only": True},
            )
            properties.ongoing_update_operation = AAZStrType(
                serialized_name="ongoingUpdateOperation",
                flags={"read_only": True},
            )
            properties.reboot_behavior = AAZStrType(
                serialized_name="rebootBehavior",
                flags={"read_only": True},
            )
            properties.total_number_of_updates_available = AAZIntType(
                serialized_name="totalNumberOfUpdatesAvailable",
                flags={"read_only": True},
            )
            properties.total_number_of_updates_pending_download = AAZIntType(
                serialized_name="totalNumberOfUpdatesPendingDownload",
                flags={"read_only": True},
            )
            properties.total_number_of_updates_pending_install = AAZIntType(
                serialized_name="totalNumberOfUpdatesPendingInstall",
                flags={"read_only": True},
            )
            properties.total_time_in_minutes = AAZIntType(
                serialized_name="totalTimeInMinutes",
                flags={"read_only": True},
            )
            properties.total_update_size_in_bytes = AAZFloatType(
                serialized_name="totalUpdateSizeInBytes",
                flags={"read_only": True},
            )
            properties.update_titles = AAZListType(
                serialized_name="updateTitles",
                flags={"read_only": True},
            )
            properties.updates = AAZListType(
                flags={"read_only": True},
            )

            update_titles = cls._schema_on_200.properties.update_titles
            update_titles.Element = AAZStrType()

            updates = cls._schema_on_200.properties.updates
            updates.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.updates.Element
            _element.estimated_install_time_in_mins = AAZIntType(
                serialized_name="estimatedInstallTimeInMins",
            )
            _element.reboot_behavior = AAZStrType(
                serialized_name="rebootBehavior",
            )
            _element.status = AAZStrType()
            _element.target_version = AAZStrType(
                serialized_name="targetVersion",
            )
            _element.update_size = AAZFloatType(
                serialized_name="updateSize",
            )
            _element.update_title = AAZStrType(
                serialized_name="updateTitle",
            )
            _element.update_type = AAZStrType(
                serialized_name="updateType",
            )

            system_data = cls._schema_on_200.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200


class _ShowUpdateSummaryHelper:
    """Helper class for ShowUpdateSummary"""


__all__ = ["ShowUpdateSummary"]
