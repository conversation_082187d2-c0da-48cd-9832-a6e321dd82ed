# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: disable=too-many-lines
# pylint: disable=too-many-statements

# from azure.cli.core.commands import CliCommandType
# from azure.cli.core.profiles import ResourceType


def load_command_table(self, _):  # pylint: disable=unused-argument
    with self.command_group('databoxedge order'):
        from .custom import OrderCreate
        self.command_table['databoxedge order create'] = OrderCreate(loader=self)
