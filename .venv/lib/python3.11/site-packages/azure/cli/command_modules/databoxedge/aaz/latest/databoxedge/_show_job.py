# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge show-job",
)
class ShowJob(AAZCommand):
    """Get the details of a specified job on a Data Box Edge/Data Box Gateway device.

    :example: Get the details of a specified job on a Data Box Edge/Data Box Gateway device.
        az databoxedge show-job --device-name testedgedevice --name 12345678-1234-1234-1234-123456789012 --resource-group GroupForEdgeAutomation
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.databoxedge/databoxedgedevices/{}/jobs/{}", "2021-02-01-preview"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.device_name = AAZStrArg(
            options=["-d", "--device-name"],
            help="The device name.",
            required=True,
            id_part="name",
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The job name.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.JobsGet(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class JobsGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}/jobs/{name}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "name", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.end_time = AAZStrType(
                serialized_name="endTime",
                flags={"read_only": True},
            )
            _schema_on_200.error = AAZObjectType(
                flags={"read_only": True},
            )
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.percent_complete = AAZIntType(
                serialized_name="percentComplete",
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True, "read_only": True},
            )
            _schema_on_200.start_time = AAZStrType(
                serialized_name="startTime",
                flags={"read_only": True},
            )
            _schema_on_200.status = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            error = cls._schema_on_200.error
            error.code = AAZStrType(
                flags={"read_only": True},
            )
            error.error_details = AAZListType(
                serialized_name="errorDetails",
                flags={"read_only": True},
            )
            error.message = AAZStrType(
                flags={"read_only": True},
            )

            error_details = cls._schema_on_200.error.error_details
            error_details.Element = AAZObjectType()

            _element = cls._schema_on_200.error.error_details.Element
            _element.code = AAZStrType(
                flags={"read_only": True},
            )
            _element.message = AAZStrType(
                flags={"read_only": True},
            )
            _element.recommendations = AAZListType(
                flags={"read_only": True},
            )

            recommendations = cls._schema_on_200.error.error_details.Element.recommendations
            recommendations.Element = AAZStrType()

            properties = cls._schema_on_200.properties
            properties.current_stage = AAZStrType(
                serialized_name="currentStage",
                flags={"read_only": True},
            )
            properties.download_progress = AAZObjectType(
                serialized_name="downloadProgress",
                flags={"read_only": True},
            )
            properties.error_manifest_file = AAZStrType(
                serialized_name="errorManifestFile",
                flags={"read_only": True},
            )
            properties.folder = AAZStrType()
            properties.install_progress = AAZObjectType(
                serialized_name="installProgress",
                flags={"read_only": True},
            )
            properties.job_type = AAZStrType(
                serialized_name="jobType",
                flags={"read_only": True},
            )
            properties.refreshed_entity_id = AAZStrType(
                serialized_name="refreshedEntityId",
                flags={"read_only": True},
            )
            properties.total_refresh_errors = AAZIntType(
                serialized_name="totalRefreshErrors",
                flags={"read_only": True},
            )

            download_progress = cls._schema_on_200.properties.download_progress
            download_progress.download_phase = AAZStrType(
                serialized_name="downloadPhase",
                flags={"read_only": True},
            )
            download_progress.number_of_updates_downloaded = AAZIntType(
                serialized_name="numberOfUpdatesDownloaded",
                flags={"read_only": True},
            )
            download_progress.number_of_updates_to_download = AAZIntType(
                serialized_name="numberOfUpdatesToDownload",
                flags={"read_only": True},
            )
            download_progress.percent_complete = AAZIntType(
                serialized_name="percentComplete",
                flags={"read_only": True},
            )
            download_progress.total_bytes_downloaded = AAZFloatType(
                serialized_name="totalBytesDownloaded",
                flags={"read_only": True},
            )
            download_progress.total_bytes_to_download = AAZFloatType(
                serialized_name="totalBytesToDownload",
                flags={"read_only": True},
            )

            install_progress = cls._schema_on_200.properties.install_progress
            install_progress.number_of_updates_installed = AAZIntType(
                serialized_name="numberOfUpdatesInstalled",
                flags={"read_only": True},
            )
            install_progress.number_of_updates_to_install = AAZIntType(
                serialized_name="numberOfUpdatesToInstall",
                flags={"read_only": True},
            )
            install_progress.percent_complete = AAZIntType(
                serialized_name="percentComplete",
                flags={"read_only": True},
            )

            return cls._schema_on_200


class _ShowJobHelper:
    """Helper class for ShowJob"""


__all__ = ["ShowJob"]
