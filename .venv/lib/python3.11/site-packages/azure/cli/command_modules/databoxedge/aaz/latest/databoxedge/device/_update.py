# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge device update",
)
class Update(AAZCommand):
    """Update a Data Box Edge/Data Box Gateway resource.

    :example: Modify a Data Box Edge/Data Box Gateway resource.
        az databoxedge device update --name "testedgedevice" --tags Key1="value1" Key2="value2" --resource-group "GroupForEdgeAutomation"
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.databoxedge/databoxedgedevices/{}", "2021-02-01-preview"],
        ]
    }

    def _handler(self, command_args):
        super()._handler(command_args)
        self._execute_operations()
        return self._output()

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.device_name = AAZStrArg(
            options=["-n", "--name", "--device-name"],
            help="The device name.",
            required=True,
            id_part="name",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.tags = AAZDictArg(
            options=["--tags"],
            help="The tags attached to the Data Box Edge/Gateway resource.",
        )

        tags = cls._args_schema.tags
        tags.Element = AAZStrArg()

        # define Arg Group "Parameters"

        # define Arg Group "Properties"
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.DevicesUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class DevicesUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PATCH"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                typ=AAZObjectType,
                typ_kwargs={"flags": {"required": True, "client_flatten": True}}
            )
            _builder.set_prop("properties", AAZObjectType, typ_kwargs={"flags": {"client_flatten": True}})
            _builder.set_prop("tags", AAZDictType, ".tags")

            tags = _builder.get(".tags")
            if tags is not None:
                tags.set_elements(AAZStrType, ".")

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.etag = AAZStrType()
            _schema_on_200.id = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.identity = AAZObjectType()
            _schema_on_200.kind = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.location = AAZStrType(
                flags={"required": True},
            )
            _schema_on_200.name = AAZStrType(
                flags={"read_only": True},
            )
            _schema_on_200.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _schema_on_200.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            _schema_on_200.sku = AAZObjectType()
            _schema_on_200.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _schema_on_200.tags = AAZDictType()
            _schema_on_200.type = AAZStrType(
                flags={"read_only": True},
            )

            identity = cls._schema_on_200.identity
            identity.principal_id = AAZStrType(
                serialized_name="principalId",
                flags={"read_only": True},
            )
            identity.tenant_id = AAZStrType(
                serialized_name="tenantId",
                flags={"read_only": True},
            )
            identity.type = AAZStrType()

            properties = cls._schema_on_200.properties
            properties.configured_role_types = AAZListType(
                serialized_name="configuredRoleTypes",
                flags={"read_only": True},
            )
            properties.culture = AAZStrType(
                flags={"read_only": True},
            )
            properties.data_box_edge_device_status = AAZStrType(
                serialized_name="dataBoxEdgeDeviceStatus",
            )
            properties.description = AAZStrType()
            properties.device_hcs_version = AAZStrType(
                serialized_name="deviceHcsVersion",
                flags={"read_only": True},
            )
            properties.device_local_capacity = AAZIntType(
                serialized_name="deviceLocalCapacity",
                flags={"read_only": True},
            )
            properties.device_model = AAZStrType(
                serialized_name="deviceModel",
                flags={"read_only": True},
            )
            properties.device_software_version = AAZStrType(
                serialized_name="deviceSoftwareVersion",
                flags={"read_only": True},
            )
            properties.device_type = AAZStrType(
                serialized_name="deviceType",
                flags={"read_only": True},
            )
            properties.edge_profile = AAZObjectType(
                serialized_name="edgeProfile",
                flags={"read_only": True},
            )
            properties.friendly_name = AAZStrType(
                serialized_name="friendlyName",
            )
            properties.model_description = AAZStrType(
                serialized_name="modelDescription",
            )
            properties.node_count = AAZIntType(
                serialized_name="nodeCount",
                flags={"read_only": True},
            )
            properties.resource_move_details = AAZObjectType(
                serialized_name="resourceMoveDetails",
                flags={"read_only": True},
            )
            properties.serial_number = AAZStrType(
                serialized_name="serialNumber",
                flags={"read_only": True},
            )
            properties.time_zone = AAZStrType(
                serialized_name="timeZone",
                flags={"read_only": True},
            )

            configured_role_types = cls._schema_on_200.properties.configured_role_types
            configured_role_types.Element = AAZStrType()

            edge_profile = cls._schema_on_200.properties.edge_profile
            edge_profile.subscription = AAZObjectType()

            subscription = cls._schema_on_200.properties.edge_profile.subscription
            subscription.id = AAZStrType()
            subscription.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            subscription.registration_date = AAZStrType(
                serialized_name="registrationDate",
            )
            subscription.registration_id = AAZStrType(
                serialized_name="registrationId",
            )
            subscription.state = AAZStrType()
            subscription.subscription_id = AAZStrType(
                serialized_name="subscriptionId",
            )

            properties = cls._schema_on_200.properties.edge_profile.subscription.properties
            properties.location_placement_id = AAZStrType(
                serialized_name="locationPlacementId",
            )
            properties.quota_id = AAZStrType(
                serialized_name="quotaId",
            )
            properties.registered_features = AAZListType(
                serialized_name="registeredFeatures",
            )
            properties.serialized_details = AAZStrType(
                serialized_name="serializedDetails",
            )
            properties.tenant_id = AAZStrType(
                serialized_name="tenantId",
            )

            registered_features = cls._schema_on_200.properties.edge_profile.subscription.properties.registered_features
            registered_features.Element = AAZObjectType()

            _element = cls._schema_on_200.properties.edge_profile.subscription.properties.registered_features.Element
            _element.name = AAZStrType()
            _element.state = AAZStrType()

            resource_move_details = cls._schema_on_200.properties.resource_move_details
            resource_move_details.operation_in_progress = AAZStrType(
                serialized_name="operationInProgress",
            )
            resource_move_details.operation_in_progress_lock_timeout_in_utc = AAZStrType(
                serialized_name="operationInProgressLockTimeoutInUTC",
            )

            sku = cls._schema_on_200.sku
            sku.name = AAZStrType()
            sku.tier = AAZStrType()

            system_data = cls._schema_on_200.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            tags = cls._schema_on_200.tags
            tags.Element = AAZStrType()

            return cls._schema_on_200


class _UpdateHelper:
    """Helper class for Update"""


__all__ = ["Update"]
