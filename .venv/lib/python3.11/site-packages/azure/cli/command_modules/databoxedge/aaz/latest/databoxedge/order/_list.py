# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge order list",
)
class List(AAZCommand):
    """List all the orders related to a Data Box Edge/Data Box Gateway device.

    :example: List all the orders related to a Data Box Edge/Data Box Gateway device.
        az databoxedge order list --device-name testedgedevice --resource-group GroupForEdgeAutomation
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.databoxedge/databoxedgedevices/{}/orders", "2021-02-01-preview"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.device_name = AAZStrArg(
            options=["-d", "--device-name"],
            help="The order details of a device.",
            required=True,
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.OrdersListByDataBoxEdgeDevice(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class OrdersListByDataBoxEdgeDevice(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}/orders",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType(
                flags={"read_only": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.id = AAZStrType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.properties = AAZObjectType(
                flags={"client_flatten": True},
            )
            _element.provisioning_state = AAZStrType(
                serialized_name="provisioningState",
                flags={"read_only": True},
            )
            _element.system_data = AAZObjectType(
                serialized_name="systemData",
                flags={"read_only": True},
            )
            _element.type = AAZStrType(
                flags={"read_only": True},
            )

            properties = cls._schema_on_200.value.Element.properties
            properties.contact_information = AAZObjectType(
                serialized_name="contactInformation",
                flags={"required": True},
            )
            properties.current_status = AAZObjectType(
                serialized_name="currentStatus",
            )
            _ListHelper._build_schema_order_status_read(properties.current_status)
            properties.delivery_tracking_info = AAZListType(
                serialized_name="deliveryTrackingInfo",
                flags={"read_only": True},
            )
            properties.order_history = AAZListType(
                serialized_name="orderHistory",
                flags={"read_only": True},
            )
            properties.return_tracking_info = AAZListType(
                serialized_name="returnTrackingInfo",
                flags={"read_only": True},
            )
            properties.serial_number = AAZStrType(
                serialized_name="serialNumber",
                flags={"read_only": True},
            )
            properties.shipment_type = AAZStrType(
                serialized_name="shipmentType",
            )
            properties.shipping_address = AAZObjectType(
                serialized_name="shippingAddress",
            )

            contact_information = cls._schema_on_200.value.Element.properties.contact_information
            contact_information.company_name = AAZStrType(
                serialized_name="companyName",
                flags={"required": True},
            )
            contact_information.contact_person = AAZStrType(
                serialized_name="contactPerson",
                flags={"required": True},
            )
            contact_information.email_list = AAZListType(
                serialized_name="emailList",
                flags={"required": True},
            )
            contact_information.phone = AAZStrType(
                flags={"required": True},
            )

            email_list = cls._schema_on_200.value.Element.properties.contact_information.email_list
            email_list.Element = AAZStrType()

            delivery_tracking_info = cls._schema_on_200.value.Element.properties.delivery_tracking_info
            delivery_tracking_info.Element = AAZObjectType()
            _ListHelper._build_schema_tracking_info_read(delivery_tracking_info.Element)

            order_history = cls._schema_on_200.value.Element.properties.order_history
            order_history.Element = AAZObjectType()
            _ListHelper._build_schema_order_status_read(order_history.Element)

            return_tracking_info = cls._schema_on_200.value.Element.properties.return_tracking_info
            return_tracking_info.Element = AAZObjectType()
            _ListHelper._build_schema_tracking_info_read(return_tracking_info.Element)

            shipping_address = cls._schema_on_200.value.Element.properties.shipping_address
            shipping_address.address_line1 = AAZStrType(
                serialized_name="addressLine1",
            )
            shipping_address.address_line2 = AAZStrType(
                serialized_name="addressLine2",
            )
            shipping_address.address_line3 = AAZStrType(
                serialized_name="addressLine3",
            )
            shipping_address.city = AAZStrType()
            shipping_address.country = AAZStrType(
                flags={"required": True},
            )
            shipping_address.postal_code = AAZStrType(
                serialized_name="postalCode",
            )
            shipping_address.state = AAZStrType()

            system_data = cls._schema_on_200.value.Element.system_data
            system_data.created_at = AAZStrType(
                serialized_name="createdAt",
            )
            system_data.created_by = AAZStrType(
                serialized_name="createdBy",
            )
            system_data.created_by_type = AAZStrType(
                serialized_name="createdByType",
            )
            system_data.last_modified_at = AAZStrType(
                serialized_name="lastModifiedAt",
            )
            system_data.last_modified_by = AAZStrType(
                serialized_name="lastModifiedBy",
            )
            system_data.last_modified_by_type = AAZStrType(
                serialized_name="lastModifiedByType",
            )

            return cls._schema_on_200


class _ListHelper:
    """Helper class for List"""

    _schema_order_status_read = None

    @classmethod
    def _build_schema_order_status_read(cls, _schema):
        if cls._schema_order_status_read is not None:
            _schema.additional_order_details = cls._schema_order_status_read.additional_order_details
            _schema.comments = cls._schema_order_status_read.comments
            _schema.status = cls._schema_order_status_read.status
            _schema.tracking_information = cls._schema_order_status_read.tracking_information
            _schema.update_date_time = cls._schema_order_status_read.update_date_time
            return

        cls._schema_order_status_read = _schema_order_status_read = AAZObjectType()

        order_status_read = _schema_order_status_read
        order_status_read.additional_order_details = AAZDictType(
            serialized_name="additionalOrderDetails",
            flags={"read_only": True},
        )
        order_status_read.comments = AAZStrType()
        order_status_read.status = AAZStrType(
            flags={"required": True},
        )
        order_status_read.tracking_information = AAZObjectType(
            serialized_name="trackingInformation",
            flags={"read_only": True},
        )
        cls._build_schema_tracking_info_read(order_status_read.tracking_information)
        order_status_read.update_date_time = AAZStrType(
            serialized_name="updateDateTime",
            flags={"read_only": True},
        )

        additional_order_details = _schema_order_status_read.additional_order_details
        additional_order_details.Element = AAZStrType()

        _schema.additional_order_details = cls._schema_order_status_read.additional_order_details
        _schema.comments = cls._schema_order_status_read.comments
        _schema.status = cls._schema_order_status_read.status
        _schema.tracking_information = cls._schema_order_status_read.tracking_information
        _schema.update_date_time = cls._schema_order_status_read.update_date_time

    _schema_tracking_info_read = None

    @classmethod
    def _build_schema_tracking_info_read(cls, _schema):
        if cls._schema_tracking_info_read is not None:
            _schema.carrier_name = cls._schema_tracking_info_read.carrier_name
            _schema.serial_number = cls._schema_tracking_info_read.serial_number
            _schema.tracking_id = cls._schema_tracking_info_read.tracking_id
            _schema.tracking_url = cls._schema_tracking_info_read.tracking_url
            return

        cls._schema_tracking_info_read = _schema_tracking_info_read = AAZObjectType(
            flags={"read_only": True}
        )

        tracking_info_read = _schema_tracking_info_read
        tracking_info_read.carrier_name = AAZStrType(
            serialized_name="carrierName",
        )
        tracking_info_read.serial_number = AAZStrType(
            serialized_name="serialNumber",
        )
        tracking_info_read.tracking_id = AAZStrType(
            serialized_name="trackingId",
        )
        tracking_info_read.tracking_url = AAZStrType(
            serialized_name="trackingUrl",
        )

        _schema.carrier_name = cls._schema_tracking_info_read.carrier_name
        _schema.serial_number = cls._schema_tracking_info_read.serial_number
        _schema.tracking_id = cls._schema_tracking_info_read.tracking_id
        _schema.tracking_url = cls._schema_tracking_info_read.tracking_url


__all__ = ["List"]
