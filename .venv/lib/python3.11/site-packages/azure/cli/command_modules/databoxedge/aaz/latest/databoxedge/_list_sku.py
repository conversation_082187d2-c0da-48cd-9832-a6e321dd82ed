# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge list-sku",
)
class ListSku(AAZCommand):
    """List all the available Skus and information related to them.

    :example: List all the available Skus in the region and information related to them.
        az databoxedge list-sku
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/providers/microsoft.databoxedge/availableskus", "2021-02-01-preview"],
        ]
    }

    AZ_SUPPORT_PAGINATION = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_paging(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.AvailableSkusList(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance.value, client_flatten=True)
        next_link = self.deserialize_output(self.ctx.vars.instance.next_link)
        return result, next_link

    class AvailableSkusList(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/providers/Microsoft.DataBoxEdge/availableSkus",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()

            _schema_on_200 = cls._schema_on_200
            _schema_on_200.next_link = AAZStrType(
                serialized_name="nextLink",
                flags={"read_only": True},
            )
            _schema_on_200.value = AAZListType(
                flags={"read_only": True},
            )

            value = cls._schema_on_200.value
            value.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element
            _element.api_versions = AAZListType(
                serialized_name="apiVersions",
                flags={"read_only": True},
            )
            _element.availability = AAZStrType(
                flags={"read_only": True},
            )
            _element.capabilities = AAZListType(
                flags={"read_only": True},
            )
            _element.costs = AAZListType(
                flags={"read_only": True},
            )
            _element.family = AAZStrType(
                flags={"read_only": True},
            )
            _element.kind = AAZStrType(
                flags={"read_only": True},
            )
            _element.location_info = AAZListType(
                serialized_name="locationInfo",
                flags={"read_only": True},
            )
            _element.locations = AAZListType(
                flags={"read_only": True},
            )
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.resource_type = AAZStrType(
                serialized_name="resourceType",
                flags={"read_only": True},
            )
            _element.shipment_types = AAZListType(
                serialized_name="shipmentTypes",
                flags={"read_only": True},
            )
            _element.signup_option = AAZStrType(
                serialized_name="signupOption",
                flags={"read_only": True},
            )
            _element.size = AAZStrType(
                flags={"read_only": True},
            )
            _element.tier = AAZStrType(
                flags={"read_only": True},
            )
            _element.version = AAZStrType(
                flags={"read_only": True},
            )

            api_versions = cls._schema_on_200.value.Element.api_versions
            api_versions.Element = AAZStrType()

            capabilities = cls._schema_on_200.value.Element.capabilities
            capabilities.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.capabilities.Element
            _element.name = AAZStrType(
                flags={"read_only": True},
            )
            _element.value = AAZStrType(
                flags={"read_only": True},
            )

            costs = cls._schema_on_200.value.Element.costs
            costs.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.costs.Element
            _element.extended_unit = AAZStrType(
                serialized_name="extendedUnit",
                flags={"read_only": True},
            )
            _element.meter_id = AAZStrType(
                serialized_name="meterId",
                flags={"read_only": True},
            )
            _element.quantity = AAZIntType(
                flags={"read_only": True},
            )

            location_info = cls._schema_on_200.value.Element.location_info
            location_info.Element = AAZObjectType()

            _element = cls._schema_on_200.value.Element.location_info.Element
            _element.location = AAZStrType(
                flags={"read_only": True},
            )
            _element.sites = AAZListType(
                flags={"read_only": True},
            )
            _element.zones = AAZListType(
                flags={"read_only": True},
            )

            sites = cls._schema_on_200.value.Element.location_info.Element.sites
            sites.Element = AAZStrType()

            zones = cls._schema_on_200.value.Element.location_info.Element.zones
            zones.Element = AAZStrType()

            locations = cls._schema_on_200.value.Element.locations
            locations.Element = AAZStrType()

            shipment_types = cls._schema_on_200.value.Element.shipment_types
            shipment_types.Element = AAZStrType()

            return cls._schema_on_200


class _ListSkuHelper:
    """Helper class for ListSku"""


__all__ = ["ListSku"]
