# --------------------------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
#
# Code generated by aaz-dev-tools
# --------------------------------------------------------------------------------------------

# pylint: skip-file
# flake8: noqa

from azure.cli.core.aaz import *


@register_command(
    "databoxedge bandwidth-schedule update",
)
class Update(AAZCommand):
    """Update a bandwidth schedule.

    :example: Update a bandwidth schedule.
        az databoxedge bandwidth-schedule update --name "bandwidth-1" --device-name "testedgedevice" --rate-in-mbps 150 --resource-group "GroupForEdgeAutomation" --start "0:0:0" --stop 12:00:00 --days Sunday
    """

    _aaz_info = {
        "version": "2021-02-01-preview",
        "resources": [
            ["mgmt-plane", "/subscriptions/{}/resourcegroups/{}/providers/microsoft.databoxedge/databoxedgedevices/{}/bandwidthschedules/{}", "2021-02-01-preview"],
        ]
    }

    AZ_SUPPORT_NO_WAIT = True

    AZ_SUPPORT_GENERIC_UPDATE = True

    def _handler(self, command_args):
        super()._handler(command_args)
        return self.build_lro_poller(self._execute_operations, self._output)

    _args_schema = None

    @classmethod
    def _build_arguments_schema(cls, *args, **kwargs):
        if cls._args_schema is not None:
            return cls._args_schema
        cls._args_schema = super()._build_arguments_schema(*args, **kwargs)

        # define Arg Group ""

        _args_schema = cls._args_schema
        _args_schema.device_name = AAZStrArg(
            options=["-d", "--device-name"],
            help="The device name.",
            required=True,
            id_part="name",
        )
        _args_schema.name = AAZStrArg(
            options=["-n", "--name"],
            help="The bandwidth schedule name.",
            required=True,
            id_part="child_name_1",
        )
        _args_schema.resource_group = AAZResourceGroupNameArg(
            required=True,
        )
        _args_schema.days = AAZListArg(
            options=["--days"],
            help="The days of the week when this schedule is applicable.",
        )
        _args_schema.rate_in_mbps = AAZIntArg(
            options=["--rate-in-mbps"],
            help="The bandwidth rate in Mbps.",
        )
        _args_schema.start = AAZStrArg(
            options=["--start"],
            help="The start time of the schedule in UTC.",
        )
        _args_schema.stop = AAZStrArg(
            options=["--stop"],
            help="The stop time of the schedule in UTC.",
        )

        days = cls._args_schema.days
        days.Element = AAZStrArg(
            nullable=True,
            enum={"Friday": "Friday", "Monday": "Monday", "Saturday": "Saturday", "Sunday": "Sunday", "Thursday": "Thursday", "Tuesday": "Tuesday", "Wednesday": "Wednesday"},
        )
        return cls._args_schema

    def _execute_operations(self):
        self.pre_operations()
        self.BandwidthSchedulesGet(ctx=self.ctx)()
        self.pre_instance_update(self.ctx.vars.instance)
        self.InstanceUpdateByJson(ctx=self.ctx)()
        self.InstanceUpdateByGeneric(ctx=self.ctx)()
        self.post_instance_update(self.ctx.vars.instance)
        yield self.BandwidthSchedulesCreateOrUpdate(ctx=self.ctx)()
        self.post_operations()

    @register_callback
    def pre_operations(self):
        pass

    @register_callback
    def post_operations(self):
        pass

    @register_callback
    def pre_instance_update(self, instance):
        pass

    @register_callback
    def post_instance_update(self, instance):
        pass

    def _output(self, *args, **kwargs):
        result = self.deserialize_output(self.ctx.vars.instance, client_flatten=True)
        return result

    class BandwidthSchedulesGet(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [200]:
                return self.on_200(session)

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}/bandwidthSchedules/{name}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "GET"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "name", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_bandwidth_schedule_read(cls._schema_on_200)

            return cls._schema_on_200

    class BandwidthSchedulesCreateOrUpdate(AAZHttpOperation):
        CLIENT_TYPE = "MgmtClient"

        def __call__(self, *args, **kwargs):
            request = self.make_request()
            session = self.client.send_request(request=request, stream=False, **kwargs)
            if session.http_response.status_code in [202]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )
            if session.http_response.status_code in [200]:
                return self.client.build_lro_polling(
                    self.ctx.args.no_wait,
                    session,
                    self.on_200,
                    self.on_error,
                    lro_options={"final-state-via": "azure-async-operation"},
                    path_format_arguments=self.url_parameters,
                )

            return self.on_error(session.http_response)

        @property
        def url(self):
            return self.client.format_url(
                "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.DataBoxEdge/dataBoxEdgeDevices/{deviceName}/bandwidthSchedules/{name}",
                **self.url_parameters
            )

        @property
        def method(self):
            return "PUT"

        @property
        def error_format(self):
            return "ODataV4Format"

        @property
        def url_parameters(self):
            parameters = {
                **self.serialize_url_param(
                    "deviceName", self.ctx.args.device_name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "name", self.ctx.args.name,
                    required=True,
                ),
                **self.serialize_url_param(
                    "resourceGroupName", self.ctx.args.resource_group,
                    required=True,
                ),
                **self.serialize_url_param(
                    "subscriptionId", self.ctx.subscription_id,
                    required=True,
                ),
            }
            return parameters

        @property
        def query_parameters(self):
            parameters = {
                **self.serialize_query_param(
                    "api-version", "2021-02-01-preview",
                    required=True,
                ),
            }
            return parameters

        @property
        def header_parameters(self):
            parameters = {
                **self.serialize_header_param(
                    "Content-Type", "application/json",
                ),
                **self.serialize_header_param(
                    "Accept", "application/json",
                ),
            }
            return parameters

        @property
        def content(self):
            _content_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=self.ctx.vars.instance,
            )

            return self.serialize_content(_content_value)

        def on_200(self, session):
            data = self.deserialize_http_content(session)
            self.ctx.set_var(
                "instance",
                data,
                schema_builder=self._build_schema_on_200
            )

        _schema_on_200 = None

        @classmethod
        def _build_schema_on_200(cls):
            if cls._schema_on_200 is not None:
                return cls._schema_on_200

            cls._schema_on_200 = AAZObjectType()
            _UpdateHelper._build_schema_bandwidth_schedule_read(cls._schema_on_200)

            return cls._schema_on_200

    class InstanceUpdateByJson(AAZJsonInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance(self.ctx.vars.instance)

        def _update_instance(self, instance):
            _instance_value, _builder = self.new_content_builder(
                self.ctx.args,
                value=instance,
                typ=AAZObjectType
            )
            _builder.set_prop("properties", AAZObjectType, ".", typ_kwargs={"flags": {"required": True, "client_flatten": True}})

            properties = _builder.get(".properties")
            if properties is not None:
                properties.set_prop("days", AAZListType, ".days", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("rateInMbps", AAZIntType, ".rate_in_mbps", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("start", AAZStrType, ".start", typ_kwargs={"flags": {"required": True}})
                properties.set_prop("stop", AAZStrType, ".stop", typ_kwargs={"flags": {"required": True}})

            days = _builder.get(".properties.days")
            if days is not None:
                days.set_elements(AAZStrType, ".")

            return _instance_value

    class InstanceUpdateByGeneric(AAZGenericInstanceUpdateOperation):

        def __call__(self, *args, **kwargs):
            self._update_instance_by_generic(
                self.ctx.vars.instance,
                self.ctx.generic_update_args
            )


class _UpdateHelper:
    """Helper class for Update"""

    _schema_bandwidth_schedule_read = None

    @classmethod
    def _build_schema_bandwidth_schedule_read(cls, _schema):
        if cls._schema_bandwidth_schedule_read is not None:
            _schema.id = cls._schema_bandwidth_schedule_read.id
            _schema.name = cls._schema_bandwidth_schedule_read.name
            _schema.properties = cls._schema_bandwidth_schedule_read.properties
            _schema.provisioning_state = cls._schema_bandwidth_schedule_read.provisioning_state
            _schema.system_data = cls._schema_bandwidth_schedule_read.system_data
            _schema.type = cls._schema_bandwidth_schedule_read.type
            return

        cls._schema_bandwidth_schedule_read = _schema_bandwidth_schedule_read = AAZObjectType()

        bandwidth_schedule_read = _schema_bandwidth_schedule_read
        bandwidth_schedule_read.id = AAZStrType(
            flags={"read_only": True},
        )
        bandwidth_schedule_read.name = AAZStrType(
            flags={"read_only": True},
        )
        bandwidth_schedule_read.properties = AAZObjectType(
            flags={"required": True, "client_flatten": True},
        )
        bandwidth_schedule_read.provisioning_state = AAZStrType(
            serialized_name="provisioningState",
            flags={"read_only": True},
        )
        bandwidth_schedule_read.system_data = AAZObjectType(
            serialized_name="systemData",
            flags={"read_only": True},
        )
        bandwidth_schedule_read.type = AAZStrType(
            flags={"read_only": True},
        )

        properties = _schema_bandwidth_schedule_read.properties
        properties.days = AAZListType(
            flags={"required": True},
        )
        properties.rate_in_mbps = AAZIntType(
            serialized_name="rateInMbps",
            flags={"required": True},
        )
        properties.start = AAZStrType(
            flags={"required": True},
        )
        properties.stop = AAZStrType(
            flags={"required": True},
        )

        days = _schema_bandwidth_schedule_read.properties.days
        days.Element = AAZStrType()

        system_data = _schema_bandwidth_schedule_read.system_data
        system_data.created_at = AAZStrType(
            serialized_name="createdAt",
        )
        system_data.created_by = AAZStrType(
            serialized_name="createdBy",
        )
        system_data.created_by_type = AAZStrType(
            serialized_name="createdByType",
        )
        system_data.last_modified_at = AAZStrType(
            serialized_name="lastModifiedAt",
        )
        system_data.last_modified_by = AAZStrType(
            serialized_name="lastModifiedBy",
        )
        system_data.last_modified_by_type = AAZStrType(
            serialized_name="lastModifiedByType",
        )

        _schema.id = cls._schema_bandwidth_schedule_read.id
        _schema.name = cls._schema_bandwidth_schedule_read.name
        _schema.properties = cls._schema_bandwidth_schedule_read.properties
        _schema.provisioning_state = cls._schema_bandwidth_schedule_read.provisioning_state
        _schema.system_data = cls._schema_bandwidth_schedule_read.system_data
        _schema.type = cls._schema_bandwidth_schedule_read.type


__all__ = ["Update"]
